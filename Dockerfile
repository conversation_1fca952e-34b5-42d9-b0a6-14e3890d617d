FROM php:7.4-apache

RUN apt-get update && apt-get install -y \
    libpq-dev \
    && docker-php-ext-install pdo pdo_pgsql pgsql \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

RUN a2enmod rewrite

# Enable .htaccess overrides by adding a configuration file
RUN echo '<Directory /var/www/html/atrpgt>\n\tAllowOverride All\n\tRequire all granted\n</Directory>' > /etc/apache2/conf-available/atrpgt.conf \
    && a2enconf atrpgt.conf

WORKDIR /var/www/html/atrpgt

COPY . .

RUN chown -R www-data:www-data /var/www/html/atrpgt/application/cache \
    /var/www/html/atrpgt/application/logs \
    && chmod -R 755 /var/www/html/atrpgt/application/cache \
    /var/www/html/atrpgt/application/logs

    # Create sessions directory and set permissions
RUN mkdir -p /var/www/html/atrpgt/application/cache/sessions \
    && chown -R www-data:www-data /var/www/html/atrpgt/application/cache/sessions \
    && chmod -R 755 /var/www/html/atrpgt/application/cache/sessions

EXPOSE 80

CMD ["apache2-foreground"] 