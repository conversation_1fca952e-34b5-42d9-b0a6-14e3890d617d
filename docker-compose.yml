services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html/atrpgt
    #env_file:
     # - env.php
    #depends_on:
    #  - db
   # environment:
   #   - DB_HOST=**************
   #   - DB_PORT=5432
   #   - DB_NAME=pgtdb
   #   - DB_USER=wgi
   #   - DB_PASSWORD=pgDevDiAwan

  # db:
  #   image: postgres:14
  #   ports:
  #     - "5432:5432"
  #   environment:
  #     - POSTGRES_DB=pgtdb
  #     - POSTGRES_USER=wgi
  #     - POSTGRES_PASSWORD=pgDevDiAwan
  #   volumes:
  #     - pgdata:/var/lib/postgresql/data

# volumes:
#   pgdata: 