<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends CI_Controller {

	/**
	 * Index Page for this controller.
	 *
	 * Maps to the following URL
	 * 		http://example.com//welcome
	 *	- or -
	 * 		http://example.com//welcome/index
	 *	- or -
	 * Since this controller is set as the default controller in
	 * config/routes.php, it's displayed at http://example.com/
	 *
	 * So any other public methods not prefixed with an underscore will
	 * map to //welcome/<method_name>
	 * @see https://codeigniter.com/user_guide/general/urls.html
	 */
	public function __construct()
    {
    // created the construct so that the helpers, libraries, models can be loaded all through this controller
    parent::__construct();
		$this->load->helper('url');
		$this->load->library('session');
		if($this->session->has_userdata('users')==false){
			redirect('login');
		}
    }
	public function index()
	{
		header("Access-Control-Allow-Origin: *");
		$data = array();

		// $idparent = $this->session->users[0]["id_sub_user_group"];

		// if($idparent==0)
		// {
		// 	$parent = $this->session->users[0]["id_user_group"];
		// }	
		// else
		// {	
		// 	$parent = $this->session->users[0]["id_sub_user_group"];
		// }	

		// $data['group'] = $parent; 
		//$data['group'] = $this->session->users['id_user_group'];

		$this->template->set('title', 'Home');
		$this->template->load('default_layout', 'contents' , 'home', $data);
		//**
		// echo $this->session->users[0]["username"];
		// echo "<pre>";
		// print_r($this->session->users); 
		// echo "</pre>";
		// die();
		/**/
	}
}
