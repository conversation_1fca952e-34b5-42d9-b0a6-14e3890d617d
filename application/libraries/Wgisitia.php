<?php

if(!defined('BASEPATH')) exit('No direct script access allowed');

include_once APPPATH . "vendor/autoload.php";
use \Firebase\JWT\JWT;


class Wgisitia
{
    // const ci = null;
    // const lrsdb = null;

	public function __construct() {
        $CI =& get_instance();
        $this->ci = $CI;
        $this->ci->load->database();

        $this->ci->load->library('session');
       // $this->lrsdb = $this->ci->load->database('lrs', TRUE);
        $this->lrs_tolerance = 30; //meter

        if ($this->ci->session->has_userdata('users') == false) {
            redirect('login');
        }
    }


    // =============================== AUTH section =================================

    // cek token valid atau nggak
    public function tokencheck($token, $isEcho = false) {
        try {
            $payload = json_encode(JWT::decode($token, WGI_JWT_KEY, array(WGI_JWT_ALG)));
            $jpayload = json_decode($payload);
            if ($isEcho) {
                echo '{"status": "ok", "data":' . $payload . '}';
            }
            return $jpayload;
        } catch (Exception $e) {
            if ($isEcho) $this->msg_failed($e->getMessage());
            return false;
        }
    }

    // cek apakah user boleh menjalankan kontroller tertentu?
    public function isallow($uristring) {
        $contname = explode('/', $uristring);

        $sesdata = $this->ci->session->all_userdata();
        $idgroup = $sesdata['users']['id_user_group_real'];

        $sql = "select count(*) as jml from v_group_module where id_user_group=$idgroup and url like '$contname[0]%'";
        $res = $this->ci->db->query($sql)->result_array();
        if (sizeof($res) == 1) {
            if ($res[0]['jml'] > 0) {
                return true;
            }
        }
        return false;
    }


    // =============================== Utility section =================================

    private function msg_failed($msg) {
        echo '{"status":"failed", "error":"'.$msg.'"}';
    }

    private function filt_num_decimal($str) {
        return preg_replace("/[^0-9.]/", "", trim($str));
    }

    private function filt_num_integer($str) {
        return preg_replace("/[^0-9]/", "", trim($str));
    }

    private function filt_alphanumeric($str) {
        return preg_replace("/[^A-Za-z0-9]/", "", trim($str));
    }

    private function filt_alphanumericplus($str) {
        return preg_replace("/[^A-Za-z0-9 _.-]/", "", $str);
    }


    public function wgi_is_decimal($str) {
        if ($str === $this->filt_num_decimal($str)) return true;
        return false;
    }

    public function wgi_is_integer($str) {
        if ($str === $this->filt_num_integer($str)) return true;
        return false;
    }

    public function wgi_is_alpha($str) {
        if ($str === $this->filt_alphanumeric($str)) return true;
        return false;
    }

    public function wgi_is_alphaplus($str) {
        if ($str === $this->filt_alphanumericplus($str)) return true;
        return false;
    }


    // =============================== Data utility section =================================


    public function first_val($arr, $key) {
        if (sizeof($arr) > 0) {
            if (isset($arr[0][$key])) {
                return $arr[0][$key];
            }
        }

        return null;
    }


        // =============================== LRS section =================================



    private function route_len($routeid) {
        $thang = $this->ci->session->konfig_tahun_ang;

        // $sql = "select st_length(geom::geography) as pjg from bm_lrs_ruas where routeid='$routeid'";
        $sql = "select shape_len as pjg from bm_lrs_ruas where routeid='$routeid' and thang=$thang";

        $aret = $this->lrsdb->query($sql)->result_array();
        return $this->first_val($aret, 'pjg');
    }




    public function lrs_ruas($kdsatker)
    {
        $thang = $this->ci->session->konfig_tahun_ang;

        //param sanity check
        $kdsatker = $this->filt_alphanumeric($kdsatker);

        $sql = "select routeid as id, route_name as val from bm_lrs_ruas where kd_satker='$kdsatker' and thang=$thang order by routeid";

        $aret = $this->lrsdb->query($sql)->result_array();
        echo json_encode($aret);
    }



    public function lrs_route_len($routeid, $isecho=1) {
        $thang = $this->ci->session->konfig_tahun_ang;

        $routeid = $this->filt_alphanumeric($routeid);

        $sql = "select shape_len as pjg from bm_lrs_ruas where routeid='$routeid' and thang=$thang";

        $aret = $this->lrsdb->query($sql)->result_array();
        $val = $this->first_val($aret, 'pjg');
        if (sizeof($aret) > 0) {
            $pjg = $aret[0]['pjg'];
            $ret = array(
                    "status" => "ok",
                    "length" => floatval($val),
                    "unit" => "m"
                );
            if ($isecho == 1) echo json_encode($ret);
            return floatval($val);
        }

        if ($isecho == 1) $this->msg_failed('Route not found!');
        return null;
    }


    public function lrs_sta_segment($routeid, $from, $to, $isecho = 1, $fmt = "geojson", $srid = 4326)
    {
        $thang = $this->ci->session->konfig_tahun_ang;

        $routeid = $this->filt_alphanumeric($routeid);
        $from = $this->filt_num_decimal($from);
        $to = $this->filt_num_decimal($to);

        $routelen = $this->route_len($routeid);
        if (!$routelen) {
            $ret["status"] = "failed";
            $ret["error"] = "Data id route, STA awal atau STA akhir tidak valid!";
            //$this->msg_failed('Data id route, STA awal atau STA akhir tidak valid!');
            return;
        } else if (($from >= $to) or ($to > $routelen) or ($from < 0)) {
            $ret["status"] = "failed";
            $ret["error"] = "Data id route, STA awal atau STA akhir tidak valid!";
            //$this->msg_failed('Data id route, STA awal atau STA akhir tidak valid!');
            return;
        }

        $q1 = "select st_asewkt(st_linesubstring(geom, ($from/$routelen), ($to/$routelen))) as wkt
               from bm_lrs_ruas
               where routeid = '$routeid' and thang=$thang";
        $wkt = $this->first_val($this->lrsdb->query($q1)->result_array(), 'wkt');
        if (!$wkt) {
            $ret["status"] = "failed";
            $ret["error"] = "Route, STA awal atau STA akhir tidak valid!";
            //$this->msg_failed("Route, STA awal atau STA akhir tidak valid!");
            return;
        }
        if ($isecho) echo '{"status":"ok", "geom":"' . $wkt . '"}';

        return $wkt;
    }

    public function lrs_sta_segment_draw($routeid, $from, $to, $isecho = 0) {

        $arr = explode(';', $this->lrs_sta_segment($routeid, $from, $to, $isecho));
        if (count($arr) > 1){
            $b = explode(',', str_replace(array('LINESTRING(', ')'), '', $arr[1]));
            $c = '';
            for ($i = 0; $i < count($b); $i++){
                $c = $c . '[' . str_replace(' ', ',', $b[$i]) . '],';                
            }
            $d = 'var route = [' . substr($c, 0, -1) . ']';
            //echo $d;
            //$this->response($d, 200); 
            $json = [
                'status' => 'ok',
                'data' => $d
            ];
            echo json_encode($json); 
        }        


    }


    public function lrs_coord_sta($lon, $lat, $isecho = 1, $srid = 4326)
    {
        $thang = $this->ci->session->konfig_tahun_ang;


        //echo $lon.' '.$lat;
      //  die();

        //echo '<br>';

        $lon = $this->filt_num_decimal($lon);
        //$lat = $this->filt_num_decimal($lat);

        //echo $lon.' '.$lat;
        //die();

        //kotak 0.01 derajat (kl 1 km) dari lon, lat
        $x1 = $lon - 0.01; $x2 = $lon + 0.01;
        $y1 = $lat - 0.01; $y2 = $lat + 0.01;

        $q1 =   "select routeid, route_name, kabupaten_kota, jarak from
                (select routeid, route_name, kabupaten_kota, st_distance(geom::geography, st_geomfromtext('SRID=4326;POINT($lon $lat)')::geography) as jarak
                from (
                        select routeid, route_name, kabupaten_kota, geom from bm_lrs_ruas
                        where geom && st_geomfromtext('SRID=4326;POLYGON(($x1 $y1, $x2 $y1, $x2 $y2, $x1 $y2, $x1 $y1))')
                    ) z
                ) x
                order by jarak asc limit 1";
               // echo $q1;  
        // die();                    

        $jarak = $this->first_val($this->lrsdb->query($q1)->result_array(), 'jarak');

        if (($jarak === null) or ($jarak > $this->lrs_tolerance)) {
            $this->msg_failed('Route not found at this location!');
            return;
        }

        $routeid = $this->first_val($this->lrsdb->query($q1)->result_array(), 'routeid');
        $route = $this->first_val($this->lrsdb->query($q1)->result_array(), 'route_name');
        $kabkot = $this->first_val($this->lrsdb->query($q1)->result_array(), 'kabupaten_kota');

        $q2 =  "select round(ST_LineLocatePoint(geom, st_geomfromtext('SRID=4326;POINT($lon $lat)'))*shape_len) as sta
                from bm_lrs_ruas
                where routeid='$routeid' and thang=$thang";
        $sta = $this->first_val($this->lrsdb->query($q2)->result_array(), 'sta');

        $aret = array(
            "status"    => "ok",
            "x"         => floatval($lon),
            "y"         => floatval($lat),
            "routeid"   => $routeid,
            "route"     => $route,
            "kabkot"    => $kabkot,
            "at"        => intval($sta),
            "unit"      => "m"
        );

        $ret = json_encode($aret);
        if ($isecho != 1) {
            return $ret;
        } else {
            echo $ret;
        }
    }


    public function lrs_sta_coord($routeid, $sta, $isecho = 1, $srid = 4326) {
        $thang = $this->ci->session->konfig_tahun_ang;

        $routeid = $this->filt_alphanumeric($routeid);
        $sta = $this->filt_num_decimal($sta);

        $found = false;

        $rslen = $this->route_len($routeid);
        if (($rslen <= 0) or ($rslen < $sta)) {
            $found = false;
        } else {
            $sql = "select st_x(pt) as lon, st_y(pt) as lat
                    from (
                        select ST_LineInterpolatePoint(geom, $sta/shape_len) as pt from bm_lrs_ruas where routeid = '$routeid' and thang=$thang
                    ) z";

            $aret = $this->lrsdb->query($sql)->result_array();
            if (sizeof($aret) > 0) {
                $ret["status"] = "ok";
                $ret["routeid"] = $routeid;
                $ret["lon"] = $aret[0]['lon'];
                $ret["lat"] = $aret[0]['lat'];

                $found = true;
            }
        }

        if (!$found) {
            $ret["status"] = "failed";
            $ret["error"] = "Route or STA not found";
        }

        if ($isecho) echo json_encode($ret);
        return $ret;
    }


    //TODO: tambahi thang
    public function lrs_coord_jbt($nojbt, $isecho = 1) {
        $nojbt = $this->filt_alphanumericplus($nojbt);
        $thang = $this->ci->session->konfig_tahun_ang;

        $q1 =  "select st_x(geom) as lon, st_y(geom) as lat
                from bm_lrs_jembatan where nojbt = '$nojbt' and valid=1 and thang=$thang";

        $ares = $this->lrsdb->query($q1)->result_array();
        if (sizeof($ares) < 1) {
            if ($isecho) $this->msg_failed("Jembatan dengan nomor tersebut tidak ditemukan!");
            $ret = array(
                "status"    => "failed",
                "error"     => "Jembatan dengan nomor tersebut tidak ditemukan!"
            );
            return $ret;
        }

        $lon = $this->first_val($ares, 'lon');
        if (!$lon) {
            if ($isecho) $this->msg_failed("Geometri jembatan dengan nomor tersebut tidak ditemukan!");
            $ret = array(
                "status"    => "failed",
                "error"     => "Geometri jembatan dengan nomor tersebut tidak ditemukan!"
            );
            return $ret;
        }
        $lat = $this->first_val($ares, 'lat');
        $ret = array(
            "status"    => "ok",
            "lon"       => $lon,
            "lat"       => $lat
        );

        if ($isecho) echo json_encode($ret);

        return $ret;
    }

   public function lrs_bridge($noruas, $isecho = 1)
    {
        $thang = $this->ci->session->konfig_tahun_ang;

        $q1 =  "select nojbt, namajbt from bm_lrs_jembatan where noruas = '$noruas' and valid=1 and uptodate = 1 and thang=$thang";

        $ares = $this->lrsdb->query($q1)->result_array();

        $realdata = array();
        foreach ($ares as $dt) {
            $row["id"] = $dt['nojbt'];
            $row["val"] = $dt['nojbt'] .' - '. $dt['namajbt'];
            // $row["noprop"] = $dt->NOPROV;
            // $row["tahun"] = $dt->TAHUN;
            // $row["panjang"] = $dt->PANJANG_JE;
            // $row["noruas"] = $dt->NORUAS;
            // $row["sffx"] = $dt->SFFX;
            // $row["lon"] = $dt->LONGITUD_1;
            // $row["lat"] = $dt->LATITUDE_1;
            // $row["jml_bentang"] = $dt->JML_BENTAN;
            // $row["lebar"] = $dt->LEBAR_JEMB;

            $realdata[] = $row;
        }

        if ($isecho) echo json_encode($realdata);

        return $realdata;
    }
    
    //pok
    private function route_len_pok($routeid) {
        $thang = $this->ci->session->konfig_tahun_ang;

        // $sql = "select st_length(geom::geography) as pjg from bm_lrs_ruas where routeid='$routeid'";
        $sql = "select shape_len as pjg from bm_lrs_ruas_pok where routeid='$routeid' and thang=$thang";

        $aret = $this->lrsdb->query($sql)->result_array();
        return $this->first_val($aret, 'pjg');
    }
    
    public function lrs_sta_coord_pok($routeid, $sta, $isecho = 1, $srid = 4326) {
        $thang = $this->ci->session->konfig_tahun_ang;

        $routeid = $this->filt_alphanumeric($routeid);
        $sta = $this->filt_num_decimal($sta);

        $found = false;

        $rslen = $this->route_len_pok($routeid);
        if (($rslen <= 0) or ($rslen < $sta)) {
            $found = false;
        } else {
            $sql = "select st_x(pt) as lon, st_y(pt) as lat
                    from (
                        select ST_LineInterpolatePoint(geom, $sta/shape_len) as pt from bm_lrs_ruas_pok where routeid = '$routeid' and thang=$thang
                    ) z";

            $aret = $this->lrsdb->query($sql)->result_array();
            if (sizeof($aret) > 0) {
                $ret["status"] = "ok";
                $ret["routeid"] = $routeid;
                $ret["lon"] = $aret[0]['lon'];
                $ret["lat"] = $aret[0]['lat'];

                $found = true;
            }
        }

        if (!$found) {
            $ret["status"] = "failed";
            $ret["error"] = "Route or STA not found";
        }

        if ($isecho) echo json_encode($ret);
        return $ret;
    }
    
    public function lrs_coord_sta_pok($lon, $lat, $isecho = 1, $srid = 4326){
        $thang = $this->ci->session->konfig_tahun_ang;
        $lon = $this->filt_num_decimal($lon);

        //kotak 0.01 derajat (kl 1 km) dari lon, lat
        $x1 = $lon - 0.01; $x2 = $lon + 0.01;
        $y1 = $lat - 0.01; $y2 = $lat + 0.01;

        $q1 =   "select routeid, route_name, jarak from
                (select routeid, route_name, st_distance(geom::geography, st_geomfromtext('SRID=4326;POINT($lon $lat)')::geography) as jarak
                from (
                        select routeid, route_name, geom from bm_lrs_ruas_pok
                        where geom && st_geomfromtext('SRID=4326;POLYGON(($x1 $y1, $x2 $y1, $x2 $y2, $x1 $y2, $x1 $y1))')
                    ) z
                ) x
                order by jarak asc limit 1";
               // echo $q1;  
        // die();                    

        $jarak = $this->first_val($this->lrsdb->query($q1)->result_array(), 'jarak');

        if (($jarak === null) or ($jarak > $this->lrs_tolerance)) {
            $this->msg_failed('Route not found at this location!');
            return;
        }

        $routeid = $this->first_val($this->lrsdb->query($q1)->result_array(), 'routeid');
        $route = $this->first_val($this->lrsdb->query($q1)->result_array(), 'route_name');
        $kabkot = $this->first_val($this->lrsdb->query($q1)->result_array(), 'kabupaten_kota');

        $q2 =  "select round(ST_LineLocatePoint(geom, st_geomfromtext('SRID=4326;POINT($lon $lat)'))*shape_len) as sta
                from bm_lrs_ruas_pok
                where routeid='$routeid' and thang=$thang";
        $sta = $this->first_val($this->lrsdb->query($q2)->result_array(), 'sta');

        $aret = array(
            "status"    => "ok",
            "x"         => floatval($lon),
            "y"         => floatval($lat),
            "routeid"   => $routeid,
            "route"     => $route,
            "kabkot"    => $kabkot,
            "at"        => intval($sta),
            "unit"      => "m"
        );

        $ret = json_encode($aret);
        if ($isecho != 1) {
            return $ret;
        } else {
            echo $ret;
        }
    }
    
    public function lrs_sta_segment_pok($routeid, $from, $to, $isecho = 1, $fmt = "geojson", $srid = 4326){
        $thang = $this->ci->session->konfig_tahun_ang;

        $routeid = $this->filt_alphanumeric($routeid);
        $from = $this->filt_num_decimal($from);
        $to = $this->filt_num_decimal($to);

        $routelen = $this->route_len_pok($routeid);
        if (!$routelen) {
            $this->msg_failed('Data id route, STA awal atau STA akhir tidak valid!');
            return;
        } else if (($from >= $to) or ($to > $routelen) or ($from < 0)) {
            $this->msg_failed('Data id route, STA awal atau STA akhir tidak valid!');
            return;
        }

        $q1 = "select st_asewkt(st_linesubstring(geom, ($from/$routelen), ($to/$routelen))) as wkt
               from bm_lrs_ruas_pok
               where routeid = '$routeid' and thang=$thang";
        $wkt = $this->first_val($this->lrsdb->query($q1)->result_array(), 'wkt');
        if (!$wkt) {
            $this->msg_failed("Route, STA awal atau STA akhir tidak valid!");
            return;
        }
        if ($isecho) echo '{"status":"ok", "geom":"' . $wkt . '"}';

        return $wkt;
    }
    
    public function lrs_sta_segment_draw_pok($routeid, $from, $to, $isecho = 0) {
        $arr = explode(';', $this->lrs_sta_segment_pok($routeid, $from, $to, $isecho));
        if (count($arr) > 1){
            $b = explode(',', str_replace(array('LINESTRING(', ')'), '', $arr[1]));
            $c = '';
            for ($i = 0; $i < count($b); $i++){
                $c = $c . '[' . str_replace(' ', ',', $b[$i]) . '],';                
            }
            $d = 'var route = [' . substr($c, 0, -1) . ']';
            //echo $d;
            //$this->response($d, 200); 
            $json = [
                'status' => 'ok',
                'data' => $d
            ];
            echo json_encode($json); 
        }     
    }
    
    public function lrs_route_len_pok($routeid, $isecho=1) {
        $thang = $this->ci->session->konfig_tahun_ang;

        $routeid = $this->filt_alphanumeric($routeid);

        $sql = "select shape_len as pjg from bm_lrs_ruas_pok where routeid='$routeid' and thang=$thang";

        $aret = $this->lrsdb->query($sql)->result_array();
        $val = $this->first_val($aret, 'pjg');
        if (sizeof($aret) > 0) {
            $pjg = $aret[0]['pjg'];
            $ret = array(
                    "status" => "ok",
                    "length" => floatval($val),
                    "unit" => "m"
                );
            if ($isecho == 1) echo json_encode($ret);
            return floatval($val);
        }

        if ($isecho == 1) $this->msg_failed('Route not found!');
        return null;
    }
    
    //exit xss removed
    public function handle_removed($arr, $string = 'removed'){
        foreach($arr as $key => $value) {
            if(preg_match("/\b".$string."\b/i", $value)) {
                //echo $key;
                exit();
            }
        }
    }


}
