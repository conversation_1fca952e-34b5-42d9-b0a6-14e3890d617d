<?php

(defined('BASEPATH')) OR exit('No direct script access allowed');

class MY_Controller extends CI_Controller {

    public function __construct() {
        parent::__construct();
    }

    public function get_data($url) {
        $url = $url; // http://************:12890/ $url = registarsi/
        $ch = curl_init($url);

        $header = [];
        $header[] = 'Content-type: application/json';
        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
        $header[] = "Cache-Control: no-cache";
        $header[] = "accept-encoding:*";
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);

        if (!$result) {
            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
            die("Koneksi Gagal");
        } else {
            return $result;
            //print_r($result);
        }
    }

    //listing tanpa parameter untuk akses module yg disertai authorize token
    public function get_data_module($url) {
        $url = $url; // http://************:12890/ $url = registarsi/
        $ch = curl_init($url);

        $header = [];
        $header[] = 'Content-type: application/json';
        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
        $header[] = "Cache-Control: no-cache";
        $header[] = "accept-encoding:*";
        $header[] = "Authorization:" . $this->session->userdata("token");
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);

        if (!$result) {
            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
            die("Koneksi Gagal");
        } else {
            return $result;
            //print_r($result);
        }
    }

    //listing tanpa parameter untuk akses module yg disertai authorize token
    public function get_data_module_by_id($url) {
        $url = $url; // http://************:12890/ $url = registarsi/
        $ch = curl_init($url);

        $header = [];
        $header[] = 'Content-type: application/json';
        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
        $header[] = "Cache-Control: no-cache";
        $header[] = "accept-encoding:*";
        $header[] = "Authorization:" . $this->session->userdata("token");
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);

        if (!$result) {
            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
            die("Koneksi Gagal");
        } else {
            return $result;
            //print_r($result);
        }
    }

    //insert data ke module disertai authorize token
    function insert_module($url, $data_raw) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => "12891",
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($data_raw),
            CURLOPT_HTTPHEADER => array(
                "Cache-Control: no-cache",
                "client-id: webgis",
                "client-pass: webgisindonesia",
                "content-type: application/json",
                "Authorization:" . $this->session->userdata("token"),
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return "cURL Error #:" . $err;
        } else {
            return $response;
        }
    }

    function insert_moduleduo($url, $data_raw) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => "12890",
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($data_raw),
            CURLOPT_HTTPHEADER => array(
                "Cache-Control: no-cache",
                "client-id: webgis",
                "client-pass: webgisindonesia",
                "content-type: application/json",
                "Authorization:" . $this->session->userdata("token"),
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return "cURL Error #:" . $err;
        } else {
            return $response;
        }
    }

    //insert
    function listingModule($url, $data_raw) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => "12890",
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($data_raw),
            CURLOPT_HTTPHEADER => array(
                "Cache-Control: no-cache",
                "client-id: webgis",
                "client-pass: webgisindonesia",
                "content-type: application/json",
                "Authorization:" . $this->session->userdata("token"),
            ),
        ));
        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return "cURL Error #:" . $err;
        } else {
            return $response;
        }
    }

    //Update data ke module disertai authorize token
    function update_module($url, $data_raw) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => "12891",
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "PUT",
            CURLOPT_POSTFIELDS => json_encode($data_raw),
            CURLOPT_HTTPHEADER => array(
                "Cache-Control: no-cache",
                "client-id: webgis",
                "client-pass: webgisindonesia",
                "content-type: application/json",
                "Authorization:" . $this->session->userdata("token"),
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return "cURL Error #:" . $err;
        } else {
            return $response;
        }
    }

    public function post_data($url, $data_raw) {
        $data = json_encode($data_raw);
        $url = $url; //url di set global 
        $ch = curl_init($url);
        $header = [];
        $header[] = 'Content-type: application/json';
        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
        $header[] = "Cache-Control: no-cache";
        $header[] = "accept-encoding:*";
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        $result = curl_exec($ch);
        $err = curl_error($ch);
        curl_close($ch);

        if (!$result) {
            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
            return $err;
        } else {
            return $result;
            //print_r($result);
        }
    }

    function newpost_data($url, $data_raw) {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => "12891",
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($data_raw),
            CURLOPT_HTTPHEADER => array(
                "Cache-Control: no-cache",
                "client-id: webgis",
                "client-pass: webgisindonesia",
                "content-type: application/json"
            ),
        ));

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return "cURL Error #:" . $err;
        } else {
            return $response;
        }
    }

    /*

      function postresponsedobel($data,$link)
      {

      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);

      $payload = json_encode($data);

      $header = [];
      $header[] = 'Content-type: application/json';
      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";


      curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);


      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }


      }




      function postresponse($data,$link)
      {


      $payload = json_encode($data);



      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);

      $header = [];
      $header[] = 'Content-type: application/json';
      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      //   $header[] = "accept-encoding:*";
      curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($ch, CURLOPT_POST,true);
      $result = curl_exec($ch);
      curl_close($ch);

      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }

      }

      function loginpost($data){
      $payload = json_encode($data);
      $url = 'http://localhost:4311/asretqe/gate/onlinerecruitment/logasd';
      $ch = curl_init($url);

      $header = [];
      $header[] = 'Content-type: application/json';
      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      //   $header[] = "accept-encoding:*";
      curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      curl_setopt($ch, CURLOPT_POST,true);
      $result = curl_exec($ch);
      curl_close($ch);

      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }

      }

      function posresponseauth($data,$link,$token)
      {


      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);

      $payload = json_encode($data);

      $header = [];
      $header[] = 'Content-type: application/json';
      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      $header[] = 'Authorization: '.$token;

      curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);


      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }
      }

      function getresponseauth($link,$token)
      {





      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);

      $header = [];
      $header[] = 'Content-type: application/json';
      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      $header[] = 'Authorization: '.$token;
      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);

      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }

      }


      function getresponse($link)
      {



      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);

      $header = [];
      $header[] = 'Content-type: application/json';
      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);

      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }


      }


      function putresponse($data,$link)
      {


      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);

      $payload = json_encode($data);

      $header = [];
      $header[] = 'Content-type: application/json';
      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";

      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT"); //delete
      curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);


      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }
      }

      function putresponseauth($data,$link,$token)
      {


      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);

      $payload = json_encode($data);

      $header = [];
      $header[] = 'Content-type: application/json';
      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      $header[] = 'Expect: ';
      $header[] = 'Authorization: '.$token;
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT"); //delete
      curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);


      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }
      }




      function putimageauth($cfile,$link,$token,$filename)
      {
      //$url = 'http://vghouse.co.id:4758/'.$link;
      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);


      $params = array('imagetoko' => $cfile);
      $header = [];

      $header[] = "content-type: multipart/form-data";

      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      $header[] = 'Authorization: '.$token;
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST"); //delete
      curl_setopt($ch, CURLOPT_POSTFIELDS, $params);

      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);


      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }

      }

      function postimagetoko($cfile,$link,$token,$filename)
      {

      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);


      $params = array('imageproduk' => $cfile);
      $header = [];

      $header[] = "content-type: multipart/form-data";

      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      $header[] = 'Authorization: '.$token;
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST"); //delete
      curl_setopt($ch, CURLOPT_POSTFIELDS, $params);

      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);


      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }
      }

      function postimageprodukglobal($params,$link,$token,$filename)
      {

      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);



      $header = [];

      $header[] = "content-type: multipart/form-data";

      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      $header[] = 'Authorization: '.$token;
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST"); //delete
      curl_setopt($ch, CURLOPT_POSTFIELDS, $params);

      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);


      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }
      }

      function putimageprodukglobal($params,$link,$token,$filename)
      {

      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);



      $header = [];

      $header[] = "content-type: multipart/form-data";

      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      $header[] = 'Authorization: '.$token;
      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT"); //delete
      curl_setopt($ch, CURLOPT_POSTFIELDS, $params);

      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);


      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      return $result;
      }
      }



      function validasitokenuser()
      {

      $session = $this->session->userdata();

      if(empty($session["token"]))
      {
      $this->session->set_flashdata('pesan', 'anda tidak memiliki otoritas untuk masuk ke halaman ini');
      redirect(base_url());
      }



      $url = 'http://localhost:4311/'.$link;

      $ch = curl_init($url);

      $header = [];
      $header[] = 'Content-type: application/json';

      $header[] = 'client-id:onlinerecruit';
      $header[] = 'client-pass:candraajiokesip';
      $header[] = "Cache-Control: no-cache";
      $header[] = "accept-encoding:*";
      // $header[] = 'Authorization: '.$session["token"];
      $header[] = 'Authorization: '.$session["token"];
      curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
      $result = curl_exec($ch);
      curl_close($ch);

      if(!$result)
      {
      die("Koneksi Gagal");
      }else
      {
      $validasi = json_decode($result);

      if($validasi->username != $session["username"])
      {
      $this->session->set_flashdata('pesan', 'anda tidak memiliki otoritas untuk masuk ke halaman ini');
      redirect(base_url());
      }else
      {
      return $validasi;
      }



      }


      }

     * */

    public function __destruct() {
        
    }

}
