<?php

/**
 * WGI Datatable SSP helper
 * 
 */
defined('BASEPATH') OR exit('No direct script access allowed');


// ------------------------------------------------------------------------

if (!function_exists('datatable_ssp')) {


    function datatable_ssp($table, $primarykey, $columns, $where = '') {
        /*         * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
         * If you just want to use the basic configuration for DataTables with PHP
         * server-side, there is no need to edit below this line.
         */


        // SQL server connection information
        $sql_details = array(
            'user' => 'sa',
            'pass' => 'saW3bgi5',
            'db' => 'dbeplanningv4',
            'host' => '***********'
        );

        require( '/var/www/html/sitia/application/libraries/SspHandler.php' );

        if ($where !== '') {
            echo json_encode(
                    SSP::complex($_GET, $sql_details, $table, $primarykey, $columns, null, $where)
            );
        } else {
            echo json_encode(
                    SSP::simple($_GET, $sql_details, $table, $primarykey, $columns)
            );
        }
    }

}
