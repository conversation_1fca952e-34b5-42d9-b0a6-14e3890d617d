<?php

/**
 * WGI Datatable SSP helper
 * 
 */
defined('BASEPATH') OR exit('No direct script access allowed');


// ------------------------------------------------------------------------

if (!function_exists('datatable_ssp')) {


    function datatable_ssp($table, $primarykey, $columns, $where=NULL) {
        /*         * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
         * If you just want to use the basic configuration for DataTables with PHP
         * server-side, there is no need to edit below this line.
         */
        

        // SQL server connection information
        $sql_details = array(
            'user' => 'wgi',
            'pass' => 'pgDevDiAwan',
            'db' => 'pgtdb',
            'host' => WGI_DB_LRS_HOST,
            'port' => WGI_DB_LRS_PORT
        );

        require( WGI_APP_BASE_FOLDER.'application/libraries/SspHandler.php' );

        if ($where !== NULL) {
            echo json_encode(
                  //  SSP::complex($_GET, $sql_details, $table, $primarykey, $columns, null, $where)
                    SSP::complex($_POST, $sql_details, $table, $primarykey, $columns, null, $where)
            );
        } else {
            echo json_encode(
//                    SSP::complex($_GET, $sql_details, $table, $primarykey, $columns, null, $where)
                    SSP::complex($_POST, $sql_details, $table, $primarykey, $columns)
            );
        }
    }

    function tanggal_indonesia($tanggal) {
        $date = date('Y-m-d', strtotime($tanggal)); // ubah sesuai format penanggalan standart
    
        $bulan = array('01' => 'Januari', // array bulan konversi
            '02'                => 'Februari',
            '03'                => 'Maret',
            '04'                => 'April',
            '05'                => 'Mei',
            '06'                => 'Juni',
            '07'                => 'Juli',
            '08'                => 'Agustus',
            '09'                => 'September',
            '10'                => 'Oktober',
            '11'                => 'November',
            '12'                => 'Desember',
        );
        $date = explode('-', $date); // ubah string menjadi array dengan paramere '-'
    
        return $date[2] . ' ' . $bulan[$date[1]] . ' ' . $date[0]. ' ' . @$date[3]; // hasil yang di kembalikan
    }

}