<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Reff_group_users extends CI_Controller {

	/**
	 * Index Page for this controller.
	 *
	 * Maps to the following URL
	 * 		http://example.com//welcome
	 *	- or -
	 * 		http://example.com//welcome/index
	 *	- or -
	 * Since this controller is set as the default controller in
	 * config/routes.php, it's displayed at http://example.com/
	 *
	 * So any other public methods not prefixed with an underscore will
	 * map to //welcome/<method_name>
	 * @see https://codeigniter.com/user_guide/general/urls.html
	 */
	public function __construct()
    {
    // created the construct so that the helpers, libraries, models can be loaded all through this controller
    parent::__construct();
		$this->load->helper('url');
		$this->load->library('session');
		if($this->session->has_userdata('users')==false){
			redirect('login');
		}
    }
	public function index()
	{
		header("Access-Control-Allow-Origin: *");
		$data = array();
		/**keterangan parameter
		$this->template->load('default_layout', 'contents' , 'index', $data);
		1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
		2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
		3.index = nama view yang akan di load
		**/
		$title="Group Users";
		$js_file = $this->load->view('reff_group_users/js_file', '', true);
		$modal_filter = $this->load->view('reff_group_users/modal_filter', '', true);
		$modal_tambah = $this->load->view('reff_group_users/modal_tambah', '', true);
		$data=array("modal_filter"=>$modal_filter,
								"modal_tambah"=>$modal_tambah,
								"title"=>$title,
  	);
		$this->template->set('title',$title);
		$this->template->set('jv_script',$js_file);
		$this->template->load('default_layout', 'contents' , 'index', $data);
	}
	
	public function list_usulan(){
		$data_usulan["data"]=array(
		                 array(
						   "id"=>"1",
                		   "nama_paket"=>"paket usualan pembangunan jalan kabupaten indra giri 700 meter",
                           "output"    =>"7.000.000",
						   "total_pagu"=>"Data 3"
						  ),
                         array(
						   "id"=>"2",
                		   "nama_paket"=>"Data 4",
                            "output"=>"Data 5",
							"total_pagu"=>"Data 6"
						  )	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )	
	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )							  
	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )							  
	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )							  
	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )	
						  ,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )	
	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )							  
	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )							  
	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )							  
	,
						  array(
						   "id"=>"3",
                		   "nama_paket"=>"Data 7",
                            "output"=>"Data 8",
							"total_pagu"=>"Data 9"
						  )	
						  
					 );
		$data_source=json_encode($data_usulan);
		echo $data_source;
	}
	
	function get_response()
	{
	  
	  $url = '113.20.29.25:12890/testdata/listuser'; //url di set global
	 
	  $ch = curl_init($url);

	  $header = [];
	  $header[] = 'Content-type: application/json';
	  $header[] = 'client-id:webgis'; //client-id di set global / di constructor
	  $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
	  $header[] = "Cache-Control: no-cache";
	  $header[] = "accept-encoding:*";
	  curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
	  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	  $result = curl_exec($ch);
	  curl_close($ch);

	  if(!$result)
	  {
		//bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
		die("Koneksi Gagal");
	  }else
	  {
		return $result;
		//print_r($result);
		
	  }
	  

	}

	//$dataapi  = get_response();

//silahkan diolah :
//print_r($dataapi);
}
