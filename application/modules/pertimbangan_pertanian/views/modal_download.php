<style>
.control-label{
  font-weight:bold;
}
  </style>

<div class="modal fade" id="modal-download" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:125%">
            <div class="modal-header">
                <h4 class="modal-title">Upload File</h4>
                <button type="button" class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close">
                    <span
                        aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body">
              
            <form class="form-horizontal" id="submit">
              <input type="hidden" id="id_up" name="id_up">
              <input type="hidden" id="kd_prov_up" name="kd_prov_up">
              <input type="hidden" id="kd_kabkot_up" name="kd_kabkot_up">
              <fieldset>
              <div class="form-group">
                <label class="col-md-12 control-label" for="textinput">Jenis</label>  
                <div class="col-md-12">
                <select id="layer" name="layer" required class="bootstrap-select form-control" data-live-search="true">
                      <!-- <option value="#">Pilih</option> -->
                    </select>  
                <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
                </div>
              </div>
              
              <!-- <div class="form-group">
                <label class="col-md-12 control-label" for="textinput">Tahun Data</label>  
                <div class="col-md-12">
                <input id="xtahun_data" name="tahun" type="text" placeholder="" class="form-control input-md numberonly" maxlength="4">
                </div>
              </div> -->
              <!-- <div class="form-group">
                <label class="col-md-12 control-label" for="textinput">Fitur</label>  
                <div class="col-md-12">
                <select id="fitur" name="fitur" required class="bootstrap-select form-control" data-live-search="true">
                      <option value="#">--Pilih--</option>
                      <option value="poly">Polygon</option>
                      <option value="poin">Point</option>
                      <option value="line">Line</option>
                    </select>  
                  </div>
                </div> -->
                <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
              <div class="form-group">
                <label class="col-md-12 control-label" for="textinput">Upload File</label>  
                <div class="col-md-12">
                <input id="filess" name="filess" type="file" required placeholder="" class="form-control input-md">
                  
                </div>
              </div>
              <div class="form-group">
                <div class="col-md-12">
                <button type="submit" 
                    class="btn btn-primary btn-md">Simpan</button>
                </div>
              </div>
              </fieldset>
              </form>
                <br>
              <div class="dt-responsive table-responsive">
                <table id="table_id2" class="table table-striped table-bordered nowrap"  style="width:100%;">
                    <thead>
                        <tr>
                            <th>File </th>
                            <th>Status</th>
                            <th>Luas</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>
                </div>
            <div class="modal-footer">
                <button type="button"
                    class="btn btn-default waves-effect "
                    data-bs-dismiss="modal">Close</button>
             
            </div>

        </div>
    </div>
</div>