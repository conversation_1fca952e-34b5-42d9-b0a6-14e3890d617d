<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!--style for modal wizard-->
<!--testing-->



<!--<div class="content bg-gray-lighter">
    <div class="row items-push">
        <div class="col-sm-7">
            <h1 class="page-heading">
                &nbsp;
            </h1>
        </div>
        <div class="col-sm-5 text-right hidden-xs">
            <ol class="breadcrumb push-10-t">
                <li><?//php echo $title; ?></li>
                <li><a class="link-effect" href="">Listing</a></li>
            </ol>
        </div>
    </div>
</div>-->
<!--<h1 class="page-heading" style="margin-left: 20px; margin-bottom: 0px; margin-top: 0px;"> <?php echo $title; ?> </h1>-->
<div class="block">
    <div class="block-header">
        <div class="row form-inline">
            <div class="col-md-12">
                <!-- <div class="form-group">
                    <label for="thang">Filter Tahun Anggaran :</label>
                    <select class="form-control" id="fthang" name="fthang" size="1"></select>
                </div> -->
                <button type="button" class="btn btn-minw btn-success" onclick="javascript:dtTambahRow()" style="margin-left:20px;">
                    <i class="fa fa-plus"> </i>&nbsp; Tambah <?php echo $title; ?>
                </button>
            </div>
        </div>
    </div>
    <div class="block-content" style="overflow-x:scroll;">
        <div class="col-sm-12">	
            <div id="alert_information" style="display:none; text-align: center;" class="alert alert-success alert-dismissable">
                <button type="button" class="close" data-bs-dismiss="alert" aria-hidden="true">×</button>
                <h3 class="font-w300 push-15"><i class="fa fa-info-circle"></i> <b>Informasi</b></h3>
                <div id="alert-content" style="font-weight: bold;"></div>
            </div>
        </div>
        <div id="table-container">
            <table id="table_id" class="display" style="width:100%;">
                <thead>
                    <tr>
                        <th>Nama Provinsi</th>
                        <th>Kode Provinsi</th>
                        <!-- <th>Kode Provinsi IRMSv3</th>
                        <th>Kode Provinsi RKAKL</th>
                        <th>Kode Provinsi RAMS</th>
                        <th>Kode Balai</th>
                        <th>ID</th> -->
                        <th>Action</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>
<?php echo $modal_filter; ?>
<?php echo $modal_tambah; ?>
<?php echo $jv_script; ?>
