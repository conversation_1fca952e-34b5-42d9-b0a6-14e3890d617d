<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Profile extends MY_Controller {
//fahmi
    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();
    }

    //used for lookup purpose
    public function listing() {
        $url = "http://localhost:12892/referensi/provinsi/listall";
        echo $this->get_data_module($url);
    }

    function get_data_by_id($id) {
        $url = "http://localhost:12892/referensi/provinsi/getrow/" . $id;
        echo $this->get_data_module($url);
    }

    public function provinsi_by_kd_satker($id) {
        $url = "http://localhost:12892/prakonreg/reg_satker/satkerprov/" . $id;
        echo $this->get_data_module($url);
    }

    //used for grid datatble purpose
    function datalist() {

        $link = "http://localhost:12892/referensi/provinsi/jumlahquerylist";
        $jumlahraw = $this->get_data_module($link);
        $jumlahdata = json_decode($jumlahraw);
        $jumlah = $jumlahdata->jumlah;


        $columns = [
            0 => 'kd_prov',
            1 => 'nama_prov',
            2 => 'ibukota',
            3 => 'kd_prov_rams'
        ];

        $limit = $this->input->post('length');
        $start = $this->input->post('start');
        $totalData = $jumlah;
        $totalFiltered = $totalData;

        if (isset($_POST['order'])) {
            $order = $columns[$this->input->post('order')[0]['column']];
            $dir = $this->input->post('order')[0]['dir'];
        } else {
            $order = 'kd_prov';
            $dir = 'desc';
        }

        if (!empty($this->input->post('search')['value'])) {
            if (empty($limit)) {
                $limit = $jumlah;
            }

            if (empty($start)) {
                $start = '0';
            }
            $search = $this->input->post('search')['value'];
            $kolom = 'nama_prov';

            $linksearchdataprov = "http://localhost:12892/referensi/provinsi/searchquerylist";
            $datasearchkirimprov = [
                "limit" => $limit,
                "start" => $start,
                "order" => $order,
                "dir" => $dir,
                "search" => $search,
                "kolom" => $kolom
            ];
            $datasearchraw = $this->insert_moduleduo($linksearchdataprov, $datasearchkirimprov);

            $datasearchprovraw = json_decode($datasearchraw);
            $prov = $datasearchprovraw->data;
            $totalFiltered = $datasearchprovraw->jumlahlimit;
        } else {

            if (empty($limit)) {
                $limit = $jumlah;
            }

            if (empty($start)) {
                $start = '0';
            }

            $linkdataprov = "http://localhost:12892/referensi/provinsi/querylist";
            $datakirimprov = [
                "limit" => $limit,
                "start" => $start,
                "order" => $order,
                "dir" => $dir
            ];

            $dataprovraw = $this->insert_moduleduo($linkdataprov, $datakirimprov);

            $dataprov = json_decode($dataprovraw);

            $prov = $dataprov->data;
        }

        foreach ($prov as $p) {

            $row[] = [
                "kd_prov" => $p->kd_prov,
                "nama_prov" => $p->nama_prov,
                "ibukota" => $p->ibukota,
                "kd_prov_bpiw" => $p->kd_prov_bpiw,
                "kd_prov_sipro" => $p->kd_prov_sipro,
                "kd_prov_krisna" => $p->kd_prov_krisna,
                "kd_prov_rkakl" => $p->kd_prov_rkakl,
                "kd_prov_bps" => $p->kd_prov_bps,
                "kd_prov_rams" => $p->kd_prov_rams
            ];
        }

        $output = array(
            "draw" => intval($this->input->post('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $row
        );

        $this->output->set_content_type('application/json')->set_output(json_encode($output, JSON_PRETTY_PRINT));
    }

    public function simpan() {

        /**
          { "data": "kd_prov" },
          { "data": "nama_prov" },
          { "data": "ibukota" },
          { "data": "kd_prov_bpiw" },
          { "data": "kd_prov_sipro" },
          { "data": "kd_prov_krisna" },
          { "data": "kd_prov_rkakl" },
          { "data": "kd_prov_bps" },
          { "data": "kd_prov_rams" },
         * */
        $url = "http://localhost:12892/referensi/provinsi/add";

        $kd_prov = $this->input->post("kd_prov");
        $nama_prov = $this->input->post("nama_prov");
        $ibukota = $this->input->post("ibukota");
        $kd_prov_bpiw = $this->input->post("kd_prov_bpiw");
        $kd_prov_sipro = $this->input->post("kd_prov_sipro");
        $kd_prov_krisna = $this->input->post("kd_prov_krisna");
        $kd_prov_rkakl = $this->input->post("kd_prov_rkakl");
        $kd_prov_bps = $this->input->post("kd_prov_bps");
        $kd_prov_rams = $this->input->post("kd_prov_rams");

        $data_raw = array(
            "kd_prov" => $kd_prov,
            "nama_prov" => $nama_prov,
            "ibukota" => $ibukota,
            "kd_prov_bpiw" => $kd_prov_bpiw,
            "kd_prov_sipro" => $kd_prov_sipro,
            "kd_prov_krisna" => $kd_prov_krisna,
            "kd_prov_rkakl" => $kd_prov_rkakl,
            "kd_prov_bps" => $kd_prov_bps,
            "kd_prov_rams" => $kd_prov_rams
        );
        echo $this->insert_module($url, $data_raw);
    }

    public function update() {

        /**
          { "data": "kd_prov" },
          { "data": "nama_prov" },
          { "data": "ibukota" },
          { "data": "kd_prov_bpiw" },
          { "data": "kd_prov_sipro" },
          { "data": "kd_prov_krisna" },
          { "data": "kd_prov_rkakl" },
          { "data": "kd_prov_bps" },
          { "data": "kd_prov_rams" },
         * */
        $url = "http://localhost:12892/referensi/provinsi/update";

        $kd_prov = $this->input->post("kd_prov");
        $nama_prov = $this->input->post("nama_prov");
        $ibukota = $this->input->post("ibukota");
        $kd_prov_bpiw = $this->input->post("kd_prov_bpiw");
        $kd_prov_sipro = $this->input->post("kd_prov_sipro");
        $kd_prov_krisna = $this->input->post("kd_prov_krisna");
        $kd_prov_rkakl = $this->input->post("kd_prov_rkakl");
        $kd_prov_bps = $this->input->post("kd_prov_bps");
        $kd_prov_rams = $this->input->post("kd_prov_rams");

        $data_raw = array(
            "kd_prov" => $kd_prov,
            "nama_prov" => $nama_prov,
            "ibukota" => $ibukota,
            "kd_prov_bpiw" => $kd_prov_bpiw,
            "kd_prov_sipro" => $kd_prov_sipro,
            "kd_prov_krisna" => $kd_prov_krisna,
            "kd_prov_rkakl" => $kd_prov_rkakl,
            "kd_prov_bps" => $kd_prov_bps,
            "kd_prov_rams" => $kd_prov_rams
        );
        //echo "<pre>";
        //print_r($data_raw);
        //echo "<pre>";
        //die();
        echo $this->update_module($url, $data_raw);
    }

    public function index() {
        header("Access-Control-Allow-Origin: *");
        $data = array();
        /*         * keterangan parameter
          $this->template->load('default_layout', 'contents' , 'index', $data);
          1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
          2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
          3.index = nama view yang akan di load
         * */

        //echo "<pre>";
        //print_r($this->session->userdata);
        //echo "</pre>";
        //die();

        $title = "Profile";
        $js_file = $this->load->view('profile/js_file', '', true);
        $modal_filter = $this->load->view('profile/modal_filter', '', true);
        $modal_tambah = $this->load->view('profile/modal_tambah', '', true);
        $modal_edit = $this->load->view('profile/modal_edit', '', true);
        $modal_view = $this->load->view('profile/modal_view', '', true);
        $id_user = $this->session->users['id_user'];
        $sql = "select a.nip,
                       a.nomorhp,
					   a.id_sub_user_group,
                       a.email,
                       a.username,
                       a.firstname,
                       a.lastname,
					   a.nama_user_group as role_desc,
					   a.nama_bujt 
                       from v_users a
					   where a.id_user= $id_user";
        $query = $this->db->query($sql);
        $data_user = $query->result_array()[0];
        //echo $data_user["id_sub_user_group"]; die();
        //print_r($data_user); die();
        $parent = $data_user["id_sub_user_group"];
        $sql = "select id_user_group as id_sub_user_group,nama as parent_desc from aset_user_group where id_user_group='2'";
        $query1 = $this->db->query($sql);
        $data_parent = $query1->result_array();


        $data = array("modal_filter" => $modal_filter,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "modal_view" => $modal_view,
            "data_user" => $data_user,
            "data_parent" => $data_parent,
            "title" => $title,
        );
        $this->template->set('title', $title);
        $this->template->set('jv_script', $js_file);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }

    function get_response() {
        //echo "11111";
        //$url = '************:12890/testdata/list'; //url di set
        //$url = 'http://localhost:12890/zxcQsd/listprovinsi'; //url di set global
        $url = 'http://localhost:12890/zxcQsd/listprovinsi'; //url di set global

        $ch = curl_init($url);
        $header = [];
        $header[] = 'Content-type: application/json';
        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
        $header[] = "Cache-Control: no-cache";
        $header[] = "accept-encoding:*";
        //$header[] = 'Authorization: '.$token;
        curl_setopt($ch, CURLOPT_PORT, 12890);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);

        curl_close($ch);
        if (!$result) {
            //echo curl_errno($ch);
            //echo curl_error($ch);
            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
            die("Koneksi Gagal");
        } else {
            echo $result;
            //print_r($result);
        }
    }

    //$dataapi  = get_response();
//silahkan diolah :
//print_r($dataapi);
    //new code
    public function update_data() {
        $data = $this->input->post('formData');
        if (strlen($data["password"]) > 1){
            $updates = [
                'firstname' => $data["firstname"],
                'lastname' => $data["lastname"],
                'email' => $data["email"],
                'password' => password_hash($data["password"], PASSWORD_DEFAULT),
                'nomorhp' =>$data["nomorhp"],
                'nip' =>$data["nip"]

            ];
        } else {
            $updates = [
                'firstname' => $data["firstname"],
                'lastname' => $data["lastname"],
                'email' => $data["email"],
                'nomorhp' =>$data["nomorhp"],
                'nip' => $data["nip"]
            ];
        }
        // print_r($updates);

        $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
        // $this->db->set('updated_at', 'getutcdate()', FALSE);
        $this->db->where('id_user', $data["id"])->update('aset_users', $updates);
    }

}
