<!-- fahmi-->
<?php
    //define data
    $firstname="";
    if($this->session->users['firstname'] != ""){
        $firstname= $this->session->users['firstname'];
    }else{
        $firstname= "";
    }

    $lastname="";
    if($this->session->users['lastname'] != ""){
        $lastname= $this->session->users['lastname'];
    }else{
        $lastname= "";
    }

    $kd_bujt="";
    if($this->session->users['kd_bujt'] != ""){
        $kd_bujt= $this->session->users['kd_bujt'];
    }else{
        $kd_bujt= "";
    }



    //session dpr
    //  $kd_dapil="";
    // if($this->session->users['kd_dapil'] != ""){
    //     $kd_dapil= $this->session->users['kd_dapil'];
    // }else{
    //     $kd_dapil= "";
    // }


    $parent_group="";
    if($this->session->users['id_user_group'] != ""){ 
        $parent_group= $this->session->users['id_user_group'];
    }else{
        $parent_group= "";
    }


    // ''="";
    // if($this->session->users['kd_fraksi'] != ""){
    //     ''= $this->session->users['kd_fraksi'];
    // }else{
    //     ''= "";
    // }

     $id_sub_user_group="";
    if($this->session->users['id_sub_user_group'] != ""){
        $id_sub_user_group= $this->session->users['id_sub_user_group'];
    }else{
        $id_sub_user_group= "";
    }

      /**
     ''="";
    if($this->session->users['kd_fraksi'] != ""){
        ''= $this->session->users['kd_fraksi'];
    }else{
        ''= "";
    }
    **/

    //new code
    $acc = $this->db->query('select * from v_users where id_user = '. $this->session->users['id_user'] .'')->row();
    //echo $acc->firstname;

?>
<script>
  $(document).ready(function() {
      //alert(111);
      set_val();
  });
   //xx
   function edit_profile(){
      $("#frm-edit")[0].reset();

    // alert(1111);
      var id_user= $("#id_userx").val();
      var firstname = $("#firstnamex").val();
      var lastname = $("#lastnamex").val();
      var email = $("#emailx").val();
      var nip = $("#nip").val();
      var nohp = $("#nohp").val();

      $("#frm-edit #id_user").val(id_user);
      $("#frm-edit #firstname").val(firstname);
      $("#frm-edit #lastname").val(lastname);
      $("#frm-edit #email").val(email);
      $("#frm-edit #nomorhp").val(nohp);
      $("#frm-edit #nips").val(nip);
      //
      $.ajax({
        url: "<?php echo base_url('/user_management/get_parent/'); ?>"+id_user,
        cache: false,
        success: function(response){
          console.log("-------response-------")
           console.log(JSON.parse(response));
        }
      });

      $("#modal-edit").modal("show");
       //edit_profile
   }
   function bind_lookup_dapil(){
           jQuery.ajax({
                  contentType : 'application/json',
                  //type: "GET",
                  url: "<?php echo base_url("/reff_dapil/listing");?>",
                  success: function (response) {
                    var obj_ajx= jQuery.parseJSON(response);
                    $("#kd_dapil").empty();
                     var session_to_js_kd_dapil = "";
                      //alert(session_to_js_kd_dapil);
                      if(session_to_js_kd_dapil == ""){
                        $("#kd_fraksi").append("<option>--Pilih--</option>");
                      }
                    $("#kd_dapil").append("<option>--Pilih--</option>");
                    for(var i=0; i<= obj_ajx.data.length-1; i++){
                        kd_dapil=obj_ajx.data[i].kd_dapil;
                        kd_prov_rkakl=obj_ajx.data[i].kd_prov_rkakl;
                        var data_value= kd_dapil+"||"+kd_prov_rkakl;

                        if(session_to_js_kd_dapil ==kd_dapil){

                        $("#kd_dapil").append("<option selected value= "+data_value+" >"+obj_ajx.data[i].nama_bujt+"</option>");
                      }else{
                           $("#kd_dapil").append("<option value= "+data_value+" >"+obj_ajx.data[i].nama_bujt+"</option>");
                      }
                    }
                  },
                  error: function (xhr, ajaxOptions, thrownError) {
                    alert(xhr.status);
                    alert(thrownError);
                  }
            });
        }


    function bind_lookup_komisi(){
      var id_sub_user_group= "<?php echo $id_sub_user_group; ?>";
      var parent_group= "<?php echo $parent_group; ?>";
       jQuery.ajax({
              contentType : 'application/json',
              //type: "GET",
              url: "<?php echo base_url("/reff_komisi/get_komisi_sub_user_goup/");?>"+parent_group,
              success: function (response) {
                var obj= jQuery.parseJSON(response);
                //console.log(obj);
                $("#kd_komisi").empty();
                //var session_to_js_kd_komisi = "<?php //echo $kd_komisi; ?>";
                //alert(session_to_js_kd_komisi);
                //$("#kd_komisi").append("<option>--Pilih--</option>");
                for(var i=0; i<= obj.data.length-1; i++){
                  //alert(obj.data[i].nama);

                  if(id_sub_user_group == obj.data[i].id_user_group){
                    //alert(obj.data[i].kd_komisi);
                    $("#kd_komisi").append("<option selected value= "+obj.data[i].id_user_group+" >"+obj.data[i].nama+"</option>");
                  }else{
                   $("#kd_komisi").append("<option value= "+obj.data[i].id_user_group+" >"+obj.data[i].nama+"</option>");
                  }

                }
              },
              error: function (xhr, ajaxOptions, thrownError) {
                alert(xhr.status);
                alert(thrownError);
              }
        });
    }

     function bind_lookup_fraksi(){
       jQuery.ajax({
              contentType : 'application/json',
              //type: "GET",
              url: "<?php echo base_url("/reff_fraksi/listing");?>",
              success: function (response) {
                var obj= jQuery.parseJSON(response);
                 //please see define data at the top off file
                 //
                 //var kd_fraksi
                 var session_to_js_kd_fraksi = "<?php echo ''; ?>";
                 //alert(session_to_js_kd_fraksi);
                 if(session_to_js_kd_fraksi == ""){
                  $("#kd_fraksi").append("<option>--Pilih--</option>");
                 }
                $("#kd_fraksi").empty();
                //$("#kd_fraksi").append("<option>--Pilih--</option>");
                for(var i=0; i<= obj.data.length-1; i++){
                    if(session_to_js_kd_fraksi==obj.data[i].kd_fraksi){
                     //alert("kd_fraksi session :"+session_to_js_kd_fraksi +"kd_fraksi_ajax:" +obj.data[i].kd_fraksi)
                    $("#kd_fraksi").append("<option selected value= "+obj.data[i].kd_fraksi+" >"+obj.data[i].nama_fraksi+"</option>");
                    }else{
                      $("#kd_fraksi").append("<option value= "+obj.data[i].kd_fraksi+" >"+obj.data[i].nama_fraksi+"</option>");
                    }
                }
              },
              error: function (xhr, ajaxOptions, thrownError) {
                alert(xhr.status);
                alert(thrownError);
              }
        });
    }

  function bind_satker(){
        //alert(1);
        jQuery.ajax({
        contentType : 'application/json',
        //type: "GET",
        url: "<?php echo base_url("/reff_satker/listing");?>",
        success: function (response) {
        var obj= jQuery.parseJSON(response);
        $("#kd_bujt").empty();

        //please see define data at the top off file
         var session_to_js_kd_bujt = "<?php echo $kd_bujt; ?>";
         if(session_to_js_kd_bujt == ""){
         $("#kd_bujt").append("<option>Pilih</option>");
          }else{
            $("#kd_bujt").append("<option>"+session_to_js_kd_bujt+"</option>");
         }
        for(var i=0; i<= obj.data.length-1; i++){
          var kd_prov_rkakl=obj.data[i].kd_prov_rkakl;
          var id_kabkot_rkakl=obj.data[i].id_kabkot_rkakl;
          var kd_kppn=obj.data[i].kd_kppn;
          var kd_bujt=obj.data[i].kd_bujt;

          //value of select
          var data_value= kd_prov_rkakl+"||"+kd_prov_rkakl+id_kabkot_rkakl+"||"+kd_kppn+"||"+kd_bujt;
          $("#kd_bujt").append("<option value= "+data_value+" >"+obj.data[i].kd_bujt+" | "+obj.data[i].nama_satker+"</option>");
        }
        },
        error: function (xhr, ajaxOptions, thrownError) {
        alert(xhr.status);
        alert(thrownError);
        }
      });
    }

    //this is not data binding function. just mapping variabel  between local provinsi to binamarga provinsi
     function get_provinsi_by_kd_satker(obj){

         var kd_prov_rkakl=obj.value.split("||")[0];
         var id_kabkot_rkakl=obj.value.split("||")[1];
         var kd_kppn=obj.value.split("||")[2];
         var kd_bujt=obj.value.split("||")[3];
        // console.log(kd_satker);
         jQuery.ajax({
        contentType : 'application/json',
        //type: "GET",
        url: "<?php echo base_url("/reff_provinsi/provinsi_by_kd_satker/");?>"+kd_prov_rkakl,
        success: function (response) {
        var ajx_obj = jQuery.parseJSON(response);
        console.log(ajx_obj);
         $("#kd_prov_satker").empty();
        //for(var i=0; i<= ajx_obj.data.length-1; i++){
          nama_prov=ajx_obj.data[0].nama_prov;
          kd_prov  = ajx_obj.data[0].kd_prov;
          kd_prov_irmsv3=ajx_obj.data[0].kd_prov_irmsv3;
          //alert("kd_prov_irmsv3"+kd_prov_irmsv3);
          //var data_text=kd_prov+" | "+nama_prov;
          //var data_value=kd_prov +"||"+kd_prov_irmsv3;
         // $("#kd_prov_satker").append("<option value="+data_value+">"+data_text+"</option>");
          get_ruas_by_kd_prov_irmsv3(kd_prov_irmsv3);
        //}


        },
        error: function (xhr, ajaxOptions, thrownError) {
        alert(xhr.status);
        alert(thrownError);
        }
      });
      get_kabkota_by_kd_satker(id_kabkot_rkakl);
      get_kppn_by_kd_satker(kd_kppn);
      get_ppk_by_kd_bujt(kd_bujt);
     }

      function get_kabkota_by_kd_satker(id_kabkot_rkakl){
         //var id_kabkot_rkakl=obj.value;
         //console.log(kd_satker);
         jQuery.ajax({
        contentType : 'application/json',
        //type: "GET",
        url: "<?php echo base_url("/reff_kab_kota/kabkota_by_kd_satker/");?>"+id_kabkot_rkakl,
        success: function (response) {
        var ajx_obj = jQuery.parseJSON(response);
        console.log(ajx_obj);
         $("#id_kabkot_satker").empty();
        for(var i=0; i<= ajx_obj.data.length-1; i++){
          var kab_kota=ajx_obj.data[i].kab_kota;
          var id_kabkot  = ajx_obj.data[i].id_kabkot;
          $("#id_kabkot_satker").append("<option value="+id_kabkot+">"+kab_kota+"</option>");
        }


        },
        error: function (xhr, ajaxOptions, thrownError) {
        alert(xhr.status);
        alert(thrownError);
        }
      });
     }

      function get_ppk_by_kd_bujt(kd_bujt){
        jQuery.ajax({
        contentType : 'application/json',
        //type: "GET",
        url: "<?php echo base_url("/reff_ppk/ppk_by_kd_satker/");?>"+kd_bujt,
        success: function (response) {
        var ajx_obj = jQuery.parseJSON(response);
        console.log(ajx_obj);
         $("#kd_ppk").empty();
           $("#kd_ppk").append("<option>--Pilih--</option>");
        for(var i=0; i<= ajx_obj.data.length-1; i++){
          var kd_ppk=ajx_obj.data[i].kd_ppk;
          var nama_ppk  = ajx_obj.data[i].nama_ppk;
          $("#kd_ppk").append("<option value="+kd_ppk+">"+nama_ppk+"</option>");
        }


        },
        error: function (xhr, ajaxOptions, thrownError) {
        alert(xhr.status);
        alert(thrownError);
        }
      });
      }

       function get_kppn_by_kd_satker(kd_kppn){
        //alert(kd_kppn);
         //var id_kabkot_rkakl=obj.value;
         //console.log(kd_kppn);
         jQuery.ajax({
        contentType : 'application/json',
        //type: "GET",
        url: "<?php echo base_url("/reff_kppn/kppn_by_id/");?>"+kd_kppn,
        success: function (response) {
        var ajx_obj = jQuery.parseJSON(response);
        console.log(ajx_obj);
         $("#kd_kppn").empty();
        for(var i=0; i<= ajx_obj.data.length-1; i++){
          var kd_kppn=ajx_obj.data[i].kd_kppn;
          var nama_kanwil  = ajx_obj.data[i].nama_kanwil;
          $("#kd_kppn").append("<option value="+kd_kppn+">"+nama_kanwil+"</option>");
        }


        },
        error: function (xhr, ajaxOptions, thrownError) {
        alert(xhr.status);
        alert(thrownError);
        }
      });
     }

    function update_data(){

        if ($('#repasswordx').val() !== $('#passwordx').val()){
            alert("Password tidak terkonfirmasi dengan benar harap cek kembali");
        } else {
            var obj = {
                "id" : $('#id_user').val(),
                "firstname" : $('#firstname').val(),
                "lastname" : $('#lastname').val(),
                "email" : $('#email').val(),
                "password" : $('#repasswordx').val(),
                "nomorhp" : $('#nomorhp').val(),
                "nip" : $('#nips').val()
            };
            //console.log(obj);
            var url = "<?php echo base_url("profile/update_data") ?>";
            var params = {"formData": obj, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                $('#modal-edit').modal('hide');
                 location.reload();
            }).fail(function () {
                alert("error");
            });
        }
    }

    function set_val(){
        $("#username").val("<?php echo $this->session->users["username"]; ?>");
        $("#id_userx").val("<?php echo $this->session->users['id_user']; ?>");
        $("#id_user_group").val("<?php echo $this->session->users["id_user_group"]; ?>");
        $("#emailx").val("<?php echo $this->session->users["email"]; ?>");
        $("#firstnamex").val("<?php echo $acc->firstname; ?>");
        $("#lastnamex").val("<?php echo $acc->lastname; ?>");
        // $("#id_userx").val("<?php echo $acc->username; ?>");
    }

</script>
