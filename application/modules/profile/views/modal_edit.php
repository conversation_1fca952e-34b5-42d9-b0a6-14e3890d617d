<?php
defined('BASEPATH') OR exit('No direct script access allowed');
//fahmi
?>
<!--
{ "data": "kd_prov" },
{ "data": "id_kabkot" },
{ "data": "kd_kab_kota_bpiw" },
{ "data": "kab_kota" },
{ "data": "kd_kab_irmsv3" },
{ "data": "kd_kab_rams" },
{ "data": "kd_kab_bps" },
{ "data": "kd_kab_rkakl" }
-->
 <!-- Slide Right Modal -->
 <div class="modal fade" id="modal-edit" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-slideright">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Edit Profile</h3>
                </div>
                <div class="block-content">
                    <!-- form start -->
                    <form role="form" id="frm-edit">
                      <div class="box-body">
                         <div class="form-group" style="display:none;">
                            <label for="exampleInputPassword1">ID User</label>
                            <input type="text" class="form-control" id="id_user" placeholder="">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">Nama Depan</label>
                            <input id="firstname" type="text" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">Nama Belakang</label>
                            <input type="text" id="lastname"  class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">Email</label>
                            <input type="text" class="form-control" id="email" placeholder="">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">NIP</label>
                            <input type="text" class="form-control" id="nips" placeholder="">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">No. Handphone</label>
                            <input type="text" class="form-control" id="nomorhp" placeholder="">
                        </div>
<!--                        <div class="form-group">
                            <label for="exampleInputPassword1">Password</label>
                            <input id="passwordx" type="password" class="form-control" id="kd_kab_kota_bpiw" placeholder="">
                        </div>-->
                        <div class="form-group">
                            <label for="exampleInputPassword1">Password</label>
                            <input id="passwordx" type="password" class="form-control" placeholder="">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">Konfirmasi Password</label>
                            <input type="password" class="form-control" id="repasswordx" placeholder="">
                        </div>
                        <div class="form-group" id="form-peran" style="display: none;">
                            <label for="id_user_group">Peran</label>
                            <select onchange="getChildren(this)" class="form-control" id="id_user_group">
                            </select>
                        </div>
                        <div class="form-group" style="display: none;">
                            <label for="exampleInputPassword1">Komisi</label>
                            <select class="form-control" id="id_user_group"></select>
                        </div>
                        <div class="form-group" style="display: none;">
                            <label for="exampleInputPassword1">Fraksi</label>
                            <select class="form-control" id="id_sub_user_group"></select>
                        </div>
                      </div>
                      <!-- /.box-body -->
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Close</button>
                <button onclick="update_data();" class="btn btn-sm btn-primary" type="button><i class="fa fa-check"></i> Ok</button>
            </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->
