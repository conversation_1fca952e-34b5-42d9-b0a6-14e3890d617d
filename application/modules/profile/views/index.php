<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
 
 <?php
 //fahmi   //define data

    $firstname="";
    if($this->session->users['firstname'] != ""){
        $firstname= $this->session->users['firstname'];
    }else{
        $firstname= "";
    }

    $lastname="";
    if($this->session->users['lastname'] != ""){
        $lastname= $this->session->users['lastname'];
    }else{
        $lastname= "";
    }

    //session satker
    $kd_bujt="";
    if($this->session->users['kd_bujt'] != ""){
        $kd_bujt= $this->session->users['kd_bujt'];
    }else{
        $kd_bujt= "";
    }

     $data_user['username'];

    //  $kd_fraksi="";
    // if($this->session->users['kd_fraksi'] != ""){
    //     $kd_fraksi= $this->session->users['kd_fraksi'];
    // }else{
    //     $kd_fraksi= "";
    // }


    //session dpr
    //  $kd_dapil="";
    // if($this->session->users['kd_dapil'] != ""){
    //     $kd_dapil= $this->session->users['kd_dapil'];
    // }else{
    //     $kd_dapil= "";
    // }


?>
<div class="content bg-image" style="background-image: url(<?php echo base_url('assets/img/photos/<EMAIL>'); ?>);">
    <div class="push-50-t push-15 clearfix">
        <div class="push-15-r pull-left animated fadeIn">
            <img class="img-avatar img-avatar-thumb" src="assets/img/avatars/avatar13.jpg" alt="">
        </div>
        <h1 class="h2 text-white push-5-t animated zoomIn">
            <?php echo $this->session->users["username"]; ?>
        </h1>
        <h2 class="h5 text-white-op animated zoomIn">
            <?php echo $this->session->users["roledesc"]; ?>
        </h2>
    </div>
</div>

<form>
    <div class="block">
        <ul class="nav nav-tabs nav-justified push-20" data-toggle="tabs">
            <li class="active">
                <a href="#tab-profile-personal"><i class="fa fa-fw fa-pencil"></i> Personal</a>
            </li>
            <!--
            <li class="">
                <a href="#tab-profile-password"><i class="fa fa-fw fa-asterisk"></i> Password</a>
            </li>
            -->
        </ul>
        <div class="block-content tab-content">
            <!-- Personal Tab -->
            <div class="tab-pane fade active in" id="tab-profile-personal">
                <div class="row items-push">
                    <div class="col-sm-6 col-sm-offset-3 form-horizontal">
                        <form id="frm-index">
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <label>Perusahaan/BUJT</label>
                                    <div class="form-control-static font-w700">
                                        <input id="username" readonly type="text" class="form-control" value="<?=$data_user['username'];?>"/>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" style="display:none;">
                                <div class="col-xs-12">
                                    <label for="profile-email">User</label>
                                    <input readonly class="form-control input-lg" id="id_userx" name="id_user" placeholder="" value="" type="email">
                                </div>
                            </div>
                             <?php
                                // if($data_user['nama_bujt'] != "")
                                // {
                            ?>
                                <!--div class="form-group">
                                    <div class="col-xs-6">
                                        <label for="profile-firstname">Perusahaan/BUJT</label>
                                        <!--please see define data at the top-->
                                        <!--input  value="<?php// echo $data_user['nama_bujt'] ?>" readonly class="form-control input-lg" id="kd_fraksi" name="firstname" placeholder=""  type="text">
                                    <!--/div>
                                    <!--div class="col-xs-6">
                                        <label for="profile-lastname">Fraksi</label>
                                        <!--please see define data at the top-->
                                        <!--input type="text" value="<?php //echo $data_user['nama_fraksi'] ?>" readonly class="form-control input-lg" id="nama_fraksi" name="lastname" placeholder="" >
                                    </div-->
                                <!--/div-->
                            <?php// } ?>
                             <div class="form-group" style="display:block;">
                                <div class="col-xs-12">
                                    <label for="profile-email">Grup/Peran</label>
                                    <input readonly class="form-control input-lg" id="id_user_group" name="id_user_group" placeholder="" value="<?=$data_user['role_desc'];?>" type="email">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-xs-12">
                                    <label for="profile-email">Email</label>
                                    <input readonly class="form-control input-lg" id="emailx" name="email" placeholder="" value="<?php echo $data_user['email'] ?>" type="email">
                                </div>
                            </div>


                            <div class="form-group">
                                <div class="col-xs-6">
                                    <label for="profile-firstname">Nama Depan</label>
                                    <!--please see define data at the top-->
                                    <input readonly class="form-control input-lg" id="firstnamex" name="firstname" placeholder="" value="<?php echo $data_user['firstname'] ?>" type="text">
                                </div>
                                <div class="col-xs-6">
                                    <label for="profile-lastname">Nama Belakang</label>
                                    <!--please see define data at the top-->
                                    <input type="text" readonly class="form-control input-lg" id="lastnamex" name="lastname" placeholder="" value="<?php echo $data_user['lastname'] ?>" >
                                </div>
                                <div class="col-xs-12">
                                    <label for="profile-email">No HP</label>
                                    <input readonly class="form-control input-lg" id="nohp" name="role_desc" placeholder="" value="<?php echo $data_user['nomorhp'] ?>" type="text">
                                </div>
                                <div class="col-xs-12">
                                    <label for="profile-email">NIP</label>
                                    <input readonly class="form-control input-lg" id="nip" name="role_desc" placeholder="" value="<?php echo $data_user['nip'] ?>" type="text">
                                </div>
                            </div>
                             <!--div class="form-group">
                                <div class="col-xs-12">
                                    <label for="profile-email">Peran</label>
                                    <input readonly class="form-control input-lg" id="role_desc" name="role_desc" placeholder="" value="<?php echo $data_user['role_desc'] ?>" type="text">
                                </div>
                            </div-->
                              <!--div class="form-group">
                                <div class="col-xs-12">
                                    <input readonly class="form-control input-lg" id="sub_role_desc" name="sub_role_desc" placeholder="" value="<?php //echo $data_children['sub_role_desc'] ?>" type="text">
                                </div>
                            </div-->
                           
                        </div>
                    </div>
                </div>
            </div>
            </form>
            <!-- END Personal Tab -->
        <div class="block-content block-content-full bg-gray-lighter text-center">
            <button onclick="edit_profile()" class="btn btn-sm btn-primary" type="button">
                <i class="fa fa-pencil push-5-r"></i>
                Edit
            </button>
        </div>
        </div>
    <?php //echo $modal_filter; ?>
    <?php //echo $modal_tambah; ?>
    <?php echo $modal_edit; ?>
    <?php //echo $modal_view; ?>

<div class="modal fade bd-example-modal-lg loading" data-backdrop="static" data-keyboard="false" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content" style="text-align:center; background:transparent; margin-top: 150%;">
            <span class="fa fa-spinner fa-pulse fa-3x fa-fw"></span>
        </div>
    </div>
</div>
