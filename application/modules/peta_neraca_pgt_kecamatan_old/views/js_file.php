<script>
    // var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    // var xhrdata = null;
    // var table = null;

    // var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    // var roledesc = "<?php echo $this->session->users['role']; ?>";


    $(document).ready(function () {
        // $(window).on("resize", function () { $("#map").height($(window).height() - 220); map.invalidateSize(); }).trigger("resize");


        var map = L.map('map', {
            'minZoom' : 5,
            'maxZoom' : 10
        }).setView([-1.736, 119.246], 5);

        <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // L.marker([51.5, -0.09]).addTo(map)
        //     .bindPopup('A pretty CSS3 popup.<br> Easily customizable.')
        //     .openPopup();
        // End Map----------------------------------------------------------------------------------------------------
    });
</script>