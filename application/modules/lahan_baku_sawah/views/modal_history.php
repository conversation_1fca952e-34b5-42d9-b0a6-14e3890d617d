<style>
    /* Custom CSS to set modal width */
    .custom-modal {
      max-width: 80%;
    }
  </style>
<div class="modal fade" id="modal_history" tabindex="-1" role="dialog">
    <div class="modal-dialog custom-modal" style="" role="document">
        <div class="modal-content" >
            <div class="modal-header">
                <h4 class="modal-title">History</h4>
                <button type="button" class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close">
                    <span
                        aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body" id="">
            <input type="hidden" name="" id="idhist">
            <div class="dt-responsive table-responsive">
                <table id="dt-history" style="width:100%" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Proses</th>
                            <th>Oleh</th>
                            <th>Waktu</th>
                        </tr>
                    </thead>
                </table>
            </div>
            </div>
            <div class="modal-footer">
                <button type="button"
                    class="btn btn-default waves-effect "
                    data-bs-dismiss="modal">Close</button>
                
            </div>
        </div>
    </div>
</div>


<script>
    $('#dt-history').on('click', 'tr', function () {
        // var table = $('#dt-server-processing').DataTable();
        // var row = table.row( this ).data();
        // console.log(data);
        var tr = $(this).closest('tr');
        var row = table.row(tr);
        if (row.child.isShown()) {
            // This row is already open - close it
            row.child.hide();
            tr.removeClass('shown');
        } else {
            // console.log(row.data()[0])
            var id = row.data()[4];
            
            var views = 'v_lahanbakusawah_prov_hist';           
            $.get("<?php echo base_url(); ?>lookup/getChildHistory/" +id+'/'+views, function(data) {
                data = JSON.parse(data)
                series = data;
                console.log(data)
                row.child(data).show();
                tr.addClass('shown');
            });
        }
    })

    function dtHistory(kdpkab,tahun) {
            var lay = 'v_lahanbakusawah_prov_hist';           
            listingHist(kdpkab,lay,tahun)
        $("#modal_history").modal("show");
    }
    function listingHist(kdpkab,lay,tahun) {
        if (!$('#dt-history').hasClass('dataTable')) {
            $('#dt-history').DataTable().clear().destroy()
        }
        table = $('#dt-history').DataTable({
            "draw": 0,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "bDestroy": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>lookup/ssp_paket_hist/"+kdpkab+'/'+lay+'/'+tahun,"data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
            }},
            "columnDefs": [
                {
                    "aTargets": [0],
                    "mRender": function (data, type, full,meta) {
                        // console.log(full)
                        return meta.row + meta.settings._iDisplayStart + 1;
                    }
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        if(full[1] == 'Insert'){
                            return '<span class="badge badge-success">Mengentri Data</span>';
                        }else if(full[1] == 'Update'){
                            return '<span class="badge badge-primary">Mengubah Data</span>';
                        }else{
                            return '<span class="badge badge-danger">Menghapus Data</span>';
                        }
                    }
                },
                {
                    "aTargets": [2],
                    "mRender": function (data, type, full) {
                        return full[2];
                    }
                },
                {
                    "aTargets": [3],
                    "mRender": function (data, type, full) {
                        return dateTimeIndo(full[3]);
                    }
                },
                
            ],
            
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }
    
    function dateTimeIndo(v) {
        // Tanggal dan waktu dalam format "YYYY-MM-DD HH:MM:SS.ssssss"
        var dateTimeString = v;

        // Membuat objek Date dari string
        var dateTime = new Date(dateTimeString);

        // Opsi untuk memformat tanggal
        var dateOptions = {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        };

        // Opsi untuk memformat waktu
        var timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        };

        // Format tanggal Indonesia
        var formattedDate = dateTime.toLocaleDateString('id-ID', dateOptions);

        // Format waktu
        var formattedTime = dateTime.toLocaleTimeString('id-ID', timeOptions);

        // Hasil akhir
        return formattedDate + " Pukul " + formattedTime;

    }
</script>