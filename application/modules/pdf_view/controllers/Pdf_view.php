<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Pdf_view extends MY_Controller {

    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->helper('dtssp'); //datatable server side processing helper
        
        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string())){
            header('location:' . base_url());
        }
    }

    public function index() {
        header("Access-Control-Allow-Origin: *");
    
        /*keterangan parameter
          $this->template->load('default_layout', 'contents' , 'index', $data);
          1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
          2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
          3.index = nama view yang akan di load
        */
        $title = "Jenis Alat";
        $js_file = $this->load->view('pdf_view/js_file', '', true);
        $modal_filter = $this->load->view('pdf_view/modal_filter', '', true);
        $modal_tambah = $this->load->view('pdf_view/modal_tambah', '', true);
        $data = array("modal_filter" => $modal_filter,
            "modal_tambah" => $modal_tambah,
            "title" => $title,
        );
        $this->template->set('title', $title);
        $this->template->set('jv_script', $js_file);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }
    public function get_pdf() {
        $query = $this->db->query("select * from aset_upload_pandu  order by id desc limit 1");
        $data_satker = json_decode(json_encode($query->result()), true);

        echo json_encode($data_satker);
    }
    public function up() {
        if (is_array($_FILES)) {
            $x = str_replace('-', '', date('Y-m'));
            $nama_dir = FCPATH . 'uploads/' . $x . "/";

            if (is_dir($nama_dir)) {
                
            } else {
                mkdir(FCPATH . 'uploads/' . $x, 0777, true);
//            $m = FCPATH . 'uploads/' . str_replace('-', '', date('Y-m'));
            }
            $upload_path_url = str_replace('-', '', date('Y-m'));
            if (is_uploaded_file($_FILES['filess']['tmp_name'])) {
                $sourcePath = $_FILES['filess']['tmp_name'];
                $namf = $_FILES['filess']['name'];
                $rep = str_replace(" ", "_", $namf);
                $fil = date('Ymd') . date("his") . $rep;
                $targetPath = FCPATH . "uploads/" . $upload_path_url . "/" . $fil;
                move_uploaded_file($sourcePath, $targetPath);
               // $this->db->where('id_usulan', $id);
                

            //    $this->db->set('created_at', date("Y-m-d h:i:s"), FALSE);
            //    $this->db->set('updated_at',date("Y-m-d h:i:s"), FALSE);
            //    $this->db->set('', $this->session->users['id_user'], FALSE);
                $res = $this->db->insert('aset_upload_pandu', 
                   array('attachment' => $fil,
                    'id_user' =>$this->session->users['id_user'],
                    'created_by' =>$this->session->users['id_user'],
                    'nm_file' =>$this->input->post('jdlfile'),
                    'tahun' => $this->session->konfig_tahun_ang ));
            }
        }
    }
 public function ssp_attachment() {
        $id_usulan = $this->input->post("id", TRUE);
        $role = $this->input->post("role", TRUE);
        // echo $role." ***".$id_usulan; die();
        $table = 'aset_upload_pandu';
        $primaryKey = 'id';
        $columns = array(
            array('db' => 'attachment', 'dt' => 0),
            array('db' => 'id', 'dt' => 1),
            array('db' => 'nm_file', 'dt' => 2)
        );
        datatable_ssp($table, $primaryKey, $columns);
    }
      public function hps_lampiran($id) {

        $this->db->delete('aset_upload_pandu', array('id' => $id));

        echo json_encode(array("status" => TRUE));
    }
    public function addform() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        $param =array_merge($param, array('tahun'=>date('Y')));
        unset($param ['id']);
        $cek = $this->db->get_where('aset_r_peralatan', array('peralatan' => $param['peralatan'],'satuan' => $param['satuan'], 'tahun' => date('Y')));
        // $cek_irms = $this->db->get_where('provinsi', array('kd_prov_irmsv3' => $param['kd_prov_irmsv3'], 'thang' => $param['thang']));
        // $cek_rams = $this->db->get_where('provinsi', array('kd_prov_rams' => $param['kd_prov_rams'], 'thang' => $param['thang']));
        // $cek_rkakl = $this->db->get_where('provinsi', array('kd_prov_rkakl' => $param['kd_prov_rkakl'], 'thang' => $param['thang']));
        if ($cek->num_rows() > 0) {
            echo "<h4 style='color:orange;'><i class='fa fa-times'></i> Data sudah ada<h4>";
        } 
        // else if ($cek_irms->num_rows() > 0) {
        //     echo "<h4 style='color:orange;'><i class='fa fa-times'></i> Kode Provinsi IRMSv3 Tersebut Sudah Ada<h4>";
        // } else if ($cek_rams->num_rows() > 0) {
        //     echo "<h4 style='color:orange;'><i class='fa fa-times'></i> Kode Provinsi RAMS Tersebut Sudah Ada<h4>";
        // } else if ($cek_rkakl->num_rows() > 0) {
        //     echo "<h4 style='color:orange;'><i class='fa fa-times'></i> Kode Provinsi RKAKL Tersebut Sudah Ada<h4>";
        // } 
        else {
            $res = $this->db->insert('aset_r_peralatan', $param);
            if (!$res) {
                echo "Gagal";
            } else {
                echo "<h4 style='color:green;'><i class='fa fa-check'></i> Berhasil Menambah Data<h4>";
            }
        }
    }

    public function editform() {
        $param = $this->input->post('formData', TRUE);
        // $this->wgisitia->handle_removed($param);
        $id = $param['id'];
        $param =array_merge($param, array('tahun'=>date('Y')));
        // $cek = $this->db->get_where('aset_r_provinsi', array('kd_prov' => $id));
        $cek = $this->db->get_where('aset_r_peralatan', array('peralatan' => $param['peralatan'],'satuan' => $param['satuan'], 'tahun' => date('Y')));
        if ($cek->num_rows() > 0){
            echo "<h4 style='color:orange;'><i class='fa fa-times'></i> Data Sudah Ada<h4>";
        } else {
           // echo "Gagal";
            unset($param ['id']);
            $this->db->where('id_alat', $id);
            $this->db->update('aset_r_peralatan', $param);
            echo "<h4 style='color:green;'><i class='fa fa-check'></i> Berhasil Merubah Data<h4>";
        }
    }

    public function deleteform() {
        $id = $this->input->post('id', TRUE);
        $this->db->where('id_alat', $id);
        $res = $this->db->delete('aset_r_peralatan');

        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

//    function get_response() {
//        $url = '113.20.29.25:12890/testdata/listuser'; //url di set global
//        $ch = curl_init($url);
//        $header = [];
//        $header[] = 'Content-type: application/json';
//        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
//        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
//        $header[] = "Cache-Control: no-cache";
//        $header[] = "accept-encoding:*";
//        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
//        $result = curl_exec($ch);
//        curl_close($ch);
//
//        if (!$result) {
//            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
//            die("Koneksi Gagal");
//        } else {
//            return $result;
//            //print_r($result);
//        }
//    }
    
    function get_thang() {
        //$query = $this->db->query("SELECT thang, uraian FROM r_thang");
        $query = $this->db->get("r_thang");
        $data_thang = json_decode(json_encode($query->result()), true);
        echo json_encode($data_thang);
    }
    
    function get_induk($thang) {
        //$query = $this->db->query("SELECT * FROM rf_satker WHERE thang = $thang AND kdlokasi = '$lokasi' AND kdsatker = kdinduk");
        //$query = $this->db->query("SELECT * FROM rf_satker WHERE thang = $thang AND kdsatker = kdinduk");
        $where = "thang = $thang AND kdsatker = kdinduk";
        $query = $this->db->where($where)->get("rf_satker");
        $data_thang = json_decode(json_encode($query->result()), true);
        echo json_encode($data_thang);
    }

}
