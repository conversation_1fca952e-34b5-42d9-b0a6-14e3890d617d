<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<style>
    #pdf1{
        height:500px;
    }
</style>
<div class="block">
    <div class="block-header">
        <div class="row form-inline">
                  <?php 
                                  if($this->session->users['role'] !='bujt'){       
                             ?>
                <div class="col-md-12"> 
                             <hr style='margin:10px;'>
                       
                             <form class="form-inline"  id="submit">
                             <div class="form-group col-md-12">
                             <label class="control-label col-md-1">Nama File</label>      
                            <div class="col-md-4">
                             <div class="input-group" style="width:100%;">
                                
                                 <input type="text" class="form-control" placeholder='Masukan Nama File' id="jdlfile" name="jdlfile"/>
                             </div>   
                           </div>
                            <div class="col-md-4">
                             <div class="input-group input-file" name="filess" style="width:100%;">
                                 <span class="input-group-btn">
                                 <button class="btn btn-default btn-choose" type="button" style="width:100%;"><i class="fa fa-upload"></i> Upload File</button>
                                 </span>
                                 <input type="text" class="form-control" placeholder='Tidak ada file yang diupload' id="nmfile" disabled/>
                             </div>   
                            <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
                           </div>
                           </form> 
                    <div class="col-md-3">
                    <button type="submit" class="btn btn-success"><i class="fa fa-save"></i> Simpan</button>  
                    </div>
                    
               </div>
               <?php } ?>
          </div>
    </div>
    <div class="block-content" style="overflow-x:scroll;">
            <div class="box-body">            
                            <br/><br/>

                            <div class="row">
                                <div class="col-md-12">
                                    <div id="table-container2">
                                        <table id="table_id2" class="display" style="width:100%">
                                            <thead>
                                                <tr>
                                                    <th >Nama File</th>
                                                    
                                                    <th>
                                                        Action
                                                    </th>
                                                </tr>
                                            </thead>							
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
        <!-- <div id="pdf1"></div> -->
    </div>
</div>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/pdf/pdfobject.js"></script>

<script>
    $(document).ready(function () {
       
       
    })


 
</script>
