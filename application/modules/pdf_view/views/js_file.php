<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
     var roledesc = "<?php echo $this->session->users['role']; ?>";
    var xhrdata = null;
    var table = null;
    function clear_input() {
        $("#formData :input").val("");
    }
function get_extentsion_file(file) {
    var extension = file.substr((file.lastIndexOf('.') + 1));
    switch (extension) {
    case 'jpg':
            case 'png':
            case 'jpeg':
            case 'gif':
            return 'fa fa-image'; // There's was a typo in the example where
    break; // the alert ended with pdf instead of gif.
    case 'zip':
            case 'rar':
            //alert('was zip rar');
            return 'fa fa-file-archive-o'
            break;
    case 'pdf':
            return 'fa fa-file-pdf-o';
    case 'xlsx':
            return 'fa fa-file-excel-o';
    break;
    default:
            "fa fa-file"
    }
    }
 var table_attachment = null;
    function listing_attachment2() {
        var display ='block';
        if(roledesc == 'bujt'){ 
            display ='none';
        }

            table_attachment = $('#table_id2').DataTable({
        
                "draw": 0,
                "columnDefs": [{"orderable": true, "targets": [0]}],
                "order": [[0, "desc"]],
                "processing": true,
                "serverSide": true,
                "ajax": {
                    type: "POST",
                    url: "<?php echo base_url(); ?>pdf_view/ssp_attachment",
                    data: function (d) {
                        d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    }
                },
                "aoColumnDefs": [
                    {
                        "aTargets": [0],
                        "mRender": function (data, type, full) {
                            var ico_class = get_extentsion_file(full[0]);
                            var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp";
                            var subs_img=full[0].substr(0,6);
                            if(ico_class == "fa fa-image"){
                                html_icon="<a class='fancybox' rel='group' href='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'><img class='img-responsive' style='width:40%;' src='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'></a>";
                            }
                            return html_icon+full[2];
                        }
                    },

                    {
                        "aTargets": [1],
                        "mRender": function (data, type, full) {
                            //console.log("full attachment");
                            //console.log(full);
                            var data_full_attachment = full[0];
                            var dire = data_full_attachment.substr(0, 6);
                            var html_button = [
                                "<a target='_blank' style='float:left;'  class='btn btn-primary' href='<?php echo base_url(); ?>uploads/" + dire + "/" + data_full_attachment + "'><i class='fa fa-download'></i></a> ",
                                " <button target='_blank' style ='display:"+display+";width:auto;' class='btn btn-danger' onclick=hapus_lampiran('" + full[1] + "')><i class='fa fa-trash'></i></button>",
//                            "<button onclick=download_lampiran('" + data_full_attachment + "') class='btn btn-primary btn-xs'>",
//                            "<i class='fa fa-download'>",
//                            "</i>",
//                            "</button>",
                            ].join("\n");
                            return html_button;
                        }
                    }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
            });
            table_attachment.on('xhr', function () {
                xhrdata1 = table_attachment.ajax.json();
                console.log(xhrdata1);
            });

            $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        

    }
function hapus_lampiran(id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

            var url = "<?php echo base_url(); ?>" + "pdf_view/hps_lampiran/" + id ;
            var params = {"formData": {}, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params)
                    .done(function (data) {
                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload()
                        var tab = $('#table_id2').DataTable();
                        tab.ajax.reload();
                        ;
                    })
                    .fail(function () {
                        alert("error");
                    })
        }
    }
    function bs_input_file() {
        $(".input-file").before(
                function () {
                    if (!$(this).prev().hasClass('input-ghost')) {
                        var element = $("<input type='file' class='input-ghost' style='visibility:hidden; height:0'>");
                        element.attr("name", $(this).attr("name"));
                        element.change(function () {
                            element.next(element).find('input').val((element.val()).split('\\').pop());
                        });
                        $(this).find("button.btn-choose").click(function () {
                            element.click();
                        });
                        $(this).find("button.btn-reset").click(function () {
                            element.val(null);
                            $(this).parents(".input-file").find('input').val('');
                        });
                        $(this).find('input').css("cursor", "pointer");
                        $(this).find('input').mousedown(function () {
                            $(this).parents('.input-file').prev().click();
                            return false;
                        });
                        return element;
                    }
                }
        );
    }
    function listing() {
      
        table = $('#table_id').DataTable({
            "draw": 0,
            "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>pdf_view/ssp","data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
                // d.thang = $('#fthang').val();
                
            }},
            "aoColumnDefs": [
                {
                    "aTargets": [3],
                    "mRender": function (data, type, row) {
                        var id = row[4];
                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "<i class='fa fa-pencil'>",
                            "</i>",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>"//,
                            //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fast backward ui icon'></i>",
                    "last": "<i class='fast forward ui icon'></i>",
                    "next": "<i class='step forward ui icon'></i>",
                    "previous": "<i class='step backward ui icon'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }
    
    function bind_combo_induk(thang, selval){
        var data = get_induk(thang);
        var objthang = JSON.parse(data);
        $("#kdinduk").empty();
        $('#kdinduk').append("<option value='' >" + "--Pilih--" + "</option>");
        for(var i=0; i<= objthang.length-1; i++){
            $("#kdinduk").append("<option value="+objthang[i].kdsatker+">"+objthang[i].nmsatker+"</option>");
        }
        if (selval){
            $("#kdinduk").val(selval);
        }
    }
    
    function get_induk(thang){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('pdf_view/get_induk/') ?>" + thang,
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }

    function dtEditRow(id) {

    var data=tampil_data(id);


    $("#frm-tambah #nama").val(data[0].peralatan);
    $("#frm-tambah #satuan").val(data[0].satuan);
    refreshComboboxOutput6WoThang('jns_alat', 2, 'tahun', 2021, data[0].kd_pdf_view);
    $("#frm-tambah #id").val(id);

        //$("#kd_kab_kota_bpiw").html(prov.);
        // $("#kd_kel").html(data_selected[8]);

        

    //  $("#frm-tambah #kd_kec2").val( data_selected[4] !== null ? data_selected[3].substring(7,10) : '');

    $("#modalTitle").text("Edit Peralatan");
    $("#modeform").val("edit");
    $("#btn-simpan").show();
    $('#modal-tambah').modal('show');
    }


    function dtTambahRow() {
        $('#frm-tambah')[0].reset();
        $("#frm-tambah :input").val("");
        refreshComboboxOutput6WoThang('jns_alat', 2, 'tahun', 2021);
        //$("input").prop('disabled', false);
            $("#hid").show("slow");
            $("#hida").show("slow");
        var thangchoose = $("#fthang").val();
        //$("#thang").val(thangchoose);
        // updateComboboxAndSelectedWoThang("thang", 21, thangchoose);
        // bind_combo_induk(thangchoose);
        $('#modalTitle').text('Tambah ');
        $('#modeform').val('tambah');
        $('#modal-tambah').modal('show');
    }
   
    function dtdetail(id){
        //console.log(xhrdata);

        var data_selected = xhrdata.data.filter(x => x[9] === id)[0];  //46

        var waydata = {
            thang: $("#fthang").val(),
            ibukota: data_selected[2],
            kd_prov:data_selected[0],
            kd_prov_bps: data_selected[3],
            kd_prov_irmsv3: data_selected[4],
            kd_prov_krisna:data_selected[5],
            kd_prov_rams: data_selected[6],
            kd_prov_rkakl: data_selected[7],
            kd_prov_sipro: data_selected[8],
            nama_prov: data_selected[1]

        }

        way.set('formData', waydata);
        $('#modalTitle').text('Detail');
//        $('#modeform').val('edit');
        $("input").prop('disabled', true);
	$("#hid").hide("slow");
	$("#hida").hide("slow");
        $('#modal-tambah').modal('show');
    }

    function dtDeleteRow(id) {
        url = "<?php echo base_url();?>pdf_view/deleteform";
        //console.log(id);
        var r = confirm("Data yang anda pilih akan kami hapus!, apakah anda yakin mau melanjutkan");
        if (r === true) {
            $.post(url, {id: id,"<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"}).done(function (data) {
	    $("#alert-content").empty();
            $("#alert-content").append("<h4 style='color:green;'><i class='fa fa-check'></i> Berhasil Menghapus Data<h4>");
            $("#alert_information").css({display: "block"});
            setTimeout(close_alert, 3000);
            table.ajax.reload();
            })

        }


    }
    function tampil_data(id){
    var url=''
    var tadata=''

    urls = "<?php echo base_url(); ?>pdf_view/tampildata/"+id;
    $.ajax({
                url: urls,
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                success: function (data) {
                tadata = data;
                },
                failure: function (errMsg) {
                alert(errMsg);
                }
        });
        return tadata;
  }

    function simpanForm() {
        var mode = $('#modeform').val();
        //var serializeData = way.get('formData');
        
        if($( "#frm-tambah" ).valid() === true){
            var serializeData = {
                "peralatan": $("#nama").val(),
                "kd_pdf_view": $("#jns_alat").val(),
                // "kd_prov_bps": $("#Kode_provinsi").val(),
                // "kd_prov_irmsv3": $("#kd_prov_irmsv3").val(),
                //"ibukota" : $("#ibukota").val(),
                //"kd_prov_krisna": $("#kd_prov_krisna").val(),
                // "kd_prov_rams": $("#kd_prov_rams").val(),
                // "kd_prov_rkakl": $("#kd_prov_rkakl").val(),
                //"kd_prov_sipro": $("#kd_prov_sipro").val(),
                "satuan": $("#satuan").val(),
                "id": $("#id").val(),
                // "kd_satker_balai": $("#kdinduk").val()
            };

            //console.log(serializeData);
            if (mode === 'tambah') {
                url = "<?php echo base_url(); ?>pdf_view/addform";
            } else if (mode === 'edit') {
                url = "<?php echo base_url(); ?>pdf_view/editform";
            }

            var params = {"formData": serializeData,"<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};       
            $.post(url, params).done(function (data) {
                $("#alert-content").empty("");
                $("#alert-content").append(data);
                $("#alert_information").css({display: "block"});
                setTimeout(close_alert, 3000);
                table.ajax.reload();
            });
        } else {
            $("label.error").css("color","red");
        }
    }

    function close_alert() {
        $("#alert_information").css({display: "none"});
    }
    
    function bind_combo_thang(selval){
        var data = get_thang();
        var objthang = JSON.parse(data);
        for(var i=0; i<= objthang.length-1; i++){
            $("#fthang").append("<option value="+objthang[i].thang+">"+objthang[i].uraian+"</option>");
        }
        if (selval){
            $("#fthang").val(selval);
        }
    }
    function get_pdf(){
        $.ajax({
             url: "<?php echo base_url('pdf_view/get_pdf') ?>",
             contentType: "application/json; charset=utf-8",
             dataType: "json",
             async: false,
             success: function (dataa) {
                     for (var i = 0; i <= dataa.length - 1; i++) {                  
                         var subs_img=dataa[i].attachment.substr(0,6);
                         var lok = "uploads/"+subs_img+"/"+dataa[i].attachment
                         PDFObject.embed(lok, "#pdf1");
                        }                                                   
             },
             failure: function (errMsg) {
             alert(errMsg);
             }
             });
    }
    function get_thang(){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('pdf_view/get_thang') ?>",
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }

    $(document).ready(function () {
        listing_attachment2()
        // get_pdf();
        $('#submit').submit(function (e) {
            e.preventDefault();
            var file = new FormData;
//            var id = $("#id_usulan").val();
           // console.log(id);
            $.ajax({
                url: '<?php echo base_url(); ?>pdf_view/up',
                type: "post",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                var tab = $('#table_id2').DataTable();
                 tab.ajax.reload();
                tlist_paket.ajax.reload();
                    $("input").val('');
                }
            });
        });
        // bind_combo_thang(thangs);
        bs_input_file();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");

        $(".si-close").click(function(){
            $("input").prop('disabled', false);
        });
        
        $('#fthang').change( function() {
            table.draw();
        });
        
        
        
    });

</script>
