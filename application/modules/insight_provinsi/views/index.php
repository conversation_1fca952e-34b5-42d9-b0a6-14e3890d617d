<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>

 <div class="page-body">
    <!-- Server Side Processing table start -->
    <div class="card">
        <div class="card-header">
            <h5><?=$title;?></h5>
        </div>
        <div class="card-block">
        <div class="row">     
                <div class="col-sm-6">
                    <!-- <button type="button" style="float:left!important" class=" btn btn-primary waves-effect waves-light add" onclick="dtUpload();">Tambah Kemampuan Tanah -->

                </div>
                <div class="col-sm-6 d-flex align-items-end justify-content-end" style="">
                    <!-- <button style="margin-left:3px;border-radius:20%;color:blue;border-color:blue;" onclick="downloadCaraUpload()" title="Petunjuk Upload" class=""><i class="fa-regular fa-2x fa-file-pdf"></i></button> -->
                    <!-- <button style="margin-left:3px;border-radius:20%;color:red;border-color:red" title="Dokumen " class=""onclick="viewDokumen()"><i class="fa-regular fa-2x fa-file-pdf"></i></button> -->
                </div>
                <div class="col-md-5" style="margin-top:20px;display:inline">
                        <label style="font-size:15pt;">Provinsi</label>
                        <select id="ikd_prov"onchange="filterChange('prov')" name="ikd_prov" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                </div>
                <div class="col-md-5" style="margin-top:20px">
                    <label style="font-size:15pt">Tahun Data</label>
                    <select id="itahun_data" onchange="filterChange('kabkot')" name="itahun_data" class="bootstrap-select form-control" data-live-search="true">
                    <option value="">Semua Tahun</option>
                    <?php
                            for ($i=date('Y'); $i > 1991 ; $i--) { 
                                echo '<option value="'.$i.'">'.$i.'</option>';
                            }
                    ?>
                    </select>
                </div>
            </div>
            <br/>
            <div>
                
            </div>
            <div class="dt-responsive table-responsive">
                <table id="dt-server-processing" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Provinsi</th>
                            <th>Tematik</th>
                            <th>Peta</th>
                            <th>Tahun</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <!-- Server Side Processing table end -->
</div>   
<!-- <script src="http://code.jquery.com/jquery-2.1.4.min.js"></script> -->
<?php echo $jv_script; ?>
<?php //echo $modal_tambah; ?>
<?php //echo $modal_download; ?>
<?php //echo $modal_edit; ?>

<!-- <script src="http://cdn.datatables.net/1.10.7/js/jquery.dataTables.js"></script> -->
<!-- <script src="//cdn.rawgit.com/ashl1/datatables-rowsgroup/v1.0.0/dataTables.rowsGroup.js"></script> -->

<!-- <script src="http://code.jquery.com/jquery-1.11.1.min.js"></script> -->

    <!-- <link href="//datatables.net/download/build/nightly/jquery.dataTables.css" rel="stylesheet" type="text/css" /> -->
    <!-- <script src="//datatables.net/download/build/nightly/jquery.dataTables.js"></script> -->
    <script src="//cdn.rawgit.com/ashl1/datatables-rowsgroup/v1.0.0/dataTables.rowsGroup.js"></script>

