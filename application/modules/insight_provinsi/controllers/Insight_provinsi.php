<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class In<PERSON>_<PERSON><PERSON><PERSON> extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();


        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());

        
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "Pencairan Umum";
        $js_file = $this->load->view('insight_provinsi/js_file', '', true);
        // $insight = $this->getByProv();
        // exit();
        
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "jv_script" => $js_file,
        );
        
        $this->load->view('index', $data);
    }

    public function ssp_paket()
    {
        
        
        $tahun = $this->input->post('tahun_data', true);
        $prov = $this->input->post('kd_prov', true);
        $role = $this->session->users['id_user_group'];

        $this->db->join('aset_group_modules', 'aset_group_modules.kode_module = v_tema_peta.kd_module', 'left');
        $this->db->select('v_tema_peta.*,aset_group_modules.id_user_group');
        $this->db->where('aset_group_modules.id_user_group', $role);
                
        $tema =$this->db->get('v_tema_peta')->result();
        
        // $this->db->where('table_name !=','spatial.v_d_npgt_prov');
        $list_view = $this->db->get('v_list_view')->result();
        $list=[];
        foreach ($list_view as $key => $value) {
            array_push($list,$value->table_name);
        }
        
        $data=[];
        foreach ($tema as $key => $value) {
           
            if (!array_key_exists($value->parent_tema,$data)) {
                    $data[$value->parent_tema] = [];
            }

        }
        
        $data = [];
        $datas=[];
        
        // echo "<pre>";
        // print_r ($tema);
        // echo "</pre>";exit();
        // $arrRole = [2 => []]

        foreach ($tema as $key => $value) {
           
            if (in_array($value->v_dasboard,$list)) {
                $get=[];
                $value->v_dasboard = $value->v_dasboard == 'v_d_npgt_prov' ? 'vm_npgt_prov':$value->v_dasboard;
                $value->v_dasboard = $value->v_dasboard == 'v_d_npgt_kec' ? 'vm_npgt_kec':$value->v_dasboard;
                $value->v_dasboard = $value->v_dasboard == 'v_d_npgt_perkebunan' ? 'vm_npgt_perkebunan':$value->v_dasboard;
                // $value->v_dasboard = $value->v_dasboard == 'v_d_tn_tnbh' ? 'vm_tnbh':$value->v_dasboard;
                // $value->v_dasboard = $value->v_dasboard == 'v_d_tn_tnbk' ? 'vm_tnbk':$value->v_dasboard;
                // $value->v_dasboard = $value->v_dasboard == 'v_d_tn_tntk' ? 'vm_tntk':$value->v_dasboard;
                $value->v_dasboard = str_replace('v_d_wp3wt','vm_wp3wt',$value->v_dasboard);
                if($tahun != '' && $tahun != NULL){
                    $this->db->where('tahun_data', $tahun);
                }
                if($prov != '' && $prov != NULL){
                    $this->db->where('kdppum', $prov);
                    $this->db->select('kdppum,wadmpr,tahun_data');
                    $this->db->group_by('kdppum,wadmpr,tahun_data');
                    
                    $get = $this->db->get('spatial.'.$value->v_dasboard)->result();

                }

                // $this->db->join('spatial.batas_admin_indonesia b', 'table.column = table.column', 'left');
                
                if (!empty($get)) {
                    foreach ($get as $k => $v) {
                        
                        $v->layer = $value->nama_layer;
                        $v->module = $value->nama_module;
                      
                        
                        array_push($datas,$v);    
                    }
                    
                }
            }

        }
        // $data;
        $arr=[];
        foreach ($datas as $key => $value) {
            $a=[$value->module,$value->layer,$value->wadmpr,$value->tahun_data];
            array_push($arr , $a);
        }

        $datacount = count($arr);
        $data = ['data' => $arr,'draw' => 1,'recordsFiltered' => $datacount, 'recordsTotal' => $datacount];
        echo json_encode($data);
        // echo "<pre>";
        // print_r ($arr);
        // echo "</pre>";
        
        
         
    }


    function update_form() {
        // $this->wgisitia->handle_removed($param);
        $param = $this->input->post('formData', false);
        
        // echo "<pre>";
        // print_r ($this->input->post('formData', FALSE));
        // echo "</pre>";
        // strip_tags($_POST[''], '<p><a>');
        // exit();

        
        
        
        $data_detail = [ 
            "kontak" => $param["kontak"], 
        ];

        $this->db->where('id', 1);
        $this->db->update('landing_page', $data_detail);

        echo json_encode(array("status" => TRUE,"data"=>$param["kontak"]));
    }

    


}