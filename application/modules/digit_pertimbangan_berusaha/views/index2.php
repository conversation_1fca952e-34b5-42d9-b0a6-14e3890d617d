<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>

<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
   integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
   crossorigin=""/>
   <link rel="stylesheet" href="https://unpkg.com/@geoman-io/leaflet-geoman-free@latest/dist/leaflet-geoman.css" /> 
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
   integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
   crossorigin=""></script>
   	<script src="https://unpkg.com/@geoman-io/leaflet-geoman-free@latest/dist/leaflet-geoman.min.js"></script>  

   
 <div class="page-body">
    <!-- Server Side Processing table start -->
    <div class="card">
        <div class="card-header">
            <h5><?=$title;?></h5>
        </div>
        <div class="" style="padding:20px">
        <form class="form-horizontal" id="frm-tambah">
            <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">

            <div class="row">
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="kd_prov">Provinsi</label>
                        <div class="col-md-12">
                        <select id="kd_prov" name="kd_prov" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="kd_prov">Kab/Kota</label>
                        <div class="col-md-12">
                            <select id="kd_kabkot" name="kd_kabkot" class="bootstrap-select form-control" data-live-search="true">
                            </select>
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="luas">geom</label>  
                        <div class="col-md-12">
                        <input id="geom" name="geom" type="text" placeholder="" class="form-control input-md float-number">
                        
                        </div>
                    </div>
            </div>
         
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" onclick="$('#frm-tambah').submit()" class="btn btn-primary">Save changes</button>
        </form>
        </div>
        <div class="card-block">
            
        <div id="map2" style="height: 400px;width: 100%"></div>
        
        </div>
    </div>
    <!-- Server Side Processing table end -->
</div>   
<input type="hidden" name="alias_menu" id="alias_menu" value="<?php echo $menu?>">

<script>
    $(document).ready(function () {
        var map = L.map('map2').setView([51.505, -0.09], 13);
        L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '© OpenStreetMap'
        }).addTo(map);
        var drawnItems = new L.FeatureGroup().addTo(map);

        map.pm.addControls({
            position: 'topleft',
            drawPolygon: true, // Enable drawing polygons
        });

        map.on('pm:create', function(event) {
            var layer = event.layer;
            drawnItems.addLayer(layer);
        });
        map.pm.addControls({
  position: 'topleft',
  drawPolygon: true,
  snappable: true, // Enable snapping to existing features
  snapDistance: 20, // Set the snapping distance
});

map.on('pm:create', function(event) {
  var layer = event.layer;

  // Style the drawn polygon
  layer.setStyle({
    color: 'blue',
    fillColor: 'lightblue',
    fillOpacity: 0.4,
  });

  drawnItems.addLayer(layer);
});
        $('#frm-tambah').submit(function (e) {
            e.preventDefault();
            console.log('sub')
            var file = new FormData(this);
            $.ajax({
                    // url: '<?php echo base_url(); ?>digit_pertimbangan_psrp_tnh_timbul/up',
                    url: '<?php echo base_url(); ?>digit_pertimbangan_psrp_tnh_timbul/insertShp',
                    type: "post",
                    data: file,
                    processData: false,
                    contentType: false,
                    cache: false,
                    async: false,
                    success: function (data) {
                        // $("select").val('--pilih--');
                        // $("select").selectpicker("refresh");
                        data=JSON.parse(data)
                        console.log(data.sts)
                        if(data.sts=='gagal'){
                            swal.close()
                            Swal.fire(
                                'Gagal!',
                                data.msg,
                                'error'
                            )
                        }else{

                            $("#filess").val('')
                            var tab = $('#table_id2').DataTable();
                            tab.ajax.reload();
                            var table = $('#dt-server-processing').DataTable();
                            table.ajax.reload();
                            swal.close()
                            Swal.fire(
                                'Sukses!',
                                'Data Tersimpan!',
                                'success'
                            )
                        }
                        
                    },error: function (jqXHR, exception) {
                        // console.log(jqXHR);
                        swal.close()
                        Swal.fire(
                            'Gagal!',
                            'Data Gagal Tersimpan!',
                            'error'
                        )
                    }
                });
        });
    });

    // $('#modDigitasi').on('shown.bs.modal', function (e) {
        // var map = L.map('map2').setView([-2.7521401146517785, 116.07226320582281], 5);

           
</script>