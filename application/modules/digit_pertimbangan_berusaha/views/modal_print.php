<link href="<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.css" rel="stylesheet" />
<script type="text/javascript"
    src="<?=base_url();?>assets/themes/adminity/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>

<script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/leaflet/leaflet.js"></script>

<script src="<? echo base_url();?>assets/js/pgt-maps/leaflet-src.js"></script>

<!-- <script src="<?php //echo base_url(); ?>assets/peta/geoman/leaflet-geoman.min.js"></script>   -->
<script src="<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.js"></script>
<script src="<?php echo base_url(); ?>assets/peta/mapbox/leaflet-mapbox-gl.js"></script>


<script type="text/javascript" src="<?=base_url();?>node_modules/leaflet-easyprint/dist/bundle.custom.js"></script>
<script type="text/javascript" src="<?=base_url();?>node_modules/dom-to-image/src/dom-to-image.js"></script>
<script type="text/javascript" src="<?=base_url();?>node_modules/file-saver/FileSaver.js"></script>



<script src="<? echo base_url();?>assets/js/L.TileLayer.BetterWMS.js"></script>



<style>
.leaflet-grab {
    cursor: auto;
}

.leaflet-dragging .leaflet-grab {
    cursor: move;
}

.leaflet-control-layers-separator {
    display: block !important
}

.opt_select {
    color: black !important;
}

body {
    max-height: 400px;
    /* Set your desired maximum height */
    overflow-y: auto;
    /* Enable vertical scrolling if content exceeds the height */
}

/* Make sure the size of the image fits perfectly into the container */
</style>

<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel"></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body">
                <form id="uploadForm" class="form-horizontal">
                    <fieldset class="border rounded-3 p-3">
                        <legend class="float-none w-auto px-3">Peta</legend>
                        <img id="capturedImage" src="" alt="Captured Image" class="img-fluid">
                        <img id="captured-image" src="" style="width:200px;display:none" alt="Captured Image">
                        <img id="captured-mataangin" src="" style="width:170px;;display:none" alt="Captured Image">

                        <!-- <button type="button" style="float:right" class="btn btn-primary" onclick="crop()" id="cropButton">Crop</button> -->

                    </fieldset>
                    <fieldset class="border rounded-3 p-3">
                        <legend class="float-none w-auto px-3">Isian</legend>

                        <div class="form-group">
                            <label class="col-md-12 control-label" for="id_ptp">Nama Pemohon</label>
                            <div class="col-md-12">
                                <select id="id_ptp" name="id_ptp" class="bootstrap-select form-control"
                                    data-live-search="true">
                                    <!-- <option value="#">Pilih</option> -->
                                </select>

                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-md-12 control-label" for="legenda">Legenda</label>
                            <div class="col-md-12">

                                <select id="legenda" name="legenda" class="bootstrap-select form-control"
                                    data-live-search="true"> -->
                                </select>


                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="ditinjau">Ditinjau Oleh</label>
                            <div class="col-md-12">
                                <input id="ditinjau" name="ditinjau" type="text" placeholder=""
                                    class="form-control input-md numberonly">

                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="tanggal">Tanggal</label>
                            <div class="col-md-12">
                                <input id="tanggal" name="tanggal" type="date" placeholder=""
                                    class="form-control input-md numberonly">

                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="digambar">Digambar Oleh</label>
                            <div class="col-md-12">
                                <input id="digambar" name="digambar" type="text" placeholder=""
                                    class="form-control input-md numberonly">

                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="diperiksa">Diperiksa Oleh</label>
                            <div class="col-md-12">
                                <input id="diperiksa" name="diperiksa" type="text" placeholder=""
                                    class="form-control input-md numberonly">

                            </div>
                        </div>
                    </fieldset>

                    <fieldset class="border rounded-3 p-3">
                        <legend class="float-none w-auto px-3">Sumber Peta</legend>

                        <div class="form-group div_sumber_peta">
                            <label class="col-md-10 control-label" for="sumber_peta">Sumber Peta</label>
                            <div class="row">

                                <div class="col-md-10">
                                    <input id="sumber_peta" name="sumber_peta[]" type="text" placeholder=""
                                        class="form-control input-md numberonly">
                                </div>
                                <button type="button" onclick="addSumber()"
                                    class="col-md-2 btn btn-sm btn-primary">Tambah</button>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset class="border rounded-3 p-3">
                        <legend class="float-none w-auto px-3"></legend>

                        <div class="form-group">
                            <label class="col-md-10 control-label" for="tanggal_surat">Tanggal Surat</label>
                            <div class="col-md-10">
                                <input id="tanggal_surat" name="tanggal_surat" type="date" placeholder=""
                                    class="form-control input-md numberonly">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-10 control-label" for="kepala_nama">Nama Kepala</label>
                            <div class="col-md-10">
                                <input id="kepala_nama" name="kepala_nama" type="text" placeholder=""
                                    class="form-control input-md numberonly">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-10 control-label" for="kepala_nip">NIP Kepala</label>
                            <div class="col-md-10">
                                <input id="kepala_nip" name="kepala_nip" type="text" placeholder=""
                                    class="form-control input-md numberonly">
                            </div>
                        </div>
                    </fieldset>
            </div>


            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="sub()" id="">Submit</button>
                <button type="button" class="btn btn-secondary" onclick="($('#imageModal').modal('hide'))"
                    data-dismiss="modal">Close</button>
                <!-- <button type="button" class="btn btn-primary" id="saveImageButton">Save Image</button> -->
            </div>
            </form>
        </div>
    </div>
</div>



<div class="modal " id="modPrintPeta" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Print Peta</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" id="frm-tambah">
                    <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>"
                        value="<?=$this->security->get_csrf_hash();?>" style="display: none">
                    <input type="hidden" id="pgid" name="pgid">
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-xs-12">
                            <div class="card" id="divMap" style="">
                                <!-- <button type="button" onclick="calculateArea()">get</button> -->
                                <div id="maps" style="height: 80vh;width: 100%"></div>
                            </div>
                        </div>
                    </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <!-- <button type="submit" class="btn btn-primary">Save changes</button> -->
                </form>
            </div>
            <div id="div-mataangin" style="width:200px">
                <div class="center-div" style="">
                    <img src="<?=base_url('uploads/print_peta/default/mata_angin.png')?>" alt="">

                </div>
                <div>
                    <center style="display:block;margin-bottom:10px">Skala 1 : <span id="span_skala"></span></center>
                    <center>
                        <div id="scales" style=""></div>
                    </center>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
$(document).ready(function() {

});


var maps
var drawnItems = new L.FeatureGroup();
var drawnPolygons
$('#modPrintPeta').on('shown.bs.modal', function(e) {

    var mapss = document.getElementById('maps');

    if (mapss && mapss.classList.contains('leaflet-container')) {} else {
        maps = L.map('maps').setView([-2.7521401146517785, 116.07226320582281], 5);
        var tombol_upload = L.control({
            position: 'topright'
        });


        osmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');
        var gl = L.mapboxGL({
            style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
        })
        googleHybrid = L.tileLayer('http://{s}.google.com/vt?lyrs=s,h&x={x}&y={y}&z={z}', {
            maxZoom: 20,
            subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
        });
        L.control.layers({
            "OSM": osmLayer.addTo(maps),
            "Google Hybrid": googleHybrid,
        }, {

        }, {
            position: 'topright',
            collapsed: false
        }).addTo(maps);

        var kotak = {
            width: 600,
            height: 500,
            className: 'customSize',
            name: 'Kotak'
        };
        var printer = L.easyPrint({
            tileLayer: osmLayer,
            filename: 'cropmap',
            exportOnly: true,
            sizeModes: [kotak],
            hideControlContainer: true,
        }).addTo(maps);
        var tombol_upload = L.control({
            position: 'topleft'
        });
        tombol_upload.onAdd = function(maps) {
            var buttonDiv = L.DomUtil.create('div', 'custom-button');
            buttonDiv.innerHTML =
                '<button type="" id="capture-button" onclick="manualPrint()" class=""><svg fill="#000000" height="20px" width="16px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64" enable-background="new 0 0 64 64" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="Printer"> <path d="M57.7881012,14.03125H52.5v-8.0625c0-2.2091999-1.7909012-4-4-4h-33c-2.2091999,0-4,1.7908001-4,4v8.0625H6.2119002 C2.7871001,14.03125,0,16.8183498,0,20.2431507V46.513649c0,3.4248009,2.7871001,6.2119026,6.2119002,6.2119026h2.3798995 c0.5527,0,1-0.4472008,1-1c0-0.5527-0.4473-1-1-1H6.2119002C3.8896,50.7255516,2,48.8359489,2,46.513649V20.2431507 c0-2.3223,1.8896-4.2119007,4.2119002-4.2119007h51.5762024C60.1102982,16.03125,62,17.9208508,62,20.2431507V46.513649 c0,2.3223-1.8897018,4.2119026-4.2118988,4.2119026H56c-0.5527992,0-1,0.4473-1,1c0,0.5527992,0.4472008,1,1,1h1.7881012 C61.2128983,52.7255516,64,49.9384499,64,46.513649V20.2431507C64,16.8183498,61.2128983,14.03125,57.7881012,14.03125z M13.5,5.96875c0-1.1027999,0.8971996-2,2-2h33c1.1027985,0,2,0.8972001,2,2v8h-37V5.96875z"></path> <path d="M44,45.0322495H20c-0.5517998,0-0.9990005,0.4472008-0.9990005,0.9990005S19.4482002,47.0302505,20,47.0302505h24 c0.5517006,0,0.9990005-0.4472008,0.9990005-0.9990005S44.5517006,45.0322495,44,45.0322495z"></path> <path d="M44,52.0322495H20c-0.5517998,0-0.9990005,0.4472008-0.9990005,0.9990005S19.4482002,54.0302505,20,54.0302505h24 c0.5517006,0,0.9990005-0.4472008,0.9990005-0.9990005S44.5517006,52.0322495,44,52.0322495z"></path> <circle cx="7.9590998" cy="21.8405495" r="2"></circle> <circle cx="14.2856998" cy="21.8405495" r="2"></circle> <circle cx="20.6121998" cy="21.8405495" r="2"></circle> <path d="M11,62.03125h42v-26H11V62.03125z M13.4036999,38.4349518h37.1925964v21.1925964H13.4036999V38.4349518z"></path> </g> </g></svg></button>';
            return buttonDiv;
        };
        // tombol_upload.addTo(map);
        function manualPrint() {
            printer.printMap('customSize', 'MyManualPrint')
        }

        var lay = '<hr><div class="form-check">' +
            '<input class="form-check-input" onchange="getLokasi()" type="checkbox" value="" id="plokasi">' +
            '<label class="form-check-label"  for="plokasi">' +
            'Lokasi' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="rrtrw()" type="checkbox" value="" id="rtrw">' +
            '<label class="form-check-label"  for="rtrw">' +
            'RTRW' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="kkemampuan_tanah()" type="checkbox" value="" id="kemampuanTanah">' +
            '<label class="form-check-label"  for="kemampuanTanah">' +
            'kemampuan Tanah' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'WP3wt' +
            '</label>' +
            '</div>' +
            '<div id="divWp3wt" style="display:none;padding-left:20px">' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'Penataan Perbatasan' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'Penataan Pulau Pulau Kecil' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'Penataan Wilayah Tertentu' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'Penataan Pesisir' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'Tanah Timbul' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'Sebaran Pulai Kecil (POI)' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'Sebaran Pulai Kecil' +
            '</label>' +
            '</div>' +
            '<div class="form-check">' +
            '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="" id="wp3wt">' +
            '<label class="form-check-label"  for="wp3wt">' +
            'Sebaran Pulai Kecil Terluar' +
            '</label>' +
            '</div>' +
            '</div>'




        $('.leaflet-control-layers').append(lay)




        // maps.on('baselayerchange', function (event) {
        //     ptpBerusaha()
        // });


    }


})

function showPopup() {
    if (geojson_edit != null) {
        var datas = geojson_edit.features[0].properties

        function script1() {
            initCombobox('kd_prov', 19);

            $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
            });
            refreshSelectboot2('kd_kabkot', 20, 'kd_prov', datas.kdppum);
            console.log(1)
        }

        function script2() {
            console.log(2)
            console.log(datas.kdppum)
            console.log(datas.kdpkab)
            setTimeout(function() {
                $('#kd_prov').val(datas.kdppum).selectpicker('refresh')
                $('#kd_kabkot').val(datas.kdpkab).selectpicker('refresh')
            }, 500);
        }
        $.when(script1()).then(script2());

        $('.modal-bodyp').slimScroll({
            height: '250px'
        });

        $('#tahun_data').val(datas.tahun_data).selectpicker('refresh');
        $('#gid').val(datas.gid);
        $('#kdppum').val(datas.kdppum)
        $('#kdpkab').val(datas.kdpkab)
        $('#land_use').val(datas.land_use)
        $('#penguasaan').val(datas.penguasaan)
        $('#tata_ruang').val(datas.tata_ruang)
        $('#kesesuain').val(datas.kesesuain)
        $('#ketersedia').val(datas.ketersedia)
        $('#x').val(datas.x)
        $('#y').val(datas.y)
        $('#luas_ha').val(datas.luas_ha)
        $('#pemohon').val(datas.pemohon)
        $('#jns_ptp').val(datas.jns_ptp)
        $('#nomor_ptp').val(datas.nomor_ptp)
        $('#tgl_ptp').val(datas.tgl_ptp)
        $('#luas_m2').val(datas.luas_m2)
        $('#rencana_ke').val(datas.rencana_ke)
        $('#hasil_ptp').val(datas.hasil_ptp)
        $('#lokasi').val(datas.lokasi)
        $('#nama_perus').val(datas.nama_perus)
        $('#nib').val(datas.nib)
        $('#kode_kbli').val(datas.kode_kbli)
        $('#kbli').val(datas.kbli)
    }
}



function getPolygons(params) {
    var layers = L.PM.Utils.findLayers(maps);
    var polygonsArray = [];
    var polygons = layers
    // Loop through the drawn polygons and convert each one to GeoJSON Polygon
    for (var i = 0; i < polygons.length; i++) {
        var latlngs = layers[i].getLatLngs()[0];
        // var lu=L.GeometryUtil.geodesicArea(layers[i].getLatLngs());
        var coordinates = [];
        // console.log(layers[i].getLatLngs()[0])
        for (var j = 0; j < latlngs.length; j++) {
            coordinates.push(latlngs[j].lng + ' ' + latlngs[j].lat);
        }

        // Push the first point again to close the polygon
        coordinates.push(coordinates[0]);

        polygonsArray.push([coordinates]);
    }
    // Create a GeoJSON MultiPolygon from the individual polygons

    multiPolygon = 'MULTIPOLYGON ' + JSON.stringify(polygonsArray).replace(/\[/g, '(').replace(/\]/g, ')').replace(
        /\"/g, '')
    $('#geom').val(multiPolygon)
}

function getMultiPolygonGeoJSON(polygons) {
    return false
    // Create an array to store the individual polygons
    var polygonsArray = [];

    // Loop through the drawn polygons and convert each one to GeoJSON Polygon
    for (var i = 0; i < polygons.length; i++) {
        var latlngs = polygons[i].latlngs[0];
        var coordinates = [];

        // Convert the latlngs to GeoJSON coordinates
        for (var j = 0; j < latlngs.length; j++) {
            coordinates.push(latlngs[j].lng + ' ' + latlngs[j].lat);
        }

        // Push the first point again to close the polygon
        coordinates.push(coordinates[0]);

        polygonsArray.push([coordinates]);
    }

    // Create a GeoJSON MultiPolygon from the individual polygons
    var geojson = {
        type: 'MultiPolygon',
        coordinates: polygonsArray,
    };

    return polygonsArray;
}

function getPolygon() {
    var layers = L.PM.Utils.findLayers(maps);
    var group = L.featureGroup();
    layers.forEach((layer) => {
        group.addLayer(layer);
        var latt = layer.getLatLngs()[0]
        // var areas = L.GeometryUtil.geodesicArea(latt)
    });
    shapes = group.toGeoJSON();
    $('#geom').val(JSON.stringify(shapes))
    // console.log(shapes)
}

function clearAllLayer() {
    maps.eachLayer(function(layer) {
        if (layer._path != null) {
            layer.remove()
        }
    });
}


var lokasi
var rtrw
var kemampuan_tanah
var wp3wt
var url = 'http://************:4980/geoserver/pgt/wms';

function getLokasi() {
    // alert('okay')
    var filter = ' gid = ' + $('#pgid').val()
    if ($('#plokasi').prop('checked')) {
        console.log('load')
        if (maps.hasLayer(lokasi)) {
            maps.removeLayer(lokasi);
        };
        maps.closePopup()
        lokasi = L.tileLayer.betterWms(url, {
            layers: 'pgt:ptp_kppr_berusaha',
            styles: 'pgt:ptp_kppr_berusaha',
            transparent: true,
            format: 'image/png8',
            cql_filter: filter,
            title: 'lokasi',
            cache: false,
            onEachFeature: function(feature, layer) {
                layer.on({
                    click: closePopup() // Attach the custom click function to each feature
                });
            },
            authkey: gsAuthKey
        });
        lokasi.addTo(maps)
        console.log(lokasi._map._lastCenter.lat)
        console.log(lokasi._map._lastCenter.lng)
        // map.fitBounds([lokasi._map._lastCenter.lat,lokasi._map._lastCenter.lng]);
        // var initialLatLng = L.latLng(lokasi._map._lastCenter.lat,lokasi._map._lastCenter.lng); // Replace with your desired coordinates
        // var initialZoomLevel = 18; // Adjust the zoom level as needed

        // Set the initial center and zoom of the map
        // maps.setView(initialLatLng, initialZoomLevel);
    } else {
        if (maps.hasLayer(lokasi)) {
            maps.removeLayer(lokasi);
        };
    }
}

function kkemampuan_tanah() {
    // alert('okay')
    if ($('#kemampuanTanah').prop('checked')) {
        console.log('load')
        if (maps.hasLayer(kemampuan_tanah)) {
            maps.removeLayer(kemampuan_tanah);
        };
        maps.closePopup()
        kemampuan_tanah = L.tileLayer.betterWms(url, {
            layers: 'pgt:kemampuan_tanah',
            styles: 'pgt:kemampuan_tanah',
            transparent: true,
            format: 'image/png8',
            title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
            cache: false,
            onEachFeature: function(feature, layer) {
                layer.on({
                    click: closePopup() // Attach the custom click function to each feature
                });
            },
            authkey: gsAuthKey
        });
        kemampuan_tanah.addTo(maps)
        kemampuan_tanah.bringToFront();
    } else {
        if (maps.hasLayer(kemampuan_tanah)) {
            maps.removeLayer(kemampuan_tanah);
        };
    }
}



function rrtrw() {
    // alert('okay')
    if ($('#rtrw').prop('checked')) {
        console.log('load')
        if (maps.hasLayer(rtrw)) {
            maps.removeLayer(rtrw);
        };
        maps.closePopup()
        rtrw = L.tileLayer.betterWms(url, {
            layers: 'pgt:rtrw',
            styles: 'pgt:rtrw',
            transparent: true,
            format: 'image/png8',
            title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
            cache: false,
            onEachFeature: function(feature, layer) {
                layer.on({
                    click: closePopup() // Attach the custom click function to each feature
                });
            },
            authkey: gsAuthKey
        });
        rtrw.addTo(maps)
        rtrw.bringToFront();
    } else {
        if (maps.hasLayer(rtrw)) {
            maps.removeLayer(rtrw);
        };
    }
}

function wwp3wt() {
    // alert('okay')
    if ($('#wp3wt').prop('checked')) {
        $('#divWp3wt').css('display', 'block')
    } else {
        $('#divWp3wt').css('display', 'none')
    }
}


function closePopup() {
    maps.closePopup()
}

function numericPopup(v) {
    var id = v.attr('id')
    var inputValue = $('#' + id).val();
    var numericValue = inputValue.replace(/[^0-9]/g, ''); // Remove non-numeric characters
    var formattedValue = numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.'); // Format with commas
    $('#' + id).val(formattedValue); // Set the formatted value to the input
}



function show_modal() {
    console.log('modal show')
    $('#imageModal').modal('show');
    console.log('modal showing')
    $.get("<?= base_url('peta/getLegenda')?>", function(data) {
        data.forEach(function(item) {
            $("#legenda").append('<option value="' + item.id +
                '" data-image="<?php echo base_url('uploads/legenda/')?>' + item.img + '">' + item
                .text + '</option>');
            $("#legenda1").append('<option value="' + item.id +
                '" data-image="<?php echo base_url('uploads/legenda/')?>' + item.img + '">' + item
                .text + '</option>');
        });

        // Trigger an event to refresh the Select2 dropdown
        $("#legenda").trigger("change");
        $(".js-example-basic-multiple").select2()
        initComboboxPemohon('id_ptp', 26)
        // $("#legenda").select2({
        //     templateResult: formatState,
        //     templateSelection: formatState
        // });
        // $('#select2-multiple').select2();
        // $('#imageModal').on('show.bs.modal', function () {
        console.log('modal show')

        var elementToCapture = document.getElementById("map2");
        setTimeout(function() {
            elementToCapture.style.display = "block";
            map2.invalidateSize();
            setTimeout(function() {
                domtoimage.toPng(elementToCapture)
                    .then(function(dataUrl) {
                        var img = document.getElementById("captured-image");
                        img.src = dataUrl;
                    })
                    .catch(function(error) {
                        console.error("Error capturing element:", error);
                    })
                    .finally(function() {
                        // Hide the div again after capture
                        elementToCapture.style.display = "none";
                    });
            }, 2000);
        }, 10);
        var elementToCaptures = document.getElementById("div-mataangin");
        setTimeout(function() {
            elementToCaptures.style.display = "block";
            map2.invalidateSize();
            setTimeout(function() {
                domtoimage.toPng(elementToCaptures)
                    .then(function(dataUrl) {
                        var img = document.getElementById("captured-mataangin");
                        img.src = dataUrl;
                    })
                    .catch(function(error) {
                        console.error("Error capturing element:", error);
                    })
                    .finally(function() {
                        // Hide the div again after capture
                        elementToCaptures.style.display = "none";
                    });
            }, 2000);
        }, 10);
        // });
    }, "json");
    // cropper = new Cropper(document.getElementById('capturedImage'), {
    //             aspectRatio: 19 / 17,
    //             viewMode: 10, // Crop box can cover the whole preview
    //             guides: true,
    //             autoCropArea: 1,
    //             background: false,
    //             movable: false,
    //             zoomable: false,
    //             rotatable: false,
    //             scalable: false,
    //         });
}

function formatState(opt) {
    if (!opt.id) {
        return opt.text.toUpperCase();
    }

    var optimage = $(opt.element).attr('data-image');
    if (!optimage) {
        return opt.text.toUpperCase();
    } else {
        var $opt = $(
            '<span ><img src="' + optimage +
            '" width="60px" /> <span class"opt_select" style="color:black!important">' + opt.text.toUpperCase() +
            '</span></span>'
        );
        return $opt;
    }
};
</script>