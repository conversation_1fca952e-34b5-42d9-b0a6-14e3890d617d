
<link rel="stylesheet" href="<? echo base_url();?>assets/js/pgt-maps/leaflet.css" />
<link rel="stylesheet" href="<? echo base_url();?>assets/js/pgt-maps/L.Control.Layers.Tree.css" />
<link href="<?php echo base_url(); ?>assets/peta/select2/select2.min.css" rel="stylesheet" />
<link href="<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.css" rel="stylesheet" />
<link href="<?php echo base_url(); ?>assets/peta/leaflet-betterscale-master/L.Control.BetterScale.css" rel="stylesheet" />
<link rel="stylesheet" href="<?php //echo base_ url(); ?>assets/peta/leaflet-draw/css/L.Control.ZoomBox.min.css">
<link rel="stylesheet" href="<?php echo base_url(); ?>node_modules/leaflet-control-geocoder/dist/Control.Geocoder.css" />



<script type="text/javascript"
    src="<?=base_url();?>assets/themes/adminity/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>

<script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/leaflet/leaflet.js"></script>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/leaflet-draw/js/L.Control.ZoomBox.min.js"></script>
<script>
    var zoombox = L.control.zoomBox()
</script>


<script src="<? echo base_url();?>assets/js/pgt-maps/leaflet-src.js"></script>
<script src="<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.js"></script>
<script src="<?php echo base_url(); ?>assets/peta/mapbox/leaflet-mapbox-gl.js"></script>

<script src="<? echo base_url();?>node_modules/leaflet-control-geocoder/dist/Control.Geocoder.js"></script>


<script type="text/javascript" src="<?=base_url();?>node_modules/leaflet-easyprint/dist/bundle.custom.js"></script>
<script type="text/javascript" src="<?=base_url();?>node_modules/dom-to-image/src/dom-to-image.js"></script>
<script type="text/javascript" src="<?=base_url();?>node_modules/file-saver/FileSaver.js"></script>


<script src="<? echo base_url();?>assets/js/pgt-maps/L.Control.Layers.Tree.js"></script>

<script src="<? echo base_url();?>assets/js/L.TileLayer.BetterWMS.js"></script>
<script src="<? echo base_url();?>assets/peta/leaflet-betterscale-master/L.Control.BetterScale.js"></script>



<style>
#map{
    /* position: absolute; */
    top: 0;
    left: 0;
    width: 100%;
    /* height: 900px; */
    min-height: 80vh;
}

#map2{
    /* position: absolute; */
    top: 0;
    left: 0;
    width: 100%;
    /* height: 900px; */
    min-height: 600px;
    display: none;
}

.leaflet-grab {
   cursor: auto;
}
.leaflet-dragging .leaflet-grab{
   cursor: move;
}

.leaflet-control-layers-separator{
    display:block!important
}

.opt_select{
  color:black!important;
}

body {
            max-height: 400px; /* Set your desired maximum height */
            overflow-y: auto; /* Enable vertical scrolling if content exceeds the height */
        }
/* Make sure the size of the image fits perfectly into the container */
.center-div {
    display: flex;
    justify-content: center;
    align-items: center;
    /* border: 1px solid black; */
}

.center-div img {
    max-width: 100%; /* Ensure the image doesn't exceed the container width */
    max-height: 100%; /* Ensure the image doesn't exceed the container height */
}
</style>


<input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
<input type="hidden" id="pgid" name="pgid" value="<?=$data['gid']?>">
<input type="hidden" id="imageChange" name="imageChange">
<div class="row">
    <div class="col-sm-12 col-md-12 col-xs-12">
        <div class="card" id="divMap" style="">
 
        <!-- <button type="button" onclick="calculateArea()">get</button> -->
            <div id="map" style="height: 80vh;width: 100%"></div>
            <div style="padding:50px 10% 10px 10%">

                <button class="btn btn-primary" style="float:right;" onclick="callPage('digit_pertimbangan_berusaha')">Kembali</button>
            </div>
            <div class="dt-responsive table-responsive" style="padding:50px 10% 50px 10%">
                <table id="dt-pdf" style="width:100%" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Judul</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>
<div id="div-mataangin" style="width:200px;display:none">
    <div class="center-div" style="">
        <img src="<?=base_url('uploads/print_peta/default/mata_angin.png')?>" alt="">
        
    </div>
    <div>
        <center style="display:block;margin-bottom:10px">Skala 1 : <span id="span_skala"></span></center>
        <center><div id="scales" style=""></div></center>
    </div>
</div> 
<div id="map2"></div>



<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="imageModalLabel"></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      
      <div class="modal-body">
        <form id="uploadForm" class="form-horizontal" >
        <fieldset class="border rounded-3 p-3">
        <legend class="float-none w-auto px-3">Peta</legend>
        <!-- <input type="hidden" name="jns_pdf" id="jns_pdf">  -->
        <img id="capturedImage" src="" alt="Captured Image" class="img-fluid">
        <img id="captured-image" src="" style="width:200px;display:none" alt="Captured Image">
        <img id="captured-mataangin" src="" style="width:170px;;display:none" alt="Captured Image">

        <!-- <button type="button" style="float:right" class="btn btn-primary" onclick="crop()" id="cropButton">Crop</button> -->
      
      </fieldset>
        <fieldset class="border rounded-3 p-3">
        <legend class="float-none w-auto px-3">Isian</legend>
      
        <div class="form-group">
          <label class="col-md-12 control-label" for="id_ptp">Nama Pemohon</label>
          <div class="col-md-12">
            <select id="id_ptp" name="id_ptp"  class="bootstrap-select form-control" data-live-search="true">
              <option value="<?=$data['gid']?>"><?=$data['pemohon']?></option>
            </select>
            
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-12 control-label" for="id_ptp">Jenis Peta</label>
          <div class="col-md-12">
            <select id="jns_pdf" name="jns_pdf"  class="bootstrap-select form-control" data-live-search="true">
              <option value="1">Petunjuk Lokasi</option>
              <option value="2">Penggunaan Tanah</option>
              <option value="3">Penguasaan Tanah</option>
              <option value="4">Rencana tata Ruang</option>
              <option value="5">Kesesuaian Penggunaan Tanah</option>
              <option value="6">Ketersediaan Tanah</option>
            </select>
            
          </div>
        </div>
      
      
        <!-- <div class="form-group">
          <label class="col-md-12 control-label" for="legenda">Legenda</label>  
          <div class="col-md-12">

            <select id="legenda" name="legenda"  class="bootstrap-select form-control" data-live-search="true">
            </select>

          
          </div>
        </div> -->
        <div class="form-group">
          <label class="col-md-12 control-label" for="ditinjau">Ditinjau Oleh</label>
          <div class="col-md-12">
          <input id="ditinjau" name="ditinjau"   type="text" placeholder="" class="form-control input-md numberonly" >

          </div>
        </div>
        <div class="form-group">
          <label class="col-md-12 control-label" for="tanggal">Tanggal</label>
          <div class="col-md-12">
          <input id="tanggal" name="tanggal"   type="date" placeholder="" class="form-control input-md numberonly" >

          </div>
        </div>
        <div class="form-group">
          <label class="col-md-12 control-label" for="digambar">Digambar Oleh</label>
          <div class="col-md-12">
          <input id="digambar" name="digambar"   type="text" placeholder="" class="form-control input-md numberonly" >

          </div>
        </div>
        <div class="form-group">
          <label class="col-md-12 control-label" for="diperiksa">Diperiksa Oleh</label>
          <div class="col-md-12">
          <input id="diperiksa" name="diperiksa"   type="text" placeholder="" class="form-control input-md numberonly" >

          </div>
        </div>
      </fieldset>

      <fieldset class="border rounded-3 p-3">
        <legend class="float-none w-auto px-3">Sumber Peta</legend>
    
        <div class="form-group div_sumber_peta">
          <label class="col-md-10 control-label" for="sumber_peta">Sumber Peta</label>
          <div class="row">

            <div class="col-md-10">
              <input id="sumber_peta" name="sumber_peta[]"   type="text" placeholder="" class="form-control input-md numberonly" >
            </div>
            <button type="button" onclick="addSumber()" class="col-md-2 btn btn-sm btn-primary">Tambah</button>
          </div>
        </div>
      </fieldset>
      <fieldset class="border rounded-3 p-3">
        <legend class="float-none w-auto px-3"></legend>
    
        <div class="form-group">
          <label class="col-md-10 control-label" for="tanggal_surat">Tanggal Surat</label>
            <div class="col-md-10">
              <input id="tanggal_surat" name="tanggal_surat"   type="date" placeholder="" class="form-control input-md numberonly" >
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-10 control-label" for="kepala_nama">Nama Kepala</label>
            <div class="col-md-10">
              <input id="kepala_nama" name="kepala_nama"   type="text" placeholder="" class="form-control input-md numberonly" >
          </div>
        </div>
        <div class="form-group">
          <label class="col-md-10 control-label" for="kepala_nip">NIP Kepala</label>
            <div class="col-md-10">
              <input id="kepala_nip" name="kepala_nip"   type="text" placeholder="" class="form-control input-md numberonly" >
          </div>
        </div>
      </fieldset>
    </div>

      
      <div class="modal-footer">
        <button type="button"  class="btn btn-success" onclick="sub()" id="">Submit</button>
        <button type="button" class="btn btn-secondary" onclick="($('#imageModal').modal('hide'))" data-dismiss="modal">Close</button>
        <!-- <button type="button" class="btn btn-primary" id="saveImageButton">Save Image</button> -->
      </div>
    </form>
    </div>
  </div>
</div>








<script>
      var map
      var map2
    var drawnItems = new L.FeatureGroup();
    var drawnPolygons
    var marker
    var lokasi = L.tileLayer.betterWms()
    var rtrw = L.tileLayer.betterWms()
    var kemampuan_tanah = L.tileLayer.betterWms()
    var wp3wt = L.tileLayer.betterWms()
    $(document).ready(function () {
        
        
            listingPdf()
    

            map = L.map('map').setView([-2.7521401146517785, 116.07226320582281], 5);
            var tombol_upload = L.control({ position: 'topright' });
            // lokasi.addTo(map)
            map2 = L.map('map2', {
                    zoom: 5,
                    center: L.latLng([-1.736, 119.246]),
                    attributionControl: false
                }),
                osmLayers = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');
            map2.addLayer(osmLayers);

            osmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');

            googleHybrid = L.tileLayer('http://{s}.google.com/vt?lyrs=s,h&x={x}&y={y}&z={z}', {
                maxZoom: 20,
                subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
            });
             L.control.layers({
                "OSM": osmLayer.addTo(map),
                "Google Hybrid": googleHybrid,
            }, {
                
            }, {
                position: 'topright',
                collapsed: false
            }).addTo(map);

            var kotak = {
                width: 600,
                height: 500,
                className: 'customSize',
                name: 'Kotak'
            };
            var printer = L.easyPrint({
                filename: 'cropmap',
                exportOnly: true,
                sizeModes: [ kotak],
                hideControlContainer: true,
            }).addTo(map);
            var tombol_upload = L.control({ position: 'topleft' });
            tombol_upload.onAdd = function(map) {
                var buttonDiv = L.DomUtil.create('div', 'custom-button');
                buttonDiv.innerHTML = '<button type="" id="capture-button" onclick="manualPrint()" class=""><svg fill="#000000" height="20px" width="16px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64" enable-background="new 0 0 64 64" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="Printer"> <path d="M57.7881012,14.03125H52.5v-8.0625c0-2.2091999-1.7909012-4-4-4h-33c-2.2091999,0-4,1.7908001-4,4v8.0625H6.2119002 C2.7871001,14.03125,0,16.8183498,0,20.2431507V46.513649c0,3.4248009,2.7871001,6.2119026,6.2119002,6.2119026h2.3798995 c0.5527,0,1-0.4472008,1-1c0-0.5527-0.4473-1-1-1H6.2119002C3.8896,50.7255516,2,48.8359489,2,46.513649V20.2431507 c0-2.3223,1.8896-4.2119007,4.2119002-4.2119007h51.5762024C60.1102982,16.03125,62,17.9208508,62,20.2431507V46.513649 c0,2.3223-1.8897018,4.2119026-4.2118988,4.2119026H56c-0.5527992,0-1,0.4473-1,1c0,0.5527992,0.4472008,1,1,1h1.7881012 C61.2128983,52.7255516,64,49.9384499,64,46.513649V20.2431507C64,16.8183498,61.2128983,14.03125,57.7881012,14.03125z M13.5,5.96875c0-1.1027999,0.8971996-2,2-2h33c1.1027985,0,2,0.8972001,2,2v8h-37V5.96875z"></path> <path d="M44,45.0322495H20c-0.5517998,0-0.9990005,0.4472008-0.9990005,0.9990005S19.4482002,47.0302505,20,47.0302505h24 c0.5517006,0,0.9990005-0.4472008,0.9990005-0.9990005S44.5517006,45.0322495,44,45.0322495z"></path> <path d="M44,52.0322495H20c-0.5517998,0-0.9990005,0.4472008-0.9990005,0.9990005S19.4482002,54.0302505,20,54.0302505h24 c0.5517006,0,0.9990005-0.4472008,0.9990005-0.9990005S44.5517006,52.0322495,44,52.0322495z"></path> <circle cx="7.9590998" cy="21.8405495" r="2"></circle> <circle cx="14.2856998" cy="21.8405495" r="2"></circle> <circle cx="20.6121998" cy="21.8405495" r="2"></circle> <path d="M11,62.03125h42v-26H11V62.03125z M13.4036999,38.4349518h37.1925964v21.1925964H13.4036999V38.4349518z"></path> </g> </g></svg></button>';
                return buttonDiv;
            };
            L.control.betterscale({
                imperial: false, // Disable the imperial system
                metric: true,     // Enable the metric system (meters)
                }).addTo(map);
            // tombol_upload.addTo(map);
            function manualPrint () {
                printer.printMap('customSize', 'MyManualPrint')
            }

            var lay = '<hr><div class="form-check">'+
                        '<input class="form-check-input inp" onchange="getLokasi()" type="checkbox" value="" id="plokasi">'+
                        '<label class="form-check-label"  for="plokasi">'+
                            'Lokasi'+
                        '</label>'+
                       '</div>'+
                       '<div class="form-check">'+
                            '<input class="form-check-input inp" onchange="rrtrw()" type="checkbox" value="5" id="rtrw">'+
                            '<label class="form-check-label"  for="rtrw">'+
                                'RTRW'+
                            '</label>'+
                        '</div>'+
                        '<div class="form-check">'+
                            '<input class="form-check-input inp" onchange="kkemampuan_tanah()" type="checkbox" value="4" id="kemampuanTanah">'+
                            '<label class="form-check-label"  for="kemampuanTanah">'+
                                'kemampuan Tanah'+
                            '</label>'+
                        '</div>'+
                        '<div class="form-check">'+
                            '<input class="form-check-input inp" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                            '<label class="form-check-label"  for="wp3wt">'+
                                'WP3wt'+
                            '</label>'+
                        '</div>'+
                        '<div id="divWp3wt" style="display:none;padding-left:20px">'+
                            '<div class="form-check">'+
                                '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                                '<label class="form-check-label"  for="wp3wt">'+
                                    'Penataan Perbatasan'+
                                '</label>'+
                            '</div>'+
                            '<div class="form-check">'+
                                '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                                '<label class="form-check-label"  for="wp3wt">'+
                                    'Penataan Pulau Pulau Kecil'+
                                '</label>'+
                            '</div>'+
                            '<div class="form-check">'+
                                '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                                '<label class="form-check-label"  for="wp3wt">'+
                                    'Penataan Wilayah Tertentu'+
                                '</label>'+
                            '</div>'+
                            '<div class="form-check">'+
                                '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                                '<label class="form-check-label"  for="wp3wt">'+
                                    'Penataan Pesisir'+
                                '</label>'+
                            '</div>'+
                            '<div class="form-check">'+
                                '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                                '<label class="form-check-label"  for="wp3wt">'+
                                    'Tanah Timbul'+
                                '</label>'+
                            '</div>'+
                            '<div class="form-check">'+
                                '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                                '<label class="form-check-label"  for="wp3wt">'+
                                    'Sebaran Pulai Kecil (POI)'+
                                '</label>'+
                            '</div>'+
                            '<div class="form-check">'+
                                '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                                '<label class="form-check-label"  for="wp3wt">'+
                                    'Sebaran Pulai Kecil'+
                                '</label>'+
                            '</div>'+
                            '<div class="form-check">'+
                                '<input class="form-check-input" onchange="wwp3wt()" type="checkbox" value="3" id="wp3wt">'+
                                '<label class="form-check-label"  for="wp3wt">'+
                                    'Sebaran Pulai Kecil Terluar'+
                                '</label>'+
                            '</div>'+
                        '</div>'
                        



            $('.leaflet-control-layers').append(lay) 

            // $('#plokasi').click()
            $('input[type="checkbox"]').change(function() {
            if (this.checked) {
                var checkedCheckboxId = $(this).attr('id');
                console.log('Checked checkbox ID:', checkedCheckboxId);
            }
            });
            
            // map.on('baselayerchange', function (event) {
            //     ptpBerusaha()
            // });


            
    map.on('move', function (e) {
        var zoomLevel = map.getZoom();
        var center = map.getCenter();
        var newLatLng = L.latLng(center.lat, center.lng); // Replace with the desired latitude and longitude
        var newZoomLevel = 8; // Replace with the desired zoom level

        map2.setView(newLatLng, newZoomLevel);
             

        var customIcon = L.icon({
            iconUrl: '<?=base_url()?>uploads/print_peta/default/red_location.jpg', // Replace with the path to your custom icon image
            iconSize: [32, 32], // Width and height of the icon
            iconAnchor: [16, 16], // Position of the icon's anchor point (center by default)
        });
            if (marker) {
                map2.removeLayer(marker);
            }
            // marker = L.marker([center.lat, center.lng]).addTo(map2); // Latitude and longitude of the marker
             marker = L.marker([center.lat, center.lng], { icon: customIcon }).addTo(map2);
        
             var scal = $('.leaflet-control-better-scale.leaflet-control').html()
             scal = scal.replace('leaflet-control-better-scale-label leaflet-control-better-scale-first-number','leaflet-control-better-scale-label leaflet-control-better-scale-first-number num-first')
            scal = scal.replace('leaflet-control-better-scale-label leaflet-control-better-scale-second-number','leaflet-control-better-scale-label leaflet-control-better-scale-second-number num-second')

             $('#scales').html(scal)
             var scalStr = $('.leaflet-control-better-scale-second-number').html()             
             if (scalStr.includes('km')) {
                 console.log(parseFloat($('.num-second').html())*1000)
                 var first = parseFloat($('.num-first').html())*1000
                 var second = parseFloat($('.num-second').html().replace('km',''))*1000
                 $('.num-first').html(number_format(first))
                 $('.num-second').html(number_format(second)+'m')
                 $('#span_skala').html(number_format(second))
            }else{
                var first = parseFloat($('.num-first').html())
                 var second = parseFloat($('.num-second').html().replace('m',''))
                 $('.num-first').html(number_format(first))
                 $('.num-second').html(number_format(second)+'m')
                $('#span_skala').html(second)
            }
            
            var width = $('.leaflet-control-better-scale-label-div').width()
            $('.leaflet-control-better-scale-ruler').width(width+80)
            $('.leaflet-control-better-scale-label-div').width(width+40)
            //  console.log(second)
            // Update a DOM element with the zoom level and coordinates
            // document.getElementById('zoom').innerHTML = 'Zoom Level: ' + zoomLevel;
            // document.getElementById('lat').innerHTML = 'Latitude: ' + center.lat;
            // document.getElementById('lng').innerHTML = 'Longitude: ' + center.lng;
    });

    });

    function number_format(meters) {
        
        if (meters > 2000) {
            const powerOfTen = Math.pow(10, Math.floor(Math.log10(meters)));
            meters =   Math.round(meters / powerOfTen) * powerOfTen;
            return (meters / 1000).toFixed(3);
            
        }else if (meters < 2000 && meters > 1000) {
            return (meters / 1000).toFixed(3);
            
        }else{
            return meters;

        }
    }

            
        

    function showPopup() {
        if (geojson_edit != null) {
            var datas = geojson_edit.features[0].properties
            function script1() {
                initCombobox('kd_prov', 19);
                
                $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                    refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
                });
                refreshSelectboot2('kd_kabkot', 20, 'kd_prov', datas.kdppum);
                console.log(1)
            }
            function script2() {
                console.log(2)
                console.log(datas.kdppum)
                console.log(datas.kdpkab)
                setTimeout(function() {
                    $('#kd_prov').val(datas.kdppum).selectpicker('refresh') 
                    $('#kd_kabkot').val(datas.kdpkab).selectpicker('refresh') 
                }, 500); 
            }
            $.when(script1()).then(script2());

            $('.modal-bodyp').slimScroll({
                height: '250px'
            });

            $('#tahun_data').val(datas.tahun_data).selectpicker('refresh'); 
            $('#gid').val(datas.gid);
            $('#kdppum').val(datas.kdppum) 
            $('#kdpkab').val(datas.kdpkab)  
            $('#land_use').val(datas.land_use)
            $('#penguasaan').val(datas.penguasaan)
            $('#tata_ruang').val(datas.tata_ruang)
            $('#kesesuain').val(datas.kesesuain)
            $('#ketersedia').val(datas.ketersedia)
            $('#x').val(datas.x)
            $('#y').val(datas.y)
            $('#luas_ha').val(datas.luas_ha)
            $('#pemohon').val(datas.pemohon)
            $('#jns_ptp').val(datas.jns_ptp)
            $('#nomor_ptp').val(datas.nomor_ptp)
            $('#tgl_ptp').val(datas.tgl_ptp)
            $('#luas_m2').val(datas.luas_m2)
            $('#rencana_ke').val(datas.rencana_ke)
            $('#hasil_ptp').val(datas.hasil_ptp)
            $('#lokasi').val(datas.lokasi)
            $('#nama_perus').val(datas.nama_perus)
            $('#nib').val(datas.nib)
            $('#kode_kbli').val(datas.kode_kbli)
            $('#kbli').val(datas.kbli)
        }
    }

  

    function getPolygons(params) {
        var layers = L.PM.Utils.findLayers(map);
        var polygonsArray = [];
        var polygons = layers
            // Loop through the drawn polygons and convert each one to GeoJSON Polygon
        for (var i = 0; i < polygons.length; i++) {
            var latlngs = layers[i].getLatLngs()[0];
            // var lu=L.GeometryUtil.geodesicArea(layers[i].getLatLngs());
            var coordinates = [];
            // console.log(layers[i].getLatLngs()[0])
            for (var j = 0; j < latlngs.length; j++) {
                coordinates.push(latlngs[j].lng+' '+latlngs[j].lat);
            }

            // Push the first point again to close the polygon
            coordinates.push(coordinates[0]);

            polygonsArray.push([coordinates]);
        }
        // Create a GeoJSON MultiPolygon from the individual polygons
        
        multiPolygon = 'MULTIPOLYGON '+JSON.stringify(polygonsArray).replace(/\[/g, '(').replace(/\]/g, ')').replace(/\"/g, '')
        $('#geom').val(multiPolygon)
    }

    function getMultiPolygonGeoJSON(polygons) {
        return false
    // Create an array to store the individual polygons
    var polygonsArray = [];

    // Loop through the drawn polygons and convert each one to GeoJSON Polygon
    for (var i = 0; i < polygons.length; i++) {
        var latlngs = polygons[i].latlngs[0];
        var coordinates = [];

        // Convert the latlngs to GeoJSON coordinates
        for (var j = 0; j < latlngs.length; j++) {
            coordinates.push(latlngs[j].lng+' '+latlngs[j].lat);
        }

        // Push the first point again to close the polygon
        coordinates.push(coordinates[0]);

        polygonsArray.push([coordinates]);
    }

    // Create a GeoJSON MultiPolygon from the individual polygons
    var geojson = {
        type: 'MultiPolygon',
        coordinates: polygonsArray,
    };

    return polygonsArray;
    }

    function getPolygon() {
        var layers = L.PM.Utils.findLayers(map);
        var group = L.featureGroup();
        layers.forEach((layer)=>{
            group.addLayer(layer);
            var latt = layer.getLatLngs()[0]
            // var areas = L.GeometryUtil.geodesicArea(latt)
        });
        shapes = group.toGeoJSON();
        $('#geom').val(JSON.stringify(shapes))
        // console.log(shapes)
    }

    function clearAllLayer() {
        map.eachLayer(function(layer){
            if (layer._path != null) {
                layer.remove()
            }
        });
    }

    
    
    var url = 'http://************:4980/geoserver/pgt/wms';

    function getLokasi() {
        // alert('okay')
        var filter = ' gid = '+$('#pgid').val()
        if ($('#plokasi').prop('checked')) {
            console.log('load')
            if (map.hasLayer(lokasi)) {
                map.removeLayer(lokasi);
            };
            map.closePopup()
            lokasi = L.tileLayer.betterWms(url, {
                layers: 'pgt:ptp_kppr_berusaha',
                styles: 'pgt:ptp_kppr_berusaha',
                transparent: true,
                format: 'image/png8',
                cql_filter: filter,
                title: 'lokasi',
                cache: false,
                onEachFeature: function(feature, layer) {
                    layer.on({
                        click:  closePopup()// Attach the custom click function to each feature
                    });
                }
            });
            lokasi.addTo(map)
            // console.log(lokasi._map._lastCenter.lat)
            // console.log(lokasi._map._lastCenter.lng)
            // map.fitBounds([lokasi._map._lastCenter.lat,lokasi._map._lastCenter.lng]);
            // var initialLatLng = L.latLng(lokasi._map._lastCenter.lat,lokasi._map._lastCenter.lng); // Replace with your desired coordinates
                // var initialZoomLevel = 18; // Adjust the zoom level as needed

                // Set the initial center and zoom of the map
                // map.setView(initialLatLng, initialZoomLevel);
        }else{
            if (map.hasLayer(lokasi)) {
                map.removeLayer(lokasi);
            };
        }
    }
    
    function kkemampuan_tanah() {
        // alert('okay')
        if ($('#kemampuanTanah').prop('checked')) {
            console.log('load')
            if (map.hasLayer(kemampuan_tanah)) {
                map.removeLayer(kemampuan_tanah);
            };
            map.closePopup()
            kemampuan_tanah = L.tileLayer.betterWms(url, {
                layers: 'pgt:kemampuan_tanah',
                styles: 'pgt:kemampuan_tanah',
                transparent: true,
                format: 'image/png8',
                title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
                cache: false,
                onEachFeature: function(feature, layer) {
                    layer.on({
                        click:  closePopup()// Attach the custom click function to each feature
                    });
                }
            });
            kemampuan_tanah.addTo(map)
            kemampuan_tanah.bringToFront();
        }else{
            if (map.hasLayer(kemampuan_tanah)) {
                map.removeLayer(kemampuan_tanah);
            };
        }
    }

    
    
    function rrtrw() {
        // alert('okay')
        if ($('#rtrw').prop('checked')) {
            if (map.hasLayer(rtrw)) {
                map.removeLayer(rtrw);
            };
            map.closePopup()
            rtrw = L.tileLayer.betterWms(url, {
                layers: 'pgt:rtrw',
                styles: 'pgt:rtrw',
                transparent: true,
                format: 'image/png8',
                title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
                cache: false,
                onEachFeature: function(feature, layer) {
                    layer.on({
                        click:  closePopup()// Attach the custom click function to each feature
                    });
                }
            });
            rtrw.addTo(map)
            rtrw.bringToFront();
        }else{
            if (map.hasLayer(rtrw)) {
                map.removeLayer(rtrw);
            };
        }
    }

    function wwp3wt() {
        // alert('okay')
        if ($('#wp3wt').prop('checked')) {
            $('#divWp3wt').css('display','block')
        }else{
            $('#divWp3wt').css('display','none')
        }
    }


    function closePopup() {
        map.closePopup()
    }
    function numericPopup(v) {
        var id = v.attr('id')
        var inputValue = $('#'+id).val();
            var numericValue = inputValue.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            var formattedValue = numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.'); // Format with commas
            $('#'+id).val(formattedValue); // Set the formatted value to the input
    }
   


    function show_modal() {
        $('#imageModal').modal('show');
        console.log('modal showing')
        $.get("<?= base_url('peta/getLegenda')?>", function(data) {
            data.forEach(function (item) {
                $("#legenda").append('<option value="'+item.id+'" data-image="<?php echo base_url('uploads/legenda/')?>'+item.img+'">'+item.text+'</option>');
                $("#legenda1").append('<option value="'+item.id+'" data-image="<?php echo base_url('uploads/legenda/')?>'+item.img+'">'+item.text+'</option>');
            });
            
            $("#legenda").trigger("change");

                console.log('modal show')

                var elementToCapture = document.getElementById("map2");
                    setTimeout(function() {
                        elementToCapture.style.display = "block";
                        map2.invalidateSize();
                        setTimeout(function() {
                            domtoimage.toPng(elementToCapture)
                                .then(function (dataUrl) {
                                    console.log('cap map2')
                                    var img = document.getElementById("captured-image");
                                    img.src = dataUrl;
                                })
                                .catch(function (error) {
                                    console.error("Error capturing element:", error);
                                })
                                .finally(function () {
                                    // Hide the div again after capture
                                    elementToCapture.style.display = "none";
                                });
                        }, 2000);
                    }, 10);           
                    var elementToCaptures = document.getElementById("div-mataangin");
                    setTimeout(function() {
                        elementToCaptures.style.display = "block";
                        map2.invalidateSize();
                        setTimeout(function() {
                            domtoimage.toPng(elementToCaptures)
                            .then(function (dataUrl) {
                                console.log('cap mata')
                                var img = document.getElementById("captured-mataangin");
                                img.src = dataUrl;
                            })
                            .catch(function (error) {
                                console.error("Error capturing element:", error);
                            })
                            .finally(function () {
                                // Hide the div again after capture
                                elementToCaptures.style.display = "none";
                            });
                        }, 2000);
                    }, 10);           
                    // });
                }, "json");
        // cropper = new Cropper(document.getElementById('capturedImage'), {
        //             aspectRatio: 19 / 17,
        //             viewMode: 10, // Crop box can cover the whole preview
        //             guides: true,
        //             autoCropArea: 1,
        //             background: false,
        //             movable: false,
        //             zoomable: false,
        //             rotatable: false,
        //             scalable: false,
        //         });
    }

    function formatState (opt) {
        if (!opt.id) {
            return opt.text.toUpperCase();
        } 

        var optimage = $(opt.element).attr('data-image'); 
        if(!optimage){
        return opt.text.toUpperCase();
        } else {                    
            var $opt = $(
            '<span ><img src="' + optimage + '" width="60px" /> <span class"opt_select" style="color:black!important">' + opt.text.toUpperCase() + '</span></span>'
            );
            return $opt;
        }
    };


    
function sub() {
  var formData = new FormData($('#uploadForm')[0]);
          var imgElement = document.getElementById('capturedImage');
          var imageUrl = imgElement.src;
          var img_zoom 
          fetch(imageUrl)
            .then(response => response.blob())
            .then(blob => {
              console.log(blob)
              formData.append('zoom', blob, 'image_zoom.png');
              var imgElement = document.getElementById('captured-image');
              var imageUrl = imgElement.src;
              var img_lokasi 
              fetch(imageUrl)
              .then(response => response.blob())
                .then(blob => {
                  formData.append('lokasi', blob, 'image_lokasi.png');
                    var imgElement = document.getElementById('captured-mataangin');
                    var imageUrl = imgElement.src;
                    var img_lokasi 
                    fetch(imageUrl)
                    .then(response => response.blob())
                    .then(blob => {
                      formData.append('mata_angin', blob, 'mata_angin.png');
                      formData.append("<?php echo $this->security->get_csrf_token_name(); ?>", "<?php echo $this->security->get_csrf_hash(); ?>");
              
                
                          $.ajax({
                              url: '<?=base_url()?>'+'peta2/uploadPeta',
                              type: "post",
                              data: formData,
                              processData: false,
                              contentType: false,
                              cache: false,
                              async: false,
                              success: function (data) {
                                  console.log(data)
                                //   var urlToOpen = "<?php echo base_url(); ?>peta2/export_pdf_peta/"+data;
                                //   window.open(urlToOpen, '_blank');
                                  
                              },error: function (jqXHR, exception) {
                                  
                              }
                          });
                        })
                    .catch(error => {
                      console.error('Error fetching and converting image to Blob:', error);
                    });
                  })
                .catch(error => {
                  console.error('Error fetching and converting image to Blob:', error);
                });
              })
            .catch(error => {
              console.error('Error fetching and converting image to Blob:', error);
            });

  
}


var no_sumber = 1
function addSumber() {
  
  var str = '<div id="sumber'+no_sumber+'" class="row"><div  class="col-md-10">'+
    '<input id="sumber_peta'+no_sumber+'" name="sumber_peta[]"   type="text" placeholder="" class="form-control input-md numberonly" >'+
  '</div>'+
  '<button type="button" onclick="delSumber(\'sumber'+no_sumber+'\')" class="col-md-2 btn btn-sm btn-danger">Hapus</button></div>'
  $('.div_sumber_peta').append(str)
}

function delSumber(v) {
  $('#'+v).remove()
}
function listingPdf() {
       id = $('#pgid').val()
        table = $('#dt-pdf').DataTable({
            "draw": 0,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "bDestroy": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>digit_pertimbangan_berusaha/ssp_paket_pdf/"+id,"data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
            }},
            "columnDefs": [
                {
                    "aTargets": [0],
                    "mRender": function (data, type, full,meta) {
                        // console.log(full)
                        return meta.row + meta.settings._iDisplayStart + 1;
                    }
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
                },
                {
                    "aTargets": [2],
                    "mRender": function (data, type, full) {
                        var html_button = [
                            "<img role='button' title='Print Peta' onclick=showPdf('"+full[0]+"') style='height:30px' src='<?=base_url("uploads/icon/print.svg")?>' >",
                             ].join("\n");
                        return html_button;
                    }
                }
                
            ],
            
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }

    function showPdf(v) {
        var urlToOpen = "<?php echo base_url(); ?>peta2/export_pdf_peta/"+v;
        window.open(urlToOpen, '_blank');
                                  
    }

    function callPage(pageRefInput) {
	
        var data_post = {"<?php echo $this->security->get_csrf_token_name(); ?>" : "<?php echo $this->security->get_csrf_hash(); ?>", "uri": pageRefInput};
        
        $.ajax({
            dataType: "text",
            type: "POST",
            data: data_post,
            url: pageRefInput,
            success: function (data) {
                $('.content').empty();
                $('.content').html(data);
            },
            error: function (xhr, ajaxOptions, thrownError) {

            }
        });

    }
</script>

