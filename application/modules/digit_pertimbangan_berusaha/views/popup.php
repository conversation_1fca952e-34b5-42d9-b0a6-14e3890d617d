<style>
.form-group {
    width: 90% !important
}

.modal-footer {
    width: 90%;
}
</style>
<div>
    <div class="modal-bodyp ">
        <form class="form-horizontal" id="frm-tambah">
            <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>"
                value="<?=$this->security->get_csrf_hash();?>" style="display: none">
            <input type="hidden" name="wadmpr" id="wadmpr">
            <input type="hidden" name="wadmkk" id="wadmkk">
            <input type="hidden" name="gid" id="gid">

            <div id="divRow" class="row">
                <div class="form-group ">
                    <label class="col-md-12 control-label" for="kd_prov">Provinsi</label>
                    <div class="col-md-12">
                        <select id="kd_prov" name="kd_prov" onchange="" class="bootstrap-select form-control"
                            data-live-search="true">
                        </select>
                    </div>
                </div>
                <div class="form-group ">
                    <label class="col-md-12 control-label" for="kd_kabkot">Kab/Kota</label>
                    <div class="col-md-12">
                        <select id="kd_kabkot" name="kd_kabkot" class="bootstrap-select form-control"
                            data-live-search="true">
                        </select>
                    </div>
                </div>
                <div class="form-group ">
                    <label class="col-md-12 control-label" for="thnkgt">Tahun Data</label>
                    <div class="col-md-12">
                        <select id="thnkgt" name="thnkgt" class="bootstrap-select form-control" data-live-search="true">
                            <?php 
                            $now = date('Y');
                            for ($i=$now; $i >= 1990 ; $i--) { 
                                echo '<option value="'.$i.'">'.$i.'</option>';
                            }
                        ?>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="lokasi">Lokasi</label>
                    <div class="col-md-12">
                        <input id="lokasi" name="lokasi" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="kuasaptp">Pemohon</label>
                    <div class="col-md-12">
                        <input id="kuasaptp" name="kuasaptp" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="subjekptp">Nama Perusahaan</label>
                    <div class="col-md-12">
                        <input id="subjekptp" name="subjekptp" type="text" placeholder=""
                            class="form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="rcnkgt">Rencana Kegiatan</label>
                    <div class="col-md-12">
                        <input id="rcnkgt" name="rcnkgt" type="text" placeholder=""
                            class="form-control input-md numeric">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="niboss">NIB</label>
                    <div class="col-md-12">
                        <input id="niboss" name="niboss" type="text" placeholder=""
                            class="form-control input-md numeric">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="kdkbli">Kode KBLI</label>
                    <div class="col-md-12">
                        <input id="kdkbli" name="kdkbli" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="kbli">KBLI</label>
                    <div class="col-md-12">
                        <input id="kbli" name="kbli" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="jptp">Jenis PTP</label>
                    <div class="col-md-12">
                        <input id="jptp" name="jptp" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="noptp">Nomor PTP</label>
                    <div class="col-md-12">
                        <input id="noptp" name="noptp" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="tglptp">Tanggal PTP</label>
                    <div class="col-md-12">
                        <input id="tglptp" name="tglptp" required type="date" placeholder=""
                            class="form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="hslptp">Hasil PTP</label>
                    <div class="col-md-12">
                        <!-- <input id="hslptp" name="hslptp" type="text" placeholder="" class="form-control input-md "> -->
                        <select id="hslptp" name="hslptp" onchange="" class="bootstrap-select form-control"
                            data-live-search="true">
                            <option value="">Tidak Ada hasil</option>
                            <option value="Sesuai">Sesuai</option>
                            <option value="Tidak Sesuai">Tidak Sesuai</option>
                            <option value="Sesuai Bersyarat">Sesuai Bersyarat</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="luasha">Luas ha</label>
                    <div class="col-md-12">
                        <input id="luasha" name="luasha" oninput="numericPopup($(this))" type="text" placeholder=""
                            class="numeric form-control input-md ">
                    </div>
                </div>
                <div class="form-group">
                    <m class="col-md-12 control-label" for="luasm2">luas m<sup>2</sup></label>
                        <div class="col-md-12">
                            <input id="luasm2" name="luasm2" oninput="numericPopup($(this))" type="text" placeholder=""
                                class="numeric form-control input-md ">
                        </div>
                </div>
                <div class="form-group">
                    <label class="col-md-12 control-label" for="">Foto</label>
                    <div class="row" id="divFoto">
                        <div class="col-md-10">
                            <input id="foto" name="foto" type="file" placeholder="" class=" form-control input-md ">
                        </div>
                        <div class="col-md-2">
                            <button type="button" style="height:100%" onclick="addFotos()" class="btn btn-success"><i
                                    class="fa fa-plus fa-2xl" aria-hidden="true"></i></button>
                        </div>
                    </div>
                </div>

                <div class="form-group ">
                    <label class="col-md-12 control-label" for="luas"></label>
                    <div class="col-md-12">
                        <input id="geom" name="geom" type="hidden" placeholder=""
                            class="form-control input-md float-number">

                    </div>
                </div>
            </div>

        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="closePopup()">Close</button>
        <button type="submit" class="btn btn-primary">Save changes</button>
    </div>
</div>

<script>


</script>