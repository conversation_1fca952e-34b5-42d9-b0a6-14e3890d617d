<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Report_rtrw extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $satker = $this->session->users['kd_bujt'];
        $title = "Report RTRW";

        $js_file = $this->load->view('report_rtrw/js_file', '', true);
        $modal_tambah = $this->load->view('report_rtrw/modal_tambah', '', true);
        $modal_edit = $this->load->view('report_rtrw/modal_edit', '', true);
        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "title" => $title,
            "jv_script" => $js_file
        );
        
        $this->load->view('index', $data);
    }
    public function getKab($kdProv,$tahun,$layer=null)
    {
        $layer='spatial.v_d_pgtl_rtrw'; 
        $this->db->where('kdppum', $kdProv);
        if($tahun != 'null'){
            $this->db->where('tahun_data', $tahun);
        }elseif($tahun == 'null'){
            $this->db->where('tahun_data is null' );
        }
        $data = $this->db->get($layer.'_drill')->result();
            
        $str = '';
        $no = 0;
        foreach ($data as $key => $value) {
            if($no < 4){
                $str .= '<td>- '.$value->wadmkk.'<td>';
            }else{
                $str .= '<td>- '.$value->wadmkk.'<td></tr><tr><td style="width:100px"></td>';
                $no=-1;
            }
            $no++;
        }
        $str = substr($str, 0, -2);
        $html = '<table cellpadding="5" cellspacing="0" border="0" style="width:100%;padding-left:50px;table-layout: fixed;">
        <tr><td style="width:100px">List Kabupaten:</td>'.$str.'</tr></table>';
            
        echo json_encode(['html'=>$html]);
    }

    public function save_form() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $data_detail = [ 
            "kd_prov" => $this->input->post("formData")["kd_prov"], 
            "kd_status" => $this->input->post("formData")["kd_status"], 
            "luas" => $this->input->post("formData")["luas"], 
            "tahun_data" => $this->input->post("formData")["tahun_data"]
        ];


        // $this->db->set('created_at', 'now()', FALSE);
        $this->db->set('waktu', 'now()', FALSE);
        // $this->db->set('created_by', $this->session->users['id_user'], FALSE);
        // $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
        $this->db->insert('tb_ptpil', $data_detail);


        echo json_encode(array("status" => TRUE));
    }

    function update_form() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
        
        // echo "<pre>";
        // print_r ($this->input->post("formData"));
        // echo "</pre>";exit();
        
        $data_detail = [ 
            "kd_prov" => $this->input->post("formData")["kd_prov"], 
            "kd_status" => $this->input->post("formData")["kd_status"], 
            "luas" => $this->input->post("formData")["luas"], 
            "tahun_data" => $this->input->post("formData")["tahun_data"]
        ];

        $this->db->set('waktu', 'now()', FALSE);
        // $this->db->set('updated_by', $this->session->users['id_user'], FALSE);

        $this->db->where('txtidtahap61', $this->input->post("formData")["id"]);
        $this->db->update('tb_ptpil', $data_detail);



        echo json_encode(array("status" => TRUE));
    }


    public function getYear($layer=null,$tahun=null)
    {
        
        // $layer=str_replace('v_d','vm',$layer);
        $layer = 'spatial.v_r_pgtl_rtrw';
        $this->db->select('tahun_data');
        $this->db->group_by('tahun_data');
        $datas = $this->db->get($layer)->result();
        $data = rsort($datas);
        $data=[];
        foreach ($datas as $key => $value) {
            array_push($data,$value->tahun_data);
        }

        $sel = in_array($tahun,$data)? '':'selected';
        // echo $sel;
        $str='<option '.$sel.' value="">Semua Tahun</option>';
        foreach ($data as $key => $value) {
            $text = $value == null ? 'Tidak Ada Tahun' : $value;
            $value = $value == null ? '0' : $value;
            $sel = $value == $tahun ? 'selected' : '';
            $str .= '<option '.$sel.' value="'.$value.'">'.$text.'</option>';
        }
        echo json_encode($str);
        
    }
    public function ssp_paket() {
      
 
        $kd_prov=@$this->input->post("kd_prov",true);
        
        $table = 'spatial.v_r_pgtl_rtrw';
        $primaryKey = 'kdppum'; //test        

        $where="";
        if (!empty($kd_prov) && !empty($tahun_data) ) {

            if($tahun_data != 0){
                $where .="kdppum = '".$kd_prov."' and tahun_data = '".$tahun_data."'";
            }else{
                $where .="kdppum = '".$kd_prov."' and tahun_data is null";
            }
        }else if (!empty($kd_prov) && empty($tahun_data) ) {
                $where .="kdppum = '".$kd_prov."'";
        
        }else if (empty($kd_prov) && !empty($tahun_data) ) {
            if($tahun_data != '0'){
                $where .="tahun_data = '".$tahun_data."'";
            }else{
                $where .=" tahun_data is null";
            }
        }
                
        $columns = array(
            array('db' => 'kdppum', 'dt' => 0),
            array('db' => 'wadmpr', 'dt' => 1),
            array('db' => 'jml', 'dt' => 2),
            array('db' => 'tahun_data', 'dt' => 3),
        );

        datatable_ssp($table, $primaryKey, $columns,$where);

        
    }
    public function ajax_delete($id, $tahun=NULL) {
        // echo 'id '.$ida;
        $this->M_model->delete_by_id('tb_ptpil','txtidtahap61', $id);
        echo json_encode(array("status" => TRUE));
    }


    public function get_img($id) { 
        $query = $this->db->query("select * from aset_dokumen_jembatan where id_jembatan=$id order by id_dokumen_jmb desc");
        $data_satker = json_decode(json_encode($query->result()), true);

        echo json_encode($data_satker);
    }
   
    public function get_rekap_pencatatan() {
        $wh = "";
        $set_bujt="";
        $set_id_rekap="";
        $tahun =  $this->session->konfig_tahun_ang;
        if($this->session->users['role'] == 'bujt'){
            $wh = "and a.kd_bujt=".$this->session->users['kd_bujt'];
            $set_bujt=",bb.kd_bujt";
            $set_id_rekap="bb.id_rekap_pencatatan,";

        }
        $query = $this->db->query(" 
        select $set_id_rekap aa.tahun,aa.status,aa.nama_perolehan as nama_perolehan,aa.id_status,aa.id_aset_perolehan,
        bb.satuan $set_bujt,
        sum(bb.jml_ruas) as jml_ruas,
        sum(bb.kuantitas) as kuantitas,
        sum(bb.konstruksi_dasar)as konstruksi_dasar,
        sum(bb.konstruksi_lapisan)as konstruksi_lapisan,
        sum(bb.pemeliharaan)as pemeliharaan,
        sum(bb.peningkatan_total)as peningkatan_total
        from 
            (
            select a.status,a.id_status,b.nama_perolehan,b.id_aset_perolehan,a.tahun from aset_r_statusjln a
            left join aset_r_perolehan b on (''='')
            where a.tahun =b.tahun
            )
            aa
            left join (
            select count(a.id_ruas) as jml_ruas,
            a.id_status,a.id_aset_perolehan,a.kd_bujt,a.tahun as th,
            b.id_rekap_pencatatan,
            b.kuantitas,
            'KM' as satuan,
            b.konstruksi_dasar,
            b.konstruksi_lapisan,
            b.pemeliharaan,
            b.peningkatan_total
            from aset_t_ruas	a 
            left join aset_rekap_pencatatan b on ( b.id_bentuk_aset=1 and b.id_status=a.id_status and b.id_aset_perolehan=a.id_aset_perolehan and a.kd_bujt=b.kd_bujt and b.tahun=2022)
            where a.tahun =$tahun $wh 
            group by a.id_status,a.id_aset_perolehan,a.kd_bujt,a.tahun,
            b.id_rekap_pencatatan,b.kuantitas,b.satuan,b.konstruksi_dasar,b.konstruksi_lapisan,
            b.pemeliharaan,b.peningkatan_total
            )bb 
        on (aa.id_status=bb.id_status and aa.id_aset_perolehan=bb.id_aset_perolehan) 
        where aa.tahun =$tahun
        group by $set_id_rekap aa.tahun,aa.status,aa.nama_perolehan,aa.id_status,aa.id_aset_perolehan,bb.satuan $set_bujt
        
        ");
        $data = json_decode(json_encode($query->result()), true);
        echo json_encode($data);
    }

    public function get_dt_jembatan($id) { 
        $query = $this->db->query("select * from aset_r_jembatan where id_jembatan=$id");
        $data_satker = json_decode(json_encode($query->result()), true);

        echo json_encode($data_satker);
    }
    private function hir_satkerList($kd_bujt) {
//        echo "step 1: $kd_bujt<br>";

        $child_roles = '';
        $role = $kd_bujt;
//        $this->db->get_where('v_satker', ['kdinduk' => $role])->result_array();
        $this->db->select('kdsatker, nmsatker');
        $this->db->from('v_satker');
        $this->db->where('kdinduk', $role);
        $ares = $this->db->get()->result_array();

        //$aDef=array();
        //$data=array();
        $data = array();
//        $i=0;
        foreach ($ares as $key => $val) {

            $arr["0"] = "--Pilih Satker--";
            $arr[$val['kdsatker']] = $val['nmsatker'];
            $data = $arr;
        }
        return $data;
    }

    public function get_wps() {
        $p = $this->db->get_where('v_prov_satker', array('kdsatker' => $this->session->users['kd_bujt']))->row();
        $query = $this->db->query("select wps_kode,wps_nama FROM r_kws_dalam_wps where id_prov=$p->kd_prov group by wps_kode,wps_nama order by wps_kode asc");
        $data_satker = json_decode(json_encode($query->result()), true);

        echo json_encode($data_satker);
    }

    function get_kppn_by_province($kd_satker) {
        
    }

    function get_kdprov_irmsv3() {
        $query = $this->db->query("select kd_prov_irmsv3 from v_prov_satker where kd_prov=" . $this->session->users['kd_prov']);
        $data_province = json_decode(json_encode($query->result()), true);
        //echo $data_province[0]["kd_prov_irmsv3"];
        return $data_province[0]["kd_prov_irmsv3"];
    }

    function get_vprov_satker($kd_bujt) {
        $query = $this->db->query("select kdlokasi,nama_prov from v_prov_satker where kdsatker=" . $kd_bujt);
        $data_province = json_decode(json_encode($query->result()), true);
        //print_r($data_province);
        echo json_encode($data_province);
    }

    function get_ruas_by_province() {
        //echo $this->session->users['kd_prov']."||". $this->get_kdprov_irmsv3(); die();
        //echo $this->session->users['kd_prov'];die();
        //$kd_prov_rkakl=$this->get_kdprov_rkakl($this->session->users['kd_bujt']);
        $query = $this->db->query("select no_ruas,linkname from v_ruas where left(linkid,2)= " . $this->get_kdprov_irmsv3());
        $data_province = json_decode(json_encode($query->result()), true);

        echo json_encode($data_province);
    }

    public function get_kws($wps) {
        $query = $this->db->query("select kws_kode,kws_nama FROM r_kws_dalam_wps where wps_kode=$wps group by kws_kode,kws_nama order by kws_kode asc");
        $data_satker = json_decode(json_encode($query->result()), true);

        echo json_encode($data_satker);
    }

    public function get_sub($kws) {
        $query = $this->db->query("select subkawasan_nama FROM r_kws_dalam_wps where kws_kode='$kws' group by subkawasan_nama");
        $subkws = json_decode(json_encode($query->result()), true);

        echo json_encode($subkws);
    }

    public function get_detail_usulan($id_usulan, $tahap) {
        if (substr($tahap, 0, 2) === 'RD'){
            $tahap = "like 'RD%'";
        } else {
            $tahap = "= '$tahap'";
        }
        $str_sql = "select a.*, a.id_paket as wid_paket, b.nama_sub_komponen, b.jnskontrak,
                        b.rc_ded_status, b.rc_fs_status, b.rc_lahan_status, b.rc_doklin_status, 
                        b.kak_status, b.rab_status, b.flow1, b.flow2, b.flow3, 
                        a.kdkabkota2 as kdkabkot
                    from detail_usulan_paket a
                    left join vw_paket_status b on a.id_paket = b.id_paket
                    where a.id_usulan = $id_usulan and a.kd_tahapan $tahap";
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
        echo json_encode($data_usulan_indikatif[0]);
    }

//    public function get_detail_usulan($id_usulan, $tahap) {
//        $str_sql = "select a.*,a.id_paket as wid_paket,b.nama_sub_komponen, b.jnskontrak,
//        b.rc_ded_status,b.rc_fs_status,b.rc_lahan_status,
//        b.rc_doklin_status,b.kak_status,b.rab_status,b.flow1,b.flow2,b.flow3
//        from detail_usulan_paket a
//        left join vw_paket_status b on a.id_paket = b.id_paket
//                    where a.id_usulan=" . $id_usulan . " and a.kd_tahapan='$tahap'";
//        $query = $this->db->query($str_sql);
//        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
//        echo json_encode($data_usulan_indikatif[0]);
//    }
    // public function get_total_usulan($kdprop, $tahap) {
    //     $str_sql = "select * from v_usulan_total
    //                 where kd_prov_rkakl='$kdprop' and kd_tahapan='$tahap' and thang=".$this->session->konfig_tahun_ang;
    //     $query = $this->db->query($str_sql);
    //     $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
    //     echo json_encode($data_usulan_indikatif[0]);
    // }

    public function get_total_usulan($kdprop, $tahap, $thang) {
        $totalfisik = 0;
        $totalfisiksetuju = 0;
        $totalfisiktolak = 0;
        $totalfisikhold = 0;

        $totalnonfisik = 0;
        $totalnonfisiksetuju = 0;
        $totalnonfisiktolak = 0;
        $totalnonfisikhold = 0;

        $totalpj = 0;
        $totalpjsetuju = 0;
        $totalpjtolak = 0;
        $totalpjhold = 0;

        $totalpem = 0;
        $totalpemsetuju = 0;
        $totalpemtolak = 0;
        $totalpemhold = 0;

        $totaljem = 0;
        $totaljemsetuju = 0;
        $totaljemtolak = 0;
        $totaljemhold = 0;

        $totalpemjem = 0;

        $totaljbh = 0;
        $totaljbhsetuju = 0;
        $totaljbhtolak = 0;
        $totaljbhhold = 0;

        $role = $this->session->users['id_user_group_real'];
        if ($tahap == 'PI') {
            $tabel = "vw_paket_pi";
        } else {
            $tabel = "vw_paket";
        }
        $str_sql = "select * from $tabel
                    where kdlokasi='$kdprop' and kd_tahapan='$tahap' and thang=$thang";
        $query = $this->db->query($str_sql)->result_array();
        foreach ($query as $d) {
            //fisik GA 2409 Selain ABF
            if (($d['kd_program'] == '08' and $d['kd_kegiatan'] == '2409' and ( $d['kd_output'] != '951' and $d['kd_output'] != '014' and $d['kd_output'] != '970' and $d['kd_output'] != '994' and $d['kd_output'] != '019' and $d['kd_output'] != '020' and $d['kd_output'] != '021')) or ( $d['kd_program'] === 'GA' and $d['kd_kegiatan'] === '2409' and $d['kd_output'] !== 'ABF')) {
                $totalfisik += $d['jumlah'];
            }
            //nonfisik WA 4484, GA 2409 ABF, GA selain 2409 (2410, 4979)
            if (($d['kd_program'] === '08' and $d['kd_kegiatan'] <> '2409' or ( $d['kd_kegiatan'] == '2409' and ( $d['kd_output'] == '951' or $d['kd_output'] == '014' or $d['kd_output'] == '970' or $d['kd_output'] == '994' or $d['kd_output'] == '019' or $d['kd_output'] == '020' or $d['kd_output'] == '021'))) or ( $d['kd_program'] === 'WA' or ( $d['kd_program'] === 'GA' and $d['kd_kegiatan'] <> '2409') or ( $d['kd_program'] === 'GA' and $d['kd_kegiatan'] === '2409' and $d['kd_output'] === 'ABF'))) {
                $totalnonfisik += $d['jumlah'];
            }
            //preservasi_jalan GA 2409 CDC
            if (($d['kd_program'] == '08' and $d['kd_kegiatan'] == '2409' and ( $d['kd_output'] == '001' or $d['kd_output'] == '002' or $d['kd_output'] == '003')) or ( $d['kd_program'] === 'GA' and $d['kd_kegiatan'] === '2409' and $d['kd_output'] === 'CDC')) {
                $totalpj += $d['jumlah'];
            }
            //pembangunan_jalan selain GA 2409 CBC 003
            if (($d['kd_program'] == '08' and $d['kd_kegiatan'] == '2409' and ( $d['kd_output'] == '007' or $d['kd_output'] == '008' or $d['kd_output'] == '009')) or ( $d['kd_program'] === 'GA' and $d['kd_kegiatan'] === '2409' and $d['kd_output'] === 'CBC' and $d['kd_sub_output'] !== '003')) {
                $totalpem += $d['jumlah'];
            }
            //preservasi_jembatan GA 2409 CDF
            if (($d['kd_program'] == '08' and $d['kd_kegiatan'] == '2409' and ( $d['kd_output'] == '004' or $d['kd_output'] == '005' or $d['kd_output'] == '006' or $d['kd_output'] == '010')) or ( $d['kd_program'] === 'GA' and $d['kd_kegiatan'] === '2409' and $d['kd_output'] === 'CDF')) {
                $totaljem += $d['jumlah'];
            }
            //pembangunan_jembatan GA 2409 CBF
            if (($d['kd_program'] == '08' and $d['kd_kegiatan'] == '2409' and ( $d['kd_output'] == '004' or $d['kd_output'] == '005' or $d['kd_output'] == '006' or $d['kd_output'] == '010')) or ( $d['kd_program'] === 'GA' and $d['kd_kegiatan'] === '2409' and $d['kd_output'] === 'CBF')) {
                $totalpemjem += $d['jumlah'];
            }
            //jbh GA 2409 CBC 003
            if (($d['kd_program'] == '08' and $d['kd_kegiatan'] == '2409' and ( $d['kd_output'] == '012')) or ( $d['kd_program'] === 'GA' and $d['kd_kegiatan'] === '2409' and $d['kd_output'] === 'CBC' and $d['kd_sub_output'] === '003')) {
                $totaljbh += $d['jumlah'];
            }
        }

        $dtarray = array(
            'fisik' => $totalfisik,
            'non_fisik' => $totalnonfisik,
            'preservasi_jalan' => $totalpj,
            'pembangunan_jalan' => $totalpem,
            'jembatan' => $totaljem,
            'pembangunan_jembatan' => $totalpemjem,
            'jbh' => $totaljbh,
        );
        //$data_usulan_indikatif = json_decode(json_encode($dtarray), true);
        echo json_encode($dtarray);
    }

    public function get_total_pagu($kdprop, $tahap) {
        $str_sql = "select * from v_pagu_total
                        where kd_prov_rkakl='$kdprop' and kd_tahapan='$tahap' and thang=" . $this->session->konfig_tahun_ang;
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
        echo json_encode($data_usulan_indikatif[0]);
    }

    public function get_akun_by_kdgbkpk($kdgbkpk) {
        $str_sql = "select kdakun,nmakun from v_akun where kdgbkpk=" . $kdgbkpk;
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);

        echo json_encode($data_usulan_indikatif);
    }

    public function get_irmsv3_rujukan() {

        $noruas = $this->input->post("noruas", TRUE);
        $staa = $this->input->post("staa", TRUE);
        $stae = $this->input->post("stae", TRUE);


        //break;
        // echo $noruas.'-'.$staa.'-'.$stae.'<br>';
        // return false;



        if ((empty($noruas) && empty($staa) && empty($stae))) {
            //            echo 'kosong semua';
            //            return false;
            echo '{"data":[]}';
        } else if ((!empty($noruas) && strlen($staa) == 0 && empty($stae))) {
            //            echo 'ruas ada';
            //            return false;
            echo '{"data":[]}';
        } else if ((!empty($noruas) && strlen($staa) == 1 && empty($stae))) {
            //             echo 'ruas ada staa ada';
            //            return false;
            echo '{"data":[]}';
        } else if ((!empty($noruas) && $staa != 0 && empty($stae))) {
            //             echo 'ruas ada staa ada';
            //            return false;
            echo '{"data":[]}';
        } else if (!empty($noruas) && !empty($staa) && !empty($stae) || !empty($noruas) && strlen($staa) == 1 && !empty($stae)) {

            //            echo 'komplit';
            //            return false;

            $staa = $staa / 1000;
            $stae = $stae / 1000;


            //$kdsatker = $this->session->users['kd_bujt'];
            //header('Content-Type: application/json');
            //$url = "http://gisportal.binamarga.pu.go.id/arcgis/rest/services/Hosted/ELRS_Road_Network/FeatureServer/0/query?where=RouteId+is+not+null+and+kd_satker+%3D+%2704$kdsatker%27&objectIds=&time=&geometry=&geometryType=esriGeometryEnvelope&inSR=&spatialRel=esriSpatialRelIntersects&distance=&units=esriSRUnit_Foot&relationParam=&outFields=routeid%2Croute_name%2Cnoprop%2Ckd_satker%2Csk_year%2Csurvey_len&returnGeometry=false&maxAllowableOffset=&geometryPrecision=&outSR=&gdbVersion=&historicMoment=&returnDistinctValues=false&returnIdsOnly=false&returnCountOnly=false&returnExtentOnly=false&orderByFields=&groupByFieldsForStatistics=&outStatistics=&returnZ=false&returnM=false&multipatchOption=xyFootprint&resultOffset=&resultRecordCount=&returnTrueCurves=false&sqlFormat=none&resultType=&f=pjson";


            $url = "https://gisportal.binamarga.pu.go.id/arcgis/rest/services/AMS_BM_DEV/PMSAnalysisResults/MapServer/0/query?where=ROUTE_NAME+%3D+%27$noruas%27+and+(OFFSET_FROM+>%3D+$staa+and+OFFSET_TO+<%3D+$stae)++&text=&objectIds=&time=&geometry=&geometryType=esriGeometryEnvelope&inSR=&spatialRel=esriSpatialRelIntersects&relationParam=&outFields=UNIQUE_ID%2C+PMS_TREATMENT_NAME%2C+TREATMENT_COST%2C+PMS_BUDGET_CAT_ID%2C+SCN_YEAR_NUM%2C+LENGTH%2C+BM_REGION%2C+BM_PROVINCE%2C+ROUTE_NAME%2C+LANE_DIR_NAME%2C+OFFSET_FROM%2C+OFFSET_TO&returnGeometry=false&returnTrueCurves=false&maxAllowableOffset=&geometryPrecision=&outSR=&returnIdsOnly=false&returnCountOnly=false&orderByFields=&groupByFieldsForStatistics=&outStatistics=&returnZ=false&returnM=false&gdbVersion=&returnDistinctValues=false&resultOffset=&resultRecordCount=&queryByDistance=&returnExtentsOnly=false&datumTransformation=&parameterValues=&rangeValues=&f=pjson";


            $sgj = file_get_contents($url);
            $gj = json_decode($sgj);

            $data = array();
            $realdata = array();

            foreach ($gj->features as $item) {
                $prop = $item->attributes;
                $data[] = $prop;
            }


            foreach ($data as $dt) {
                $row["unique_id"] = $dt->UNIQUE_ID;
                $row["pms_treatment_name"] = $dt->PMS_TREATMENT_NAME;
                $row["treatment_cost"] = $dt->TREATMENT_COST;
                $row["pms_budget_cat_id"] = $dt->PMS_BUDGET_CAT_ID;
                $row["scn_year_num"] = $dt->SCN_YEAR_NUM;
                $row["length"] = $dt->LENGTH;
                $row["bm_region"] = $dt->BM_REGION;
                $row["bm_province"] = $dt->BM_PROVINCE;
                $row["route_name"] = $dt->ROUTE_NAME;
                $row["lane_dir_name"] = $dt->LANE_DIR_NAME;
                //$row["lane_id_name"] = $dt->LANE_ID_NAME;
                $row["offset_from"] = $dt->OFFSET_FROM * 1000;
                $row["offset_to"] = $dt->OFFSET_TO * 1000;

                $realdata[] = $row;
            }



            echo '{"data": ' . json_encode($realdata) . '}';
        }
    }

    public function update_rekap_pencatatan($kolom,$val,$id){
        $idp = $this->input->post('id_perolehan');
        $ids = $this->input->post('id_status');
        $data = array($kolom=>$val,'id_bentuk_aset'=>1,'id_status'=>$ids,'id_aset_perolehan'=>$idp,'tahun'=>date('Y'),'kd_bujt'=>$this->session->users['kd_bujt']);
        if($id == 'null'){
             $this->db->insert('aset_rekap_pencatatan', $data);
            $insert_id = $this->db->insert_id();
            echo $insert_id;
            return  $insert_id;
        }else {
            $this->db->where('id_rekap_pencatatan', $id);
     		return $this->db->update('aset_rekap_pencatatan', $data);           
        }
    }
    public function update_detail_usulan() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $id_user = $this->session->users['id_user'];
        $kd_bujt = $this->session->users['kd_bujt'];
        $id_paket = $this->input->post("formData")["id_paket"];
        $id_usulan = $this->input->post("formData")["id_usulan"];
        $tahapan = $this->input->post("formData")["kd_tahapan"];
        $ppk = $this->input->post("formData")["id_ppk"];
        $kab = $this->session->users['kdkabkota'];
        $dekon = $this->session->users['kddekon'];
        $arr_insert = $this->input->post("formData");
        $kdkabkotbpss = substr($this->input->post("formData")["kdkabkota2"], 2, 2);
        $kdprog = $this->input->post("formData")["kd_program"];
        $kdgiat = $this->input->post("formData")["kd_kegiatan"];
        $ruas = $this->input->post("formData")["id_ruas"];
        $jembatan = $this->input->post("formData")["id_jembatan"];
        $staw = $this->input->post("formData")["sta_awal"];
        $stak = $this->input->post("formData")["sta_akhir"];
        $thang = $this->input->post("formData")["thang"];
        $kdoutput = $this->input->post("formData")["kd_output"];
        $kdsoutput = $this->input->post("formData")["kd_sub_output"];
        $kdkmpnen = $this->input->post("formData")["kd_komponen"];
        $kdskmpnen = $this->input->post("formData")["kd_sub_komponen"];
        $kdakun = $this->input->post("formData")["kdakun"];
        $kdbeban = $this->input->post("formData")["kdbeban"];
        $kdjnsban = $this->input->post("formData")["kdjnsban"];
        $kdctarik = $this->input->post("formData")["kdctarik"];
        $header2 = $this->input->post("formData")["header2"];
        $nmppk = $this->input->post("formData")["nmppk"];
        $auto = $this->db->get_where('d_output', array('thang' => $thang, 'kdsatker' => $kd_bujt, 'kd_tahapan' => $tahapan,
            'kdprogram' => $kdprog, 'kdgiat' => $kdgiat, 'kdoutput' => $kdoutput));
        if ($auto->num_rows() === 0) {
            $auto = '2';
        } else {
            $auto = $auto->row()->kdauto;
        }

        $this->db->trans_start();

        if ($tahapan === 'KO') {
            $geomCheck = $this->input->post("formData")["geom"];
            if (strlen($geomCheck) > 0) {
                if ($jembatan !== NULL || $jembatan !== '' && $ruas !== NULL || $ruas !== NULL) {
                    $id_paket = $this->input->post("formData")["id_paket"];
                    $geomVal = $this->input->post("formData")["geom"];
                    $satuan = strtoupper($this->input->post("formData")["satuan"]);
                    $geom = '';
                    if ($satuan == 'KM') {
                        $geom = "geometry::STGeomFromText('$geomVal',4326)";
                        $data_geo = [
                            "thang" => $this->input->post("formData")["thang"],
                            "kd_tahapan" => 'KO',
                            "id_paket" => $id_paket,
                            "id_detail" => $id_usulan,
                            "latitude" => $this->input->post("formData")["latitude3"],
                            "longitude" => $this->input->post("formData")["longitude3"],
                            "latitude2" => $this->input->post("formData")["latitude4"],
                            "longitude2" => $this->input->post("formData")["longitude4"]
                        ];

                        $this->db->set('geom_pok2', $geom, false);
                        $this->db->insert('geo_pok_ruas', $data_geo);
                    } else if ($satuan == 'M') {
                        $geom = "geometry::STGeomFromText('POINT($geomVal)',4326)";
                        $data_geo_jem = [
                            "thang" => $this->input->post("formData")["thang"],
                            "kd_tahapan" => 'KO',
                            "id_paket" => $id_paket,
                            "id_detail" => $id_usulan,
                            "latitude" => $this->input->post("formData")["latitude3"],
                            "longitude" => $this->input->post("formData")["longitude3"]
                        ];
                        $this->db->set('geom_pok2', $geom, false);
                        $this->db->insert('geo_pok_jembatan', $data_geo_jem);
                    }
                }
            }
            die();
        }

        //$this->update_ppk($ppk, $id_usulan, $thang, $tahapan);
        $cek_detail = $this->get_detail_usulans($id_usulan, $tahapan, $thang);
        $noheader1 = '00';

        //add header
        $dataz = json_decode(json_encode($cek_detail[0]), true);
        if ($dataz['id_ppk'] !== $ppk) {
            //echo 'ppk beda';
            $cekz = $this->cek_ppk_akun($id_paket, $kdakun, $kdbeban, $kdjnsban, $ppk, $tahapan, $kd_bujt, $nmppk);
            $getppk = $this->get_ppk($kd_bujt, $ppk);
            $data = json_decode(json_encode($getppk[0]), true);
            if (count($cekz) === 0) {
                //echo 'add';
                $jmlppk = $this->get_jmlh_ppk($id_paket, $tahapan, $kd_bujt, $thang, $kdakun, $kdbeban, $kdjnsban);
                $a = json_encode($jmlppk[0]['max'], JSON_NUMERIC_CHECK);
                echo $a;
                if ($a === 0 or $a === 'null') {
                    if (strlen($a) < 4) {
                        $i = $a + 1;
                        $noheader1s = str_pad($i, 2, '0', STR_PAD_LEFT);
                    } else {
                        $noheader1s = '01';
                    }
                    $this->add_header1($id_paket, $kd_bujt, $id_user, $kab, $dekon, $arr_insert, $kdkabkotbpss, $data['nama_ppk'], $noheader1s);

                    //$this->db->set('kdheader', '1');
                    $this->db->set('header1', $noheader1s);
                    $this->db->set('header2', '00');
                    $this->db->set('id_ppk', $ppk);
                    $this->db->where(array('id_paket' => $id_paket, 'kdakun' => $kdakun, 'kdbeban' => $kdbeban,
                        'kdjnsban' => $kdjnsban))->update('detail_usulan_paket');
                    $noheader1 = '01';
                } else {
                    $arr = array('id_paket' => $dataz['id_paket'], 'kdakun' => $kdakun, 'header1' => '01', 'header2' => '00', 'hargasat' => 0);
                    $ppklamaa = $this->db->get_where('detail_usulan_paket', $arr)->row();
                    //echo $ppklamaa;
                    $this->db->set('detail', $data['nama_ppk']);
                    $this->db->set('id_ppk', $ppk);
                    $this->db->where('id_usulan', $ppklamaa->id_usulan)->update('detail_usulan_paket');

                    $this->db->set('header1', $ppklamaa->header1);
                    //$this->db->set('header2', $ppklamaa->header2);
                    $this->db->set('id_ppk', $ppk);
                    $this->db->where(array('id_paket' => $ppklamaa->id_paket, //'header1' => $ppklamaa->header1, 'header2' => $ppklamaa->header2,
                        'kdakun' => $ppklamaa->kdakun, 'kdbeban' => $ppklamaa->kdbeban, 'kdjnsban' => $ppklamaa->kdjnsban,
                        'kdctarik' => $ppklamaa->kdctarik))->update('detail_usulan_paket');
                    $noheader1 = $ppklamaa->header1;
                }
            } else {
                //echo 'up';
            }
        } else {
            //echo 'ppk sama </br>';
        }

        if (strlen($header2) > 0) {
            $head2 = $dataz['header2'] !== '00' ? $dataz['header2'] : '99';
            $arr2 = array('id_paket' => $dataz['id_paket'], 'kdakun' => $kdakun, 'header1' => $dataz['header1'], 'header2' => $head2, 'hargasat' => 0);
            $head2lama = $this->db->get_where('detail_usulan_paket', $arr2)->row();
            if ($head2lama) {
                $cek2 = $this->cek_sameheader2($id_paket, $tahapan, $kd_bujt, $thang, $kdakun, $kdbeban, $kdjnsban, $ppk, $header2);
                if (count($cek2) === 0) {
                    $headlama = array('id_paket' => $dataz['id_paket'], 'kdakun' => $kdakun, 'header1' => $dataz['header1'], 'header2' => $dataz['header2'], 'hargasat' => 0);
                    $headlamaa = $this->db->get_where('detail_usulan_paket', $headlama)->row();

                    $this->db->set('detail', $header2);
                    $this->db->where('id_usulan', $headlamaa->id_usulan)->update('detail_usulan_paket');
                } else {
                    $this->db->set('header2', $cek2[0]['header2']);
                    $this->db->where('id_usulan', $id_usulan)->update('detail_usulan_paket');
                }
            } else {
                $arrhead2 = array('id_paket' => $dataz['id_paket'], 'kdakun' => $kdakun, 'header1' => $dataz['header1'], 'detail' => $header2, 'hargasat' => 0);
                $head2lama2 = $this->db->get_where('detail_usulan_paket', $arrhead2)->row();
                //echo $this->db->last_query();
                if ($head2lama2) {
                    $this->db->set('header2', $head2lama2->header2);
                    $this->db->where('id_usulan', $id_usulan)->update('detail_usulan_paket');
                } else {
                    $jmlheader2 = $this->get_jmlh_header2($id_paket, $kdakun, $ppk, $tahapan, $kdbeban, $kdjnsban);
                    $b = json_encode($jmlheader2[0]['max'], JSON_NUMERIC_CHECK);
                    if ($b > 0) {
                        $j = $b + 1;
                        $noheader2 = str_pad($j, 2, '0', STR_PAD_LEFT);
                    } else {
                        $noheader2 = '01';
                    }

                    $this->add_header2($id_paket, $kd_bujt, $id_user, $kab, $dekon, $arr_insert, $kdkabkotbpss, '01', $noheader2);

                    $this->db->set('header2', $noheader2);
                    $this->db->where('id_usulan', $id_usulan)->update('detail_usulan_paket');
                }
            }
        } else {
            $this->db->set('header2', '00');
            $this->db->where('id_usulan', $id_usulan)->update('detail_usulan_paket');
        }
        ////////////////////////////
        //new codes
//        if (strlen($jembatan) > 0) {
//            //echo $jembatan;
//            $cek_jembatan = $this->isJembatanOverLap($thang, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $jembatan);
//            if (count($cek_jembatan) > 0 && (int) $id_usulan !== (int) json_decode($cek_jembatan[0]["id_usulan"])) {
//                exit('2');
//                //echo '2';
//                //die();
//            }
//        } else {
//            if (strlen($ruas) > 0) {
//                $cek_ruas = $this->isRuasOverLap($thang, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $ruas, $staw, $stak);
//                if (count($cek_ruas) > 0 && (int) $id_usulan !== (int) json_decode($cek_ruas[0]["id_usulan"])) {
//                    //echo $id_usulan;
//                    //echo json_decode($cek_ruas[0]["id_usulan"]);
//                    if ((int) $id_usulan !== (int) json_decode($cek_ruas[0]["id_usulan"])) {
//                        exit('1');
//                        //echo '1';
//                        //die();
//                    }
//                }
//            }
//        }

        $data_tag = json_decode($this->input->post("formData")["data_tag"], true);

        if ($this->input->post("formData")["status_perubahan"] == 'berubah') {
            $this->db->where('id_paket', $id_paket);
            $this->db->update('usulan_paket', array('verifikasi1' => null,
                'verifikasi2' => null,
                'verifikasi3' => null,
                'user_verifikasi1' => null,
                'user_verifikasi2' => null,
                'user_verifikasi3' => null,
                'catatan1' => null,
                'catatan2' => null,
                'catatan3' => null,
            ));
            //$cek = $this->db->get_where('perubahan_pakets', array('id_paket' => $array_detail['id_paket'], 'id_usulan' => $id_usulan, 'stat_verifikasi=>NULL'));
            $cek = $this->db->get_where('perubahan_pakets', array('id_paket' => $id_paket, 'id_usulan' => $id_usulan));
            if ($cek->num_rows() > 0) {
                //$this->db->update('perubahan_pakets', array('status' => '1'), array('id_paket' => $array_detail['id_paket'], 'stat_verifikasi=>NULL'));
                $this->db->update('perubahan_pakets', array('status' => '1'), array('id_paket' => $id_paket));
            } else {
                $jum = explode(",", $this->input->post("formData")["perubahan"]);
                $hs = count($jum);
                $data_perubahan = array();
                for ($i = 0; $i < $hs; $i++) {
                    if ($jum[$i] == '60') {
                        $kdsat = $this->db->get_where('v_satker', array('kdsatker' => $this->session->users['kd_bujt']))->row()->kdinduk;
                    } else {
                        $kdsat = '';
                    }
                    array_push($data_perubahan, array(
                        'id_paket' => $id_paket,
                        'id_usulan' => $id_usulan,
                        'thang' => $thang,
                        'id_role' => $jum[$i],
                        'kd_tahapan' => $this->session->konfig_kd_tahapan,
                        'kdsatker' => $kdsat,
                        'status' => '1',
                        'siverifikasi' => $this->session->users['id_user_group_real'],
                        'tgl_verifikasi' => date('Y-m-d h:i:s'),
                    ));
                }
//                $this->db->update('perubahan_pakets', array('status' => '1'), array('id_paket' => $array_detail['id_paket'], 'stat_verifikasi=>NULL'));
                $this->db->update('perubahan_pakets', array('status' => '1'), array('id_paket' => $id_paket));
                $this->db->insert_batch('perubahan_pakets', $data_perubahan);
            }
        }
        $cek = $this->db->query("select count(*) as ceks from rujukan_paket where id_usulan=$id_usulan")->row()->ceks;
        if ($cek > 0) {
            $this->db->delete('rujukan_paket', array('id_usulan' => $id_usulan));
        }

        $longitudes = $this->input->post("formData")["longitude"];
        $latitudes = $this->input->post("formData")["latitude"];
        $longitude2s = $this->input->post("formData")["longitude2"];
        $latitude2s = $this->input->post("formData")["latitude2"];
        $longitude = (strlen($longitudes) === 0) ? NULL : $longitudes;
        $latitude = (strlen($latitudes) === 0) ? NULL : $latitudes;
        $longitude2 = (strlen($longitude2s) === 0) ? NULL : $longitude2s;
        $latitude2 = (strlen($latitude2s) === 0) ? NULL : $latitude2s;

        $pagurmp = $this->input->post("formData")["pagurmp"];
        $paguphln = $this->input->post("formData")["paguphln"];
        $pagurkp = $this->input->post("formData")["pagurkp"];
        $blokirrmp = $this->input->post("formData")["blokirrmp"];
        $blokirphln = $this->input->post("formData")["blokirphln"];
        $blokirrkp = $this->input->post("formData")["blokirrkp"];
        $rphblokir = $this->input->post("formData")["rphblokir"];

        $update_detail = [
            "satuan" => $this->input->post("formData")["satuan"],
            "id_user" => $this->session->users['id_user'],
            "created_by" => $this->session->users['id_user'],
            "kdgbkpk" => strlen($this->input->post("formData")["kdgbkpk"]) === 0 ? NULL : $this->input->post("formData")["kdgbkpk"],
            "kdakun" => strlen($this->input->post("formData")["kdakun"]) === 0 ? NULL : $this->input->post("formData")["kdakun"],
            "jumlah" => $this->input->post("formData")["jumlah"],
            "id_ppk" => $this->input->post("formData")["id_ppk"],
            "id_jembatan" => $jembatan,
            "id_ruas" => $ruas,
            "sta_awal" => $staw,
            "sta_akhir" => $stak,
            "longitude" => $longitude,
            "latitude" => $latitude,
            "longitude2" => $longitude2,
            "latitude2" => $latitude2,
            "detail" => $this->input->post("formData")["detail"],
            "volume" => (float) $this->input->post("formData")["volume"],
            "hargasat" => $this->input->post("formData")["hargasat"],
            "jumlah" => (float) $this->input->post("formData")["jumlah"],
            "tags_val" => $this->input->post("formData")["tags_val"],
            "kdbeban" => strlen($this->input->post("formData")["kdbeban"]) === 0 ? NULL : $this->input->post("formData")["kdbeban"],
            "kdjnsban" => $this->input->post("formData")["kdjnsban"],
            "kdctarik" => $this->input->post("formData")["kdctarik"],
            //new code
            "register" => $this->input->post("formData")["register"],
            "carahitung" => $this->input->post("formData")["carahitung"],
            "paguphln" => strlen($paguphln) === 0 ? NULL : (float) $paguphln,
            "pagurmp" => strlen($pagurmp) === 0 ? NULL : (float) $pagurmp,
            "pagurkp" => strlen($pagurkp) === 0 ? NULL : (float) $pagurkp,
            "vol1" => strlen($this->input->post("formData")["vol1"]) === 0 ? NULL : (float) $this->input->post("formData")["vol1"],
            "vol2" => strlen($this->input->post("formData")["vol2"]) === 0 ? NULL : (float) $this->input->post("formData")["vol2"],
            "vol3" => strlen($this->input->post("formData")["vol3"]) === 0 ? NULL : (float) $this->input->post("formData")["vol3"],
            "vol4" => strlen($this->input->post("formData")["vol4"]) === 0 ? NULL : (float) $this->input->post("formData")["vol4"],
            "sat1" => $this->input->post("formData")["sat1"],
            "sat2" => $this->input->post("formData")["sat2"],
            "sat3" => $this->input->post("formData")["sat3"],
            "sat4" => $this->input->post("formData")["sat4"],
            "kdblokir" => $this->input->post("formData")["kdblokirs"],
            "blokirphln" => strlen($blokirphln) === 0 ? NULL : (float) $blokirphln,
            "blokirrmp" => strlen($blokirrmp) === 0 ? NULL : (float) $blokirrmp,
            "blokirrkp" => strlen($blokirrkp) === 0 ? NULL : (float) $blokirrkp,
            "rphblokir" => strlen($rphblokir) === 0 ? NULL : (float) $rphblokir,
            "kdsbu" => $this->input->post("formData")["kdsbu"],
            "kdkppn" => $this->input->post("formData")["kdkppn"],
            "kdlokasi" => $this->input->post("formData")["kdlokasi"],
            "kdkabkota2" => $kdkabkotbpss,
            "kdkabkota" => $kab
        ];

        $this->db->where('id_usulan', $id_usulan);
        $this->db->set('updated_at', 'getutcdate()', FALSE);
        $this->db->update('detail_usulan_paket', $update_detail);

        if (count($cek_detail) > 0) {
            $datacek = json_decode(json_encode($cek_detail[0]), true);
            if (strlen($datacek['id_ppk']) === 0) {
                // add header in detail
                $head1 = $this->cek_ppk_akun($id_paket, $kdakun, $kdbeban, $kdjnsban, $ppk, $tahapan, $kd_bujt, $nmppk);
                if (strlen($header2) > 0) {
                    $head2 = $this->cek_sameheader2($id_paket, $tahapan, $kd_bujt, $thang, $kdakun, $kdbeban, $kdjnsban, $ppk, $header2);
                    $datah = [
                        "header1" => $head1[0]['header1'],
                        "header2" => $head2[0]['header2']
                    ];
                } else {
                    $datah = ["header1" => $head1[0]['header1']];
                }

                $this->db->where('id_usulan', $id_usulan);
                $this->db->update('detail_usulan_paket', $datah);

                //update noheader & noitem
//                $ppk1 = $this->cek_ppk1($id_paket, $kdakun, $kdbeban, $kdjnsban, $ppk, $tahapan, $kd_bujt, $thang);
//                if(count($ppk1) > 1){
//                    $this->db->where(array('id_usulan' => $ppk1[0]->id_usulan))->delete('detail_usulan_paket');
//                }

                $this->update_header1($id_paket, $kd_bujt, $kdakun, $kdbeban, $kdjnsban, $tahapan, $thang);
                $this->update_item($id_paket, $kdakun, $kdbeban, $kdjnsban, $tahapan, $kd_bujt, $thang);
            }
        }

        $this->update_header1($id_paket, $kd_bujt, $kdakun, $kdbeban, $kdjnsban, $tahapan, $thang);
        $this->update_item($id_paket, $kdakun, $kdbeban, $kdjnsban, $tahapan, $kd_bujt, $thang);

        $sisa_header2 = $this->db->get_where('detail_usulan_paket', array('id_paket' => $id_paket, 'kdheader' => '2', 'kdakun' => $kdakun,
                    'kdbeban' => $kdbeban, 'kdjnsban' => $kdjnsban))->result();
        if (count($sisa_header2) > 0) {
            for ($a = 0; $a < count($sisa_header2); $a++) {
                $ada2 = $this->ada_ppk2($sisa_header2[$a]->id_ppk, $sisa_header2[$a]->header1, $sisa_header2[$a]->id_paket, $sisa_header2[$a]->kdsatker, $sisa_header2[$a]->kdakun, $sisa_header2[$a]->kdbeban, $sisa_header2[$a]->kdjnsban, $tahapan, $thang, $sisa_header2[$a]->header2);
                if (count($ada2) === 1) {
                    $this->db->where(array('id_usulan' => $ada2[0]->id_usulan, 'kdheader' => '2'))->delete('detail_usulan_paket');
                }
            }
        }

        $sisa_header1 = $this->db->get_where('detail_usulan_paket', array('id_paket' => $id_paket, 'kdheader' => '1', 'kdakun' => $kdakun,
                    'kdbeban' => $kdbeban, 'kdjnsban' => $kdjnsban))->result();
        if (count($sisa_header1) > 0) {
            for ($a = 0; $a < count($sisa_header1); $a++) {
                $ada = $this->ada_ppk($sisa_header1[$a]->id_ppk, $sisa_header1[$a]->header1, $sisa_header1[$a]->id_paket, $sisa_header1[$a]->kdsatker, $sisa_header1[$a]->kdakun, $sisa_header1[$a]->kdbeban, $sisa_header1[$a]->kdjnsban, $tahapan, $thang);
                if (count($ada) === 1) {
                    $this->db->where(array('id_usulan' => $ada[0]->id_usulan, 'kdheader' => '1'))->delete('detail_usulan_paket');
                }
            }
        }

        $this->update_header1($id_paket, $kd_bujt, $kdakun, $kdbeban, $kdjnsban, $tahapan, $thang);
        $this->update_item($id_paket, $kdakun, $kdbeban, $kdjnsban, $tahapan, $kd_bujt, $thang);

        $array = array('kdakun' => $kdakun,
            'thang' => $thang, 'kdsatker' => $kd_bujt, 'kdprogram' => $kdprog, 'kdgiat' => $kdgiat,
            'kdoutput' => $kdoutput, 'kdsoutput' => $kdsoutput,
            'kdkmpnen' => $kdkmpnen, 'kdskmpnen' => $kdskmpnen,
            'kdbeban' => $kdbeban, 'kdjnsban' => $kdjnsban, 'kdctarik' => $kdctarik, 'kd_tahapan' => $tahapan);

        //new code akun
        if (strlen($kdakun) > 0) {
            $cek_available_akun = $this->cek_available_akun($thang, $kd_bujt, $kdprog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $kdskmpnen, $kdakun, $kdbeban, $kdjnsban, $kdctarik, $tahapan);
            if (count($cek_available_akun) === 0) {
                $this->tambah_akun($kd_bujt, $kab, $dekon, $tahapan, $arr_insert);
            } else {
                $this->edit_akun($arr_insert, $array);
            }
        }

        $cek_blokir = $this->cek_isblokir($thang, $kd_bujt, $kdprog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $kdskmpnen, $kdakun, $kdbeban, $kdjnsban, $kdctarik, $tahapan);
        if (count($cek_blokir) === 0) {
            $data_update_akun = [
                "kdblokir" => '',
                "uraiblokir" => ''
            ];
            $this->db->where($array);
            $this->db->update('d_akun', $data_update_akun);
        }

        //$this->update_item($id_paket, $kdakun, $kdbeban, $kdjnsban, $tahapan, $kd_bujt, $thang);
        //$this->update_header1($id_paket, $kd_bujt, $kdakun, $kdbeban, $kdjnsban, $tahapan, $thang);
        $this->update_item($id_paket, $kdakun, $kdbeban, $kdjnsban, $tahapan, $kd_bujt, $thang);

        if ($auto === '0') {
            $cek_new_sum = $this->get_new_sum_out($kdprog, $kdgiat, $kdoutput, $thang, $tahapan);
            //$newsum = json_encode($cek_new_sum[0]['sumvol'], JSON_NUMERIC_CHECK);
            $data_update = [
                "vol" => $cek_new_sum
            ];
            $array = array('thang' => $thang, 'kdsatker' => $kd_bujt,
                'kddept' => '033', 'kdunit' => '04', 'kdprogram' => $kdprog,
                'kdgiat' => $kdgiat, 'kdoutput' => $kdoutput, 'kd_tahapan' => $tahapan);
            $this->db->where($array);
            $this->db->update('d_output', $data_update);
        }

        if ($auto === '1') {
            $cek_new_sum_sout = $this->get_new_sum_sout($kdprog, $kdgiat, $kdoutput, $kdsoutput, $thang, $tahapan);
            //$newsums = json_encode($cek_new_sum_sout[0]['jumlah'], JSON_NUMERIC_CHECK);
            $data_update = [
                "volsout" => $cek_new_sum_sout
            ];
            $array = array('thang' => $thang, 'kdsatker' => $kd_bujt,
                'kddept' => '033', 'kdunit' => '04', 'kdprogram' => $kdprog,
                'kdgiat' => $kdgiat, 'kdoutput' => $kdoutput, 'kdsoutput' => $kdsoutput, 'kd_tahapan' => $tahapan);
            $this->db->where($array);
            $this->db->update('d_soutput', $data_update);

            $cek_new_sum = $this->get_new_sum_out($kdprog, $kdgiat, $kdoutput, $thang, $tahapan);
            //$newsum = json_encode($cek_new_sum[0]['sumvol'], JSON_NUMERIC_CHECK);
            $data_update2 = [
                "vol" => $cek_new_sum
            ];
            $array2 = array('thang' => $thang, 'kdsatker' => $kd_bujt,
                'kddept' => '033', 'kdunit' => '04', 'kdprogram' => $kdprog,
                'kdgiat' => $kdgiat, 'kdoutput' => $kdoutput, 'kd_tahapan' => $tahapan);
            $this->db->where($array2);
            $this->db->update('d_output', $data_update2);
        }


        //new code
        $geomCheck = $this->input->post("formData")["geom"];
        if (strlen($geomCheck) > 0) {
            $id_paket = $this->input->post("formData")["id_paket"];

            $data_geo = [
                "thang" => $this->input->post("formData")["thang"],
                "kd_tahapan" => $tahapan,
                "id_paket" => $id_paket,
                "id_detail" => $id_usulan
                    //"geom_pok2" => $geompok2
            ];

            $satuan = strtoupper($this->input->post("formData")["satuan"]);
            if ($tahapan == 'KO') {
                if ($satuan == 'KM') {
                    $geomVal = $this->input->post("formData")["geom"];
                    $geo = explode(';', $geomVal);
                    $geom = "geometry::STGeomFromText('$geo[1]',4326)";
                    $array = array('thang' => $this->input->post("formData")["thang"],
                        'id_paket' => $id_paket, 'id_detail' => $id_usulan);
                    $this->db->set('geom_pok2', $geom, false);
                    $this->db->update('geo_pok_ruas', $data_geo, $array);
                    //$data = [ 'geom_pok2' => $geom];
                    // $array = array('thang' => $this->input->post("formData")["thang"],
                    //     'id_paket' => $id_paket, 'id_detail' => $id_usulan);
                    // $this->db->update('geo_pok_ruas', $data, $array);
                } else if ($satuan == 'M') {
                    $geomVal = $this->input->post("formData")["geom"];
                    $geom = "geometry::STGeomFromText('POINT($geomVal)',4326)";
                    $array = array('thang' => $this->input->post("formData")["thang"],
                        'id_paket' => $id_paket, 'id_detail' => $id_usulan);
                    $this->db->set('geom_pok2', $geom, false);
                    $this->db->update('geo_pok_jembatan', $data_geo, $array);
                    // $data = [ 'geom_pok2' => $geom];
                    // $array = array('thang' => $this->input->post("formData")["thang"],
                    //     'id_paket' => $id_paket, 'id_detail' => $id_usulan);
                    // $this->db->update('geo_pok_ruas', $data, $array);
                }
            } else {
                $geomVal = $this->input->post("formData")["geom"];
                if (strlen($jembatan) !== 0) {
                    $cjem = $this->checkgeomjembatan($thang, $tahapan, $id_paket, $id_usulan);
                    if (count($cjem) > 0) {
                        $geom = "geometry::STGeomFromText('POINT($geomVal)',4326)";
                        $this->db->set('geom_pok1', $geom, false);
                        $this->db->where('id_detail', $id_usulan)->update('geo_pok_jembatan');
                    } else {
                        $geom = "geometry::STGeomFromText('POINT($geomVal)',4326)";
                        $this->db->set('geom_pok1', $geom, false);
                        $this->db->insert('geo_pok_jembatan', $data_geo);
                    }
                } else {
                    $cruas = $this->checkgeomruas($thang, $tahapan, $id_paket, $id_usulan);
                    if (count($cruas) > 0) {
                        $geo = explode(';', $geomVal);
                        $geom = "geometry::STGeomFromText('$geo[1]',4326)";
                        $this->db->set('geom_pok1', $geom, false);
                        $this->db->where('id_detail', $id_usulan)->update('geo_pok_ruas');
                    } else {
                        $geo = explode(';', $geomVal);
                        $geom = "geometry::STGeomFromText('$geo[1]',4326)";
                        $this->db->set('geom_pok1', $geom, false);
                        $this->db->insert('geo_pok_ruas', $data_geo);
                    }
                }
            }
        }

        $insert_id = $this->input->post("formData")["id_paket"];
        $idusulan = $this->input->post("formData")["id_usulan"];
        $dttags = $this->input->post("formData")["tags_val"];
        $data1 = json_decode($this->input->post("formData")["tags_val"], true);
        //echo $dttags;
        //die();
        $data_rujuk = array();
        $this->db->delete('rujukan_paket', array('id_usulan' => $idusulan));
        //$this->db->delete('rujukan_paket', 'id_usulan', $idusulan);
        if ($dttags === '[]') {
            
        } else {
            foreach ($data1 as $dataku) {
                // if ($dataku['kd_jns_rujukan'] == '1') {
                //     $v = $this->db->get_where('v_arahan_pagu_indikatif', array('sa1thn_id' => $dataku['value']))->row()->id;
                // } else {
                //     $v = $dataku['value'];
                // }
                $v = $dataku['value'];
                array_push($data_rujuk, array(
                    'id_paket' => $insert_id,
                    'id_usulan' => $idusulan,
                    'id_rujukan' => $v,
                    'kd_jns_rujukan' => $dataku['kd_jns_rujukan'],
                    'kd_tahapan' => $tahapan
                ));
            }

            $this->db->insert_batch('rujukan_paket', $data_rujuk);
            //echo $this->db->last_query();
        }

//        $data1=json_decode($this->input->post("formData")["tags_val"],true);
//        $data_rujuk = array();
//        foreach ($data1 as $dataku)
//        {
//            array_push($data_rujuk,array(
//                'id_paket'=>$array_detail['id_paket'],
//                'id_usulan'=>$id_usulan,
//                'id_rujukan'=>$dataku['value'],
//                'kd_jns_rujukan'=>$dataku['kd_jns_rujukan'],
//                'kd_tahapan'=>$this->session->konfig_kd_tahapan,
//            ));
//
//        }
//            $this->db->insert_batch('rujukan_paket', $data_rujuk);

        /**
          $key2=array("rams","irms","sipro","eprog","dpr","pemda","renstra");
          $array_key_post=[];
          foreach($data_tag as $key=>$values){
          $str_rj="";
          foreach($values as $key_values){
          $str_rj.=$key_values.",";

          }

          $this->db->where('id_usulan', $id_usulan);
          $this->db->update('detail_usulan_paket',array("rj_".strtolower($key)=>$str_rj));
          array_push($array_key_post[strtolower($key)],"");

          }

          for($i=0; $i <=count($key2)-1; $i++){
          if (array_key_exists($key2[$i],$array_key_post))
          {
          // echo "array key exists"."<br/>";
          }else{
          //echo "Array Key not exists...";
          $this->db->where('id_usulan', $id_usulan);
          $this->db->update('detail_usulan_paket',array("rj_".$key2[$i]=>""));

          }
          }
         * */
//      echo "<pre>";
//      print_r($data_tag);
//      echo "</pre>";
//        $data_tag = array(
//	"RENSTRA" => array(4,5,),
//	"SIPRO" => array(1,2,3)
//
//        );


        $arr_val1 = [];
        $arr_val2 = [];
        $arr_val3 = [];
        $arr_val4 = [];
        $arr_val5 = [];
        $arr_val6 = [];
        $arr_val7 = [];

        $data_state = array(
            "SIPRO" => $arr_val1,
            "RENSTRA" => $arr_val2,
            "RAMS" => $arr_val3,
            "IRMS" => $arr_val4,
            "EPROG" => $arr_val5,
            "DPR" => $arr_val6,
            "PEMDA" => $arr_val7
        );

        $res = array_merge($data_state, $data_tag);

//*
//$key2 = array("rj_rams","rj_irms","rj_sipro","rj_eprog","rj_dpr","rj_pemda","rj_renstra");
//        $i = 0;
//        $len = count($res);
//        $q = "UPDATE detail_usulan_paket SET ";
//        foreach ($res as $key => $val) {
//
//            $key = "rj_" . strtolower($key);
//
//
//            $vdata = implode(",", $val);
//
//            if (empty($vdata)) {
//                $ch = "'";
//            } else {
//                $ch = $vdata . ",'";
//            }
//
//
//            $q .= $key . " = '" . $ch;
//            if ($i == $len - 1) {
//                $q .= "";
//            } else {
//                $q .= ", ";
//            }
//            $i++;
//        }
//        $q .= " where id_usulan = " . $id_usulan;
//        //echo $q;
//        $this->db->query($q);
//       echo "<pre>";
//       print_r($data_sumberdana);
//       echo "</pre>";
//
//       die();
//        foreach ($data_sumberdana as $key => $values) {
//            //echo "<br/>".$key."<br/>";
//            //echo $values;
//            $this->db->where('id_usulan', $id_usulan);
//            if ($key == "pln") {
//                $this->db->update('detail_usulan_paket', array("phln" => $values));
//            }
//
//            if ($key == "rm") {
//                $this->db->update('detail_usulan_paket', array("rm" => $values));
//            }
//
//            if ($key == "rmp") {
//                $this->db->update('detail_usulan_paket', array("rmp" => $values));
//            }
//
//            if ($key != "pln" && $key != 'rm' && $key != 'rmp') {
//                $this->db->update('detail_usulan_paket', array($key => $values));
//            }
//        }

        $this->db->trans_complete();

        echo json_encode(array("status" => TRUE));
    }

    function check_master_exists($kd_dept, $kd_unit, $kd_program, $kd_kegiatan, $kd_output, $kd_sub_output, $kd_komponen, $kd_sub_komponen, $thang, $kd_satker, $tahapan) {
        $sringsql = "select id_paket from usulan_paket "
                . "where "
                . "kd_dept=" . "'" . $kd_dept . "'" .
                " and kd_unit=" . "'" . $kd_unit . "'" .
                " and kd_program='" . $kd_program . "'" .
                " and kd_kegiatan='" . $kd_kegiatan . "'" .
                " and kd_output='" . $kd_output . "'" .
                " and kd_sub_output='" . $kd_sub_output . "'" .
                " and kd_komponen='" . $kd_komponen . "'" .
                " and kd_sub_komponen='" . $kd_sub_komponen . "'" .
                " and thang='" . $thang . "'" .
                " and kdsatker='" . $kd_satker . "'" .
                " and kd_tahapan='" . $tahapan . "'";


        $query = $this->db->query($sringsql);
        $datas = json_decode(json_encode($query->result()), true);

        //print_r($datas);
        if (count($datas) == 0) {
            return array("count_row" => count($datas));
        } else {
            return array("count_row" => count($datas),
                "id_paket" => $datas[0]["id_paket"],
            );
        }
    }


    function trimToArray($data) {
        return str_replace(']','}',str_replace('[','{',$data));

    }

    

    function get_autocomplete() {

        $result = $this->M_model->search($_GET['term']);
        if (count($result) > 0) {
            foreach ($result as $row)
                $arr_result[] = array(
                    'label' => $row->nmakun,
                );

            echo json_encode($arr_result);
        } else {
            $arr_result[] = array(
                'label' => 'Tidak Ada Data Agen Untuk Kata Kunci Tersebut',
            );
            echo json_encode($arr_result);
        }
    }

    // function update_paket_pagu() {
    //     $param = $this->input->post('formData', TRUE);
    //     $this->wgisitia->handle_removed($param);
        
    //     $id_user = $this->session->users['id_user'];
    //     $kd_bujt = $this->session->users['kd_bujt'];
    //     $panjang = $this->input->post("formData")["panjang"];
    //     $lebar = $this->input->post("formData")["lebar"];
    //     $volume = $this->input->post("formData")["volume"];
    //     $tahun_perolehan = $this->input->post("formData")["tahun_perolehan"];
    //     $satuan = $this->input->post("formData")["satuan"];
    //     $long = $this->input->post("formData")["longitude"];
    //     $lat = $this->input->post("formData")["latitude"];
    //     if($panjang == ''){
    //         $panjang = NULL;
    //     }
    //     if($lebar == ''){
    //         $lebar = NULL;
    //     }
    //     if($volume == ''){
    //         $volume = NULL;
    //     }
    //     if($tahun_perolehan == ''){
    //         $tahun_perolehan = NULL;
    //     }
    //     if($long == ''){
    //         $long= NULL;
    //     }
    //     if($lat == ''){
    //         $lat= NULL;
    //     }
    //     if($this->input->post("formData")["kd_bujt"] != 0){
    //         $bujt = $this->input->post("formData")["kd_bujt"];
    //     }else{
    //         $db = $this->db->get_where('v_bujt_ruas',array('id_ruas'=>$this->input->post("formData")["id_ruas"]))->row();
    //         $bujt = $db->kd_bujt;
    //     }
    //     $data_detail = [
    //      //   "tahun" => (int)$this->input->post("formData")["tahun"],
    //         "id_ruas" => (int)$this->input->post("formData")["id_ruas"],
    //         "kd_bujt" => $bujt,
    //         "kd_jnsjembatan" => (int)$this->input->post("formData")["kd_jnsjembatan"],
    //         "id_jembatan" => (int)$this->input->post("formData")["id_jembatan"],
    //         "kuantitas" => (int)$this->input->post("formData")["kuantitas"],
    //         "nilai_perolehan" => (float)$this->input->post("formData")["nilai_perolehan"],
    //         "kondisi" => $this->input->post("formData")["kondisi"],
    //         "longitude" => $long,
    //         "latitude" => $lat,
    //         "kd_prov" => $this->input->post("formData")["kd_prov"],
    //         "kd_kabkot" => $this->input->post("formData")["kd_kabkot"],
    //         "kd_camat" => $this->input->post("formData")["kd_camat"],
    //         "kd_lurah" => $this->input->post("formData")["kd_lurah"],
    //         "kd_jnsbmn" => 2,
    //         "uptodate" => $this->session->users['id_user'],
    //         "id_aset_perolehan" => $this->input->post("formData")["id_aset_perolehan"],
    //     ];
    //     $data_r_jembatan = [
    //         "panjang" => $panjang,
    //         "lebar" => $lebar, //xx
    //         "volume" => $volume,
    //         "tahun_perolehan" => $tahun_perolehan,
    //         "satuan" => $this->input->post("formData")["satuan"]
    //     ];

    //     $this->db->where('id_jembatan', $this->input->post("formData")["id_jembatan"]);
    //     $this->db->update('aset_r_jembatan', $data_r_jembatan);
        

    //     $this->db->where('id', $this->input->post("formData")["id_paket"]);
    //     $this->db->update('aset_t_jembatan', $data_detail);

    //     //$this->db->set('created_at', 'getutcdate()', FALSE);
    //     $this->db->set('updated_at', 'getutcdate()', FALSE);
    //     $this->db->set('uptodate', $this->session->users['id_user'], FALSE);

    //     // }

    //     //$this->db->trans_complete();

    //     echo json_encode(array("status" => TRUE));
    // }

    public function get_wps2($provinsi_id) {
        $query = $this->db->query("select wps_kode,wps_nama,kws_kode FROM r_kws_dalam_wps where id_prov=" . $provinsi_id);
        $data_wps = json_decode(json_encode($query->result()), true);

        $arr = $data_wps;

        $result = array_values(array_column($arr, null, 'wps_nama'));

        //print_r( $result );
        echo json_encode($result);
    }

    public function get_kws2($kws_kode) {
        $query = $this->db->query("select kws_kode,kws_nama,subkawasan_nama FROM r_kws_dalam_wps where kws_kode='" . $kws_kode . "'");
        $data_kws = json_decode(json_encode($query->result()), true);

        $arr = $data_kws;

        $result = array_values(array_column($arr, null, 'kws_nama'));

        //print_r( $result );
        echo json_encode($result);
    }

    public function get_subkws2($kws_kode) {
        $query = $this->db->query("select kws_kode,subkawasan_nama
                                   from r_kws_dalam_wps
                                   where kws_kode='" . $kws_kode . "'");
        $data_subkws = json_decode(json_encode($query->result()), true);

        $arr = $data_subkws;

        $result = array_values(array_column($arr, null, 'subkawasan_nama'));

        //print_r( $result );
        echo json_encode($result);
    }

    public function get_prov_irmsv3($kd_bujt) {
        $query = $this->db->query("select kd_prov FROM aset_r_bujt where kd_bujt=" . $kd_bujt);
        $data_province = json_decode(json_encode($query->result()), true);

        return $data_province[0]["kd_prov_irmsv3"];
    }
    public function up() {
        if (is_array($_FILES)) {
            $x = str_replace('-', '', date('Y-m'));
            $nama_dir = FCPATH . 'uploads/' . $x . "/";

            if (is_dir($nama_dir)) {

            } else {
                mkdir(FCPATH . 'uploads/' . $x, 0777, true);
//            $m = FCPATH . 'uploads/' . str_replace('-', '', date('Y-m'));
            }
            $upload_path_url = str_replace('-', '', date('Y-m'));
            if (is_uploaded_file($_FILES['filess']['tmp_name'])) {
                $sourcePath = $_FILES['filess']['tmp_name'];
                $namf = $_FILES['filess']['name'];
                $rep = str_replace(" ", "_", $namf);
                $fil = date('Ymd') . date("his") . $rep;
                $targetPath = FCPATH . "uploads/" . $upload_path_url . "/" . $fil;
                move_uploaded_file($sourcePath, $targetPath);
               // $this->db->where('id_usulan', $id);


               $this->db->set('created_at', 'NOW()', FALSE);
               $this->db->set('updated_at', 'NOW()', FALSE);
               $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
               $this->db->set('created_by', $this->session->users['id_user'], FALSE);
                $res = $this->db->insert('dok_finclose',
                   array('path' => $fil,
                    'judul_dok' => $this->input->post('judul', TRUE),
                    // 'uptodate' => $this->session->users['id_user'],
                    'id_fclose' => $this->input->post('id_up', TRUE)));
        
            }
        }
    }
    public function ssp_attachment() {
        $id_usulan = $this->input->post("id", TRUE);
        $role = $this->input->post("role", TRUE);
        // echo $role." ***".$id_usulan; die();
        $table = 'dok_finclose';
        $primaryKey = 'id_dokfclose';
        $columns = array(
            array('db' => 'path', 'dt' => 0),
            array('db' => 'id_dokfclose', 'dt' => 1),
            array('db' => 'id_fclose', 'dt' => 2),
            array('db' => 'judul_dok', 'dt' => 3),
        );
        datatable_ssp($table, $primaryKey, $columns, "id_fclose=$id_usulan");
    }

    
    public function hps_lampiran($id) {

        $this->db->delete('dok_finclose', array('id_dokfclose' => $id));

        echo json_encode(array("status" => TRUE));
    }
    

    public function download($file_name) {
        // echo $file_name;
        $cut = substr($file_name, 0, 6);
        #file_name = str_replace(' ', '_',$file_name);
        $download_path = "uploads/" . $cut . "/" . $file_name;
        //echo $download_path;
        // force_download('/path/to/photo.jpg', NULL);
        //echo $download_path;
        force_download($download_path, NULL);
    }

    public function history($id, $datab) {
        $tahap = $this->session->konfig_kd_tahapan;
        if (substr($tahap, 0, 2) === 'RD'){
            $tahap = "like 'RD%'";
        } else {
            $tahap = "= '$tahap'";
        }
        if ($datab == 'detail') {
            $y = $this->db->query("SELECT * FROM [dbo].[v_histori_nama_detail_usulan_paket] WHERE id_usulan = $id and kd_tahapan $tahap ORDER BY updated_at DESC")->result_array();
        } else {
            $y = $this->db->query("SELECT
                nmgiat as 'Kegiatan',
                nmoutput as 'Output',
                nmsoutput as 'Sub Output',
                nmkmpnen as 'Komponen',
                nama_sub_komponen as  'Nama Sub Komponen',
                nama_ppk as 'PPK',
                nmsatker as 'Satker',
                nmkppn as 'KPPN',
                rc_ded_status as 'RC DED',
                rc_fs_status as 'RC FS',
                rc_doklin_status as 'RC DOKLING',
                rc_lahan_status as 'RC LAHAN',
                rab_status as 'RAB',
                kak_status as 'KAK',
                tgl_perubahan as 'Tanggal Perubahan',
                username as 'Diubah Oleh',
                jenis_kontrak as 'Jenis Kontrak',
                StatusUsulan1 as 'Status Usulan 1',
                StatusUsulan2 as 'Status Usulan 2',
                StatusUsulan3 as 'Status Usulan 3',
                catatan1 as 'Catatan Usulan 1',
                catatan2 as 'Catatan Usulan 2',
                catatan3 as ' Catatan Usulan 3'
                FROM [dbo].[v_histori_nama_usulan_paket] WHERE id_paket = $id and kd_tahapan $tahap ORDER BY updated_at DESC")->result_array();
        }
        if (count($y) < 2) {
            $html = "
                   <div class='page-header'>
                       <center><h4><small>DATA HISTORY ";
            $html .= "</small></h4></center>
                   </div><center>Belum Ada Data History</center>";
            echo $html;
            return;
        }

        $html = "

               <div class='block block-themed block-transparent remove-margin-b'>
                   <div class='page-header'>
                       <center><h4><small>CATATAN PERUBAHAN DATA";
        //            $html .= $y[0]['Detail Usulan'];
        $html .= "</small></h4></center>
                   </div>
                   <div class='timeline'>";

        for ($i = 0; $i < count($y); $i++) {

            if ($i < count($y) - 1) {
                //$dat = $y[$i]['Tanggal Perubahan'];
                //$teksperbaikan = str_replace("+07:00", "", $dat);

                $html .= "<h5 style='text-align:center;'> Di Ubah Oleh: " . $y[$i + 1]['Diubah Oleh'] . " </h5>";
                $html .= "<div class='separator text-muted' style='color:#fff'>
                                       <time>";
                //   $html .= "<div class='col-md-12'><div style='padding-bottom:10px;'><h5>oleh: " . $y[$i + 1]['Diubah Oleh'] . " </h5></div></div>";
                $html .= "</time>
                                   </div>

                                   <article class='panel panel-primary'>
                                       <div class='panel-heading icon'>
                                           <h4>";

                $html .= $i + 1;
                $html .= "
                                       </div>";

                $html .= "            <table class='table table-striped'>
                                      <thead>
                                               <tr>
                                                   <th>Item Data</th>
                                                   <th>Data Lama</th>
                                                   <th>Data Baru</th>
                                               </tr>
                                           </thead>";

                foreach ($y[$i] as $key => $val) {
                    if ($val == $y[$i + 1][$key])
                        echo '';
                    else {



                        $html .= "
                                               <tbody>
                                                   <tr>
                                                       <td>";
                        $html .= $key;
                        $html .= "</td>
                                                       <td>";
                        $html .= $y[$i + 1][$key];
                        $html .= "</td>
                                                       <td>";
                        $html .= $val;
                        $html .= "</td>
                                                   </tr>
                                               </tbody>";
                    }
                }
                $html .= "
                                          </table>
                               </article>'
                                       ";
            }
        }

        $html .= "

                               </div>
                               </div> ";

        echo $html;
    }

//     public function history($id, $datab) {
//         if ($datab == 'detail') {
//             $y = $this->db->query("SELECT * FROM [dbo].[v_histori_nama_detail_usulan_paket] WHERE id_usulan = $id  ORDER BY updated_at DESC")->result_array();
//         } else {
//             $y = $this->db->query("SELECT * FROM [dbo].[v_histori_nama_usulan_paket] WHERE id_paket = $id  ORDER BY updated_at DESC")->result_array();
//         }
//         if (count($y) < 2) {
//             $html = "
//                 <div class='page-header'>
//                     <center><h4><small>DATA HISTORY ";
//             $html .= "</small></h4></center>
//                 </div><center>Belum Ada Data History</center>";
//             echo $html;
//             return;
//         }
//
//         $html = "
//
//             <div class='block block-themed block-transparent remove-margin-b'>
//                 <div class='page-header'>
//                     <center><h4><small>CATATAN PERUBAHAN DATA";
// //            $html .= $y[0]['Detail Usulan'];
//         $html .= "</small></h4></center>
//                 </div>
//                 <div class='timeline'>";
//
//         for ($i = 0; $i < count($y); $i++) {
//
//             if ($i < count($y) - 1) {
//                 $dat = $y[$i]['Tanggal Perubahan'];
//                 $teksperbaikan = str_replace("+07:00", "", $dat);
//
//                 $html .= "<div class='separator text-muted' style='font-style:bold !important'>
//                                     <time>";
//                 $html .= "<div class='col-md-12'><div style='padding-bottom:10px;'><h5>Tanggal perubahan: " . $teksperbaikan . ", <br>oleh: " . $y[$i + 1]['Diubah Oleh'] . " </h5></div></div>";
//                 $html .= "</time>
//                                 </div>
//
//                                 <article class='panel panel-primary'>
//                                     <div class='panel-heading icon'>
//                                         <h4>";
//
//                 $html .= $i + 1;
//                 $html .= "
//                                     </div>";
//
//                 $html .= "            <table class='table table-striped'>
//                                    <thead>
//                                             <tr>
//                                                 <th>Item Data</th>
//                                                 <th>Data Lama</th>
//                                                 <th>Data Baru</th>
//                                             </tr>
//                                         </thead>";
//
//                 foreach ($y[$i] as $key => $val) {
//                     if ($key != "updated_at") {
//                         if ($val == $y[$i + 1][$key])
//                             echo '';
//                         else {
//
//
//                             $html .= "
//                                             <tbody>
//                                                 <tr>
//                                                     <td>";
//                             $html .= $key;
//                             $html .= "</td>
//                                                     <td>";
//                             $html .= $y[$i + 1][$key];
//                             $html .= "</td>
//                                                     <td>";
//                             $html .= $val;
//                             $html .= "</td>
//                                                 </tr>
//                                             </tbody>";
//                         }
//                     }
//                 }
//                 $html .= "
//                                        </table>
//                             </article>'
//                                     ";
//             }
//         }
//
//         $html .= "
//
//                             </div>
//                             </div> ";
//
//         echo $html;
//     }

    private function parent_role($roleid) {
        $parent_roles = '';
        $role = $roleid;
        $parent = -1;
        while ($parent != 0) {
            $ares = $this->db->get_where('user_group', ['id_user_group' => $role])->result_array();
            if (sizeof($ares) == 1) {
                $role = $ares[0]['id_user_group'];
                $parent = $ares[0]['id_sub_user_group'];
                $parent_roles .= $parent . ',';
                $role = $parent;
            } else {
                $parent = 0;
            }
        }
        return substr($parent_roles, 0, -1);
    }

    private function parent_satker($kd_bujt) {
        $parent_roles = '';
        $role = $kd_bujt;
        $parent = '-';
        $i = 1;
        while ($parent != '') {
            $ares = $this->db->get_where('v_satker', ['kdsatker' => $role])->result_array();
//            echo $this->db->last_query();
//         echo json_encode($ares);
            if (sizeof($ares) == 1) {
                $role = $ares[0]['kdsatker'];
                $parent = $ares[0]['kdinduk'];
                $parent_roles .= "'" . $parent . "',";
                $role = $parent;
                if ($role == $parent) {
                    $parent = '';
                }
            } else {
                $parent = '';
            }
            $i++;
        }
        return substr($parent_roles, 0, -1);
    }

    private function child_satker($kd_bujt) {
//        echo "step 1: $kd_bujt<br>";

        $child_roles = '';
        $role = $kd_bujt;
        $ares = $this->db->get_where('v_satker', ['kdinduk' => $role])->result_array();
        foreach ($ares as $child) {
            if ($child['kdsatker'] != $kd_bujt) {
                $child_roles .= "'" . $child['kdsatker'] . "',";
            }
        }
        return substr($child_roles, 0, -1);
    }

    public function getkomponensbysuboutput($kd_suboutput) {
        $query = $this->db->query("select "
                . "kdkmpnen', 'nmkmpnen', 'kdgiat', 'kdoutput', 'kdsoutput "
                . " FROM v_komponen where kdsoutput=" . $kd_suboutput);
        $data_komponen = json_decode(json_encode($query->result()), true);

        echo json_encode($data_komponen);
    }

    public function check_satker_if_array4($satker, $childsatker) {
        //echo $childsatker;

        $arrChSat = explode(',', $childsatker);
        $where = '';
        if (count($arrChSat) > 1) {
            $where .= "((kd_satker_balai='$satker')";
            for ($i = 0; $i < count($arrChSat); $i++) {

                $where .= " or (kd_satker_balai in ($arrChSat[$i]))";
            }

            $where .= ") and ((stat_usulan1=1) and (stat_usulan2=1))";

            $where = $where;
        } else {
            $where = "((kd_satker_balai='$satker') or (kd_satker_balai='$childsatker') )";
        }

        return $where;
    }

    public function check_satker_if_array($satker, $childsatker, $kd_prov_irms, $yearnow) {
        //echo $childsatker;

        $arrChSat = explode(',', $childsatker);
        $where = '';
        if (count($arrChSat) > 1) {
            $where .= "thang=$yearnow and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai='$satker')";
            for ($i = 0; $i < count($arrChSat); $i++) {

                $where .= " or (kd_satker_balai in ($arrChSat[$i]))";
            }

            $where .= ") and ((stat_usulan1=1) and (stat_usulan2=1))";

            $where = $where;
        } else {
            $where = "thang=$yearnow and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai='$satker') or (kd_satker_balai='$childsatker') )";
        }

        return $where;
    }

    public function check_satker_if_array2($satker, $childsatker, $yearnow) {
        //echo $childsatker;

        $arrChSat = explode(',', $childsatker);
        $where = '';
        if (count($arrChSat) > 1) {
            $where .= "(thang=$yearnow) and ((kd_bujt=$satker)";
            for ($i = 0; $i < count($arrChSat); $i++) {

                $where .= " or (kd_bujt in ($arrChSat[$i]))";
            }

            $where .= ")";

            $where = $where;
        } else {
            $where = "(thang=$yearnow) and ((kd_bujt=$satker) or (kd_bujt=$childsatker) )";
        }

        return $where;
    }

    public function check_satker_if_array3($satker, $childsatker, $kd_prov_irms, $yearnow) {
        //echo $childsatker;

        $arrChSat = explode(',', $childsatker);
        $where = '';
        if (count($arrChSat) > 1) {
            $where .= "thang=$yearnow and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai='$satker')";
            for ($i = 0; $i < count($arrChSat); $i++) {

                $where .= " or (kd_satker_balai in ($arrChSat[$i]))";
            }

            $where .= ") and (stat_usulan1=1)";

            $where = $where;
        } else {
            $where = "thang=$yearnow and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai='$satker') or (kd_satker_balai='$childsatker') )";
        }

        return $where;
    }

    public function check_satker_if_array5($satker, $childsatker, $yearnow, $kds, $kd_komponen, $kd_giat, $kd_output, $kd_sub_output) {
        //echo $childsatker;

        $arrChSat = explode(',', $childsatker);
        $where = '';
        if (count($arrChSat) > 1) {
            $where .= "(thang=$yearnow) and ((kd_bujt=$satker)";
            for ($i = 0; $i < count($arrChSat); $i++) {

                $where .= " or (kd_bujt in ($arrChSat[$i])) and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "'";
            }

            $where .= ")";

            $where = $where;
        } else {
            $where = "(thang=$yearnow) and ((kd_bujt=$satker) or (kd_bujt=$childsatker) )";
        }

        return $where;
    }

    public function setapproval() {

        $url = "http://localhost:12891/usulan/approval/approved";

        $id_usulan = $this->input->post("id_usulan", TRUE);
        $col_stat = $this->input->post("col_stat", TRUE);
        $val_stat = $this->input->post("val_stat", TRUE);

        if ($val_stat === "true") {
            $val_stat = 1;
        } else {
            $val_stat = 0;
        }

        $data_raw = array(
            "id_usulan" => $id_usulan,
            "col_stat" => $col_stat,
            "val_stat" => $val_stat
        );
        // echo "<pre>";
        // print_r($data_raw);
        // echo "</pre>";
        echo $this->insert_module($url, $data_raw);
    }

//    public function usulanlist()
//    {
//        $table = 'usulan_dpr';
//        $primaryKey = 'id_usulan';
//
//        $columns = array(
//            array('db' => 'id_usulan', 'dt' => 0),
//            array('db' => 'thang', 'dt' => 1),
//            array('db' => 'detail', 'dt' => 2)
//        );
//        datatable_ssp($table, $primaryKey, $columns);
//    }

    public function ssp_usulanpemda() {
        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();

        $kd_prov_irms = $query->row()->kd_prov_irmsv3;

        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $subrole = $this->session->users['id_user_group'];
        $roledesc = $this->session->users['role'];
        $yearnow = $this->session->konfig_tahun_ang;
        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);
        $whereClause = $this->check_satker_if_array3($satker, $childsatker, $kd_prov_irms, $yearnow);

        $table = 'v_usulan_pemda';
        $primaryKey = 'id_usulan';

        $columns = array(
            array('db' => 'id_usulan', 'dt' => 0),
            array('db' => 'thang', 'dt' => 1),
            array('db' => 'detail', 'dt' => 2),
            //ids
            array('db' => 'sumber_usulan', 'dt' => 3),
            array('db' => 'tgl_usulan', 'dt' => 4),
            array('db' => 'pengusul2', 'dt' => 5),
            array('db' => 'nama_prov', 'dt' => 6),
            array('db' => 'rkakl_volume', 'dt' => 7),
            array('db' => 'rkakl_biaya', 'dt' => 8, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                }),
            array('db' => 'status_kewenangan', 'dt' => 9),
            array('db' => 'evaluasi', 'dt' => 10),
            array('db' => 'rc_fs', 'dt' => 11),
            array('db' => 'rc_ded', 'dt' => 12),
            array('db' => 'rc_lahan', 'dt' => 13),
            array('db' => 'rc_dokling', 'dt' => 14),
            array('db' => 'keterangan', 'dt' => 15),
            array('db' => 'usulan_volume', 'dt' => 16),
            array('db' => 'usulan_biaya', 'dt' => 17, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                })

                //detail, tgl_usulan, sumber_usulan, status_kewenangan, rc_fs, rc_ded, rc_lahan, rc_dokling, keterangan
        );
        //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
        if ($id_user == 1 || $id_user == 2 || $roledesc == 'span' || $roledesc == 'dpsi' || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                datatable_ssp($table, $primaryKey, $columns, $whereClause);
            } else {
                //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
                if ($roledesc == 'jbh' || $roledesc == 'konstruksi' || $roledesc == 'bmn' || $roledesc == 'renwilI' || $roledesc == 'renwilII' || $roledesc == 'stppreservasi') {
                    datatable_ssp($table, $primaryKey, $columns, "thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and kd_satker_balai='$subrole'");
                } else {
                    datatable_ssp($table, $primaryKey, $columns, "thang=$yearnow and kddisposisi='$satker' and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1");
                    // $parentsatker = $this->parent_satker($satker);
                    //  datatable_ssp($table, $primaryKey, $columns, "((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and ((stat_usulan1=1) and (stat_usulan2=1)) and ((kddisposisi is not null) and (kddisposisi = '$satker'))");
                    //$parentsatker = $this->parent_satker($satker);
                    //datatable_ssp($table, $primaryKey, $columns, "(thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and ((stat_usulan1=1)) and ((kddisposisi is not null) and (kddisposisi = '$satker'))) or (thang = $yearnow-1 and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and (stat_usulan2=3) and ((kddisposisi is not null) and (kddisposisi = '$satker')))");
                }
            }
        }
    }

    public function ssp_usulandpr() {
        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();

        $kd_prov_irms = $query->row()->kd_prov_irmsv3;

        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $subrole = $this->session->users['id_user_group'];
        $roledesc = $this->session->users['role'];

        $yearnow = $this->session->konfig_tahun_ang;

        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);

        $whereClause = $this->check_satker_if_array($satker, $childsatker, $kd_prov_irms, $yearnow);

        $table = 'v_usulan_dpr';
        $primaryKey = 'id_usulan';

        $columns = array(
            array('db' => 'id_usulan', 'dt' => 0),
            array('db' => 'thang', 'dt' => 1),
            array('db' => 'detail', 'dt' => 2),
            //ids
            array('db' => 'sumber_usulan', 'dt' => 3),
            array('db' => 'tgl_usulan', 'dt' => 4),
            array('db' => 'pengusul2', 'dt' => 5),
            array('db' => 'nama_prov', 'dt' => 6),
            array('db' => 'rkakl_volume', 'dt' => 7),
            array('db' => 'rkakl_biaya', 'dt' => 8, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                }),
            array('db' => 'status_kewenangan', 'dt' => 9),
            array('db' => 'evaluasi', 'dt' => 10),
            array('db' => 'rc_fs', 'dt' => 11),
            array('db' => 'rc_ded', 'dt' => 12),
            array('db' => 'rc_lahan', 'dt' => 13),
            array('db' => 'rc_dokling', 'dt' => 14),
            array('db' => 'keterangan', 'dt' => 15),
            array('db' => 'usulan_volume', 'dt' => 16),
            array('db' => 'usulan_biaya', 'dt' => 17, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                })

                //detail, tgl_usulan, sumber_usulan, status_kewenangan, rc_fs, rc_ded, rc_lahan, rc_dokling, keterangan
        );

        //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
        if ($id_user == 1 || $id_user == 2 || $roledesc == 'span' || $roledesc == 'dpsi' || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                datatable_ssp($table, $primaryKey, $columns, $whereClause);
            } else {
                //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
                if ($roledesc == 'jbh' || $roledesc == 'konstruksi' || $roledesc == 'bmn' || $roledesc == 'renwilI' || $roledesc == 'renwilII' || $roledesc == 'stppreservasi') {
                    datatable_ssp($table, $primaryKey, $columns, "thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and kd_satker_balai='$subrole'");
                } else {
                    datatable_ssp($table, $primaryKey, $columns, "thang=$yearnow and kddisposisi='$satker' and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1");
                    //$parentsatker = $this->parent_satker($satker);
                    //datatable_ssp($table, $primaryKey, $columns, "(thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and ((stat_usulan1=1)) and ((kddisposisi is not null) and (kddisposisi = '$satker'))) or (thang = $yearnow-1 and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and (stat_usulan2=3) and ((kddisposisi is not null) and (kddisposisi = '$satker')))");
                }
            }
        }
    }

    public function ssp_renstra() {
        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $roledesc = $this->session->users['role'];
        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);

        $table = 'v_renstra_target';
        $primaryKey = 'id_renstra';

        $columns = array(
            array('db' => 'id_renstra', 'dt' => 0),
            array('db' => 'id_target', 'dt' => 1),
            array('db' => 'target', 'dt' => 2),
            array('db' => 'nilai', 'dt' => 3),
            array('db' => 'satuan', 'dt' => 4),
            array('db' => 'tahun', 'dt' => 5)
                //detail, tgl_usulan, sumber_usulan, status_kewenangan, rc_fs, rc_ded, rc_lahan, rc_dokling, keterangan
        );

        //if ($role == 3 || $id_user == 1 || $id_user == 2 || $id_user == 4 || $id_user == 5 || $role == 6 || $role == 1) {
        if ($roledesc == 'satkerfisik' || $id_user == 1 || $id_user == 2 || $id_user == 4 || $id_user == 5 || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                datatable_ssp($table, $primaryKey, $columns);
            } else {
                $parentsatker = $this->parent_satker($satker);
                datatable_ssp($table, $primaryKey, $columns);
            }
        }
    }

    public function siprolist() {
        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $subrole = $this->session->users['id_user_group'];
        $roledesc = $this->session->users['role'];

        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);

        $whereClause = $this->check_satker_if_array4($satker, $childsatker);

        $table = 'v_rujukan_sipro';
        $primaryKey = 'sa1thn_id';

        $columns = array(
            array('db' => 'sa1thn_id', 'dt' => 0),
            array('db' => 'kegiatan_nama', 'dt' => 1),
            array('db' => 'output_nama', 'dt' => 2),
            array('db' => 'suboutput_nama', 'dt' => 3),
            array('db' => 'aktivitas_nama', 'dt' => 4),
            array('db' => 'sub_aktivitas', 'dt' => 5),
            array('db' => 'tahun_anggaran', 'dt' => 6),
            array('db' => 'volume', 'dt' => 7),
            array('db' => 'rpm', 'dt' => 8, 'formatter' => function( $d, $row ) {
                    return number_format($d, 0, ',', '.');
                }),
            array('db' => 'phln', 'dt' => 9, 'formatter' => function( $d, $row ) {
                    return number_format($d, 0, ',', '.');
                }),
            array('db' => 'sbsn', 'dt' => 10, 'formatter' => function( $d, $row ) {
                    return number_format($d, 0, ',', '.');
                }),
            array('db' => 'rmp', 'dt' => 11, 'formatter' => function( $d, $row ) {
                    return number_format($d, 0, ',', '.');
                }),
            array('db' => 'id', 'dt' => 12)
        );

        //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
        if ($id_user == 1 || $id_user == 2 || $roledesc == 'span' || $roledesc == 'dpsi' || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                datatable_ssp($table, $primaryKey, $columns, "kd_satker_balai='$satker'");
            } else {
                //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
                if ($roledesc == 'jbh' || $roledesc == 'konstruksi' || $roledesc == 'bmn' || $roledesc == 'renwilI' || $roledesc == 'renwilII' || $roledesc == 'stppreservasi') {
                    datatable_ssp($table, $primaryKey, $columns, "kd_satker_balai='$subrole'");
                } else {
                    $kdsatker = datatable_ssp($table, $primaryKey, $columns, " satkerid =" . $this->getKdProvFromVsatker($kdsatker));
//                    echo $parent_satker;
                    //$whosparent = $parent_satker;
                    //echo $parent_satker;
                    //datatable_ssp($table, $primaryKey, $columns, "kd_satker_balai=$parent_satker");
                }
            }
        }
    }

    public function siprolist_pagu() {
        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $subrole = $this->session->users['id_user_group'];
        $roledesc = $this->session->users['role'];
        $yearnow = $this->session->konfig_tahun_ang;

        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);

        $whereClause = $this->check_satker_if_array4($satker, $childsatker);

        $table = 'vw_rujuk_indikatif';
        $primaryKey = 'sa1thn_id';

        $columns = array(
            array('db' => 'sa1thn_id', 'dt' => 0), //ditampilkan//tarik
            array('db' => 'jenis_arahan', 'dt' => 1), //ditampilkan//tarik
            array('db' => 'nmgiat', 'dt' => 2), //tarik
            array('db' => 'nmoutput', 'dt' => 3), //tarik
            array('db' => 'nmsoutput', 'dt' => 4), //tarik
            array('db' => 'nmkmpnen', 'dt' => 5), //tarik
            array('db' => 'nama_sub_komponen', 'dt' => 6),
            array('db' => 'rc_fs_status', 'dt' => 7), //tarik
            array('db' => 'rc_ded_status', 'dt' => 8), //tarik
            array('db' => 'rc_lahan_status', 'dt' => 9),
            array('db' => 'rc_doklin_status', 'dt' => 10),
            array('db' => 'nmisu', 'dt' => 11), //tarik
            array('db' => 'subkawasan_nama', 'dt' => 12), //tarik
            //array('db' => 'role_user_stat', 'dt' => 13), //tarik
            array('db' => 'thang', 'dt' => 14), //tarik
            //array('db' => 'role_user_stat', 'dt' => 15), //tarik
            array('db' => 'kd_kegiatan', 'dt' => 16), //tarik
            array('db' => 'kd_output', 'dt' => 17), //tarik
            array('db' => 'kd_sub_output', 'dt' => 18), //tarik
            array('db' => 'kd_komponen', 'dt' => 19), //tarik
            array('db' => 'nama_sub_komponen', 'dt' => 20), //tarik
            array('db' => 'kdlokasi', 'dt' => 21), //tarik
            array('db' => 'kdkabkota', 'dt' => 22), //tarik
            array('db' => 'kd_sub_komponen', 'dt' => 23), //tarik
            array('db' => 'sa1thn_id', 'dt' => 24), //tarik
            array('db' => 'volume', 'dt' => 25), //tarik
            array('db' => 'satuan_output', 'dt' => 26), //tarik
            array('db' => 'rpm', 'dt' => 27), //tarik
            array('db' => 'phln', 'dt' => 28), //tarik
            array('db' => 'sbsn', 'dt' => 29), //tarik
            array('db' => 'rmp', 'dt' => 30), //tarik
        );


        //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
        if ($id_user == 1 || $id_user == 2 || $roledesc == 'span' || $roledesc == 'dpsi' || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                //datatable_ssp($table, $primaryKey, $columns, "kd_satker_balai='$satker'");
                //datatable_ssp($table, $primaryKey, $columns, " kdinduk='$satker'");
                //datatable_ssp($table, $primaryKey, $columns, " kdinduk='$satker' and status = 'KONREG_FINAL'");
                datatable_ssp($table, $primaryKey, $columns, " kdinduk='$satker' and thang = $yearnow");
            } else {
                //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
                if ($roledesc == 'jbh' || $roledesc == 'konstruksi' || $roledesc == 'bmn' || $roledesc == 'renwilI' || $roledesc == 'renwilII' || $roledesc == 'stppreservasi') {
                    //datatable_ssp($table, $primaryKey, $columns, "kd_satker_balai='$subrole'");
                    //datatable_ssp($table, $primaryKey, $columns, " kdinduk='$subrole'");
                    //datatable_ssp($table, $primaryKey, $columns, " kdinduk='$subrole' and status = 'KONREG_FINAL'");
                    datatable_ssp($table, $primaryKey, $columns, " kdinduk='$subrole' and thang = $yearnow");
                } else {
                    //datatable_ssp($table, $primaryKey, $columns, " kdlokasi =" . $this->getKdProvFromVsatker($satker));
                    //datatable_ssp($table, $primaryKey, $columns, " kdlokasi =" . $this->getKdProvFromVsatker($satker) . " and status = 'KONREG_FINAL'");
                    //$kdsatker = datatable_ssp($table, $primaryKey, $columns, " satkerid ='$satker' and thang = $yearnow");
                    datatable_ssp($table, $primaryKey, $columns, " kdlokasi =" . $this->getKdProvFromVsatker($satker) . " and thang = $yearnow");
                }
            }
        }
    }

    function getKdProvFromVsatker($kdsatkker) {
        $query = $this->db->query("select kd_prov from v_prov_satker where kdsatker=" . $kdsatkker);
        $data_kode_prov = json_decode(json_encode($query->result()), true);
        //echo $data_province[0]["kd_prov_irmsv3"];
        //echo $data_kode_prov[0]["kd_prov"];
        return $data_kode_prov[0]["kd_prov"];
    }

    public function pavlist() {
        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();

        $kd_prov_irms = $query->row()->kd_prov_irmsv3;



        $idruas = $this->input->post('noruas', TRUE);

//        if ($arrRuas == -1) {
//
//            $idruas = '#';
//        } else {
//            $idruas = explode("::", $arrRuas)[1];
//        }
//        if ($idruas == '#') {
//
//        } else {
        $kdruas = $idruas;
        $staa = $this->input->post('staa', TRUE);
        $stae = $this->input->post('stae', TRUE);

//    }
        $table = 'v_rujukan_irms';
        $primaryKey = 'id';

        $columns = array(
            array('db' => 'id', 'dt' => 0),
            array('db' => 'plan_year', 'dt' => 1),
            array('db' => 'budget_group', 'dt' => 2),
            array('db' => 'treatment', 'dt' => 3),
            array('db' => 'estimated_cost', 'dt' => 4,
                'formatter' => function( $d, $row ) {
                    return number_format($d, 0, ',', '.');
                }
            ),
            array('db' => 'road_name', 'dt' => 5),
            array('db' => 'linkname', 'dt' => 6),
            array('db' => 'direction', 'dt' => 7),
            array('db' => 'lane', 'dt' => 8),
            array('db' => 'bmp', 'dt' => 9),
            array('db' => 'emp', 'dt' => 10),
            array('db' => 'mwp_project_status', 'dt' => 11)
        );

        if (($idruas == '' or $idruas == null) && ($staa == '' or $staa == null) && ($stae == '' or $stae == null)) {
            datatable_ssp($table, $primaryKey, $columns, "kd_prov =" . $kd_prov_irms);
        } else if (($idruas != '' or $idruas != null) && ($staa == '' or $staa == null) && ($stae == '' or $stae == null)) {
            datatable_ssp($table, $primaryKey, $columns, "road_name = '$kdruas'");
        } else if ($idruas != '' && $staa != '' && ($stae == '' or $stae == null or $stae == '')) {
            datatable_ssp($table, $primaryKey, $columns, "road_name='$kdruas' and bmp >= $staa ");
        } else if ($idruas != '' && ($stae == '' or $stae == null or $stae == '') && ($stae == '' or $stae == null or $stae == '')) {
            datatable_ssp($table, $primaryKey, $columns, "road_name='$kdruas' and bmp >= $staa ");
        } else {
            datatable_ssp($table, $primaryKey, $columns, "road_name='$kdruas' and (bmp >= $staa and emp <= $stae)");
        }


//        if ($idruas != '#' && $staa == '#' && $stae == '#') {
//            datatable_ssp($table, $primaryKey, $columns, "road_name = '$kdruas'");
//        } else if ($idruas != '#' && $staa != '#' && $stae != '#') {
//            if (empty($staa) && empty($stae)) {
//                datatable_ssp($table, $primaryKey, $columns, "road_name='$kdruas'");
//            } else {
//                datatable_ssp($table, $primaryKey, $columns, "road_name='$kdruas' and (bmp >= $staa and emp <= $stae)");
//            }
//        } else {
//            datatable_ssp($table, $primaryKey, $columns, "kd_prov =" . $kd_prov_irms);
//        }
    }

    public function ramslist() {

        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();


        $kd_prov_irms = $query->row()->kd_prov_irmsv3;


        $arrRuas = $this->input->post('noruas', TRUE);

        if ($arrRuas == -1) {
            $idruas = '#';
        } else {
            $idruas = explode("::", $arrRuas)[2];
        }


        if ($idruas == '#') {
            
        } else {
            $kdruas = $idruas;
            $staa = $this->input->post('staa', TRUE);
            $stae = $this->input->post('stae', TRUE);
        }



        $table = 'v_rams_treatment';
        $primaryKey = 'id';

        $columns = array(
            array('db' => 'id', 'dt' => 0),
            array('db' => 'sectionid', 'dt' => 1),
            array('db' => 'section_name', 'dt' => 2),
            array('db' => 'start_m', 'dt' => 3),
            array('db' => 'end_m', 'dt' => 4),
            array('db' => 'lane', 'dt' => 5),
            array('db' => 'cost_total', 'dt' => 6),
            array('db' => 'cost_pav', 'dt' => 7),
            array('db' => 'cost_bahu', 'dt' => 8),
            array('db' => 'kd_treatment', 'dt' => 9)
        );

        if ($idruas != '#' && $staa == '#' && $stae == '#') {
            datatable_ssp($table, $primaryKey, $columns, "sectionid = $kdruas");
        } else if ($idruas != '#' && $staa != '#' && $stae != '#') {
            if (empty($staa) && empty($stae)) {
                datatable_ssp($table, $primaryKey, $columns, "sectionid=$kdruas");
            } else {
                datatable_ssp($table, $primaryKey, $columns, "sectionid=$kdruas and (start_m >= $staa and end_m <= $stae)");
            }
        } else {
            datatable_ssp($table, $primaryKey, $columns, "kd_prov =" . $kd_prov_irms);
        }
    }

    public function ssp_detail() {
        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $alias = $this->session->users['role'];

        $id_paket = $this->input->post('id_paket', TRUE);
        $thang = $this->input->post('thang', TRUE);
        $kdsatker = $this->input->post('kdsatker', TRUE);
        $kds = $this->input->post('kd_sub_komponen', TRUE);
        $kd_komponen = $this->input->post('kd_komponen', TRUE);
        $kd_giat = $this->input->post('kd_giat', TRUE);
        $kd_output = $this->input->post('kd_output', TRUE);
        $kd_sub_output = $this->input->post('kd_sub_output', TRUE);
        //$yearnow = $this->session->konfig_tahun_ang;
        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);
        $kd_tahapan = $this->input->post('konfig_tahapan', TRUE);
        $id = $this->input->post('id_paket', TRUE);
//        echo $kd_tahapan;
        if (empty($id_paket)) {
            $id_paket = 0;
        } else {
            $id_paket = $id_paket;
        }
        
        if (substr($kd_tahapan, 0, 2) === 'RD'){
            $tap = "like 'RD%'";
        } else {
            $tap = "= '$kd_tahapan'";
        }

        //$whereClause = $this->check_satker_if_array5($satker, $childsatker, $yearnow, $kds, $kd_komponen, $kd_giat, $kd_output, $kd_sub_output);

        $table = 'vw_paket_detail';
        $primaryKey = 'id_usulan'; //test

        $columns = array(
            array('db' => 'tags_val', 'dt' => 0),
            array('db' => 'id_usulan', 'dt' => 1),
            array('db' => 'thang', 'dt' => 2),
//            array('db' => 'nmgiat', 'dt' => 2),
//            array('db' => 'nmoutput', 'dt' => 3),
//            array('db' => 'nmsoutput', 'dt' => 4),
//            array('db' => 'nmkmpnen', 'dt' => 5),
            array('db' => 'nama_sub_komponen', 'dt' => 3),
            array('db' => 'detail', 'dt' => 4),
            array('db' => 'linkname', 'dt' => 5),
            array('db' => 'sta_awal', 'dt' => 6),
            array('db' => 'sta_akhir', 'dt' => 7),
            array('db' => 'nama_jembatan', 'dt' => 8),
            array('db' => 'longitude', 'dt' => 9),
            array('db' => 'latitude', 'dt' => 10),
            array('db' => 'treatment', 'dt' => 11),
            array('db' => 'volume', 'dt' => 12),
            array('db' => 'satuan', 'dt' => 13),
            array('db' => 'nmgbkpk', 'dt' => 14),
            array('db' => 'nmakun', 'dt' => 15),
            array('db' => 'jumlah', 'dt' => 16, 'formatter' => function( $d, $row ) {
                    return number_format($d, 0, ",", ".");
                }),
            array('db' => 'no_ruas', 'dt' => 17),
            array('db' => 'rj_rams', 'dt' => 18),
            array('db' => 'rj_irms', 'dt' => 19),
            array('db' => 'rj_renstra', 'dt' => 20),
            array('db' => 'rj_eprog', 'dt' => 21),
            array('db' => 'rj_dpr', 'dt' => 22),
            array('db' => 'longitude2', 'dt' => 23),
            array('db' => 'latitude2', 'dt' => 24),
            array('db' => 'nmctarik', 'dt' => 25),
            array('db' => 'data_usulan', 'dt' => 26),
//            array('db' => 'rm', 'dt' => 16),
//            array('db' => 'rmp', 'dt' => 17),
//            array('db' => 'phln', 'dt' => 18),
//            array('db' => 'pdn', 'dt' => 19),
//            array('db' => 'pdp', 'dt' => 20),
//            array('db' => 'sbsn', 'dt' => 21),
//            array('db' => 'blu', 'dt' => 22),
//            array('db' => 'pnbp', 'dt' => 23),
//            array('db' => 'opr', 'dt' => 24)
        );
                
        datatable_ssp($table, $primaryKey, $columns, "id_paket =" . $id . " and jumlah <> 0 ");


//        if ($this->session->konfig_kd_tahapan === 'X' || $alias == 'pakln') {
//            //datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and kd_tahapan='$kd_tahapan' and (kdheader is null or trim(kdheader) = '')");
//            datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and kd_tahapan $tap and jumlah <> 0 ");
//        } else {
//            //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
//            if ($id_user == 1 || $id_user == 2 || $alias == 'span' || $alias == 'dpsi' || $alias == 'ksjj' || $alias == 'admin') {
//                //datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and (kdheader is null or trim(kdheader) = '')");
//                datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and jumlah <> 0");
//                //datatable_ssp($table, $primaryKey, $columns, "thang=$yearnow");
//            } else {
//                if ($childsatker !== '') {
//                    //datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_bujt='" . $satker . "' and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and kd_tahapan='$kd_tahapan' and (kdheader is null or trim(kdheader) = '')");
//                    datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_bujt='" . $satker . "' and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and kd_tahapan $tap and jumlah <> 0");
//                    //  datatable_ssp($table, $primaryKey, $columns, "kd_bujt=$satker and thang=$yearnow");
//                } else {
//                    //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
//                    if ($alias == 'jbh' || $alias == 'konstruksi' || $alias == 'bmn' || $alias == 'renwilI' || $alias == 'renwilII' || $alias == 'stppreservasi') {
//                        //datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and (kdheader is null or trim(kdheader) = '')");
//                        datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and jumlah <> 0");
//                    } else {
//                        //datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_bujt='" . $satker . "' and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and kd_tahapan='$kd_tahapan' and (kdheader is null or trim(kdheader) = '')");
//                        datatable_ssp($table, $primaryKey, $columns, "thang=" . $yearnow . " and kd_bujt='" . $satker . "' and kd_sub_komponen='" . $kds . "' and kd_komponen='" . $kd_komponen . "' and kd_kegiatan='" . $kd_giat . "' and kd_output='" . $kd_output . "' and kd_sub_output='" . $kd_sub_output . "' and kd_tahapan $tap and jumlah <> 0");
//                    }
//                }
//            }
//        }
    }

    function get_jml_data() {
        $satker = $this->session->users['kd_bujt'];
        echo $this->db->query("SELECT COUNT(jml_detail_blm_lngkp) as jml FROM vw_paket_status where kd_bujt='$satker' and jml_detail_blm_lngkp <>0 and kd_tahapan='PPI'")->row()->jml;
    }

    public function get_kompetensi() {

        $usergroup = $this->session->users['id_user_group_real'];
        $q = $this->db->get_where('r_pagu', array('kompetensi' => $usergroup, 'kdgiat' => '2409'))->result_array();
        echo json_encode($q);
    }

    public function sum_batas($satu, $dua) {
        error_reporting(3);
        //if ($this->session->users['id_user_group_real'] == 60) {
        if ($this->session->users['role'] == 'bidrensiren') {
            $idg = " and kdgiat=2409";
            $idg1 = "2409";
            //} elseif ($this->session->users['id_user_group_real'] == 4) {
        } elseif ($this->session->users['role'] == 'span') {
            $idg = " and kdgiat != 2409";
        }
        if ($dua == 'kosong' and $satu != '') {
            $dt = "kd_prov_rkakl =$satu and  flag = 'PAGU PROVINSI'" . $idg;
//            echo "satu";
        } else if ($satu == '-1' and $dua != '') {
            $pro = $this->db->get_where('v_balai_prov', array('kd_satker_balai' => $this->session->users['kd_bujt']))->result_array();
            if ($pro[0]['kd_prov_rkakl'] != '' and $pro[1]['kd_prov_rkakl'] == '' and $pro[2]['kd_prov_rkakl'] == '' and $pro[3]['kd_prov_rkakl'] == '' and $pro[4]['kd_prov_rkakl'] == '' and $pro[5]['kd_prov_rkakl'] == '') {
                $kosong = $pro[0]['kd_prov_rkakl'];
                $dt = "kdoutput = '$dua' AND flag= 'PAGU PROVINSI' AND kd_prov_rkakl='$kosong'" . $idg;
            } else if ($pro[0]['kd_prov_rkakl'] != '' and $pro[1]['kd_prov_rkakl'] != '' and $pro[2]['kd_prov_rkakl'] == '' and $pro[3]['kd_prov_rkakl'] == '' and $pro[4]['kd_prov_rkakl'] == '' and $pro[5]['kd_prov_rkakl'] == '') {
                $kosong = $pro[0]['kd_prov_rkakl'];
                $satuu = $pro[1]['kd_prov_rkakl'];
                $dt = "kdoutput = '$dua' AND flag= 'PAGU PROVINSI' AND (kd_prov_rkakl='$kosong' or kd_prov_rkakl='$satuu')" . $idg;
            } else if ($pro[0]['kd_prov_rkakl'] != '' and $pro[1]['kd_prov_rkakl'] != '' and $pro[2]['kd_prov_rkakl'] != '' and $pro[3]['kd_prov_rkakl'] == '' and $pro[4]['kd_prov_rkakl'] == '' and $pro[5]['kd_prov_rkakl'] == '') {
                $kosong = $pro[0]['kd_prov_rkakl'];
                $satuu = $pro[1]['kd_prov_rkakl'];
                $duaa = $pro[2]['kd_prov_rkakl'];
                $dt = "kdoutput = '$dua' AND flag= 'PAGU PROVINSI' AND (kd_prov_rkakl='$kosong' or kd_prov_rkakl='$satuu' or kd_prov_rkakl='$duaa')" . $idg;
            } else if ($pro[0]['kd_prov_rkakl'] != '' and $pro[1]['kd_prov_rkakl'] != '' and $pro[2]['kd_prov_rkakl'] != '' and $pro[3]['kd_prov_rkakl'] != '' and $pro[4]['kd_prov_rkakl'] == '' and $pro[5]['kd_prov_rkakl'] == '') {
                $kosong = $pro[0]['kd_prov_rkakl'];
                $satuu = $pro[1]['kd_prov_rkakl'];
                $duaa = $pro[2]['kd_prov_rkakl'];
                $tiga = $pro[3]['kd_prov_rkakl'];
                $dt = "kdoutput = '$dua' AND flag= 'PAGU PROVINSI' AND (kd_prov_rkakl='$kosong' or kd_prov_rkakl='$satuu' or kd_prov_rkakl='$duaa' or kd_prov_rkakl='$tiga')" . $idg;
            } else if ($pro[0]['kd_prov_rkakl'] != '' and $pro[1]['kd_prov_rkakl'] != '' and $pro[2]['kd_prov_rkakl'] != '' and $pro[3]['kd_prov_rkakl'] != '' and $pro[4]['kd_prov_rkakl'] != '' and $pro[5]['kd_prov_rkakl'] == '') {
                $kosong = $pro[0]['kd_prov_rkakl'];
                $satuu = $pro[1]['kd_prov_rkakl'];
                $duaa = $pro[2]['kd_prov_rkakl'];
                $tiga = $pro[3]['kd_prov_rkakl'];
                $empat = $pro[4]['kd_prov_rkakl'];
                $dt = "kdoutput = '$dua' AND flag= 'PAGU PROVINSI' AND (kd_prov_rkakl='$kosong' or kd_prov_rkakl='$satuu' or kd_prov_rkakl='$duaa' or kd_prov_rkakl='$tiga' or kd_prov_rkakl='$empat')" . $idg;
            } else if ($pro[0]['kd_prov_rkakl'] != '' and $pro[1]['kd_prov_rkakl'] != '' and $pro[2]['kd_prov_rkakl'] != '' and $pro[3]['kd_prov_rkakl'] != '' and $pro[4]['kd_prov_rkakl'] != '' and $pro[5]['kd_prov_rkakl'] != '') {
                $kosong = $pro[0]['kd_prov_rkakl'];
                $satuu = $pro[1]['kd_prov_rkakl'];
                $duaa = $pro[2]['kd_prov_rkakl'];
                $tiga = $pro[3]['kd_prov_rkakl'];
                $empat = $pro[4]['kd_prov_rkakl'];
                $lima = $pro[5]['kd_prov_rkakl'];
                $dt = "kdoutput = '$dua' AND flag= 'PAGU PROVINSI' AND (kd_prov_rkakl='$kosong' or kd_prov_rkakl='$satuu' or kd_prov_rkakl='$duaa' or kd_prov_rkakl=$tiga or kd_prov_rkakl='$empat' or kd_prov_rkakl='$lima')" . $idg;
            }

//            echo "dua";
        } else if ($satu == $dua) {
            $dt = "kdoutput = $dua and flag = 'PAGU KOMPETENSI'" . $idg;
        } else {
            $dt = "kd_prov_rkakl = $satu and kdoutput = $dua and flag = 'PAGU PROVINSI'" . $idg;
//            echo "tiga";
        }
        $this->db->select_sum('jumlah');
        $this->db->where($dt);
        $query = $this->db->get('v_ref_batas_rekap_pagu')->row()->jumlah;
        $qur = number_format($query);
        echo str_replace(',', '', $qur);
    }

    public function sum_usulan($kdk, $kdo) {
        //if ($this->session->users['id_user_group_real'] == 60) {
        if ($this->session->users['role'] == 'bidrensiren') {
            $idg = " and kd_kegiatan=2409";
            $idg1 = "2409";
            //} else if ($this->session->users['id_user_group_real'] == 4) {
        } elseif ($this->session->users['role'] == 'span') {
            $idg = " and kd_kegiatan != 2409";
        } else {
            $idg = "";
        }
        if ($kdk == 'undefined') {
            $a = "kd_output = $kdo and kd_kegiatan=2409" . $idg;
        } else if ($kdo != 'abc' and $kdk != '-1') {
            $a = "kdlokasi = $kdk and  kd_output = $kdo" . $idg;
        } else if ($kdo == 'abc' and $kdk != '-1') {
            $a = "kdlokasi = $kdk" . $idg;
        } else if ($kdo == 'abc' and $kdk == '-1') {
            $a = "kdlokasi = $kdk and kd_output = $kdo" . $idg;
        } else {
            $yearnow = $this->session->konfig_tahun_ang;
            $satker = $this->session->users['kd_bujt'];
            $childsatker = $this->child_satker($satker);
            $whereClause = $this->check_satker_if_array2($satker, $childsatker, $yearnow);
            $a = ("kd_output = $kdo and $whereClause");
        }

        $this->db->select_sum('jumlah');
        $this->db->where($a);
        $query = $this->db->get('v_usulan_paket')->row()->jumlah;
        $qur = number_format($query);
        echo str_replace(',', '', $qur);
    }

    function displayMultipleData($string, $tbl, $col, $w) {

        // $strarr = trim($string, '{}');
    
        // print_r($strarr); die();

        if($string=='{}')
        {
            return '-';
        }
        else {
            $s = explode(',',trim($string, '{}'));

            $rd = [];
            for ($i = 0; $i < count($s); $i++) {

                $arrs = $this->get_rowdata($tbl, $col, $w, $s[$i]);
                array_push($rd, $arrs);
            }
    
            return implode("<br>", $rd);
        }
    }

    
    public function get_rowdata($tbl, $col, $w, $id) {


            $query = $this->db->query("select $col from $tbl where $w=$id");
            $res = $query->row_array();

            return $res[$col];


    }

  public function get_prop($id) { 
        $query = $this->db->query("select * from aset_r_ruas where id_ruas=$id");
        $data_satker = json_decode(json_encode($query->result()), true);
        echo json_encode($data_satker);
       
    }
    public function simpan_detail() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $id_user = $this->session->users['id_user'];
        $kd_bujt = $this->session->users['kd_bujt'];
        $kab = $this->session->users['kdkabkota'];
        $dekon = $this->session->users['kddekon'];

        $this->input->post("formData")["kdsatker"] = $kd_bujt;
        $this->input->post("formData")["kd_dept"] = "033";
        $this->input->post("formData")["kd_unit"] = "04";
        //$this->input->post("formData")["kd_program"] = "08";
        $this->input->post("formData")["created_by"] = $id_user;
        $this->input->post("formData")["id_user"] = $id_user;

        $arr_insert = $this->input->post("formData");

        $tahapan = $this->input->post("formData")["kd_tahapan"];
        $kdkabkotbpss = substr($this->input->post("formData")["kdkabkota2"], 2, 2);
        $id_paket = $this->input->post("formData")["id_paket"];
        $thang = $this->input->post("formData")["thang"];
        $kdprog = $this->input->post("formData")["kd_program"];
        $kdgiat = $this->input->post("formData")["kd_kegiatan"];
        $kdoutput = $this->input->post("formData")["kd_output"];
        $kdsoutput = $this->input->post("formData")["kd_sub_output"];
        $kdkmpnen = $this->input->post("formData")["kd_komponen"];
        $kdskmpnen = $this->input->post("formData")["kd_sub_komponen"];
        $kdakun = $this->input->post("formData")["kdakun"];
        $ppk = $this->input->post("formData")["id_ppk"];
        $header2 = $this->input->post("formData")["header2"];
        $kdbeban = $this->input->post("formData")["kdbeban"];
        $kdjnsban = $this->input->post("formData")["kdjnsban"];
        $nmppk = $this->input->post("formData")["nmppk"];
        $auto = $this->db->get_where('d_output', array('thang' => $thang, 'kdsatker' => $kd_bujt, 'kd_tahapan' => $tahapan,
            'kdprogram' => $kdprog, 'kdgiat' => $kdgiat, 'kdoutput' => $kdoutput));
        //echo $auto;
        if ($auto->num_rows() === 0) {
            $auto = '2';
        } else {
            $auto = $auto->row()->kdauto;
        }

        if (strlen($id_paket) === 0) {
            $check = $this->check_master_exists('033', '04', $kdprog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $kdskmpnen, $thang, $kd_bujt, $tahapan);
            $id_paket = $check["id_paket"];
        }

        $cek = $this->cek_ppk_akun($id_paket, $kdakun, $kdbeban, $kdjnsban, $ppk, $tahapan, $kd_bujt, $nmppk);
        $getppk = $this->get_ppk($kd_bujt, $ppk);
        $data = json_decode(json_encode($getppk[0]), true);

        $this->db->trans_start();
        //add header
        if (count($cek) === 0) {
            $jmlppk = $this->get_jmlh_ppk($id_paket, $tahapan, $kd_bujt, $thang, $kdakun, $kdbeban, $kdjnsban);
            $a = json_encode($jmlppk[0]['max'], JSON_NUMERIC_CHECK);
            if ($a > 0) {
                $i = $a + 1;
                $noheader1 = str_pad($i, 2, '0', STR_PAD_LEFT);
            } else {
                $noheader1 = '01';
            }
            $this->add_header1($id_paket, $kd_bujt, $id_user, $kab, $dekon, $arr_insert, $kdkabkotbpss, $data['nama_ppk'], $noheader1);

            $this->db->set('header1', $noheader1);
            //$this->db->set('header2', '00');
            $this->db->set('id_ppk', $ppk);
            //$this->db->where('id_paket', $id_paket)->update('detail_usulan_paket');
            $this->db->where(array('id_paket' => $id_paket, 'kdakun' => $kdakun, 'kdbeban' => $kdbeban,
                'kdjnsban' => $kdjnsban))->update('detail_usulan_paket');

            if (strlen($header2) > 0) {
                $this->add_header2($id_paket, $kd_bujt, $id_user, $kab, $dekon, $arr_insert, $kdkabkotbpss, $noheader1, '01');
            }
        } else {
            if (strlen($header2) > 0) {
                $cek2 = $this->cek_sameheader2($id_paket, $tahapan, $kd_bujt, $thang, $kdakun, $kdbeban, $kdjnsban, $ppk, $header2);
                if (count($cek2) === 0) {
                    $jmlheader2 = $this->get_jmlh_header2($id_paket, $kdakun, $ppk, $tahapan, $kdbeban, $kdjnsban);
                    $b = json_encode($jmlheader2[0]['max'], JSON_NUMERIC_CHECK);
                    if ($b > 0) {
                        $j = $b + 1;
                        $noheader2 = str_pad($j, 2, '0', STR_PAD_LEFT);
                    } else {
                        $noheader2 = '01';
                    }

                    $this->add_header2($id_paket, $kd_bujt, $id_user, $kab, $dekon, $arr_insert, $kdkabkotbpss, $cek[0]['header1'], $noheader2);
                }
            }
        }

        //new code
        $ruas = $this->input->post("formData")["id_ruas"];
        $jembatan = $this->input->post("formData")["id_jembatan"];
        $staw = $this->input->post("formData")["sta_awal"];
        $stak = $this->input->post("formData")["sta_akhir"];
        $kdctarik = $this->input->post("formData")["kdctarik"];

//        if (strlen($jembatan) > 0) {
//            $cek_jembatan = $this->isJembatanOverLap($thang, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $jembatan);
//            if (count($cek_jembatan) > 0) {
//                exit('2');
//            }
//        } else {
//            if (strlen($ruas) > 0) {
//                $cek_ruas = $this->isRuasOverLap($thang, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $ruas, $staw, $stak);
//                if (count($cek_ruas) > 0) {
//                    exit('1');
//                }
//            }
//        }

//        $data_post = $this->input->post("formData");
//        $object_sumber_dana = json_decode($data_post["obj_sumber_dana"]);
//        $arr_sumber_dana = json_decode(json_encode($object_sumber_dana), true);
        //$arr_sumberdana=$this->input->post("formData")["obj_sumber_dana"];
        //unset($data_post["obj_sumber_dana"]);
        $obj_rj_rams = json_decode($this->input->post("formData")["rj_rams"]);
        $arr_obj_rams = json_decode(json_encode($obj_rj_rams), true);
        $rj_rams = "";

        foreach ($arr_obj_rams as $values) {
            $rj_rams .= $values . ",";
        }

        $obj_rj_irms = json_decode($this->input->post("formData")["rj_irms"]);
        $arr_obj_irms = json_decode(json_encode($obj_rj_irms), true);
        $rj_irms = "";
        foreach ($arr_obj_irms as $values) {
            $rj_irms .= $values . ",";
        }


        $obj_rj_renstra = json_decode($this->input->post("formData")["rj_renstra"]);
        $arr_obj_renstra = json_decode(json_encode($obj_rj_renstra), true);
        $rj_renstra = "";
        foreach ($arr_obj_renstra as $values) {
            $rj_renstra .= $values . ",";
        }

        $obj_rj_eprog = json_decode($this->input->post("formData")["rj_eprogram"]);
        $arr_obj_eprog = json_decode(json_encode($obj_rj_eprog), true);
        $rj_eprog = "";
        foreach ($arr_obj_eprog as $values) {
            $rj_eprog .= $values . ",";
        }

        $obj_rj_dpr = json_decode($this->input->post("formData")["rj_dpr"]);
        $arr_obj_dpr = json_decode(json_encode($obj_rj_dpr), true);
        $rj_dpr = "";

        foreach ($arr_obj_dpr as $values) {
            $rj_dpr .= $values . ",";
        }

        $obj_rj_pemda = json_decode($this->input->post("formData")["rj_pemda"]);
        $arr_obj_pemda = json_decode(json_encode($obj_rj_pemda), true);
        $rj_pemda = "";

        foreach ($arr_obj_pemda as $values) {
            $rj_pemda .= $values . ",";
        }

        $obj_rj_sipro = json_decode($this->input->post("formData")["rj_sipro"]);
        $arr_obj_sipro = json_decode(json_encode($obj_rj_sipro), true);
        $rj_sipro = "";

        foreach ($arr_obj_sipro as $values) {
            $rj_sipro .= $values . ",";
        }

        $longitudes = $this->input->post("formData")["longitude"];
        $latitudes = $this->input->post("formData")["latitude"];
        $longitude2s = $this->input->post("formData")["longitude2"];
        $latitude2s = $this->input->post("formData")["latitude2"];
        $longitude = (strlen($longitudes) === 0) ? NULL : $longitudes;
        $latitude = (strlen($latitudes) === 0) ? NULL : $latitudes;
        $longitude2 = (strlen($longitude2s) === 0) ? NULL : $longitude2s;
        $latitude2 = (strlen($latitude2s) === 0) ? NULL : $latitude2s;
        $rus = $this->input->post("formData")["id_ruas"];
        $jems = $this->input->post("formData")["id_jembatan"];
        $stas = $this->input->post("formData")["sta_awal"];
        $stes = $this->input->post("formData")["sta_akhir"];
        $sta = (strlen($stas) === 0) ? NULL : $stas;
        $ste = (strlen($stes) === 0) ? NULL : $stes;
        $jem = (strlen($jems) === 0) ? NULL : $jems;
        $ru = (strlen($rus) === 0) ? NULL : $rus;
        $pagurmp = $this->input->post("formData")["pagurmp"];
        $paguphln = $this->input->post("formData")["paguphln"];
        $pagurkp = $this->input->post("formData")["pagurkp"];
        $blokirrmp = $this->input->post("formData")["blokirrmp"];
        $blokirphln = $this->input->post("formData")["blokirphln"];
        $blokirrkp = $this->input->post("formData")["blokirrkp"];
        $rphblokir = $this->input->post("formData")["rphblokir"];

        $items3 = $this->get_jmlh_item($id_paket, $kdakun, $kdbeban, $kdjnsban, $tahapan);
        $item3 = (int) json_decode($items3[0]['max']);

        $data_detail = [
            "kd_dept" => '033',
            "kd_unit" => '04',
            "kd_program" => $kdprog,
            "kd_kegiatan" => $this->input->post("formData")["kd_kegiatan"],
            "kd_output" => $this->input->post("formData")["kd_output"],
            "kd_sub_output" => $this->input->post("formData")["kd_sub_output"],
            "kd_komponen" => $this->input->post("formData")["kd_komponen"],
            "kd_sub_komponen" => $this->input->post("formData")["kd_sub_komponen"],
            "thang" => (int) $this->input->post("formData")["thang"],
            "satuan" => $this->input->post("formData")["satuan"],
            "id_user" => $id_user,
            //"created_at" => $this->input->post("formData")["created_at"],//pp
            //"updated_at" => $this->input->post("formData")["updated_at"],//pp
            "created_by" => $id_user, //pp
            //"updated_by" => $this->input->post("formData")[""],//pp
            "kdgbkpk" => $this->input->post("formData")["kdgbkpk"], //xx
            "kdakun" => $this->input->post("formData")["kdakun"], //xx
            "jumlah" => $this->input->post("formData")["jumlah"], //xx
            "id_ppk" => $this->input->post("formData")["id_ppk"], //xx
            "kdkppn" => $this->input->post("formData")["kdkppn"], //xx
            "kdsatker" => $this->session->users['kd_bujt'], //xx
            "kdlokasi" => $this->input->post("formData")["kdlokasi"], //xx//kdprov
            "kdkabkota" => $this->session->users['kdkabkota'],
            "kdkabkota2" => $kdkabkotbpss,
            "id_paket" => $id_paket,
            "id_jembatan" => $jem,
            "id_ruas" => $ru,
            "sta_awal" => $sta,
            "sta_akhir" => $ste,
            "longitude" => $longitude,
            "latitude" => $latitude,
            "longitude2" => $longitude2,
            "latitude2" => $latitude2,
            "detail" => $this->input->post("formData")["detail"],
            "volume" => (float) $this->input->post("formData")["volume"],
            "hargasat" => $this->input->post("formData")["hargasat"],
            "jumlah" => (float) $this->input->post("formData")["jumlah"],
//            "sub_kawasan_nama"=>  $this->input->post("formData")["sub_kawasan_nama"],
            "rj_rams" => $rj_rams,
            "rj_irms" => $rj_irms,
            "rj_renstra" => $rj_renstra,
            "rj_eprog" => $rj_eprog,
            "rj_dpr" => $rj_dpr,
            "rj_pemda" => $rj_pemda,
            "rj_sipro" => $rj_sipro,
            "tags_val" => $this->input->post("formData")["tags_val"],
            "kdbeban" => $this->input->post("formData")["kdbeban"],
            "kdjnsban" => $this->input->post("formData")["kdjnsban"],
            "kdctarik" => $this->input->post("formData")["kdctarik"],
            "kd_tahapan" => $this->input->post("formData")["kd_tahapan"],
            //new code
            "register" => $this->input->post("formData")["register"],
            "carahitung" => $this->input->post("formData")["carahitung"],
            "paguphln" => strlen($paguphln) == 0 ? NULL : (float) $paguphln,
            "pagurmp" => strlen($pagurmp) == 0 ? NULL : (float) $pagurmp,
            "pagurkp" => strlen($pagurkp) == 0 ? NULL : (float) $pagurkp,
            "vol1" => strlen($this->input->post("formData")["vol1"]) == 0 ? NULL : (float) $this->input->post("formData")["vol1"],
            "vol2" => strlen($this->input->post("formData")["vol2"]) == 0 ? NULL : (float) $this->input->post("formData")["vol2"],
            "vol3" => strlen($this->input->post("formData")["vol3"]) == 0 ? NULL : (float) $this->input->post("formData")["vol3"],
            "vol4" => strlen($this->input->post("formData")["vol4"]) == 0 ? NULL : (float) $this->input->post("formData")["vol4"],
            "sat1" => $this->input->post("formData")["sat1"],
            "sat2" => $this->input->post("formData")["sat2"],
            "sat3" => $this->input->post("formData")["sat3"],
            "sat4" => $this->input->post("formData")["sat4"],
            "kdblokir" => $this->input->post("formData")["kdblokirs"],
            "blokirphln" => strlen($blokirphln) == 0 ? NULL : (float) $blokirphln,
            "blokirrmp" => strlen($blokirrmp) == 0 ? NULL : (float) $blokirrmp,
            "blokirrkp" => strlen($blokirrkp) == 0 ? NULL : (float) $blokirrkp,
            "rphblokir" => strlen($rphblokir) == 0 ? NULL : (float) $rphblokir,
            //"header1" =>,
            //"header2" =>,
            //"kdheader" =>,
            "noitem" => $item3 + 1,
            //"nmitem" =>,
            //"jumlah2" =>,
            //"kdluncuran" => null,
            "kdsbu" => $this->input->post("formData")["kdsbu"],
            "kddekon" => $this->session->users['kddekon'],
            "kdjendok" => '01',
            "kdib" => '00'
        ];

        $this->db->set('created_at', 'getutcdate()', FALSE);
        $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
        $this->db->insert('detail_usulan_paket', $data_detail);
        $idusulan = $this->db->insert_id('detail_usulan_paket', $data_detail);

        // add header in detail
        $head1 = $this->cek_ppk_akun($id_paket, $kdakun, $kdbeban, $kdjnsban, $ppk, $tahapan, $kd_bujt, $nmppk);
        if (strlen($header2) > 0) {
            $head2 = $this->cek_sameheader2($id_paket, $tahapan, $kd_bujt, $thang, $kdakun, $kdbeban, $kdjnsban, $ppk, $header2);
            $data = [
                "header1" => $head1[0]['header1'],
                "header2" => $head2[0]['header2']
            ];
        } else {
            $data = [
                "header1" => $head1[0]['header1'],
                "header2" => '00'
            ];
        }

        $this->db->where('id_usulan', $idusulan);
        $this->db->update('detail_usulan_paket', $data);

        //update noheader & noitem
        $ppk1 = $this->cek_ppk1($id_paket, $kdakun, $kdbeban, $kdjnsban, $ppk, $tahapan, $kd_bujt, $thang);
        if (count($ppk1) > 1) {
            $this->db->where('id_usulan', $ppk1[0]->id_usulan)->delete('detail_usulan_paket');
        }

        $this->update_header1($id_paket, $kd_bujt, $kdakun, $kdbeban, $kdjnsban, $tahapan, $thang);
        $this->update_item($id_paket, $kdakun, $kdbeban, $kdjnsban, $tahapan, $kd_bujt, $thang);

        //code akun
        $array = array('kdakun' => $kdakun, 'kd_tahapan' => $tahapan,
            'thang' => $thang, 'kdsatker' => $kd_bujt, 'kdprogram' => $kdprog, 'kdgiat' => $kdgiat,
            'kdoutput' => $kdoutput, 'kdsoutput' => $kdsoutput,
            'kdkmpnen' => $kdkmpnen, 'kdskmpnen' => $kdskmpnen,
            'kdbeban' => $kdbeban, 'kdjnsban' => $kdjnsban, 'kdctarik' => $kdctarik);
        if (strlen($kdakun) > 0) {
            $cek_available_akun = $this->cek_available_akun($thang, $kd_bujt, $kdprog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $kdskmpnen, $kdakun, $kdbeban, $kdjnsban, $kdctarik, $tahapan);
            if (count($cek_available_akun) === 0) {
                $this->tambah_akun($kd_bujt, $kab, $dekon, $tahapan, $arr_insert);
            } else {
                $this->edit_akun($arr_insert, $array);
            }
        }

        if ($auto === '0') {
            echo 'auto 0';
            $cek_new_sum = $this->get_new_sum_out($kdprog, $kdgiat, $kdoutput, $thang, $tahapan);
            //echo json_encode($cek_new_sum[0]['sumvol'], JSON_NUMERIC_CHECK);
            //$newsum = json_encode($cek_new_sum[0]['sumvol'], JSON_NUMERIC_CHECK);
            $data_update = [
                "vol" => $cek_new_sum
            ];
            $array = array('thang' => $thang, 'kdsatker' => $kd_bujt,
                'kddept' => '033', 'kdunit' => '04', 'kdprogram' => $kdprog,
                'kdgiat' => $kdgiat, 'kdoutput' => $kdoutput, 'kd_tahapan' => $tahapan);
            $this->db->where($array);
            $this->db->update('d_output', $data_update);
        }

        if ($auto === '1') {
            echo 'auto 1';
            $cek_new_sum_sout = $this->get_new_sum_sout($kdprog, $kdgiat, $kdoutput, $kdsoutput, $thang, $tahapan);
            //$newsums = json_encode($cek_new_sum_sout[0]['jumlah'], JSON_NUMERIC_CHECK);
            $data_update = [
                "volsout" => $cek_new_sum_sout
            ];
            $array = array('thang' => $thang, 'kdsatker' => $kd_bujt,
                'kddept' => '033', 'kdunit' => '04', 'kdprogram' => $kdprog,
                'kdgiat' => $kdgiat, 'kdoutput' => $kdoutput, 'kdsoutput' => $kdsoutput, 'kd_tahapan' => $tahapan);
            $this->db->where($array);
            $this->db->update('d_soutput', $data_update);

            $cek_new_sumz = $this->get_new_sum_out($kdprog, $kdgiat, $kdoutput, $thang, $tahapan);
            //echo json_encode($cek_new_sum[0]['sumvol'], JSON_NUMERIC_CHECK);
            //$newsumz = json_encode($cek_new_sumz[0]['sumvol'], JSON_NUMERIC_CHECK);
            $data_update2 = [
                "vol" => $cek_new_sumz
            ];
            $array2 = array('thang' => $thang, 'kdsatker' => $kd_bujt,
                'kddept' => '033', 'kdunit' => '04', 'kdprogram' => $kdprog,
                'kdgiat' => $kdgiat, 'kdoutput' => $kdoutput, 'kd_tahapan' => $tahapan);
            $this->db->where($array2);
            $this->db->update('d_output', $data_update2);
        }

        //new code
        $geomcheck = $this->input->post("formData")["geom"];
        if (strlen($geomcheck) > 0) {
            $id_paket = $this->input->post("formData")["id_paket"];

            $data_geo = [
                "thang" => $this->input->post("formData")["thang"],
                "kd_tahapan" => $tahapan,
                "id_paket" => $id_paket,
                "id_detail" => $idusulan
                    //"geom_pok2" => $geompok2
            ];

            if (strlen($jem) !== 0) {
                $geomVal = $this->input->post("formData")["geom"];
                $geom = "geometry::STGeomFromText('POINT($geomVal)',4326)";

                $this->db->set('geom_pok1', $geom, false);
                $this->db->insert('geo_pok_jembatan', $data_geo);
            } else {
                $geomVal = $this->input->post("formData")["geom"];
                $geo = explode(';', $geomVal);
                $geom = "geometry::STGeomFromText('$geo[1]',4326)";

                $this->db->set('geom_pok1', $geom, false);
                $this->db->insert('geo_pok_ruas', $data_geo);
            }
        }

        $insert_id = $this->input->post("formData")["id_paket"];
        $dttags = $this->input->post("formData")["tags_val"];
        $data1 = json_decode($this->input->post("formData")["tags_val"], true);
        $data_rujuk = array();
        if ($dttags === '[]') {
            
        } else {
            foreach ($data1 as $dataku) {
                // if ($dataku['kd_jns_rujukan'] == '1') {
                //     $v = $this->db->get_where('v_arahan_pagu_indikatif', array('sa1thn_id' => $dataku['value']))->row()->id;
                // } else {
                //     $v = $dataku['value'];
                // }
                $v = $dataku['value'];

                array_push($data_rujuk, array(
                    'id_paket' => $insert_id,
                    'id_usulan' => $idusulan,
                    'id_rujukan' => $v,
                    'kd_jns_rujukan' => $dataku['kd_jns_rujukan'],
                    'kd_tahapan' => $tahapan
                ));
            }

            $this->db->insert_batch('rujukan_paket', $data_rujuk);
            //echo $this->db->last_query();
        }

        $this->db->trans_complete();

        echo json_encode(array("status" => TRUE));
    }

    public function addform() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $postparam = $this->input->post('formData', TRUE);
//        console.log($postparam);
//        echo json_encode($postparam);
//        {"id_paket":"773","id_flow":"","nama_paket":"Preservasi dan Pelebaran Ruas Jalan Takengon \u2013 Bireuen","kd_bujt":"","modeform":"tambah","nama_pengusul":"","nama_satker":"KUNKER 13 JAN 2018","keterangan":"Telah diteruskan ke Balai terkait","status":"3","id_user":"113"}
//            id_flow: (thedata[3 + idx] ? thedata[3 + idx] : ''),
//            nama_paket: (thedata[1] ? thedata[1] : ''),
        $id_paket = $postparam['id_paket'];
        $status_idx = $postparam['status_idx'];
        $status = $postparam['status'];
//            $ket_lokasi = $postparam['ket_lokasi'];
//            $kegiatan_nama = $postparam['kegiatan_nama'];
//            $output_nama = $postparam['output_nama'];
//            $suboutput_nama = $postparam['suboutput_nama'];
//            $aktivitas_nama = $postparam['aktivitas_nama'];
//            $sub_aktivitas = $postparam['sub_aktivitas'];
//            $tahun_anggaran = $postparam['tahun_anggaran'];
//            $jenis_kontrakNama = $postparam['jenis_kontrakNama'];
//            $kewenangan = $postparam['kewenangan'];
//            $sumber_dana = $postparam['sumber_dana'];
        //$volume = $postparam['volume'];
        //$satuan_output = $postparam['satuan'];
        //$jumlah = $postparam['jumlah'];
//            $rpm = $postparam['rpm'];
//            $phln = $postparam['phln'];
//            $sbsn = $postparam['sbsn'];
//            $rmp = $postparam['rmp'];
        $eval1 = $postparam['eval1'];
        $eval2 = $postparam['eval2'];
//            $sumber_arahan = $postparam['jenis_arahan'];
//        $id_usulan = $postparam['id_paket'];
//        $status_idx = $postparam['status_idx'];
//        $status = $postparam['status'];
//        $catatan = $postparam['keterangan'];
//        $evaluasi1 = $postparam['eval_usulan1'];
//        $evaluasi2 = $postparam['eval_usulan2'];
//        $evaluasi3 = $postparam['eval_usulan3'];
//        $rc_ded = $postparam['rc_ded'];
//        $rc_dokling = $postparam['rc_dokling'];
//        $rc_fs = $postparam['rc_fs'];
//        $rc_lahan = $postparam['rc_lahan'];
//        $rkakl_volume = $postparam['rkakl_volume'];
//        $rkakl_biaya = $postparam['rkakl_biaya'];
//        $status_kewenangan = $postparam['status_kewenangan'];
//        $kddisposisi = (int) $postparam['kddisposisi'];


        switch ($status_idx) {
            case 0: //evaluasi balai
                $sql = "update paket_indikatif set stat_usulan1=$status, eval_usulan1='$eval1' where id_paket=$id_paket";
                break;
            case 1: //verifikasi kpsj
                $sql = "update paket_indikatif set stat_usulan2=$status, eval_usulan2='$eval2' where id_paket=$id_paket";
                break;
        }

        $res = $this->db->query($sql);
        if (!$res) {
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"update sukses"}';
        }
    }

//    public function addform($mode) {
//        $param = $this->input->post('formPaket');
//        $param2 = $this->input->post('formDetail');
//        // echo json_encode($param);
//
//        $paket_id = [];
//        if ($mode == 'paket') {
//            $cek = $this->db->get_where('detail_usulan_paket', $param);
//            if ($cek->num_rows() > 0) {
//                echo "Data tersebut sudah ada";
//            } else {
//                $res = $this->db->insert('detail_usulan_paket', $param);
//                if (!$res) {
//                    echo "Gagal";
//                } else {
//                    echo "Berhasil Menambah Data";
//
//                    $query = $this->db->get_where('detail_usulan_paket', $param);
//
//                    $ret = $query->row();
//                    $id_paket = $ret->id_paket;
//
//                    //echo $id_paket;
//
//                    $paket_id[] = $id_paket;
//                }
//            }
//        }
//
//
//        //print_r($paket_id);
//        //echo $paket_id[0];
//
//        if ($mode == 'detail') {
//
//            $param2 = $this->input->post('formDetail');
//
//            $id_ruas = explode('::', $param2['id_ruas'])[0];
//
//            unset($param2['id_ruas']);
//            $param2['id_ruas'] = $id_ruas;
//
//            // print_r($param);
//
//            $cek = $this->db->get_where('usulan', $param2);
//            if ($cek->num_rows() > 0) {
//                echo "Data tersebut sudah ada";
//            } else {
//
//
//                //echo $id_paket;
//                //$param2['id_paket'] = $paket_id[0];
//
//                $res = $this->db->insert('usulan', $param2);
//                if (!$res) {
//                    echo "Gagal";
//                } else {
//                    echo "Berhasil Menambah Data";
//                }
//            }
//        }
//    }

    public function ajax_add($mode) {
//        echo "<pre>";
//               print_r($this->input->post());
//        echo "</pre>";
//       // echo $this->input->post()[1];
//        die();
        $satker = $this->session->users['kd_bujt'];






        switch ($mode) {
            case "paket" :
                $id_ppk = $this->input->post('id_ppk', TRUE);
                $id_ppk = ($id_ppk == '#') ? NULL : $id_ppk;

                $kdkppn = $this->input->post('kdkppn');
                $kdkppn = ($kdkppn == '-1') ? NULL : $kdkppn;


                $data = array(
                    'id_user' => $this->session->users['id_user'],
                    'kd_dept' => $this->input->post('kd_dept', TRUE),
                    'kd_unit' => $this->input->post('kd_unit', TRUE),
                    'kd_program' => $this->input->post('kd_program', TRUE),
                    'kd_kegiatan' => $this->input->post('kd_kegiatan', TRUE),
                    'kd_output' => $this->input->post('kd_output', TRUE),
                    'kd_sub_output' => $this->input->post('kd_sub_output', TRUE),
                    'kd_komponen' => $this->input->post('kd_komponen', TRUE),
                    'thang' => $this->input->post('thang', TRUE),
                    'kd_sub_komponen' => $this->input->post('kd_sub_komponen', TRUE),
                    'nama_sub_komponen' => $this->input->post('nama_sub_komponen', TRUE),
                    'id_user' => $this->session->users['id_user'],
                    'id_ppk' => NULL,
                    'kdkppn' => NULL,
                    'kdsatker' => (int) $satker
                );

                $this->db->set('created_at', 'getutcdate()', FALSE);
                $this->db->set('updated_at', 'getutcdate()', FALSE);
                $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
                $this->db->insert('detail_usulan_paket', $data);

//                $insert = $this->m_paket->save('detail_usulan_paket', $data);

                break;

            case "detail" :
                $jsontags = $this->input->post('tags', TRUE);
                $fldSipro = '';
                $fldRams = '';
                $fldDPR = '';
                $fldIRMS = '';
                $fldRenstra = '';
                $fldPemda = '';
                $fldEprog = '';

                if ($jsontags == '') {
                    $valJson = json_encode([]);
                } else {
                    $valJson = $jsontags;
                    $arrJson = json_decode($valJson);
                    //print_r($arrJson);

                    foreach ($arrJson as $tag) {
                        $tagText = $tag->text;
                        //echo "<hr>$tagText<hr>";
                        $tags = explode('|', $tagText);
                        $tagType = $tags[0];
                        $tagVal = $tags[1];
                        //echo "<hr>--> $tagType, $tagVal<br>";
                        switch ($tagType) {
                            case 'SIPRO':
                                $fldSipro .= "|$tagVal|";
                                break;
                            case 'IRMS':
                                $fldIRMS .= "|$tagVal|";
                                break;
                            case 'DPR':
                                $fldDPR .= "|$tagVal|";
                                break;
                            case 'EPROG':
                                $fldEprog .= "|$tagVal|";
                                break;
                            case 'RENSTRA':
                                $fldRenstra .= "|$tagVal|";
                                break;
                            case 'RAMS':
                                $fldRams .= "|$tagVal|";
                                break;
                            case 'PEMDA':
                                $fldPemda .= "|$tagVal|";
                                break;
                        }
                    }

//                    echo "<hr>$fldIRMS, $fldRams, $fldSipro, $fldRenstra, $fldPemda, $fldEprog, $fldDPR<hr>";
//                    $arrObj = $arrJson[0]->text;
//
//                    echo  "arrobj: $arrObj";
//
//                    $rujObj = $explode($arrObj, '|')[0];
//                    $valObj = $explode($arrObj, '|')[1];
//
//
//                    switch ($rujObj) {
//                        case 'SIPRO' :
//                                 $vdata = [];
//
//                                 array_push($vdata, $valObj);
//                            break;
//
//                        case 'RAMS' :
//                                $vdata = [];
//                                 array_push($vdata, $valObj);
//                            break;
//
//                        case 'IRMS' :
//                                $vdata = [];
//                                 array_push($vdata, $valObj);
//                            break;
//                        case 'DPR' :
//                                $vdata = [];
//                                 array_push($vdata, $valObj);
//                            break;
//                        case 'PEMDA' :
//                                $vdata = [];
//                                 array_push($vdata, $valObj);
//                            break;
//                        case 'EPROG' :
//                                $vdata = [];
//                                 array_push($vdata, $valObj);
//                            break;
//                        case 'RENSTRA' :
//                                $vdata = [];
//                                 array_push($vdata, $valObj);
//                            break;
//                    }
//
////                    |856||754|
//
//                    $this->db->insert();
                }
//
                $id_ruas = explode('::', $this->input->post('id_ruas', TRUE))[0];
                $id_ruas = ($id_ruas == '#' || $id_ruas == '-1') ? NULL : $id_ruas;

                $staa = $this->input->post('sta_awal-sel', TRUE);
                $staa = ($staa == '#') ? 0 : $staa;

                $stae = $this->input->post('sta_akhir-sel', TRUE);
                $stae = ($stae == '#') ? 0 : $stae;

                $id_jembatan = $this->input->post('id_jembatan', TRUE);
                $id_jembatan = ($id_jembatan == '#' || $id_jembatan == '-1') ? NULL : $id_jembatan;


                $lon = $this->input->post('longitude', TRUE);
                $lon = ($lon == '') ? NULL : $lon;

                $lat = $this->input->post('latitude', TRUE);
                $lat = ($lat == '') ? NULL : $lat;


                $kdgbkpk = $this->input->post('kdgbkpk', TRUE);
                $kdgbkpk = ($kdgbkpk == '#') ? '' : $kdgbkpk;

                $kdakun = $this->input->post('kdakun', TRUE);
                $kdakun = ($kdakun == '#') ? '' : $kdakun;


                $treatment = $this->input->post('treatment', TRUE);
                $treatment = ($treatment == '#') ? 0 : $treatment;

                $volume = $this->input->post('volume', TRUE);
                $volume = ($volume == '') ? 0 : $volume;

                $satuan = $this->input->post('satuan', TRUE);
                $satuan = ($satuan == '') ? '' : $satuan;

                $jumlah = $this->input->post('jumlah', TRUE);
                $jumlah = ($jumlah == '') ? 0 : $jumlah;

                $nm_ruas = $this->input->post('nm_ruas', TRUE);
                $nm_jembatan = $this->input->post('nm_jembatan', TRUE);

                $detail = '';
                if ($nm_ruas != '--Pilih--' && $nm_jembatan == '--Pilih--') {
                    $detail = $nm_ruas;
                } else if ($nm_ruas != '--Pilih--' && $nm_jembatan != '--Pilih--') {
                    $detail = $nm_jembatan;
                } else if ($nm_ruas == '--Pilih--' && $nm_jembatan == '--Pilih--') {
                    $detail = $this->input->post('detail', TRUE);
                }

                //
                //die();

                $data = array(
                    'id_paket' => $this->input->post('id_paket', TRUE),
                    'id_user' => $this->session->users['id_user'],
                    'kd_dept' => $this->input->post('kd_dept', TRUE),
                    'kd_unit' => $this->input->post('kd_unit', TRUE),
                    'kd_program' => $this->input->post('kd_program', TRUE),
                    'kd_kegiatan' => $this->input->post('kd_kegiatan-sel', TRUE),
                    'kd_output' => $this->input->post('kd_output-sel', TRUE),
                    'kd_sub_output' => $this->input->post('kd_sub_output-sel', TRUE),
                    'kd_komponen' => $this->input->post('kd_komponen-sel', TRUE),
                    'thang' => $this->input->post('thang-sel', TRUE),
                    'kd_sub_komponen' => $this->input->post('kd_sub_komponen-sel', TRUE),
//                    'nama_sub_komponen' => $this->input->post('nama_sub_komponen'),
                    //  'id_ppk' => $id_ppk,
                    //    'kdkppn' => $kdkppn,
                    'kdsatker' => (int) $satker,
                    'id_ruas' => $id_ruas,
                    'detail' => $detail,
                    'sta_awal' => $staa,
                    'sta_akhir' => $stae,
                    'id_jembatan' => $id_jembatan,
                    'longitude' => $lon,
                    'latitude' => $lat,
                    'treatment' => $treatment,
                    'volume' => $volume,
                    'satuan' => $this->input->post('satuan', TRUE),
                    'jumlah' => $this->input->post('totalpagu', TRUE),
                    'rm' => $this->input->post('rm', TRUE),
                    'rmp' => $this->input->post('rmp', TRUE),
                    'phln' => $this->input->post('pln', TRUE),
                    'sbsn' => $this->input->post('sbsn', TRUE),
                    'pnbp' => $this->input->post('pnbp', TRUE),
                    'kdgbkpk' => $kdgbkpk,
                    'kdakun' => $kdakun,
                    'tags_val' => $valJson,
                    'rj_sipro' => $fldSipro,
                    'rj_rams' => $fldRams,
                    'rj_pemda' => $fldPemda,
                    'rj_eprog' => $fldEprog,
                    'rj_dpr' => $fldDPR,
                    'rj_renstra' => $fldRenstra,
                    'rj_irms' => $fldIRMS,
                    'id_user' => $this->session->users['id_user']
                );

                $this->db->set('created_at', 'getutcdate()', FALSE);
                $this->db->set('updated_at', 'getutcdate()', FALSE);
                $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
                $this->db->insert('detail_usulan_paket', $data);
                //  $insert = $this->m_paket->save('detail_usulan_paket', $data);

                break;
        }



//        $rm = $this->input->post('rm');
//        $rm = ($rm == '') ? NULL : $rm;
//
//        $rmp = $this->input->post('rmp');
//        $rmp = ($rmp == '') ? NULL : $rmp;
//
//        $phln = $this->input->post('pln');
//        $phln = ($phln == '') ? NULL : $phln;
//
//        $pdn = $this->input->post('pdn');
//        $pdn = ($pdn == '') ? NULL : $pdn;
//
//        $pdp = $this->input->post('pdp');
//        $pdp = ($pdp == '') ? NULL : $pdp;
//
//        $sbsn = $this->input->post('sbsn');
//        $sbsn = ($sbsn == '') ? NULL : $sbsn;
//
//        $blu = $this->input->post('blu');
//        $blu = ($blu == '') ? NULL : $blu;
//
//        $pnbp = $this->input->post('pnbp');
//        $pnbp = ($pnbp == '') ? NULL : $pnbp;
//
//        $opr = $this->input->post('opr');
//        $opr = ($opr == '') ? NULL : $opr;
//        $urut = $this->mprob->cek_id();
        // print_r($this->input->post());
//        $id_ppk = $this->input->post('id_ppk');
//        $id_ppk = ($id_ppk == '#') ? NULL : $id_ppk;
//
//        $kdkppn = $this->input->post('kdkppn');
//        $kdkppn = ($kdkppn == -1) ? NULL : $kdkppn;
//
//        $satker = $this->session->users['kd_bujt'];
//
//
//
//        $data = array(
//            'id_user' => $this->session->users['id_user'],
//            'kd_dept' => $this->input->post('kd_dept'),
//            'kd_unit' => $this->input->post('kd_unit'),
//            'kd_program' => $this->input->post('kd_program'),
//            'kd_kegiatan' => $this->input->post('kd_kegiatan'),
//            'kd_output' => $this->input->post('kd_output'),
//            'kd_sub_output' => $this->input->post('kd_sub_output'),
//            'kd_komponen' => $this->input->post('kd_komponen'),
//            'thang' => $this->input->post('thang'),
//            'kd_sub_komponen' => $this->input->post('kd_sub_komponen'),
//            'nama_sub_komponen' => $this->input->post('nama_sub_komponen'),
//            'id_ppk' => $id_ppk,
//            'kdkppn' => $kdkppn,
//            'kdsatker' => (int) $satker
//        );
//
//        if (!empty($_FILES['archive']['name'])) {
//            $upload = $this->upload_file();
//            $data['archive'] = $upload;
//        }
//
//
//
//        $id_ruas = explode('::', $this->input->post('id_ruas'))[0];
//        $id_ruas = ($id_ruas == '#') ? NULL : $id_ruas;
//
//        $staa = $this->input->post('sta_awal');
//        $staa = ($staa == '#') ? NULL : $staa;
//
//        $stae = $this->input->post('sta_akhir');
//        $stae = ($stae == '#') ? NULL : $stae;
//
//        $id_jembatan = $this->input->post('id_jembatan');
//        $id_jembatan = ($id_jembatan == '#' || $id_jembatan == '-1') ? NULL : $id_jembatan;
//
//
//        $kdgbkpk = $this->input->post('kdgbkpk');
//        $kdgbkpk = ($kdgbkpk == '#') ? NULL : $kdgbkpk;
//
//        $kdakun = $this->input->post('kdakun');
//        $kdakun = ($kdakun == '#') ? NULL : $kdakun;
//
//        $lon = $this->input->post('longitude');
//        $lon = ($lon == '') ? NULL : $lon;
//
//        $lat = $this->input->post('latitude');
//        $lat = ($lat == '') ? NULL : $lat;
//
//        $treatment = $this->input->post('treatment');
//        $treatment = ($treatment == '#') ? NULL : $treatment;
//
//        $volume = $this->input->post('volume');
//        $volume = ($volume == '') ? NULL : $volume;
//
//        $rm = $this->input->post('rm');
//        $rm = ($rm == '') ? NULL : $rm;
//
//        $rmp = $this->input->post('rmp');
//        $rmp = ($rmp == '') ? NULL : $rmp;
//
//        $phln = $this->input->post('pln');
//        $phln = ($phln == '') ? NULL : $phln;
//
//        $pdn = $this->input->post('pdn');
//        $pdn = ($pdn == '') ? NULL : $pdn;
//
//        $pdp = $this->input->post('pdp');
//        $pdp = ($pdp == '') ? NULL : $pdp;
//
//        $sbsn = $this->input->post('sbsn');
//        $sbsn = ($sbsn == '') ? NULL : $sbsn;
//
//        $blu = $this->input->post('blu');
//        $blu = ($blu == '') ? NULL : $blu;
//
//        $pnbp = $this->input->post('pnbp');
//        $pnbp = ($pnbp == '') ? NULL : $pnbp;
//
//        $opr = $this->input->post('opr');
//        $opr = ($opr == '') ? NULL : $opr;
//
//        //print_r($data);
//
//        $cek = $this->m_paket->cek_id('detail_usulan_paket', $data);
//
//
//        echo '------>'.$cek->id_paket;
//
//        if (!empty($cek)) {
//            //echo 'data sudah ada';
//
//            $data3 = array(
//                'id_user' => $this->session->users['id_user'],
//                'id_paket' => $cek->id_paket,
//                'kd_dept' => $data['kd_dept'],
//                'kd_unit' => $data['kd_unit'],
//                'kd_program' => $data['kd_program'],
//                'kd_kegiatan' => $data['kd_kegiatan'],
//                'kd_output' => $data['kd_output'],
//                'kd_sub_output' => $data['kd_sub_output'],
//                'kd_komponen' => $data['kd_komponen'],
//                'kd_sub_komponen' => $data['kd_sub_komponen'],
//                'thang' => $data['thang'],
//                'id_ruas' => $id_ruas,
//                'detail' => $this->input->post('detail'),
//                'sta_awal' => $staa,
//                'sta_akhir' => $stae,
//                'id_jembatan' => $id_jembatan,
//                'longitude' => $lon,
//                'latitude' => $lat,
//                'treatment' => $treatment,
//                'volume' => $volume,
//                'kdgbkpk' => $kdgbkpk,
//                'kdakun' => $kdakun,
//                'rm' => $rm,
//                'rmp' => $rmp,
//                'phln' => $phln,
//                'pdn' => $pdn,
//                'pdp' => $pdp,
//                'sbsn' => $sbsn,
//                'blu' => $blu,
//                'pnbp' => $pnbp,
//                'opr' => $opr,
//                'tags_val' => $this->input->post('tags'),
//                'kdsatker' => (int) $satker
//            );
//
//            $insert3 = $this->m_paket->save('detail_usulan_paket', $data3);
//        } else {
//            $insert = $this->m_paket->save('detail_usulan_paket', $data);
//            if ($insert != '') {
//
//
//                $data2 = array(
//                    'id_user' => $this->session->users['id_user'],
//                    'id_paket' => $insert,
//                    'kd_dept' => $data['kd_dept'],
//                    'kd_unit' => $data['kd_unit'],
//                    'kd_program' => $data['kd_program'],
//                    'kd_kegiatan' => $data['kd_kegiatan'],
//                    'kd_output' => $data['kd_output'],
//                    'kd_sub_output' => $data['kd_sub_output'],
//                    'kd_komponen' => $data['kd_komponen'],
//                    'kd_sub_komponen' => $data['kd_sub_komponen'],
//                    'thang' => $data['thang'],
//                    'id_ruas' => $id_ruas,
//                    'detail' => $this->input->post('detail'),
//                    'sta_awal' => $staa,
//                    'sta_akhir' => $stae,
//                    'id_jembatan' => $id_jembatan,
//                    'longitude' => $lon,
//                    'latitude' => $lat,
//                    'treatment' => $treatment,
//                    'volume' => $volume,
//                    'satuan' => $this->input->post('satuan'),
//                    'kdgbkpk' => $kdgbkpk,
//                    'kdakun' => $kdakun,
//                    'rm' => $rm,
//                    'rmp' => $rmp,
//                    'phln' => $phln,
//                    'pdn' => $pdn,
//                    'pdp' => $pdp,
//                    'sbsn' => $sbsn,
//                    'blu' => $blu,
//                    'pnbp' => $pnbp,
//                    'opr' => $opr,
//                    'tags_val' => $this->input->post('tags'),
//                    'kdsatker' => (int) $satker
//                );
//
//                $insert2 = $this->m_paket->save('detail_usulan_paket', $data2);
//            }
//        }



        echo json_encode(array("status" => TRUE));
    }

    public function ajax_edit($type, $kode) {
        if ($type == 'paket') {
            $data = $this->M_model->get_by_id('v_aggr_paket_indikatif', 'id_paket', $kode);
        } else {
            $data = $this->M_model->get_by_id('v_detail_indikatif', 'id_usulan', $kode);
        }
        echo json_encode($data);
    }

    public function ajax_update($type) {
        if ($type == 'paket') {

            $id_ppk = $this->input->post('id_ppk', TRUE);
            $id_ppk = ($id_ppk == '#') ? NULL : $id_ppk;

            $kdkppn = $this->input->post('kdkppn', TRUE);
            $kdkppn = ($kdkppn == '-1') ? NULL : $kdkppn;

            $satker = $this->session->users['kd_bujt'];

            $data = array(
//                'id_user' => $this->session->users['id_user'],
                'kd_dept' => $this->input->post('kd_dept', TRUE),
                'kd_unit' => $this->input->post('kd_unit', TRUE),
                'kd_program' => $this->input->post('kd_program', TRUE),
                'kd_kegiatan' => $this->input->post('kd_kegiatan', TRUE),
                'kd_output' => $this->input->post('kd_output', TRUE),
                'kd_sub_output' => $this->input->post('kd_sub_output', TRUE),
                'kd_komponen' => $this->input->post('kd_komponen', TRUE),
                'thang' => $this->input->post('thang', TRUE),
                'kd_sub_komponen' => $this->input->post('kd_sub_komponen', TRUE),
                'nama_sub_komponen' => $this->input->post('nama_sub_komponen', TRUE),
                'id_user' => $this->session->users['id_user'],
                'id_ppk' => $id_ppk,
                'kdkppn' => $kdkppn,
//                'kdsatker' => (int) $satker
            );

            $this->db->set('updated_at', 'getutcdate()', FALSE);
            $pk = $this->db->update('detail_usulan_paket', $data, array('id_paket' => $this->input->post('id_paket', TRUE)));
            if ($pk) {
                unset($data['id_user']);
                unset($data['kdkppn']);
                unset($data['id_ppk']);
                $this->db->set('updated_at', 'getutcdate()', FALSE);
                $this->db->update('detail_usulan_paket', $data, array('id_paket' => $this->input->post('id_paket', TRUE)));
            }

            //$update = $this->m_paket->update('detail_usulan_paket', $data, array('id_paket' => $this->input->post('id_paket')));
            //echo $this->db->last_query();
        } else {
            //Item Paket
            $jsontags = $this->input->post('tags', TRUE);

            // echo $jsontags;
            $fldSipro = '';
            $fldRams = '';
            $fldDPR = '';
            $fldIRMS = '';
            $fldRenstra = '';
            $fldPemda = '';
            $fldEprog = '';

            if ($jsontags == '') {
                $valJson = '[]';
            } else {
                $valJson = $jsontags;
                $arrJson = json_decode($valJson);
                //print_r($arrJson);

                foreach ($arrJson as $tag) {
                    $tagText = $tag->text;
                    //echo "<hr>$tagText<hr>";
                    $tags = explode('|', $tagText);
                    $tagType = $tags[0];
                    $tagVal = $tags[1];
                    //echo "<hr>--> $tagType, $tagVal<br>";
                    switch ($tagType) {
                        case 'SIPRO':
                            $fldSipro .= "|$tagVal|";
                            break;
                        case 'IRMS':
                            $fldIRMS .= "|$tagVal|";
                            break;
                        case 'DPR':
                            $fldDPR .= "|$tagVal|";
                            break;
                        case 'EPROG':
                            $fldEprog .= "|$tagVal|";
                            break;
                        case 'RENSTRA':
                            $fldRenstra .= "|$tagVal|";
                            break;
                        case 'RAMS':
                            $fldRams .= "|$tagVal|";
                            break;
                        case 'PEMDA':
                            $fldPemda .= "|$tagVal|";
                            break;
                    }
                }

//                    echo "<hr>$fldIRMS, $fldRams, $fldSipro, $fldRenstra, $fldPemda, $fldEprog, $fldDPR<hr>";
            }
//
            $id_ruas = explode('::', $this->input->post('id_ruas', TRUE))[0];
            $id_ruas = ($id_ruas == '#' || $id_ruas == '-1') ? NULL : $id_ruas;

            //
            //die();

            $data = array(
                'id_paket' => $this->input->post('id_paket', TRUE),
                'id_user' => $this->session->users['id_user'],
                'kd_dept' => $this->input->post('kd_dept', TRUE),
                'kd_unit' => $this->input->post('kd_unit', TRUE),
                'kd_program' => $this->input->post('kd_program', TRUE),
                'kd_kegiatan' => $this->input->post('kd_kegiatan-sel', TRUE),
                'kd_output' => $this->input->post('kd_output-sel', TRUE),
                'kd_sub_output' => $this->input->post('kd_sub_output-sel', TRUE),
                'kd_komponen' => $this->input->post('kd_komponen-sel', TRUE),
                'thang' => $this->input->post('thang-sel', TRUE),
                'kd_sub_komponen' => $this->input->post('kd_sub_komponen-sel', TRUE),
                // 'nama_sub_komponen' => $this->input->post('nama_sub_komponen'),
                //  'id_ppk' => $id_ppk,
                //    'kdkppn' => $kdkppn,
                'kdsatker' => $this->session->users['kd_bujt'],
                'id_ruas' => $id_ruas,
                'detail' => $this->input->post('detail', TRUE),
                'satuan' => $this->input->post('sta_awal', TRUE),
                'jumlah' => $this->input->post('sta_akhir', TRUE),
                'id_jembatan' => $this->input->post('id_jembatan', TRUE),
                'longitude' => $this->input->post('longitude', TRUE),
                'latitude' => $this->input->post('latitude', TRUE),
                'treatment' => $this->input->post('treatment', TRUE),
                'volume' => $this->input->post('volume', TRUE),
//                'sta_awal' => $staa,
//                'sta_akhir' => $stae,
//                'id_jembatan' => $id_jembatan,
//                'longitude' => $lon,
//                'latitude' => $lat,
//                'treatment' => $treatment,
//                'volume' => $volume,
                'satuan' => $this->input->post('satuan', TRUE),
                'jumlah' => $this->input->post('jumlah', TRUE),
                'kdgbkpk' => $this->input->post('kdgbkpk', TRUE),
//                'kdgbkpk' => $kdgbkpk,
//                'kdakun' => $kdakun,
                'tags_val' => $valJson,
                'rj_sipro' => $fldSipro,
                'rj_rams' => $fldRams,
                'rj_pemda' => $fldPemda,
                'rj_eprog' => $fldEprog,
                'rj_dpr' => $fldDPR,
                'rj_renstra' => $fldRenstra,
                'rj_irms' => $fldIRMS,
                'id_user' => $this->session->users['id_user']
            );
            print_r($valJson);

            $this->db->set('updated_at', 'getutcdate()', FALSE);
            $this->db->update('detail_usulan_paket', $data, array('id_usulan' => $this->input->post('id_usulan', TRUE)));
            //   $update = $this->m_paket->update('detail_usulan_paket', $data, array('id_usulan' => $this->input->post('id_usulan')));
        }

        //$this->upload_gambar($this->input->post('id'));
        echo json_encode(array("status" => TRUE));
    }

    

    

    public function upload_file() {
        $config['upload_path'] = FCPATH . 'uploads/paket/';
        $config['allowed_types'] = 'doc|docx|pdf|xls|xlsx|zip|rar';
        $config['max_size'] = 1024 * 5;
        $config['file_name'] = 'Attacment' . round(microtime(true) * 1000); //just milisecond timestamp fot unique name

        $this->load->library('upload', $config);

        if (!$this->upload->do_upload('archive')) { //upload and validate
            $data['inputerror'][] = 'archive';
            $data['error_string'][] = 'Upload error: ' . $this->upload->display_errors('', ''); //show ajax error
            $data['status'] = FALSE;
            echo json_encode($data);
            exit();
        }

        return $this->upload->data('file_name');
    }

    public function get_eprogram() {
        header('Content-Type: application/json');

        $url = "https://eprogram.inweb.id/internal/page/query/run/?data=5&params=fWNp1TQFDOs-lifyhlNPVv9DEs60JbJp&format=.geojson";

        $sgj = file_get_contents($url);
        $gj = json_decode($sgj);

//        print_r($gj);
//        if (sizeof($gj[0]->data) < 1) {
//            echo '{"data": []}';
//        }
//        if (sizeof($gj->features) > 1) {
        $data = array();
        foreach ($gj->features as $item) {
            $prop = $item->properties;
            $prop->geometry = $item->geometry;
            $data[] = $prop;
        }
        echo '{"data": ' . json_encode($data) . '}';
//        }
    }

    private function exif_gps($targetPath='') {
        $g = new gps();

        // Get GPS position
        $gps = $g->getGpsPosition($targetPath);
        // if (empty($gps)) {

        //     return true;
        //     //die('Could not get GPS position' . PHP_EOL);
        // }
        //  print_r($gps); die();
        
        return $gps;
    }

    
    //new code
    public function ssp_sbm() {
        $thang = $this->session->konfig_tahun_ang;
        $table = 'v_sbu';
        $primaryKey = 'kdsbu'; //test

        $columns = array(
            array('db' => 'kdsbu', 'dt' => 0),
            array('db' => 'nmitem', 'dt' => 1),
            array('db' => 'satkeg', 'dt' => 2),
            array('db' => 'biaya', 'dt' => 3),
        );

        datatable_ssp($table, $primaryKey, $columns, "thang = $thang");
    }

    public function get_akun_all($id_usulan) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where id_usulan = " . $id_usulan;
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
        echo json_encode($data_usulan_indikatif[0]);
        return $data_usulan_indikatif;
    }

    public function get_akun_update() {
        $param = $this->input->post('data', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $thang = $this->input->post("data")["thang"];
        $kdsatker = $this->input->post("data")["kdsatker"];
        $kdgiat = $this->input->post("data")["kdgiat"];
        $kdoutput = $this->input->post("data")["kdoutput"];
        $kdsoutput = $this->input->post("data")["kdsoutput"];
        $kdkmpnen = $this->input->post("data")["kdkmpnen"];
        $kdskmpnen = $this->input->post("data")["kdskmpnen"];
        $kdakun = $this->input->post("data")["kdakun"];
        $kdbeban = $this->input->post("data")["kdbeban"];
        $kdjnsban = $this->input->post("data")["kdjnsban"];
        $kdctarik = $this->input->post("data")["kdctarik"];
        $tahapan = $this->input->post("data")["kd_tahapan"];
        if (substr($tahapan, 0, 2) === 'RD'){
            $tahapan = "like 'RD%'";
        } else {
            $tahapan = "= '$tahapan'";
        }
        $str_sql = "select *
                    from d_akun a
                    left join d_cttakun b on a.thang = b.thang and a.kdsatker = b.kdsatker
                    and a.kdgiat = b.kdgiat and a.kdoutput = b.kdoutput and a.kdsoutput = b.kdsoutput and a.kdkmpnen = b.kdkmpnen
                    and a.kdskmpnen = b.kdskmpnen and a.kdakun = b.kdakun and a.kdbeban = b.kdbeban and a.kdjnsban = b.kdjnsban
                    and a.kdctarik = b.kdctarik and a.kd_tahapan = b.kd_tahapan
                    where a.thang = $thang and a.kdsatker = '$kdsatker' and a.kdgiat = '$kdgiat'
                        and a.kdoutput = '$kdoutput' and a.kdsoutput = '$kdsoutput' and a.kdkmpnen = '$kdkmpnen'
                        and a.kdskmpnen = '$kdskmpnen' and a.kdakun = '$kdakun' and a.kdbeban = '$kdbeban'
                        and a.kdjnsban = '$kdjnsban' and a.kd_tahapan $tahapan";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (count($data) > 0) {
            echo json_encode($data[0]);
        } else {
            echo '0';
        }
        return $data;
    }

    //new codes
    public function cek_available_edit($id_detail, $id_ruas, $sta, $ste) {
        $str_sql = "select id_usulan, id_ruas, sta_awal, sta_akhir
                    from detail_usulan_paket
                    where id_usulan = " . $id_detail . " and id_ruas = " . $id_ruas .
                " and sta_awal = " . $sta . "and sta_akhir = " . $ste;
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);
        return $data_available;
    }

    public function cek_available_jembatan_edit($id_detail, $id_ruas, $id_jembatan) {
        $str_sql = "select id_usulan, id_paket, id_ruas, id_jembatan
                    from detail_usulan_paket
                    where id_usulan = " . $id_detail . " and id_ruas = " . $id_ruas .
                " and id_jembatan = '" . $id_jembatan . "'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);
        return $data_available;
    }

    public function cek_available($thang, $kdsatker, $id_ruas, $sta, $ste) {
        $str_sql = "select id_usulan, kd_kegiatan, kd_output, kd_sub_output, kd_komponen, kd_sub_komponen, id_ruas, sta_awal, sta_akhir
                    from detail_usulan_paket
                    where thang = $thang and kdsatker = '$kdsatker' and
                    id_ruas = '$id_ruas' and (((sta_awal < $sta) and (sta_akhir > $sta)) or ((sta_awal < $ste) and (sta_akhir > $ste)) or ((sta_awal >= $sta) and (sta_akhir <= $ste)) or ((sta_awal <= $sta) and (sta_akhir >= $ste)))";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);

        return $data_available;
    }

    public function cek_available_jembatan($id_ruas, $id_jembatan) {
        $str_sql = "select id_usulan, id_paket, id_ruas, id_jembatan
                    from detail_usulan_paket
                    where id_ruas = " . $id_ruas . " and id_jembatan = '" . $id_jembatan . "'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);

        return $data_available;
    }

    public function cek_available_akun($thang, $kdsatker, $kdprog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $kdskmpnen, $kdakun, $kdbeban, $kdjnsban, $kdctarik, $tahapan) {
        $str_sql = "select thang, kdsatker, kdgiat, kdoutput, kdsoutput, kdkmpnen, kdskmpnen, kdakun, kdbeban, kdjnsban, kdctarik
                    from d_akun
                    where thang = '" . $thang . "' and kddept = '033' and kdunit = '04'
                    and kdprogram = '" . $kdprog . "' and kdsatker = '" . $kdsatker . "' and kdgiat = '" . $kdgiat .
                "' and kdoutput = '" . $kdoutput . "' and kdsoutput = '" . $kdsoutput . "' and kdkmpnen = '" . $kdkmpnen .
                "' and kdskmpnen = '" . $kdskmpnen . "' and kdakun = '" . $kdakun . "' and kdbeban = '" . $kdbeban .
                "' and kdjnsban = '" . $kdjnsban . "' and kdctarik = '" . $kdctarik . "' and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);

        return $data_available;
    }

    public function cek_available_akuns($thang, $kdsatker, $prog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $kdskmpnen, $kdakun, $kdbeban, $kdjnsban, $kdctarik, $tahapan) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where thang = '" . $thang . "' and kdsatker = '" . $kdsatker . "' and kd_program = '$prog' and kd_kegiatan = '" . $kdgiat .
                "' and kd_output = '" . $kdoutput . "' and kd_sub_output = '" . $kdsoutput . "' and kd_komponen = '" . $kdkmpnen .
                "' and kd_sub_komponen = '" . $kdskmpnen . "' and kdakun = '" . $kdakun . "' and kdbeban = '" . $kdbeban .
                "' and kdjnsban = '" . $kdjnsban . "' and kdctarik = '" . $kdctarik . "' and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);

        return $data_available;
    }

    public function cek_delete_akun($thang, $kdsatker, $prog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $kdskmpnen, $tahapan) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where thang = '" . $thang . "' and kdsatker = '" . $kdsatker . "' and kd_program = '$prog' and kd_kegiatan = '" . $kdgiat .
                "' and kd_output = '" . $kdoutput . "' and kd_sub_output = '" . $kdsoutput . "' and kd_komponen = '" . $kdkmpnen .
                "' and kd_sub_komponen = '" . $kdskmpnen . "' and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);
        echo $this->db->last_query();

        return $data_available;
    }

    public function get_detail_usulans($id_usulan, $tahap, $thang) {
        $str_sql = "select a.*,a.id_paket as wid_paket,b.nama_sub_komponen,
                        b.rc_ded_status,b.rc_fs_status,b.rc_lahan_status,
                        b.rc_doklin_status,b.kak_status,b.rab_status
                    from detail_usulan_paket a
                    left join usulan_paket b on a.id_paket = b.id_paket
                    where a.id_usulan=" . $id_usulan . " and b.kd_tahapan='$tahap' and a.thang = $thang";
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_usulan_indikatif[0]);
        return $data_usulan_indikatif;
    }

    public function tahapan($tahapan) {
        $hasil = null;
        switch ($tahapan) {
            case "PAGU_INDIKATIF":
                $hasil = "PI";
                break;
            case "PAGU_ANGGARAN":
                $hasil = "PA";
                break;
            case "PAGU_ALOKASI_ANGGARAN":
                $hasil = "PAA";
                break;
        }
        return $hasil;
    }

    public function get_geom_segment($noruas, $staa, $stae) {
        //break;
        //echo $noruas.'-'.$staa.'-'.$stae.'<br>';
        //return false;
        if ((empty($noruas) && empty($staa) && empty($stae))) {
//            echo 'kosong semua';
//            return false;
            echo '{"data":[]}';
        } else if ((!empty($noruas) && strlen($staa) == 0 && empty($stae))) {
//            echo 'ruas ada';
//            return false;
            echo '{"data":[]}';
        } else if ((!empty($noruas) && strlen($staa) == 1 && empty($stae))) {
//             echo 'ruas ada staa ada';
//            return false;
            echo '{"data":[]}';
        } else if ((!empty($noruas) && $staa != 0 && empty($stae))) {
//             echo 'ruas ada staa ada';
//            return false;
            echo '{"data":[]}';
        } else if (!empty($noruas) && !empty($staa) && !empty($stae) || !empty($noruas) && strlen($staa) == 1 && !empty($stae)) {

//            echo 'komplit';
//            return false;
            //$kdsatker = $this->session->users['kd_bujt'];
            //header('Content-Type: application/json');
            //$url = "http://gisportal.binamarga.pu.go.id/arcgis/rest/services/Hosted/ELRS_Road_Network/FeatureServer/0/query?where=RouteId+is+not+null+and+kd_satker+%3D+%2704$kdsatker%27&objectIds=&time=&geometry=&geometryType=esriGeometryEnvelope&inSR=&spatialRel=esriSpatialRelIntersects&distance=&units=esriSRUnit_Foot&relationParam=&outFields=routeid%2Croute_name%2Cnoprop%2Ckd_satker%2Csk_year%2Csurvey_len&returnGeometry=false&maxAllowableOffset=&geometryPrecision=&outSR=&gdbVersion=&historicMoment=&returnDistinctValues=false&returnIdsOnly=false&returnCountOnly=false&returnExtentOnly=false&orderByFields=&groupByFieldsForStatistics=&outStatistics=&returnZ=false&returnM=false&multipatchOption=xyFootprint&resultOffset=&resultRecordCount=&returnTrueCurves=false&sqlFormat=none&resultType=&f=pjson";
            //$url = "https://gisportal.binamarga.pu.go.id/arcgis/rest/services/AMS_BM_DEV/PMSAnalysisResults/MapServer/0/query?where=ROUTE_NAME+%3D+%27$noruas%27+and+(OFFSET_FROM+>%3D+$staa+and+OFFSET_TO+<%3D+$stae)++&text=&objectIds=&time=&geometry=&geometryType=esriGeometryEnvelope&inSR=&spatialRel=esriSpatialRelIntersects&relationParam=&outFields=UNIQUE_ID%2C+PMS_TREATMENT_NAME%2C+TREATMENT_COST%2C+PMS_BUDGET_CAT_ID%2C+SCN_YEAR_NUM%2C+LENGTH%2C+BM_REGION%2C+BM_PROVINCE%2C+ROUTE_NAME%2C+LANE_DIR_NAME%2C+OFFSET_FROM%2C+OFFSET_TO&returnGeometry=false&returnTrueCurves=false&maxAllowableOffset=&geometryPrecision=&outSR=&returnIdsOnly=false&returnCountOnly=false&orderByFields=&groupByFieldsForStatistics=&outStatistics=&returnZ=false&returnM=false&gdbVersion=&returnDistinctValues=false&resultOffset=&resultRecordCount=&queryByDistance=&returnExtentsOnly=false&datumTransformation=&parameterValues=&rangeValues=&f=pjson";
            $url = "https://113.20.29.25:16980/sitia/bm_lrs/lrs_tools/lrs_sta_segment/$noruas/$staa/$stae/wkt";

            $sgj = file_get_contents($url);

            echo $sgj;

//            $gj = json_decode($sgj);
//
//            $data = array();
//
//            foreach ($gj as $item) {
//                $status = $item->status;
//                $geom = $item->geom;
//                //$data[] = $prop;
//                if ($status == "ok"){
//                    return $geom;
//                }else{
//                    return '';
//                }
//            }
            //$data['geom'];
            //print_r($data);
            //echo '{"data": ' . json_encode($realdata) . '}';
        }
    }

    //update code
    public function get_rfsatuan($kdprogss, $kdgiatss, $kdoutputss) {
        $kdprogs = $kdprogss;
        $kdprog = (strlen($kdprogs) === 0) ? 0 : $kdprogs;
        $kdgiats = $kdgiatss;
        $kdgiat = (strlen($kdgiats) === 0) ? 0 : $kdgiats;
        $kdoutputs = $kdoutputss;
        $kdoutput = (strlen($kdoutputs) === 0) ? 0 : $kdoutputs;
        $str_sql = "select *
                    from rf_sat_output
                    where kddept = '033' and kdunit = '04' and kdprog = '$kdprog' and
                    kdgiat = '$kdgiat' and kdoutput = '$kdoutput'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (count($data) > 0) {
            echo json_encode($data[0]);
        } else {
            echo '0';
        }
        return $data;
        //echo $this->db->last_query();
    }

    public function get_output() {
        $param = $this->input->post('data', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $kddept = '033';
        $kdunit = '04';
        $kdprog = $this->input->post("data")["kdprog"];
        $kdgiats = $this->input->post("data")["kdgiat"];
        $kdgiat = ($kdgiats == null || $kdgiats == '') ? 0 : $kdgiats;
        $kdoutputs = $this->input->post("data")["kdoutput"];
        $kdoutput = ($kdoutputs == null || $kdoutputs == '') ? 0 : $kdoutputs;
        $thangs = $this->input->post("data")["thang"];
        $thang = $thangs == null || $thangs == '' ? 0 : $thangs;
        $kdsatker = $this->session->users['kd_bujt'];
        $tahapan = $this->input->post("data")["kd_tahapan"];
        $str_sql = "select *
                    from d_output
                    where kdsatker = '$kdsatker' and kddept = '$kddept' and kdunit = '$kdunit' and kdprogram = '$kdprog' and
                        kdgiat = '$kdgiat' and kdoutput = '$kdoutput' and thang = $thang and kd_tahapan='$tahapan' ";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (count($data) > 0) {
            echo json_encode($data[0]);
        } else {
            echo '0';
        }
        return $data;
    }

    public function get_soutput() {
        $param = $this->input->post('data', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $kdprogs = $this->input->post("data")["kdprog"];
        $kdprog = ($kdprogs == null || $kdprogs == '') ? 0 : $kdprogs;
        $kdgiats = $this->input->post("data")["kdgiat"];
        $kdgiat = ($kdgiats == null || $kdgiats == '') ? 0 : $kdgiats;
        $kdoutputs = $this->input->post("data")["kdoutput"];
        $kdoutput = ($kdoutputs == null || $kdoutputs == '') ? 0 : $kdoutputs;
        $kdsoutputs = $this->input->post("data")["kdsoutput"];
        $kdsoutput = ($kdsoutputs == null || $kdsoutputs == '') ? 0 : $kdsoutputs;
        $thangs = $this->input->post("data")["thang"];
        $thang = $thangs == null || $thangs == '' ? 0 : $thangs;
        $kdsatker = $this->session->users['kd_bujt'];
        $tahapan = $this->input->post("data")["kd_tahapan"];
        $str_sql = "select a.kdgiat, a.kdoutput, a.kdsoutput, a.volsout, sum(b.volume) as jumlahsout
                    from d_soutput a
                    left join detail_usulan_konreg b on a.thang = b.thang and a.kdsatker = b.kdsatker and a.kddept = b.kd_dept and a.kdunit = b.kd_unit
                    and a.kdprogram = b.kd_program and a.kdgiat = b.kd_kegiatan and a.kdoutput = b.kd_output and a.kdsoutput = b.kd_sub_output
                    and a.kd_tahapan = 'PPI'
                    where a.thang = $thang and a.kdsatker = '$kdsatker' and a.kddept = '033' and a.kdunit = '04' and a.kdprogram = '$kdprog' and
                    a.kdgiat = '$kdgiat' and a.kdoutput = '$kdoutput' and a.kdsoutput = '$kdsoutput' and a.kd_tahapan = '$tahapan'
                    group by a.thang, a.kdsatker, a.kddept, a.kdunit, a.kdprogram, a.kdgiat, a.kdoutput, a.kdsoutput, a.volsout";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (count($data) > 0) {
            echo json_encode($data[0]);
        } else {
            echo '0';
        }
        return $data;
    }

    public function get_kmpnen() {
        $param = $this->input->post('data', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $kddept = '033';
        $kdunit = '04';
        $kdprog = $this->input->post("data")["kdprog"];
        $kdgiats = $this->input->post("data")["kdgiat"];
        $kdgiat = ($kdgiats == null || $kdgiats == '') ? 0 : $kdgiats;
        $kdoutputs = $this->input->post("data")["kdoutput"];
        $kdoutput = ($kdoutputs == null || $kdoutputs == '') ? 0 : $kdoutputs;
        $kdsoutputs = $this->input->post("data")["kdsoutput"];
        $kdsoutput = ($kdsoutputs == null || $kdsoutputs == '') ? 0 : $kdsoutputs;
        $kdkmpnens = $this->input->post("data")["kdkmpnen"];
        $kdkmpnen = ($kdkmpnens == null || $kdkmpnens == '') ? 0 : $kdkmpnens;
        $thangs = $this->input->post("data")["thang"];
        $thang = $thangs == null || $thangs == '' ? 0 : $thangs;
        $kdsatker = $this->session->users['kd_bujt'];
        $tahapan = $this->input->post("data")["kd_tahapan"];
        $str_sql = "select *
                    from d_kmpnen
                    where thang = $thang and kdsatker = '$kdsatker' and kddept = '$kddept' and kdunit = '$kdunit' and kdprogram = '$kdprog' and
                        kdgiat = '$kdgiat' and kdoutput = '$kdoutput' and kdsoutput = '$kdsoutput' and kdkmpnen = '$kdkmpnen' and kd_tahapan='$tahapan'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (count($data) > 0) {
            echo json_encode($data[0]);
        } else {
            echo '0';
        }
        return $data;
    }

    public function cek_available_output($thang, $kddept, $kdunit, $kdprog, $kdgiat, $kdoutput, $kdsatker, $tahapan) {
        $str_sql = "select *
                    from d_output
                    where thang = $thang and  kdsatker = '$kdsatker' and kddept = '$kddept' and
                    kdunit = '$kdunit' and kdprogram = '$kdprog' and kdgiat = '$kdgiat' and kdoutput = '$kdoutput'
                    and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        return $data_available;
    }

    public function cek_available_soutput($thang, $kddept, $kdunit, $kdprog, $kdgiat, $kdoutput, $kdsoutput, $kdsatker, $tahapan) {
        $str_sql = "select *
                    from d_soutput
                    where thang = $thang and  kdsatker = '$kdsatker' and kddept = '$kddept' and
                    kdunit = '$kdunit' and kdprogram = '$kdprog' and kdgiat = '$kdgiat' and kdoutput = '$kdoutput' and
                    kdsoutput = '$kdsoutput' and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        return $data_available;
    }

    public function cek_available_kmpnen($thang, $kddept, $kdunit, $kdprog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $kdsatker, $tahapan) {
        $str_sql = "select *
                    from d_kmpnen
                    where thang = $thang and  kdsatker = '$kdsatker' and kddept = '$kddept' and
                    kdunit = '$kdunit' and kdprogram = '$kdprog' and kdgiat = '$kdgiat' and kdoutput = '$kdoutput' and
                    kdsoutput = '$kdsoutput' and kdkmpnen = '$kdkmpnen' and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        return $data_available;
    }

    public function cek_available_outputs($thang, $kdsatker, $prog, $kdgiat, $kdoutput, $tahapan) {
        $str_sql = "select *
                    from d_soutput
                    where thang = $thang and kdsatker = '$kdsatker' and kddept = '033' and
                    kdunit = '04' and kdprogram = '$prog' and kdgiat = '$kdgiat' and
                    kdoutput = '$kdoutput' and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);

        return $data_available;
    }

    public function cek_available_soutputs($thang, $kdsatker, $prog, $kdgiat, $kdoutput, $kdsoutput, $tahapan) {
        $str_sql = "select *
                    from d_kmpnen
                    where thang = $thang and kdsatker = '$kdsatker' and kddept = '033' and
                    kdunit = '04' and kdprogram = '$prog' and kdgiat = '$kdgiat' and
                    kdoutput = '$kdoutput' and kdsoutput = '$kdsoutput' and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);

        return $data_available;
    }

    public function cek_available_kmpnens($thang, $kdsatker, $prog, $kdgiat, $kdoutput, $kdsoutput, $kdkmpnen, $tahapan) {
        $str_sql = "select *
                    from usulan_paket
                    where thang = $thang and kdsatker = '$kdsatker' and kd_dept = '033' and
                    kd_unit = '04' and kd_program = '$prog' and kd_kegiatan = '$kdgiat' and
                    kd_output = '$kdoutput' and kd_sub_output = '$kdsoutput' and kd_komponen = '$kdkmpnen'
                    and kd_tahapan = '$tahapan'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);

        return $data_available;
    }

    public function get_pakets($id_usulan, $tahap) {
        $str_sql = "select *
                    from usulan_paket
                    where id_paket = $id_usulan and kd_tahapan='$tahap'";
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_usulan_indikatif[0]);
        return $data_usulan_indikatif;
    }

    public function get_nmregister($register) {
        $str_sql = "select *
                    from v_register
                    where register = '$register'";
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
        echo json_encode($data_usulan_indikatif[0]);
    }

    public function get_new_sum_out($kdprog, $kdgiat, $kdoutput, $thang, $tahapan) {
        $kddept = '033';
        $kdunit = '04';
        //$kdprog = '08';
        $kdsatker = $this->session->users['kd_bujt'];
        $str_sql = "select SUM(volsout) as sumvol
                    from d_soutput
                    where thang = $thang and kdsatker = '$kdsatker' and kddept = '$kddept' and kdunit = '$kdunit' and kdprogram = '$kdprog' and
                    kdgiat = '$kdgiat' and kdoutput = '$kdoutput' and kd_tahapan = '$tahapan'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (strlen($data[0]['sumvol']) > 0) {
            return json_encode($data[0]['sumvol'], JSON_NUMERIC_CHECK);
        } else {
            return 0;
        }
    }

    public function get_new_sum_sout($kdprog, $kdgiat, $kdoutput, $kdsout, $thang, $tahapan) {
        $kddept = '033';
        $kdunit = '04';
        //$kdprog = '08';
        $kdsatker = $this->session->users['kd_bujt'];
        $str_sql = "select SUM(volume) as jumlah
                    from detail_usulan_paket
                    where thang = $thang and kdsatker = '$kdsatker' and kd_dept = '$kddept' and kd_unit = '$kdunit' and kd_program = '$kdprog'
                    and kd_kegiatan = '$kdgiat' and kd_output = '$kdoutput' and kd_sub_output = '$kdsout' and kd_tahapan = '$tahapan'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (strlen($data[0]['jumlah']) > 0) {
            return json_encode($data[0]['jumlah'], JSON_NUMERIC_CHECK);
        } else {
            return 0;
        }
    }

    public function cek_isblokir($thang, $kdsatker, $kdprog, $giat, $out, $sout, $kmpnen, $skmpnen, $kdakun, $kdbeban, $kdjnsban, $kdctarik, $tahapan) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where thang = $thang and kdsatker = '$kdsatker' and kd_program = '$kdprog' and kd_kegiatan = '$giat' and kd_output = '$out' and
                    kd_sub_output = '$sout' and kd_komponen = '$kmpnen' and kd_sub_komponen = '$skmpnen' and kdakun = '$kdakun' and
                    kdbeban = '$kdbeban' and kdjnsban = '$kdjnsban' and kdctarik = '$kdctarik' and kdblokir = '*' and kd_tahapan = '$tahapan'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function get_kabkota($prov, $kab) {
        $str_sql = "select *
                    from v_ref_kabkota
                    where kd_prov_rkakl = '$prov' and SUBSTRING(kd_kab_bps, 3, 2) = '$kab'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        echo json_encode($data[0]);
    }

    public function get_ppk($kd_bujt, $id_ppk) {
        $thang = $this->session->konfig_tahun_ang;
        $str_sql = "select *
                    from r_satker_ppk
                    where kdsatker = '$kd_bujt' and kd_ppk = '$id_ppk' and thang = $thang";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function get_jmlh_item($id_paket, $kdakun, $beban, $jnsban, $tahapan) {
        $str_sql = "select MAX(noitem) as max
                    from detail_usulan_paket
                    where id_paket = $id_paket and kd_tahapan = '$tahapan'
                    and kdakun = '$kdakun' and kdbeban = '$beban' and kdjnsban = '$jnsban'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function cek_header($idpaket, $tahapan) {
        $kdsatker = $this->session->users['kd_bujt'];
        $str_sql = "select *
                    from detail_usulan_paket
                    where id_paket = $idpaket and kdsatker = '$kdsatker' and
                    kd_tahapan = '$tahapan' and kdheader > 0 order by id_usulan desc";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        echo json_encode($data[0]);
    }

    public function get_header2($idpaket, $idppk, $header2, $tahapan) {
        $ppk = urldecode($idppk);
        $kdsatker = $this->session->users['kd_bujt'];
        $str_sql = "select *
                    from detail_usulan_paket
                    where id_paket = $idpaket and kdsatker = $kdsatker and
                    kd_tahapan = '$tahapan' and id_ppk = '$ppk' and kdheader = '2'
                    and detail = '$header2'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        //echo $this->db->last_query();
        return $data;
    }

    public function get_header2s($idpaket, $kdakun, $idppk, $header2, $tahapan, $header1, $kdsatker) {
        $ppk = urldecode($idppk);
        //$kdsatker = $this->session->users['kd_bujt'];
        if (substr($tahapan, 0, 2) === 'RD'){
            $tahapan = "like 'RD%'";
        } else {
            $tahapan = "= '$tahapan'";
        }
        $str_sql = "select *
                    from detail_usulan_paket
                    where id_paket = $idpaket and kdsatker = $kdsatker and
                    kd_tahapan $tahapan and id_ppk = '$ppk'
                    and header1 = '$header1' and header2 = '$header2' and hargasat = 0 and kdakun = $kdakun";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        //echo $this->db->last_query();
        //return $data;
        echo json_encode($data[0]);
    }

    public function get_jmlh_ppk($id_paket, $tahapan, $kdsatker, $thn, $akun, $beban, $jnsban) {
        $str_sql = "select MAX(header1) as max
                    from detail_usulan_paket
                    where id_paket = $id_paket and kd_tahapan = '$tahapan' and kdsatker = '$kdsatker' and thang = $thn
                    and kdakun = '$akun' and kdbeban = '$beban' and kdjnsban = '$jnsban'
                    and header2 = '00' and hargasat = 0";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function get_jmlh_header2($id_paket, $kdakun, $ppk, $tahapan, $beban, $jnsban) {
        $str_sql = "select MAX(header2) as max
                    from detail_usulan_paket
                    where id_paket = $id_paket and kd_tahapan = '$tahapan'
                    and id_ppk = '$ppk' and kdheader = '2' and kdakun = '$kdakun'
                    and kdbeban = '$beban' and kdjnsban = '$jnsban'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function cek_available_header2($id_paket, $tahapan, $kdsatker, $thn, $akun, $beban, $jnsban, $ppk) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where id_paket = $id_paket and kd_tahapan = '$tahapan' and kdsatker = '$kdsatker' and thang = $thn
                    and kdakun = '$akun' and kdbeban = '$beban' and kdjnsban = '$jnsban' and id_ppk = '$ppk' and kdheader = '2'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function cek_sameheader2($id_paket, $tahapan, $kdsatker, $thn, $akun, $beban, $jnsban, $ppk, $header2) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where id_paket = $id_paket and kd_tahapan = '$tahapan' and kdsatker = '$kdsatker' and thang = $thn
                    and kdakun = '$akun' and kdbeban = '$beban' and kdjnsban = '$jnsban' and id_ppk = '$ppk'
                    and header1 is not null and header2 is not null and hargasat = 0 and detail = '$header2'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function cek_ppk_akun($id_paket, $kdakun, $kdbeban, $kdjnsban, $ppk, $tahapan, $kdsatker, $nmppk) {
        $query = $this->db->query("select * from detail_usulan_paket 
            where id_paket = $id_paket and kdsatker = '$kdsatker' and kd_tahapan = '$tahapan' 
            and kdakun = '$kdakun' and kdbeban = '$kdbeban' and kdjnsban = '$kdjnsban'
            and detail = '$nmppk' and header1 = '01' and header2 = '00' and hargasat = 0");
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function cek_paket_akun($id_paket, $kdakun, $beban, $jnsban, $tahapan, $kdsatker, $ppk) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where id_paket = $id_paket and kdsatker = '$kdsatker' and kd_tahapan = '$tahapan' and
                    kdakun = $kdakun and kdbeban = '$beban' and kdjnsban = '$jnsban' and id_ppk = '$ppk'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    public function ssp_akademisi() {
        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();

        $kd_prov_irms = $query->row()->kd_prov_irmsv3;

        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $subrole = $this->session->users['id_user_group'];
        $roledesc = $this->session->users['role'];

        $yearnow = $this->session->konfig_tahun_ang;

        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);

        $whereClause = $this->check_satker_if_array3($satker, $childsatker, $kd_prov_irms, $yearnow);

        $table = 'v_usulan_akademis';
        $primaryKey = 'id_usulan';

        $columns = array(
            array('db' => 'id_usulan', 'dt' => 0),
            array('db' => 'thang', 'dt' => 1),
            array('db' => 'detail', 'dt' => 2),
            //ids
            array('db' => 'sumber_usulan', 'dt' => 3),
            array('db' => 'tgl_usulan', 'dt' => 4),
            array('db' => 'pengusul2', 'dt' => 5),
            array('db' => 'nama_prov', 'dt' => 6),
            array('db' => 'rkakl_volume', 'dt' => 7),
            array('db' => 'rkakl_biaya', 'dt' => 8, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                }),
            array('db' => 'status_kewenangan', 'dt' => 9),
            array('db' => 'evaluasi', 'dt' => 10),
            array('db' => 'rc_fs', 'dt' => 11),
            array('db' => 'rc_ded', 'dt' => 12),
            array('db' => 'rc_lahan', 'dt' => 13),
            array('db' => 'rc_dokling', 'dt' => 14),
            array('db' => 'keterangan', 'dt' => 15),
            array('db' => 'usulan_volume', 'dt' => 16),
            array('db' => 'usulan_biaya', 'dt' => 17, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                })
        );
        //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
        if ($id_user == 1 || $id_user == 2 || $roledesc == 'span' || $roledesc == 'dpsi' || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                datatable_ssp($table, $primaryKey, $columns, $whereClause);
            } else {
                //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
                if ($roledesc == 'jbh' || $roledesc == 'konstruksi' || $roledesc == 'bmn' || $roledesc == 'renwilI' || $roledesc == 'renwilII' || $roledesc == 'stppreservasi') {
                    datatable_ssp($table, $primaryKey, $columns, "thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and kd_satker_balai='$subrole'");
                } else {
                    datatable_ssp($table, $primaryKey, $columns, "thang=$yearnow and kddisposisi='$satker' and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1");
                    //$parentsatker = $this->parent_satker($satker);
                    //datatable_ssp($table, $primaryKey, $columns, "(thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and ((stat_usulan1=1)) and ((kddisposisi is not null) and (kddisposisi = '$satker'))) or (thang = $yearnow-1 and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and (stat_usulan2=3) and ((kddisposisi is not null) and (kddisposisi = '$satker')))");
                }
            }
        }
    }

    public function ssp_kl() {
        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();

        $kd_prov_irms = $query->row()->kd_prov_irmsv3;

        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $subrole = $this->session->users['id_user_group'];
        $roledesc = $this->session->users['role'];

        $yearnow = $this->session->konfig_tahun_ang;

        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);

        $whereClause = $this->check_satker_if_array3($satker, $childsatker, $kd_prov_irms, $yearnow);

        $table = 'v_usulan_kl';
        $primaryKey = 'id_usulan';

        $columns = array(
            array('db' => 'id_usulan', 'dt' => 0),
            array('db' => 'thang', 'dt' => 1),
            array('db' => 'detail', 'dt' => 2),
            //ids
            array('db' => 'sumber_usulan', 'dt' => 3),
            array('db' => 'tgl_usulan', 'dt' => 4),
            array('db' => 'pengusul2', 'dt' => 5),
            array('db' => 'nama_prov', 'dt' => 6),
            array('db' => 'rkakl_volume', 'dt' => 7),
            array('db' => 'rkakl_biaya', 'dt' => 8, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                }),
            array('db' => 'status_kewenangan', 'dt' => 9),
            array('db' => 'evaluasi', 'dt' => 10),
            array('db' => 'rc_fs', 'dt' => 11),
            array('db' => 'rc_ded', 'dt' => 12),
            array('db' => 'rc_lahan', 'dt' => 13),
            array('db' => 'rc_dokling', 'dt' => 14),
            array('db' => 'keterangan', 'dt' => 15),
            array('db' => 'usulan_volume', 'dt' => 16),
            array('db' => 'usulan_biaya', 'dt' => 17, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                })
        );
        //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
        if ($id_user == 1 || $id_user == 2 || $roledesc == 'span' || $roledesc == 'dpsi' || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                datatable_ssp($table, $primaryKey, $columns, $whereClause);
            } else {
                //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
                if ($roledesc == 'jbh' || $roledesc == 'konstruksi' || $roledesc == 'bmn' || $roledesc == 'renwilI' || $roledesc == 'renwilII' || $roledesc == 'stppreservasi') {
                    datatable_ssp($table, $primaryKey, $columns, "thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and kd_satker_balai='$subrole'");
                } else {
                    datatable_ssp($table, $primaryKey, $columns, "thang=$yearnow and kddisposisi='$satker' and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1");
//                    $parentsatker = $this->parent_satker($satker);
//                    datatable_ssp($table, $primaryKey, $columns, "(thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and ((stat_usulan1=1)) and ((kddisposisi is not null) and (kddisposisi = '$satker'))) or (thang = $yearnow-1 and kd_prov_irms = '$kd_prov_irms' and ((kd_satker_balai = '$satker') or (kd_satker_balai = $parentsatker)) and (stat_usulan2=3) and ((kddisposisi is not null) and (kddisposisi = '$satker')))");
                }
            }
        }
    }

    function get_tag($id_paket, $id_detail) {
        $str_sql = "select *
                    from rujukan_paket a
                    left join r_rujukan b on a.kd_jns_rujukan = b.kd_jns_rujukan
                    where a.id_paket = $id_paket and a.id_usulan = $id_detail";
        $query = $this->db->query($str_sql);
        $data = $query->result();
        if (count($data) > 0) {
            echo json_encode($data);
        } else {
            echo 0;
        }
    }

    public function isRuasOverLap($thang, $giat, $out, $sout, $kom, $ruas, $sta, $ste) {
        $over = $this->db->query("select * from detail_usulan_paket where thang = $thang and kd_kegiatan = '$giat' and kd_output = '$out' and kd_sub_output = '$sout' and kd_komponen = '$kom' and
                id_ruas = '$ruas' and (((sta_awal < $sta) and (sta_akhir > $sta)) or ((sta_awal < $ste) and (sta_akhir > $ste)) or ((sta_awal >= $sta) and (sta_akhir <= $ste))
                or ((sta_awal <= $sta) and (sta_akhir >= $ste)))")->result_array();
        return $over;
    }

    public function isJembatanOverLap($thang, $giat, $out, $sout, $kom, $jem) {
        $over = $this->db->query("select * from detail_usulan_paket where thang = $thang and id_jembatan = '$jem'")->result_array();
        return $over;
    }

    public function get_rujukan_irmsv3() {
        $noruas = $this->input->post("ruas", TRUE);
        if (strlen($noruas) > 0) {
            $noruas = $noruas;
        } else {
            $noruas = '';
        }
        // ." and Linkid='$noruas' and StartKM >=$sta and EndKM <=$staa"
        $sta = $this->input->post("sta", TRUE);
        if (strlen($sta) > 0) {
            $sta = (float) $this->input->post("sta", TRUE) / 1000;
        } else {
            $sta = '';
        }
        $stae = $this->input->post("staa", TRUE);
        if (strlen($stae) > 0) {
            $stae = (float) $this->input->post("staa", TRUE) / 1000;
        } else {
            $stae = '';
        }

        if ($noruas != '' and $sta != '' and $stae != '') {
            $filter = " and Linkid='$noruas' and StartKM >=$sta and EndKM <=$stae";
        } elseif ($noruas != '' and $sta != '' and $stae == '') {
            $filter = " and Linkid='$noruas' and StartKM >=$sta";
        } elseif ($noruas != '' and $sta == '' and $stae == '') {
            $filter = " and Linkid='$noruas'";
        } else {
            $filter = "";
        }

        $table = 'v_tmp_irms';
        $primaryKey = 'id';

        $columns = array(
            array('db' => 'LinkName', 'dt' => 0), //ditampilkan//tarik
            //array('db' => 'Lane', 'dt' => 1), //tarik
            //array('db' => 'PMSSection', 'dt' => 2), //tarik
            array('db' => 'StartKM', 'dt' => 3), //tarik
            array('db' => 'EndKM', 'dt' => 4),
            //array('db' => 'Length', 'dt' => 5), //tarik
            //array('db' => 'ScenarioYear', 'dt' => 6), //tarik
            //array('db' => 'IRI', 'dt' => 7),
            //array('db' => 'KPI', 'dt' => 8),
            //array('db' => 'PCI', 'dt' => 9), //tarik
            array('db' => 'Treatment', 'dt' => 10), //tarik
            array('db' => 'TreatmentCost', 'dt' => 11), //tarik
            array('db' => 'nmgiat', 'dt' => 12), //tarik
            array('db' => 'nmoutput', 'dt' => 13), //tarik
            array('db' => 'nmsoutput', 'dt' => 14), //tidak ditampilkan//tarik
            array('db' => 'nmkmpnen', 'dt' => 15), //tarik
            array('db' => 'kdgiat', 'dt' => 16), //tarik
            array('db' => 'kdoutput', 'dt' => 17), //tarik
            array('db' => 'kdsoutput', 'dt' => 18), //tarik
            array('db' => 'kdkmpnen', 'dt' => 19), //tarik
            array('db' => 'kd_prov_rkakl', 'dt' => 20), //tarik
            array('db' => 'kd_kab_ruas', 'dt' => 21), //tarik
            array('db' => 'Linkid', 'dt' => 22), //tarik
            array('db' => 'id', 'dt' => 23), //tarik
        );
        //if ($this->session->users['id_user_group'] == 7) {
        if ($this->session->users['role'] == 'satkernonfisik') {
            datatable_ssp($table, $primaryKey, $columns, "kdinduk=" . $this->session->users['kd_bujt'] . $filter);
        } else {
            datatable_ssp($table, $primaryKey, $columns, "kdsatker=" . $this->session->users['kd_bujt'] . $filter);
        }
    }

    public function get_rujukan_jembatan_irmsv3() {
        $noruas = $this->input->post("ruas", TRUE);
        $jembatan = $this->input->post("jembatan", TRUE);
        if ($noruas != '' and $jembatan != '') {
            $filter = " and linkid='$noruas' and no_jembatan='$jembatan'";
        } elseif ($noruas != '' and $jembatan == '') {
            $filter = " and linkid='$noruas'";
        } else {
            $filter = "";
        }

        $table = 'v_tmp_jembatan';
        $primaryKey = 'id';

        $columns = array(
            array('db' => 'nama_ruas', 'dt' => 0), //ditampilkan//tarik
            array('db' => 'nama_jembatan', 'dt' => 1), //tarik
            array('db' => 'panjang', 'dt' => 2), //tarik
            array('db' => 'penanganan', 'dt' => 3), //tarik
            array('db' => 'estimasi_biaya', 'dt' => 4),
            array('db' => 'lat', 'dt' => 5), //tarik
            array('db' => 'long', 'dt' => 6), //tarik
            array('db' => 'nmgiat', 'dt' => 7),
            array('db' => 'nmoutput', 'dt' => 8),
            array('db' => 'nmsoutput', 'dt' => 9), //tarik
            array('db' => 'nmkmpnen', 'dt' => 10), //tarik
            array('db' => 'linkid', 'dt' => 11), //tarik
            array('db' => 'no_jembatan', 'dt' => 12), //tarik
            array('db' => 'kd_prov_rkakl', 'dt' => 13), //tarik
            array('db' => 'kd_kab_ruas', 'dt' => 14), //tidak ditampilkan//tarik
            array('db' => 'nmkmpnen', 'dt' => 15), //tarik
            array('db' => 'kdgiat', 'dt' => 16), //tarik
            array('db' => 'kdoutput', 'dt' => 17), //tarik
            array('db' => 'kdsoutput', 'dt' => 18), //tarik
            array('db' => 'kdkmpnen', 'dt' => 19), //tarik
            array('db' => 'id', 'dt' => 20), //tarik
        );
        //if ($this->session->users['id_user_group'] == 7) {
        if ($this->session->users['role'] == 'satkernonfisik') {
            datatable_ssp($table, $primaryKey, $columns, "kdinduk=" . $this->session->users['kd_bujt'] . $filter);
        } else {
            datatable_ssp($table, $primaryKey, $columns, "kdsatker=" . $this->session->users['kd_bujt'] . $filter);
        }
    }

//    public function get_handleCekPPK() {
//        $kddept = '033';
//        $kdunit = '04';
//        $kdprog = '08';
//        $kdgiats = $this->input->post("data")["kdgiat"];
//        $kdgiat = (strlen($kdgiats) === 0) ? 0 : $kdgiats;
//        $kdoutputs = $this->input->post("data")["kdoutput"];
//        $kdoutput = (strlen($kdoutputs) === 0) ? 0 : $kdoutputs;
//        $kdsoutputs = $this->input->post("data")["kdsoutput"];
//        $kdsoutput = (strlen($kdsoutputs) === 0) ? 0 : $kdsoutputs;
//        $kdkmpnens = $this->input->post("data")["kdkmpnen"];
//        $kdkmpnen = (strlen($kdkmpnens) === 0) ? 0 : $kdkmpnens;
//        $kdskmpnens = $this->input->post("data")["kdskmpnen"];
//        $kdskmpnen = (strlen($kdskmpnens) === 0) ? 0 : $kdskmpnens;
//        $thangs = $this->input->post("data")["thang"];
//        $thang = $thangs == null || $thangs == '' ? 0 : $thangs;
//        $kdsatker = $this->session->users['kd_bujt'];
//        $tahapan = $this->input->post("data")["kd_tahapan"];
//        $id_paket = $this->input->post("data")["id_paket"];
//        $id_ppk = $this->input->post("data")["id_ppk"];
//        $str_sql = "select *
//                    from detail_usulan_paket
//                    where id_paket = $id_paket and thang = $thang and kdsatker = '$kdsatker' and kd_dept = '$kddept' and kd_unit = '$kdunit' and kd_program = '$kdprog' and
//                    kd_kegiatan = '$kdgiat' and kd_output = '$kdoutput' and kd_sub_output = '$kdsoutput' and kd_komponen = $kdkmpnen and kd_sub_komponen = '$kdskmpnen' and kd_tahapan = '$tahapan'
//                    and id_ppk = '$id_ppk' and header2 is not null order by id_usulan desc";
//        $query = $this->db->query($str_sql);
//        $data = json_decode(json_encode($query->result()), true);
//        //echo $this->db->last_query();
//        if (count($data) > 0) {
//            echo json_encode($data[0]);
//        } else {
//            echo '0';
//        }
//        return $data;
//    }

    public function cek_available_ppks($thang, $kdsatker, $kdakun, $kdbeban, $kdjnsban, $tahapan, $idppk) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where thang = $thang and kdsatker = '$kdsatker' and kdakun = '$kdakun'
                    and kdbeban = '$kdbeban' and kdjnsban = '$kdjnsban' and kd_tahapan = '$tahapan'
                    and id_ppk = '$idppk'";
        //echo $this->db->lastquery();
        $query = $this->db->query($str_sql);
        $data_available = json_decode(json_encode($query->result()), true);
        //echo json_encode($data_available[0]);

        return $data_available;
    }

    public function cek_update_item($id_paket, $kdakun, $beban, $jnsban, $tahapan, $satker, $tahun) {
        $str_sql = "select id_usulan, kd_sub_komponen
                    from detail_usulan_paket
                    where id_paket = $id_paket and kdsatker = '$satker' and kdakun = '$kdakun' and kdbeban = '$beban'
                    and kdjnsban = '$jnsban' and kd_tahapan = '$tahapan' and thang = $tahun
                    order by header1, header2, 
                        case when kdheader is null then 3
                             when kdheader = '' then 3
                        else kdheader
                        end, noitem, jumlah asc";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data;
    }

    //new code
    public function ssp_move() {
        $satker = $this->session->users['kd_bujt'];
        $yearnow = $this->session->konfig_tahun_ang;
        $tahapan = $this->input->post('tahapan', TRUE);
        $giat = $this->input->post('giat', TRUE);
        $out = $this->input->post('out', TRUE);
        $sout = $this->input->post('sout', TRUE);
        $id = $this->input->post('idpaket', TRUE);

        $table = 'usulan_paket';
        $primaryKey = 'id_paket';

        $columns = array(
            //array('db' => 'id_paket', 'dt' => 0),
            array('db' => 'kd_kegiatan', 'dt' => 1),
            array('db' => 'kd_output', 'dt' => 2),
            array('db' => 'kd_sub_output', 'dt' => 3),
            array('db' => 'kd_komponen', 'dt' => 4),
            array('db' => 'kd_sub_komponen', 'dt' => 5),
            array('db' => 'nama_sub_komponen', 'dt' => 6),
            array('db' => 'id_paket', 'dt' => 7)
        );

        datatable_ssp($table, $primaryKey, $columns, "kd_tahapan='$tahapan' and kdsatker='$satker' and thang=$yearnow and kd_kegiatan ='$giat'
                       and kd_output = '$out' and kd_sub_output = '$sout' and id_paket <> $id");
    }

    public function moveDetail() {
        $param = $this->input->post('data', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $id_detail = $this->input->post("data")["id_detail"];
        $id_paket = $this->input->post("data")["id_paket"];
        $tahapan = $this->input->post("data")["kd_tahapan"];
        $yearnow = $this->session->konfig_tahun_ang;
        $kdsatker = $this->session->users['kd_bujt'];
        $datafullpaket = $this->db->get_where('usulan_paket', array('id_paket' => $id_paket, 'kd_tahapan' => $tahapan))->row();
        //print_r($datafullpaket);
        $update = array('kd_kegiatan' => $datafullpaket->kd_kegiatan, 'kd_output' => $datafullpaket->kd_output, 'kd_sub_output' => $datafullpaket->kd_sub_output,
            'kd_komponen' => $datafullpaket->kd_komponen, 'kd_sub_komponen' => $datafullpaket->kd_sub_komponen, 'id_paket' => $id_paket);
        //print_r($update);
        //$this->db->where('id_usulan', $id_detail);
        $this->db->where(array('id_usulan' => $id_detail, 'kd_tahapan' => $tahapan, 'thang' => $yearnow, 'kdsatker' => $kdsatker))->update('detail_usulan_paket', $update);
        $this->db->where(array('id_usulan' => $id_detail, 'kd_tahapan' => $tahapan, 'thang' => $yearnow))->update('geo_pok_ruas', array('id_paket' => $id_paket));
        $this->db->where(array('id_usulan' => $id_detail, 'kd_tahapan' => $tahapan, 'thang' => $yearnow))->update('geo_pok_jembatan', array('id_paket' => $id_paket));

        //new code
        $this->update_item($id_paket, $datafullpaket->kdakun, $datafullpaket->kdbeban, $datafullpaket->kdjnsban, $tahapan, $kdsatker, $yearnow);
    }

    public function update_item($id_paket, $akun, $beban, $jnsban, $tahapan, $satker, $thang) {
        //new code
        $get_item = $this->cek_update_item($id_paket, $akun, $beban, $jnsban, $tahapan, $satker, $thang);
        if (count($get_item) > 0) {
            for ($i = 0; $i < count($get_item); $i++) {
                $data = json_decode(json_encode($get_item[$i]), true);
                $this->db->set('noitem', $i + 1);
                $this->db->where('id_usulan', $data['id_usulan'])->update('detail_usulan_paket');
            }
        }
    }

//    public function update_ppk($idppk, $iddetail, $thang, $tahapan){
//        $ppklama = $this->db->get_where('detail_usulan_paket', array('id_usulan' => (int)$iddetail))->row();
//        if(strlen($ppklama->id_ppk) > 0){
//            if ($ppklama->id_ppk !== $idppk){
//                $a = $this->get_ppk($ppklama->kdsatker, $idppk);
//                if (count($a) > 0){
//                    $this->db->set('detail', $a[0]['nama_ppk']);
//                    $this->db->where(array('id_paket' => $ppklama->id_paket, 'kdakun' => $ppklama->kdakun,
//                        'kdbeban' => $ppklama->kdbeban, 'kdjnsban' => $ppklama->kdjnsban, 'id_ppk' => $ppklama->id_ppk,
//                        'thang' => $thang, 'kdsatker' => $ppklama->kdsatker, 'kd_tahapan' => $tahapan, 'kdheader' => '1'
//                        ))->update('detail_usulan_paket');
//
//                    $this->db->set('id_ppk', $idppk);
//                    $this->db->where(array('id_paket' => $ppklama->id_paket, 'kdakun' => $ppklama->kdakun,
//                        'kdbeban' => $ppklama->kdbeban, 'kdjnsban' => $ppklama->kdjnsban, 'id_ppk' => $ppklama->id_ppk,
//                        'thang' => $thang, 'kdsatker' => $ppklama->kdsatker, 'kd_tahapan' => $tahapan))->update('detail_usulan_paket');
//
//                    $doubleppk = $this->double_ppk($idppk, $ppklama->id_paket, $ppklama->kdsatker, $ppklama->kdakun, $ppklama->kdbeban, $ppklama->kdjnsban, $tahapan, $thang);
//                    if(count($doubleppk) > 1){
//                        $this->db->where(array('thang' => $thang, 'kd_tahapan' => $tahapan, 'id_usulan' => $doubleppk[0]->id_usulan))->delete('detail_usulan_paket');
//
//                    }
//
//                    $this->update_header1_1($ppklama->id_paket, $ppklama->kdsatker, $ppklama->kdakun, $ppklama->kdbeban, $ppklama->kdjnsban, $tahapan, $thang);
//                    $this->update_item($ppklama->id_paket, $ppklama->kdakun, $ppklama->kdbeban, $ppklama->kdjnsban, $tahapan, $ppklama->kdsatker, $thang);
//                }
//            }
//        }
//    }
//
//    public function double_ppk($idppk, $idpaket, $satker, $akun, $beban, $jnsban, $tahapan, $tahun){
//        $str_sql = "SELECT id_usulan FROM detail_usulan_paket
//                    WHERE id_ppk = '$idppk' and id_paket = $idpaket and kdsatker = '$satker' and kdakun = '$akun'
//                        and kdbeban = '$beban' and kdjnsban = '$jnsban' and kd_tahapan = '$tahapan' and thang = $tahun
//                        and kdheader = 1
//                        ORDER BY header1 DESC";
//        $query = $this->db->query($str_sql)->result();
//        return $query;
//    }
//
    public function update_header1($idpaket, $satker, $akun, $beban, $jnsban, $tahapan, $tahun) {
        $str_sql = "SELECT id_ppk FROM detail_usulan_paket
                    WHERE id_paket = $idpaket and kdsatker = '$satker' and kdakun = '$akun'
                        and kdbeban = '$beban' and kdjnsban = '$jnsban' and kd_tahapan = '$tahapan' and thang = $tahun
                        and kdheader = 1
                        ORDER BY id_ppk ASC";
        $re = $this->db->query($str_sql)->result();

        if (count($re) > 0) {
            for ($i = 0; $i < count($re); $i++) {
                $ppk = $re[$i]->id_ppk;
                $num = $i + 1;
                $nums = str_pad($num, 2, '0', STR_PAD_LEFT);
                $this->db->set('header1', $nums);
                $this->db->where(array('thang' => $tahun, 'kd_tahapan' => $tahapan, 'id_paket' => $idpaket, 'kdsatker' => $satker,
                    'kdakun' => $akun, 'kdbeban' => $beban, 'kdjnsban' => $jnsban, 'id_ppk' => $ppk))->update('detail_usulan_paket');

                $this->update_header2($idpaket, $satker, $akun, $beban, $jnsban, $tahapan, $tahun, $nums);
            }
        }
    }

    public function ada_ppk($idppk, $header1, $idpaket, $satker, $akun, $beban, $jnsban, $tahapan, $tahun) {
        $str_sql = "SELECT id_usulan FROM detail_usulan_paket
                    WHERE id_paket = $idpaket and kdsatker = '$satker' and kd_tahapan = '$tahapan' and thang = $tahun
                        and kdakun = '$akun' and kdbeban = '$beban' and kdjnsban = '$jnsban' and id_ppk = '$idppk'
                        and header1 = '$header1'";
        $query = $this->db->query($str_sql)->result();
        return $query;
    }

    public function ada_ppk2($idppk, $header1, $idpaket, $satker, $akun, $beban, $jnsban, $tahapan, $tahun, $header2) {
        $str_sql = "SELECT id_usulan FROM detail_usulan_paket
                    WHERE id_ppk = '$idppk' and id_paket = $idpaket and kdsatker = '$satker' and kdakun = '$akun'
                        and kdbeban = '$beban' and kdjnsban = '$jnsban' and kd_tahapan = '$tahapan' and thang = $tahun
                        and header1 = '$header1' and header2 = '$header2'";
        $query = $this->db->query($str_sql)->result();
        return $query;
    }

    public function cek_ppk1($id_paket, $kdakun, $beban, $jnsban, $idppk, $tahapan, $satker, $thang) {
        $str_sql = "select *
                    from detail_usulan_paket
                    where id_paket = $id_paket and kdsatker = '$satker' and thang = $thang and id_ppk = '$idppk' and
                    kdakun = $kdakun and kdbeban = '$beban' and kdjnsban = '$jnsban' and kd_tahapan = '$tahapan'
                    and kdheader = 1 order by id_usulan desc";
        $query = $this->db->query($str_sql)->result();
        return $query;
    }

    public function update_header2($idpaket, $satker, $akun, $beban, $jnsban, $tahapan, $tahun, $header1) {
        $str_sql = "SELECT id_usulan, header2 FROM detail_usulan_paket
                    WHERE id_paket = $idpaket and kdsatker = '$satker' and kdakun = '$akun'
                        and kdbeban = '$beban' and kdjnsban = '$jnsban' and kd_tahapan = '$tahapan' and thang = $tahun
                        and header1 = '$header1' and kdheader = 2
                        ORDER BY header2 ASC";
        $header = $this->db->query($str_sql)->result();

        if (count($header) > 0) {
            for ($i = 0; $i < count($header); $i++) {
                $header2 = $header[$i]->header2;
                $num = $i + 1;
                $nums = str_pad($num, 2, '0', STR_PAD_LEFT);
                $this->db->set('header2', $nums);
                $this->db->where(array('thang' => $tahun, 'kd_tahapan' => $tahapan, 'id_paket' => $idpaket, 'kdsatker' => $satker,
                    'kdakun' => $akun, 'kdbeban' => $beban, 'kdjnsban' => $jnsban, 'header1' => $header1, 'header2' => $header2))->update('detail_usulan_paket');
            }
        }
    }

    public function add_header1($id_paket, $kdsatker, $id_user, $kab, $dekon, $arr_insert, $kdkabkotbpss, $ppks, $header) {
        $data_forheader = [
            "kd_dept" => '033',
            "kd_unit" => '04',
            "kd_program" => $arr_insert["kd_program"],
            "kd_kegiatan" => $arr_insert["kd_kegiatan"],
            "kd_output" => $arr_insert["kd_output"],
            "kd_sub_output" => $arr_insert["kd_sub_output"],
            "kd_komponen" => $arr_insert["kd_komponen"],
            "kd_sub_komponen" => $arr_insert["kd_sub_komponen"],
            "thang" => (int) $arr_insert["thang"],
            "id_user" => (int) $id_user,
            "created_by" => (int) $id_user,
            "kdgbkpk" => $arr_insert["kdgbkpk"],
            "kdakun" => $arr_insert["kdakun"],
            "id_ppk" => $arr_insert["id_ppk"],
            "kdsatker" => $kdsatker,
            "kdlokasi" => $arr_insert["kdlokasi"],
            "kdkabkota2" => $kdkabkotbpss,
            "kdkabkota" => $kab,
            "id_paket" => $id_paket,
            "detail" => $ppks,
            "volume" => 0,
            "hargasat" => 0,
            "jumlah" => 0,
            "kdbeban" => $arr_insert["kdbeban"],
            "kdjnsban" => $arr_insert["kdjnsban"],
            "kdctarik" => $arr_insert["kdctarik"],
            "kd_tahapan" => $arr_insert["kd_tahapan"],
            "register" => $arr_insert["register"],
            "carahitung" => $arr_insert["carahitung"],
            "header1" => (string) $header,
            "header2" => '00',
            "kdheader" => 1,
            "kddekon" => $dekon,
            "kdjendok" => '01',
            "kdib" => '00',
            "kdkppn" => $arr_insert["kdkppn"]
        ];

        $this->db->set('created_at', 'getutcdate()', FALSE);
        $this->db->set('updated_at', 'getutcdate()', FALSE);
        $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
        $this->db->insert('detail_usulan_paket', $data_forheader);
    }

    public function add_header2($id_paket, $kdsatker, $id_user, $kab, $dekon, $arr_insert, $kdkabkotbpss, $header1, $header2) {
        $data_forheader2 = [
            "kd_dept" => '033',
            "kd_unit" => '04',
            "kd_program" => $arr_insert["kd_program"],
            "kd_kegiatan" => $arr_insert["kd_kegiatan"],
            "kd_output" => $arr_insert["kd_output"],
            "kd_sub_output" => $arr_insert["kd_sub_output"],
            "kd_komponen" => $arr_insert["kd_komponen"],
            "kd_sub_komponen" => $arr_insert["kd_sub_komponen"],
            "thang" => (int) $arr_insert["thang"],
            "id_user" => (int) $id_user,
            "created_by" => (int) $id_user,
            "kdgbkpk" => $arr_insert["kdgbkpk"],
            "kdakun" => $arr_insert["kdakun"],
            "id_ppk" => $arr_insert["id_ppk"],
            "kdsatker" => $kdsatker,
            "kdlokasi" => $arr_insert["kdlokasi"],
            "kdkabkota2" => $kdkabkotbpss,
            "kdkabkota" => $kab,
            "id_paket" => $id_paket,
            "detail" => $arr_insert['header2'],
            "volume" => 0,
            "hargasat" => 0,
            "jumlah" => 0,
            "kdbeban" => $arr_insert["kdbeban"],
            "kdjnsban" => $arr_insert["kdjnsban"],
            "kdctarik" => $arr_insert["kdctarik"],
            "kd_tahapan" => $arr_insert["kd_tahapan"],
            "register" => $arr_insert["register"],
            "carahitung" => $arr_insert["carahitung"],
            "header1" => (string) $header1,
            "header2" => (string) $header2,
            "kdheader" => '2',
            "kddekon" => $dekon,
            "kdjendok" => '01',
            "kdib" => '00'
        ];

        $this->db->set('created_at', 'getutcdate()', FALSE);
        $this->db->set('updated_at', 'getutcdate()', FALSE);
        $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
        $this->db->insert('detail_usulan_paket', $data_forheader2);
    }

    public function tambah_akun($kdsatker, $kab, $dekon, $tahapan, $arr_insert) {
        $data_detail_akun = [
            "thang" => $arr_insert["thang"],
            "kdjendok" => '01',
            "kdsatker" => $kdsatker,
            "kddept" => '033',
            "kdunit" => '04',
            "kdprogram" => $arr_insert["kd_program"],
            "kdgiat" => $arr_insert["kd_kegiatan"],
            "kdoutput" => $arr_insert["kd_output"],
            "kdlokasi" => $arr_insert["kdlokasi"],
            "kdkabkota" => $kab,
            "kddekon" => $dekon,
            "kdsoutput" => $arr_insert["kd_sub_output"],
            "kdkmpnen" => $arr_insert["kd_komponen"],
            "kdskmpnen" => $arr_insert["kd_sub_komponen"],
            "kdakun" => $arr_insert["kdakun"],
            "kdkppn" => $arr_insert["kdkppn"],
            "kdbeban" => $arr_insert["kdbeban"],
            "kdjnsban" => $arr_insert["kdjnsban"],
            "kdctarik" => $arr_insert["kdctarik"],
            "register" => $arr_insert["register"],
            "carahitung" => $arr_insert["carahitung"],
            "prosenphln" => $arr_insert["prosenphln"],
            "prosenrkp" => $arr_insert["prosenrkp"],
            "prosenrmp" => $arr_insert["prosenrmp"],
            "kppnrkp" => $arr_insert["kppnrkp"],
            "kppnrmp" => $arr_insert["kppnrmp"],
            "kppnphln" => $arr_insert["kppnphln"],
            "regdam" => $arr_insert["regdam"],
            //"id_paket" => $this->input->post("formData")["id_paket"],
            //"kdluncuran" => '1',
            "kdblokir" => strlen($arr_insert["kdblokir"]) === 0 ? NULL : $arr_insert["kdblokir"],
            "uraiblokir" => $arr_insert["urblokir"],
            "kdib" => '00',
            "kd_tahapan" => $tahapan
        ];
        $this->db->insert('d_akun', $data_detail_akun);

        $data_detail_cttakun = [
            "thang" => $arr_insert["thang"],
            "kdjendok" => '01',
            "kdsatker" => $kdsatker,
            "kddept" => '033',
            "kdunit" => '04',
            "kdprogram" => $arr_insert["kd_program"],
            "kdgiat" => $arr_insert["kd_kegiatan"],
            "kdoutput" => $arr_insert["kd_output"],
            "kdib" => '00',
            "kdlokasi" => $arr_insert["kdlokasi"],
            "kdkabkota" => $kab,
            "kddekon" => $dekon,
            "kdsoutput" => $arr_insert["kd_sub_output"],
            "kdkmpnen" => $arr_insert["kd_komponen"],
            "kdskmpnen" => $arr_insert["kd_sub_komponen"],
            "kdakun" => $arr_insert["kdakun"],
            "kdkppn" => $arr_insert["kdkppn"],
            "kdbeban" => $arr_insert["kdbeban"],
            "kdjnsban" => $arr_insert["kdjnsban"],
            "kdctarik" => $arr_insert["kdctarik"],
            "register" => $arr_insert["register"],
            "carahitung" => $arr_insert["carahitung"],
            "ket" => $arr_insert["ket"],
            "ket2" => $arr_insert["ket2"],
            //"id_paket" => $this->input->post("formData")["id_paket"],
            "kd_tahapan" => $tahapan
        ];
        $this->db->insert('d_cttakun', $data_detail_cttakun);
    }

    public function edit_akun($arr_insert, $array) {
        $data_detail_akun = [
            "kdakun" => $arr_insert["kdakun"],
            "kdbeban" => $arr_insert["kdbeban"],
            "kdjnsban" => $arr_insert["kdjnsban"],
            "kdctarik" => $arr_insert["kdctarik"],
            "kdkppn" => $arr_insert["kdkppn"],
            "register" => $arr_insert["register"],
            "carahitung" => $arr_insert["carahitung"],
            "prosenphln" => (int) $arr_insert["prosenphln"],
            "prosenrkp" => (int) $arr_insert["prosenrkp"],
            "prosenrmp" => (int) $arr_insert["prosenrmp"],
            "kppnrkp" => $arr_insert["kppnrkp"],
            "kppnrmp" => $arr_insert["kppnrmp"],
            "kppnphln" => $arr_insert["kppnphln"],
            "regdam" => $arr_insert["regdam"],
            "kdblokir" => strlen($arr_insert["kdblokir"]) === 0 ? NULL : $arr_insert["kdblokir"],
            "uraiblokir" => $arr_insert["urblokir"]
        ];
        $this->db->where($array);
        $this->db->update('d_akun', $data_detail_akun);

        $data_detail_cttakun = [
            "kdakun" => $arr_insert["kdakun"],
            "kdbeban" => $arr_insert["kdbeban"],
            "kdjnsban" => $arr_insert["kdjnsban"],
            "kdctarik" => $arr_insert["kdctarik"],
            "kdkppn" => $arr_insert["kdkppn"],
            "register" => $arr_insert["register"],
            "carahitung" => $arr_insert["carahitung"],
            "ket" => $arr_insert["ket"],
            "ket2" => $arr_insert["ket2"],
        ];
        $this->db->where($array);
        $this->db->update('d_cttakun', $data_detail_cttakun);
    }

    public function export_indikatif() {
        $kdsatker = $this->session->users['kd_bujt'];
        $thang = $this->session->konfig_tahun_ang;
        $array = array('kd_tahapan' => 'PPI', 'thang' => $thang, 'kdsatker' => $kdsatker);
        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($array);
        $this->db->update('d_output');

        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($array);
        $this->db->update('d_soutput');

        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($array);
        $this->db->update('d_kmpnen');

        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($array);
        $this->db->update('d_akun');

        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($array);
        $this->db->update('d_cttakun');

        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($array);
        $this->db->update('usulan_paket');

        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($array);
        $this->db->update('detail_usulan_paket');

        $arrays = array('kd_tahapan' => 'PPI', 'thang' => $thang);
        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($arrays);
        $this->db->update('geo_pok_ruas');

        $this->db->set('kd_tahapan', 'PI');
        $this->db->where($arrays);
        $this->db->update('geo_pok_jembatan');
        redirect('report_rtrw/index/PI');
    }

    public function checkgeomruas($thang, $kdtahapan, $idpaket, $iddetail) {
        $str_sql = "SELECT * FROM geo_pok_ruas
                    WHERE thang = $thang and kd_tahapan = '$kdtahapan' and id_paket = $idpaket and id_detail = $iddetail";
        $query = $this->db->query($str_sql)->result();
        return $query;
    }

    public function checkgeomjembatan($thang, $kdtahapan, $idpaket, $iddetail) {
        $str_sql = "SELECT * FROM geo_pok_jembatan
                    WHERE thang = $thang and kd_tahapan = '$kdtahapan' and id_paket = $idpaket and id_detail = $iddetail";
        $query = $this->db->query($str_sql)->result();
        return $query;
    }

    public function get_kabkotarkakl($lok, $kab, $thang) {
        $str_sql = "select *
                    from v_ref_kabkota
                    where kd_prov_rkakl = '$lok' and kd_kab_rkakl = '$kab' and thang = $thang";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (count($data) > 0) {
            echo json_encode($data[0]);
        }
    }

    public function get_jmlh_soutput() {
        $param = $this->input->post('data', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $thang = $this->input->post("data")["thang"];
        $kdprog = $this->input->post("data")["kdprog"];
        $kdgiat = $this->input->post("data")["kdgiat"];
        $kdoutput = $this->input->post("data")["kdoutput"];
        $kdsout = $this->input->post("data")["kdsoutput"];
        $tahapan = $this->input->post("data")["kd_tahapan"];
        $kdsatker = $this->session->users['kd_bujt'];
        $str_sql = "select SUM(volume) as jumlahvol
                    from detail_usulan_paket
                    where thang = $thang and kdsatker = '$kdsatker' and kd_dept = '033' and kd_unit = '04' and kd_program = '$kdprog'
                    and kd_kegiatan = '$kdgiat' and kd_output = '$kdoutput' and kd_sub_output = '$kdsout' and kd_tahapan = '$tahapan'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (strlen($data[0]['jumlahvol']) > 0) {
            echo json_encode($data[0]['jumlahvol'], JSON_NUMERIC_CHECK);
        } else {
            echo 0;
        }
    }

    public function get_jmlh_output() {
        $param = $this->input->post('data', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $thang = $this->input->post("data")["thang"];
        $kdprog = $this->input->post("data")["kdprog"];
        $kdgiat = $this->input->post("data")["kdgiat"];
        $kdoutput = $this->input->post("data")["kdoutput"];
        $tahapan = $this->input->post("data")["kd_tahapan"];
        $kdsatker = $this->session->users['kd_bujt'];
        $str_sql = "select SUM(volume) as jumlahvol
                    from detail_usulan_paket
                    where thang = $thang and kdsatker = '$kdsatker' and kd_dept = '033' and kd_unit = '04' and kd_program = '$kdprog'
                    and kd_kegiatan = '$kdgiat' and kd_output = '$kdoutput' and kd_tahapan = '$tahapan'";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (strlen($data[0]['jumlahvol']) > 0) {
            echo json_encode($data[0]['jumlahvol'], JSON_NUMERIC_CHECK);
        } else {
            echo 0;
        }
    }

    public function get_from_view($id_usulan) {
        $str_sql = "select *
                    from vw_paket_detail
                    where id_usulan = " . $id_usulan;
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
        echo json_encode($data_usulan_indikatif[0]);
        return $data_usulan_indikatif;
    }

    public function ssp_diskresi() {
        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();
        $kd_prov_irms = $query->row()->kd_prov_irmsv3;
        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $subrole = $this->session->users['id_user_group'];
        $roledesc = $this->session->users['role'];
        $yearnow = $this->session->konfig_tahun_ang;
        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);
        $whereClause = $this->check_satker_if_array3($satker, $childsatker, $kd_prov_irms, $yearnow);

        $table = 'v_usulan_diskresi';
        $primaryKey = 'id_usulan';

        $columns = array(
            array('db' => 'id_usulan', 'dt' => 0),
            array('db' => 'thang', 'dt' => 1),
            array('db' => 'detail', 'dt' => 2),
            array('db' => 'sumber_usulan', 'dt' => 3),
            array('db' => 'tgl_usulan', 'dt' => 4),
            array('db' => 'pengusul2', 'dt' => 5),
            array('db' => 'nama_prov', 'dt' => 6),
            array('db' => 'rkakl_volume', 'dt' => 7),
            array('db' => 'rkakl_biaya', 'dt' => 8, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                }),
            array('db' => 'status_kewenangan', 'dt' => 9),
            array('db' => 'evaluasi', 'dt' => 10),
            array('db' => 'rc_fs', 'dt' => 11),
            array('db' => 'rc_ded', 'dt' => 12),
            array('db' => 'rc_lahan', 'dt' => 13),
            array('db' => 'rc_dokling', 'dt' => 14),
            array('db' => 'keterangan', 'dt' => 15),
            array('db' => 'usulan_volume', 'dt' => 16),
            array('db' => 'usulan_biaya', 'dt' => 17, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                })
        );
        //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
        if ($id_user == 1 || $id_user == 2 || $roledesc == 'span' || $roledesc == 'dpsi' || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                datatable_ssp($table, $primaryKey, $columns, $whereClause);
            } else {
                //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
                if ($roledesc == 'jbh' || $roledesc == 'konstruksi' || $roledesc == 'bmn' || $roledesc == 'renwilI' || $roledesc == 'renwilII' || $roledesc == 'stppreservasi') {
                    datatable_ssp($table, $primaryKey, $columns, "thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and kd_satker_balai='$subrole'");
                } else {
                    datatable_ssp($table, $primaryKey, $columns, "thang=$yearnow and kd_prov_irms='$kd_prov_irms' and stat_usulan1=1");
                }
            }
        }
    }

    public function ssp_dprd() {
        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();
        $kd_prov_irms = $query->row()->kd_prov_irmsv3;
        $id_user = $this->session->users['id_user'];
        $role = $this->session->users['id_user_group_real'];
        $subrole = $this->session->users['id_user_group'];
        $roledesc = $this->session->users['role'];
        $yearnow = $this->session->konfig_tahun_ang;
        $satker = $this->session->users['kd_bujt'];
        $childsatker = $this->child_satker($satker);
        $whereClause = $this->check_satker_if_array3($satker, $childsatker, $kd_prov_irms, $yearnow);

        $table = 'v_usulan_dprd';
        $primaryKey = 'id_usulan';

        $columns = array(
            array('db' => 'id_usulan', 'dt' => 0),
            array('db' => 'thang', 'dt' => 1),
            array('db' => 'detail', 'dt' => 2),
            array('db' => 'sumber_usulan', 'dt' => 3),
            array('db' => 'tgl_usulan', 'dt' => 4),
            array('db' => 'pengusul2', 'dt' => 5),
            array('db' => 'nama_prov', 'dt' => 6),
            array('db' => 'rkakl_volume', 'dt' => 7),
            array('db' => 'rkakl_biaya', 'dt' => 8, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                }),
            array('db' => 'status_kewenangan', 'dt' => 9),
            array('db' => 'evaluasi', 'dt' => 10),
            array('db' => 'rc_fs', 'dt' => 11),
            array('db' => 'rc_ded', 'dt' => 12),
            array('db' => 'rc_lahan', 'dt' => 13),
            array('db' => 'rc_dokling', 'dt' => 14),
            array('db' => 'keterangan', 'dt' => 15),
            array('db' => 'usulan_volume', 'dt' => 16),
            array('db' => 'usulan_biaya', 'dt' => 17, 'formatter' => function( $d, $row ) {
                    return number_format(floatval($d), 0, ',', '.');
                })
        );
        //if ($id_user == 1 || $id_user == 2 || $role == 4 || $role == 5 || $role == 6 || $role == 1) {
        if ($id_user == 1 || $id_user == 2 || $roledesc == 'span' || $roledesc == 'dpsi' || $roledesc == 'ksjj' || $roledesc == 'admin') {
            datatable_ssp($table, $primaryKey, $columns);
        } else {
            if ($childsatker != '') {
                datatable_ssp($table, $primaryKey, $columns, $whereClause);
            } else {
                //if ($role == 10 || $role == 8 || $role == 12 || $role == 14 || $role == 15 || $role == 16) {
                if ($roledesc == 'jbh' || $roledesc == 'konstruksi' || $roledesc == 'bmn' || $roledesc == 'renwilI' || $roledesc == 'renwilII' || $roledesc == 'stppreservasi') {
                    datatable_ssp($table, $primaryKey, $columns, "thang = $yearnow and kd_prov_irms = '$kd_prov_irms' and kd_satker_balai='$subrole'");
                } else {
                    datatable_ssp($table, $primaryKey, $columns, "thang=$yearnow and kd_prov_irms='$kd_prov_irms' and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1");
                }
            }
        }
    }

    public function get_paket($id_paket, $tahapan) {
        $str_sql = "select * from usulan_paket where id_paket = $id_paket and kd_tahapan = '$tahapan'";
        $query = $this->db->query($str_sql);
        $data_usulan_indikatif = json_decode(json_encode($query->result()), true);
        echo json_encode($data_usulan_indikatif[0]);
    }

    public function get_lookup_jalanjem($id_usulan, $kat) {
        if ($kat === 'ruas') {
            $str_sql = "SELECT * FROM v_lookup_ruas WHERE id_usulan = $id_usulan";
        } else if ($kat === 'jembatan') {
            $str_sql = "SELECT * FROM v_lookup_jembatan WHERE id_usulan = $id_usulan";
        }

        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        echo json_encode($data[0]);
    }

    public function get_lookup_ppk($id_paket, $akun, $header1) {
        $str_sql = "SELECT * FROM v_lookup_ppk WHERE id_paket = $id_paket AND kdakun = '$akun' AND header1 = '$header1'";

        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        echo json_encode($data[0]);
    }

    public function ssp_listtag() {
        $id = $this->input->post('id', TRUE);
        $jns = $this->input->post('jns', TRUE);

        $table = "
            (SELECT a.id_rujukan, a.kd_jns_rujukan, a.id_paket, 
                (b.kd_sub_komponen + ' - ' + b.nama_sub_komponen) AS nama_sub_komponen,
                (b.kd_kegiatan + ' - ' + c.nmgiat) AS nmgiat, (b.kd_output + ' - ' + d.nmoutput) AS nmoutput,
                (b.kd_sub_output + ' - ' + e.nmsoutput) AS nmsoutput, (b.kd_komponen + ' - ' + f.nmkmpnen) AS nmkmpnen
            FROM rujukan_paket a
            LEFT JOIN usulan_paket b ON a.id_paket = b.id_paket
            LEFT JOIN krisna.kegiatan c ON b.thang = c.thang AND b.kd_program = c.kdprogram AND b.kd_kegiatan = c.kdgiat
            LEFT JOIN krisna.[output] d ON b.thang = d.thang AND b.kd_program = d.kdprogram AND b.kd_kegiatan = d.kdgiat 
                AND b.kd_output = d.kdoutput
            LEFT JOIN krisna.soutput e ON b.thang = e.thang AND b.kd_program = e.kdprogram AND b.kd_kegiatan = e.kdgiat 
                AND b.kd_output = e.kdoutput AND b.kd_sub_output = e.kdsoutput
            LEFT JOIN krisna.komponen f ON b.thang = f.thang AND b.kd_program = f.kdprogram AND b.kd_kegiatan = f.kdgiat 
                AND b.kd_output = f.kdoutput AND b.kd_sub_output = f.kdsoutput AND b.kd_komponen = f.kdkmpnen
            WHERE a.id_rujukan = '$id' AND a.kd_jns_rujukan = $jns
            ) AS x ";
        $primaryKey = 'id_paket';

        $columns = array(
            array('db' => 'id_paket', 'dt' => 0),
            array('db' => 'nama_sub_komponen', 'dt' => 1),
            array('db' => 'nmgiat', 'dt' => 2),
            array('db' => 'nmoutput', 'dt' => 3),
            array('db' => 'nmsoutput', 'dt' => 4),
            array('db' => 'nmkmpnen', 'dt' => 5)
        );

        datatable_ssp($table, $primaryKey, $columns);
    }

    public function get_jumtag($var) {
        $prov = $this->session->users['kd_prov'];
        $query = $this->db->select('kd_prov_irmsv3')->from('provinsi')->where('kd_prov', $prov)->get();
        $kd_prov_irms = $query->row()->kd_prov_irmsv3;
        $satker = $this->session->users['kd_bujt'];
        $thnang = $this->session->konfig_tahun_ang;
        
        if ($var !== 0) {
            $view = '';
            $kondisi = '';
            switch ($var) {
                case 'DPRD':
                    $view = 'v_usulan_dprd';
                    $kondisi = " and kd_prov_irms='$kd_prov_irms' and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1";
                    break;
                case 'DISKRESI':
                    $view = 'v_usulan_diskresi';
                    $kondisi = " and kd_prov_irms='$kd_prov_irms' and stat_usulan1=1";
                    break;
                case 'KL':
                    $view = 'v_usulan_kl';
                    $kondisi = " and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1";
                    break;
                case 'AKADEMISI':
                    $view = 'v_usulan_akademis';
                    $kondisi = " and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1";
                    break;
                case 'PEMDA':
                    $view = 'v_usulan_pemda';
                    $kondisi = " and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1";
                    break;
                case 'DPR':
                    $view = 'v_usulan_dpr';
                    $kondisi = " and stat_usulan1=1 and stat_usulan2=1 and stat_usulan3=1";
                    break;
            }

            $str_sql = "SELECT COUNT(id_usulan) AS jum, kddisposisi FROM $view WHERE thang = $thnang and kddisposisi = '$satker' $kondisi GROUP BY kddisposisi";
            $query = $this->db->query($str_sql);
            $data = json_decode(json_encode($query->result()), true);
            if (count($data) > 0){
                echo json_encode($data[0]);
            } else {
                echo 0;
            }            
        } else {
            echo 0;
        }
    }
    
    public function get_jumbelumtag($thpn) {
        $satker = $this->session->users['kd_bujt'];
        $thnang = $this->session->konfig_tahun_ang;
        $view = '';
        if ($thpn === 'PI'){
            $view = 'vw_paket_status_pi';
        } else {
            $view = 'vw_paket_status';
        }
        $str_sql = "SELECT COUNT(id_paket) AS jum FROM $view WHERE thang = $thnang AND kd_bujt = '$satker' AND kd_tahapan = '$thpn' AND tagging = 0";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        if (count($data) > 0) {
            echo json_encode($data[0]);
        } else {
            echo 0;
        }
    }
    

}
