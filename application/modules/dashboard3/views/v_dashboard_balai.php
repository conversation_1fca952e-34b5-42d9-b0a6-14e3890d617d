<html>

    <head>
        <!-- Standard Meta -->
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
        <link rel="image_src" type="image/jpeg" href="/images/logo.png" />
        <link rel="icon" href="img/favicon.ico" type="image/x-icon" />
        <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon" />
        <!-- Site Properities -->
        <meta name="generator" content="Visual Studio 2015" />
        <title>Charts - v2 | Golgi Admin</title>
        <meta name="description" content="Golgi Admin Theme" />
        <meta name="keywords" content="html5, ,semantic,ui, library, framework, javascript,jquery,admin,theme" />
        <!-- <link href="plugins/chartist/chartist.min.css" rel="stylesheet" /> -->
        <link href="<?php echo base_url() . 'assets/semantic/dist/semantic.css'; ?>" rel="stylesheet" />
        <link href="<?php echo base_url() . 'assets/semantic/css/main.min.css'; ?>" rel="stylesheet" />
        <style>
            .containerli {
                margin-top: 5px;
                margin-bottom: 5px;
            }

            .ui.segments>.segment {
                border: none;
            }
            .highcharts-table-caption{
                font-size: 17px;
            }
            .highcharts-data-table table{
                width: 100% !important;
                /*                border: 2px solid gray;*/
                text-align: center;
                font-size: 12px;
                box-shadow: 0px 0px 1px 1px rgb(172, 166, 166);
            }
            .highcharts-data-table table thead th{
                padding:15px !important;
                border-bottom: 1px solid gray;
                font-size: 15px !important;
            }
            .highcharts-data-table table th{
                padding:10px;
                border-bottom: 1px solid gray;
            }
            .highcharts-data-table table td{
                padding:10px;
                border-bottom: 1px solid gray;
            }
            .highcharts-axis-title{
                font-weight: bold;
                font-size: 12px;
                font-family: arial;
            }

            .ui.segments>.segment {
                border: none;
            }
            .yuh{
                float: left;
                padding: 10px;
                color: white;
                font-weight: bold;
                font-size: 15px;
            }
            .yuh1{
                float: left;
                padding: 10px;
                color: white;
                font-weight: bold;
                font-size: 15px;
            }
            #boxi{
              float: left;

              margin-left: -5px;
            }
            #btnsearch{
                background: #4b9187;
                color: #fbfbfb;
                padding: 5px;
                border-radius: 10px;
                border-color: gr;
                font-weight: bold;
            }
        </style>


        <link href="<?php echo base_url() . 'assets/semantic/plugins/pacejs/pace.css'; ?>" rel="stylesheet" />

    </head>



    <body id="framebody" class="admin">

        <div class="pusher">
            <div class="full height">
                <!--Load Sidebar Menu In App.js loadhtml function-->
                <!-- <div class="toc"></div> -->
                <!--Load Sidebar Menu In App.js loadhtml function-->

                <div class="article">

                    <!--Load Navbar Menu In App.js loadhtml function-->
                    <!-- <div class="navbarmenu"></div> -->
                    <!--Load Navbar Menu In App.js loadhtml function-->
                    <!--Begin Container-->

                    <div class="containerli">
                        <div class="ui equal width left aligned padded grid stackable">
                            <div class="row">
                                <div class="column">
                                    <div class="field" style="width:100%;">
                                    <form action="<?php echo base_url(); ?>dashboard3/frame" method="post">
                                      <div id="boxi">
                                        <div class="yuh"><i class="fa fa-calendar"></i> Tahun</div>
                                   
											 <input type="hidden" name="<?php echo $this->security->get_csrf_token_name(); ?>" id="<?php echo $this->security->get_csrf_token_name(); ?>" value="<?php echo $this->security->get_csrf_hash(); ?>">
                                            <select id="thang" class="ui fluid dropdown" name="tah" style="width:auto;margin-left: 100px;">
                                                <?php
                                                $thang = $this->db->get('aset_r_periode')->result_array();
                                                foreach ($thang as $t) {
                                                    if (!empty($tahuns)) {
                                                        if ($t['tahun'] == $tahuns) {
                                                            $select = "selected";
                                                        } else {
                                                            $select = "";
                                                        }
                                                    } else {
                                                        if ($t['tahun'] == $this->session->konfig_tahun_ang) {
                                                            $select = "selected";
                                                        } else {
                                                            $select = "";
                                                        }
                                                    }
                                                    ?>

                                                    <option value="<?php //echo $t['tahun']; ?>" <?php //echo $select; ?> ><?php //echo $t['tahun']; ?></option>
                                                    <?php
                                                }
                                                ?>

                                                
                                            </select>
                                          </div>

                 

                                            <?php 
                                                if($this->session->users['role'] !='bujt'){                 
                                            ?>
                                            <div id="boxi">
                                               
                                            <div class="yuh"><i class="fa fa-calendar"></i> BUJT</div>
                                          <select id="bujt" class="ui fluid dropdown" name="bujt" style="width:auto;margin-left: 100px;">
                                                  <option value=''>--Pilih--</option>
                                                <?php
                                                $this->db->order_by("nama_bujt", "asc"); 
                                                $bujt = $this->db->get('aset_r_bujt')->result_array();
                                                foreach ($bujt as $t) {
                                                    if (!empty($tahuns)) {
                                                        if ($t['kd_bujt'] == $bujt_s) {
                                                            $select = "selected";
                                                        } else {
                                                            $select = "";
                                                        }
                                                    }
                                                    ?>
                                                    ?>

                                                    <option value="<?php //echo $t['kd_bujt']; ?>" <?php echo $select; ?> ><?php //echo $t['nama_bujt']; ?></option>
                                                    <?php
                                                }
                                                ?>

                                                
                                            </select>
                                            </div>
                                            <?php } ?>
                                            <!-- <div id="boxi">
                                          <div class="yuh"><i class="fa fa-calendar"></i> Jalan Tol</div>
                                          <select id="ruas" class="ui fluid dropdown" name="ruas" style="width:200px;margin-left: 100px;">
                                                    <option value=''>--Pilih--</option>
                                                <?php
                                                $kdbujt= $this->session->users['kd_bujt'];
                                                
                                                 if($this->session->users['role'] =='bujt'){
                                                    $this->db->where('kd_bujt',$kdbujt);
                                                 }
                                                $ruas = $this->db->get('aset_r_ruas')->result_array();
                                                foreach ($ruas as $t) {
                                                    if (!empty($tahuns)) {
                                                        if ($t['id_ruas'] == $ruas_s) {
                                                            $select = "selected";
                                                        } else {
                                                            $select = "";
                                                        }
                                                    }
                                                    ?>

                                                    <option value="<?php //echo $t['id_ruas']; ?>" <?php echo $select; ?> ><?php //echo $t['nm_ruas']; ?></option>
                                                    <?php
                                                }
                                                ?>

                                                
                                            </select>
                                            </div> -->
                                          <!--div id="boxi">
                                            <div class="yuh1"><i class="fa fa-calendar"></i> Tahapan</div>
                                            <form action="<?php //echo base_url(); ?>dashboard3/frame" method="post">
                                                <input type="hidden" name="<?php //echo $this->security->get_csrf_token_name(); ?>" id="<?php echo $this->security->get_csrf_token_name(); ?>" value="<?php echo $this->security->get_csrf_hash(); ?>">

                                                <select id="kdtahap" class="ui fluid dropdown" name="kdtah" style="width:auto;margin-left: 100px;">
                                                   <!-- onchange="this.form.submit()" -->
                                                  <?php
                                                //   $tahap = array("KRG", "PI", "PA", "PAA");
                                                //   foreach($tahap as $t){
                                                //     if($t=='KRG'){
                                                //       $des='Konreg';
                                                //     }
                                                //     elseif($t=='PI'){
                                                //       $des='Pagu Indikatif';
                                                //     }
                                                //     elseif($t=='PA'){
                                                //       $des='Pagu Anggaran';
                                                //     }
                                                //     elseif($t=='PAA'){
                                                //       $des='Pagu Alokasi Anggaran';
                                                //     }
                                                //   //  $tahaps='';
                                                //     if (!empty($tahaps)) {
                                                //         if ($t == $tahaps) {
                                                //             $select = "selected";
                                                //         } else {
                                                //             $select = "";
                                                //         }
                                                //     } else {
                                                //         if ($t == $this->session->konfig_kd_tahapan) {
                                                //             $select = "selected";
                                                //         } else {
                                                //             $select = "";
                                                //         }
                                                //     }
                                                  ?>
                                                  <!--option value="<?php //echo $t; ?>" <?php //echo $select; ?>><?php //echo $des; ?></option>
                                                  <?php
                                                //   }
                                                  ?>

                                                        <!-- <option value="KRG" <?php //echo $select; ?> >Konreg</option>
                                                        <option value="PI" <?php //echo $select; ?> >Pagu Indikatif</option>
                                                        <option value="PA" <?php //echo $select; ?> >Pagu Anggaran</option>
                                                        <option value="PAA" <?php //echo $select; ?> >Pagu Alokasi Anggaran</option> -->

                                                <!--/select>
                                              </div-->
                                              <div id="boxi">
                                                &nbsp;&nbsp;&nbsp;
                                                <button type="submit" name="button" id="btnsearch"><i class="search icon"></i> Lihat </button>
                                              </div>

                                            </form>
                                    </div>
                                    <div class="ui segments" style="margin-top: 49px;">
                                        <!--div class="ui segment">
                                            <h4 id="hjumlahusulan" class="ui horizontal divider header"><i class="tag blue icon"></i>Total: <?php// echo $totaldata; ?> usulan</h4>
                                        </div-->
                                    </div>
                                    <?php
                                     $group = $this->session->users['id_user_group_real'];

                                    //   if($group ==3)
                                    //   {

                                    //   $b="block";
                                    //   }
                                    //   else
                                    //   {

                                    //   $b="none";
                                    //   }
                                       ?>
                                      <div class="ui segments" style="display:block;">
                                        <div class="ui segment">
                                            <div id="container1" style="min-width: 310px; height: 500px; margin: 0 auto"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="column">
                                    <div class="ui segments">
                                        <div class="ui segment">
                                            <div id="container2" style="min-width: 310px; height: 500px; margin: 0 auto"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="column">
                                    <div class="ui segments">
                                        <div class="ui segment">
                                            <div id="container3" style="min-width: 310px; height: 500px; margin: 0 auto"></div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="column">
                                    <div class="ui segments">
                                        <div class="ui segment">
                                            <div id="container4" style="min-width: 310px; height: 700px; margin: 0 auto"></div>
                                        </div>
                                    </div>
                                </div>
                            </div><!--

                            <div class="row">
                                <div class="column">


                                    <div class="ui segments">
                                        <div class="ui segment">
                                            <div id="container6" style="min-width: 310px; height: 700px; margin: 0 auto"></div>
                                        </div>
                                    </div>

                                </div>
                            </div>-->
                        </div>
                    </div>

                </div>
            </div>
        </div>


        <!-- <input type="hidden" id="chart_prov_labels" value="<?php //echo $chart['satker']['labels']; ?>"/> -->
        <!-- <input type="hidden" id="chart_prov_vals"   value="<?php //echo $chart['satker']['vals']; ?>"/> -->
        <!-- <input type="hidden" id="chart_prov_biaya"   value="<?php //echo $chart['satker']['biaya']; ?>"/> -->

        <input type="hidden" id="chart_labels_1" value="<?php  echo $chart['ruas']['labels'];  ?>"/>
        <input type="hidden" id="chart_vals_1"   value="<?php  echo $chart['ruas']['vals'];  ?>"/>
        <input type="hidden" id="chart_cols_1"   value="<?php  echo $chart['ruas']['colors'];  ?>"/>
        <input type="hidden" id="chart_biaya_1"   value="<?php echo $chart['ruas']['biaya'];  ?>"/>
        <input type="hidden" id="chart_biaya2_1"   value="<?php echo $chart['ruas']['biaya2'];  ?>"/>

        <input type="hidden" id="chart_labels_2" value="<?php echo $chart['jenisjbt']['labels']; ?>"/>
        <input type="hidden" id="chart_vals_2"   value="<?php echo $chart['jenisjbt']['vals']; ?>"/>
        <input type="hidden" id="chart_biaya_2"   value="<?php echo $chart['jenisjbt']['biaya']; ?>"/>

        <input type="hidden" id="chart_labels_3" value="<?php echo $chart['jenisbgn']['labels']; ?>"/>
        <input type="hidden" id="chart_vals_3"   value="<?php echo $chart['jenisbgn']['vals']; ?>"/>
        <input type="hidden" id="chart_biaya_3"   value="<?php echo $chart['jenisbgn']['biaya']; ?>"/>

       <input type="hidden" id="chart_labels_4" value="<?php echo $chart['jenisalat']['labels'];  ?>"/>
        <input type="hidden" id="chart_vals_4"   value="<?php echo $chart['jenisalat']['vals'];  ?>"/>
        <input type="hidden" id="chart_biaya_4"   value="<?php echo $chart['jenisalat']['biaya'];  ?>"/>

        <!-- <input type="hidden" id="chart_labels_6" value="<?php //echo $chart['belanja']['labels']; ?>"/>
        <input type="hidden" id="chart_vals_6"   value="<?php //echo $chart['belanja']['vals']; ?>"/>
        <input type="hidden" id="chart_biaya_6"   value="<?php //echo $chart['belanja']['biaya']; ?>"/> -->

<!--        <input type="hidden" id="chart_labels_5" value="<?php //echo $chart['sdana']['labels'];  ?>"/>
        <input type="hidden" id="chart_vals_5"   value="<?php //echo $chart['sdana']['vals'];  ?>"/>
        <input type="hidden" id="chart_biaya_5"   value="<?php //echo $chart['sdana']['biaya'];  ?>"/>-->
        <script src="//ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js">
        </script>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.3.1/jspdf.umd.min.js">
        </script>
        <script type="text/javascript" src="//cdn.rawgit.com/niklasvh/html2canvas/0.5.0-alpha2/dist/html2canvas.min.js">
        </script>
        <script src="<?php echo base_url() . 'assets/semantic/js/jquery-2.1.4.min.js'; ?>"></script>
        <script src="<?php echo base_url() . 'assets/semantic/plugins/nicescrool/jquery.nicescroll.min.js'; ?>"></script>
        <script src="<?php echo base_url() . 'assets/semantic/js/chartjs/dist/Chart.bundle.min.js'; ?>"></script>

        <script src="<?php echo base_url() . 'assets/chart/highcharts.js'; ?>"></script>
        <script src="<?php echo base_url() . 'assets/chart/ex/exporting.js'; ?>"></script>
        <script src="<?php echo base_url() . 'assets/chart/ex/export-data.js'; ?>"></script>

        <script src="<?php echo base_url() . 'assets/semantic/dist/semantic.min.js'; ?>"></script>
        <script data-pace-options='{ "ajax": false }' src="<?php echo base_url() . 'assets/semantic/plugins/pacejs/pace.js'; ?>"></script>
                 
        <!--here is cumstom js loader (array of js located at base_path/asset/<js path & filename>)-->
        <script type = "text/javascript">
        <?php
        foreach ($js as $j) {
            echo file_get_contents(APPPATH . $j);
        }
        ?>
        </script>    
    </body>

</html>
