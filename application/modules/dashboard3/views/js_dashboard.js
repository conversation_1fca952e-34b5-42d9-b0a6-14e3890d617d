'use strict'

var dtable = null;

$(document).ready(function() {


    $("body").niceScroll({
        cursorcolor: "#ddd",
        cursorwidth: 5,
        cursorborderradius: 0,
        cursorborder: 0,
        scrollspeed: 50,
        autohidemode: true,
        zindex: 9999999
    });

    var vlabels = JSON.parse($('#chart_labels_1').val().replace(/'/g, '"'));
    var vvals = JSON.parse($('#chart_vals_1').val().replace(/'/g, '"'));
    //var vcolors = JSON.parse($('#chart_cols_1').val().replace(/'/g, '"'));
    var vbiaya = JSON.parse($('#chart_biaya_1').val().replace(/'/g, '"'));
    var vbiaya2 = JSON.parse($('#chart_biaya2_1').val().replace(/'/g, '"'));

    var colorCode = "#" + Math.floor(Math.random() * 16777215).toString(16);
    var color = [];
    for (var i = 0; i < 100; i++) {
        color.push("#" + Math.floor(Math.random() * 16777215).toString(16));
    }

    var filteredcol = color.filter(function(value, index, arr) {
        return value != '#000000';
    });


    //alert(color);


    Highcharts.setOptions({
        global: {
            useUTC: false,

        },
        lang: {
            decimalPoint: '.',
            thousandsSep: ','
        },
        colors: filteredcol
    });

    Highcharts.chart('container1', {
        chart: {
            type: 'column',
            zoomType: 'y'
        },
        title: {
            text: 'Total Aset Jasa Konsesi'
        },
        subtitle: {
            text: ''
        },
        xAxis: {
            categories: vlabels,
            crosshair: true
        },

        yAxis: {
            min: 0,
            labels: {
                formatter: function() {
                    return Highcharts.numberFormat(this.value, 0, ' ', ',')
                },
                style: {
                    color: '#666666'
                }
            },
            title: {
                text: 'Nilai Investasi (Rupiah)'
            }
        },
        // [
        //     { // Primary yAxis
        //     labels: {
        //         format: '{value}',
        //         style: {
        //             color: '#d4a300'
        //         }
        //     },
        //     title: {
        //         text: 'Jumlah Paket',
        //         style: {
        //             color: '#d4a300'
        //         }
        //     }
        // }, 
        // { // Secondary yAxis
        //     title: {
        //         text: 'Nilai Investasi',
        //         style: {
        //             color: '#47aded'
        //         }
        //     },
        //     labels: {
        //         format: '{value}',
        //         style: {
        //             color: '#47aded'
        //         }
        //     }
        // }
        // ],
        tooltip: {
            shared: true
        },
        legend: {
            layout: 'vertical',
            align: 'left',
            x: 100,
            verticalAlign: 'top',
            y: 0,
            floating: true,

            backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || 'rgba(255,255,255,0.25)'
        },
        plotOptions: {
            column: {
                pointPadding: 0.2,
                borderWidth: 0,
                pointWidth: 40
            }
        },
        series: [
        //     {
        //     name: 'Nilai Perolehan',
        //     //      yAxis: 1,
        //     data: vbiaya,
        //     // color: '#47aded',
        //     // borderColor: '#d4a300',
        //     tooltip: {
        //         valueSuffix: '',
        //         valuePrefix: 'Rp.'
        //     }

        // },
         {
            name: 'Nilai PPJT',
            // lineWidth: 2,
            // color: '#d4a300',
            data: vbiaya2,
            tooltip: {
                valueSuffix: '',
                valuePrefix: 'Rp.'
            }
        }]
    });


    var vlabels = JSON.parse($('#chart_labels_2').val().replace(/'/g, '"'));
    var vvals = JSON.parse($('#chart_vals_2').val().replace(/'/g, '"'));
    var vbiaya = JSON.parse($('#chart_biaya_2').val().replace(/'/g, '"'));


    Highcharts.chart('container2', {
        chart: {
            zoomType: 'xy'
        },
        title: {
            text: 'Jumlah Aset Jembatan'
        },
        subtitle: {
            text: ''
        },
        xAxis: [{
            categories: vlabels,
            crosshair: true
        }],

        yAxis: [{ // Primary yAxis
                labels: {
                    format: '{value}',
                    style: {
                        color: '#666666'
                    }
                },
                min: 0,
                title: {
                    text: 'Jumlah',
                    style: {
                        color: '#666666'
                    }
                },
                opposite: false
            },

            // { // Secondary yAxis
            //     min: 0,
            //     title: {
            //         text: 'Nilai Investasi (Rupiah)',
            //         style: {
            //             color: '#666666'
            //         }
            //     },
            //     labels: {
            //         formatter: function() {
            //             return Highcharts.numberFormat(this.value, 0, ' ', ',')
            //         },
            //         style: {
            //             color: '#666666'
            //         }
            //     },
            //     opposite: false
            // }
        ],
        tooltip: {
            formatter: function() {
                var s = '<tspan style="font-size: 10px">' + this.x + '</tspan>';
                var color = this.points[0].series.color;
                var chart = this.points[0].series.chart; //get the chart object
                var categories = chart.xAxis[0].categories; //get the categories array
                var index = 0;
                while (this.x !== categories[index]) { index++; } //compute the index of corr y value in each data arrays           
                $.each(chart.series, function(i, series) { //loop through series array
                    var ydata = (series.name == 'Nilai Perolehan') ? 'Rp.' + Highcharts.numberFormat(series.data[index].y, 0, ' ', ',') : series.data[index].y;

                    s += '<br/>' + series.name + ': ' +
                        '<b>' + ydata + '</b>'; //use index to get the y value


                });

                //console.log(s);
                return s;
            },
            shared: true
        },
        legend: {
            layout: 'vertical',
            align: 'left',
            x: 100,
            verticalAlign: 'top',
            y: 0,
            floating: true,
            backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || 'rgba(255,255,255,0.25)'
        },
        plotOptions: {
            column: {
                colorByPoint: true,
                pointWidth: 40
            },
            colors: filteredcol
        },
        series: [{
                name: 'Jumlah',
                visible: true,
                type: 'column',
                // type: 'spline',
                // lineWidth: 2,
                // // color: '#d4a300',
                data: vvals
                    // tooltip: {
                    //     valueSuffix: ''
                    // }
            }
            // ,
            // {
            //     name: 'Nilai Perolehan',
            //     //   type: 'column',
            //     visible: false,
            //     // yAxis: 1,
            //     data: vbiaya,
            //     // color: '#47aded',
            //     // borderColor: '#d4a300',
            //     // tooltip: {
            //     //     valueSuffix: '',
            //     //     valuePrefix: 'Rp.'
            //     // }

            // }

        ]
    });




    var vlabels = JSON.parse($('#chart_labels_3').val().replace(/'/g, '"'));
    var vvals = JSON.parse($('#chart_vals_3').val().replace(/'/g, '"'));
    var vbiaya = JSON.parse($('#chart_biaya_3').val().replace(/'/g, '"'));


    Highcharts.chart('container3', {
        chart: {
            zoomType: 'xy'
        },
        title: {
            text: 'Jumlah  Aset Bangunan'
        },
        subtitle: {
            text: ''
        },
        xAxis: [{
            categories: vlabels,
            crosshair: true
        }],

        yAxis: [

            { // Primary yAxis
                labels: {
                    format: '{value}',
                    style: {
                        color: '#666666'
                    }
                },
                min: 0,
                title: {
                    text: 'Jumlah',
                    style: {
                        color: '#666666'
                    }
                },
                opposite: false
            },

            // { // Secondary yAxis
            //     min: 0,
            //     title: {
            //         text: 'Nilai Investasi (Rupiah)',
            //         style: {
            //             color: '#666666'
            //         }
            //     },
            //     labels: {
            //         formatter: function() {
            //             return Highcharts.numberFormat(this.value, 0, ' ', ',')
            //         },
            //         style: {
            //             color: '#666666'
            //         }
            //     },
            //     opposite: false
            // }
        ],
        tooltip: {
            formatter: function() {
                var s = '<tspan style="font-size: 10px">' + this.x + '</tspan>';
                var color = this.points[0].series.color;
                var chart = this.points[0].series.chart; //get the chart object
                var categories = chart.xAxis[0].categories; //get the categories array
                var index = 0;
                while (this.x !== categories[index]) { index++; } //compute the index of corr y value in each data arrays           
                $.each(chart.series, function(i, series) { //loop through series array
                    var ydata = (series.name == 'Nilai Perolehan') ? 'Rp.' + Highcharts.numberFormat(series.data[index].y, 0, ' ', ',') : series.data[index].y;

                    s += '<br/>' + series.name + ': ' +
                        '<b>' + ydata + '</b>'; //use index to get the y value


                });

                // / console.log(s);
                return s;
            },
            shared: true
        },
        legend: {
            layout: 'vertical',
            align: 'left',
            x: 100,
            verticalAlign: 'top',
            y: 0,
            floating: true,
            backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || 'rgba(255,255,255,0.25)'
        },
        plotOptions: {
            series: {
                colorByPoint: true,
                pointWidth: 40
            },
            colors: filteredcol
        },
        series: [{
                name: 'Jumlah',
                visible: true,
                type: 'column',
                // type: 'spline',
                // lineWidth: 2,
                // // color: '#d4a300',
                data: vvals
                    // tooltip: {
                    //     valueSuffix: ''
                    // }
            }
            // ,
            // {
            //     name: 'Nilai Perolehan',
            //     //   type: 'column',
            //     visible: false,
            //     // yAxis: 1,
            //     data: vbiaya,
            //     // color: '#47aded',
            //     // borderColor: '#d4a300',
            //     // tooltip: {
            //     //     valueSuffix: '',
            //     //     valuePrefix: 'Rp.'
            //     // }

            // }

        ]
    });


    var vlabels = JSON.parse($('#chart_labels_4').val().replace(/'/g, '"'));
    var vvals = JSON.parse($('#chart_vals_4').val().replace(/'/g, '"'));
    var vbiaya = JSON.parse($('#chart_biaya_4').val().replace(/'/g, '"'));


    Highcharts.chart('container4', {
        chart: {
            zoomType: 'xy'
        },
        title: {
            text: 'Jumlah  Aset Peralatan'
        },
        subtitle: {
            text: ''
        },
        xAxis: [{
            categories: vlabels,
            crosshair: true
        }],

        yAxis: [

            { // Primary yAxis
                labels: {
                    format: '{value}',
                    style: {
                        color: '#666666'
                    }
                },
                min: 0,
                title: {
                    text: 'Jumlah',
                    style: {
                        color: '#666666'
                    }
                },
                opposite: false
            }

            // { // Secondary yAxis
            //     min: 0,
            //     title: {
            //         text: 'Nilai Investasi (Rupiah)',
            //         style: {
            //             color: '#666666'
            //         }
            //     },
            //     labels: {
            //         formatter: function() {
            //             return Highcharts.numberFormat(this.value, 0, ' ', ',')
            //         },
            //         style: {
            //             color: '#666666'
            //         }
            //     },
            //     opposite: false
            // }
        ],
        tooltip: {
            formatter: function() {
                var s = '<tspan style="font-size: 10px">' + this.x + '</tspan>';
                var color = this.points[0].series.color;
                var chart = this.points[0].series.chart; //get the chart object
                var categories = chart.xAxis[0].categories; //get the categories array
                var index = 0;
                while (this.x !== categories[index]) { index++; } //compute the index of corr y value in each data arrays           
                $.each(chart.series, function(i, series) { //loop through series array
                    var ydata = (series.name == 'Nilai Perolehan') ? 'Rp.' + Highcharts.numberFormat(series.data[index].y, 0, ' ', ',') : series.data[index].y;

                    s += '<br/>' + series.name + ': ' +
                        '<b>' + ydata + '</b>'; //use index to get the y value


                });

                // console.log(s);
                return s;
            },
            shared: true
        },
        legend: {
            layout: 'vertical',
            align: 'left',
            x: 100,
            verticalAlign: 'top',
            y: 0,
            floating: true,
            backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || 'rgba(255,255,255,0.25)'
        },
        plotOptions: {
            series: {
                colorByPoint: true,
                pointWidth: 40
            },
            colors: filteredcol
        },
        series: [{
                name: 'Jumlah',
                visible: true,
                type: 'column',
                // type: 'spline',
                // lineWidth: 2,
                // // color: '#d4a300',
                data: vvals
                    // tooltip: {
                    //     valueSuffix: ''
                    // }
            }
            // ,
            // {
            //     name: 'Nilai Perolehan',
            //     //   type: 'column',
            //     visible: false,
            //     // yAxis: 1,
            //     data: vbiaya,
            //     // color: '#47aded',
            //     // borderColor: '#d4a300',
            //     // tooltip: {
            //     //     valueSuffix: '',
            //     //     valuePrefix: 'Rp.'
            //     // }

            // }

        ]
    });





});