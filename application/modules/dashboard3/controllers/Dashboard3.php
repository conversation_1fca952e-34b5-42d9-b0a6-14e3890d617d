<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard3 extends MY_Controller {

    private $colors = [];

    public function __construct() {
        //
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();

        $this->colors[0] = [
            'rgba(166,206,227,0.8)',
            'rgba(31,120,180,0.8)',
            'rgba(178,223,138,0.8)',
            'rgba(51,160,44,0.8)',
            'rgba(251,154,153,0.8)',
            'rgba(227,26,28,0.8)',
            'rgba(253,191,111,0.8)',
            'rgba(255,127,0,0.8)',
            'rgba(202,178,214,0.8)',
            'rgba(106,61,154,0.8)',
            'rgba(255,255,153,0.8)',
            'rgba(177,89,40,0.8)'
        ];

        $this->colors[1] = [
            'rgba(141,211,199,0.8)',
            'rgba(255,255,179,0.8)',
            'rgba(190,186,218,0.8)',
            'rgba(251,128,114,0.8)',
            'rgba(128,177,211,0.8)',
            'rgba(253,180,98,0.8)',
            'rgba(179,222,105,0.8)',
            'rgba(252,205,229,0.8)',
            'rgba(217,217,217,0.8)',
            'rgba(188,128,189,0.8)',
            'rgba(204,235,197,0.8)',
            'rgba(255,237,111,0.8)'
        ];

        $this->colors[2] = ['rgba(141,211,199,0.8)'];
    
    }

    private function array_to_js_string($a) {
        return "['" . rtrim(implode("','", $a), ',') . "']";
    }

    private function array_to_js_number($a) {
        return "[" . rtrim(implode(',', $a), ',') . "]";
    }

    //param: color variant
    // private function chart_simple_bar($sql) {
    //     $res = $this->db->query($sql);
    //     if (!$res) {
    //         return null;
    //     } else {
    //         $ares = $res->result_array();
    //         $labels = array_column($ares, 'nama');
    //         $vals = array_column($ares, 'jumlah');
    //         $biaya = array_column($ares, 'total');
    //         $biaya2 = array_column($ares, 'total2');

    //         $n = sizeof($labels);
    //         $colors = array();
    //         for ($i = 0; $i < $n; $i++) {
    //             $colors[] = "";
    //         }

    //         $retval = array(
    //             "labels" => $this->array_to_js_string($labels),
    //             "vals" => $this->array_to_js_number($vals),
    //             "biaya" => $this->array_to_js_number($biaya),
    //             "biaya2" => $this->array_to_js_number($biaya2)
    //         );
    //         return $retval;
    //     }
    // }

    private function rand_color($varian, $idx) {
        return $this->colors[$varian][$idx % sizeof($this->colors[$varian])];
    }

    private function chart_simple_bar($sql, $colval) {
        $res = $this->db->query($sql);
        if (!$res) {
            return null;
        } else {
            $ares = $res->result_array();
          $labels = array_column($ares, 'nama');
            $vals = array_column($ares, 'jumlah');
            $biaya = array_column($ares, 'total');
            $biaya2 = array_column($ares, 'total2');

            $n = sizeof($labels);
            $colors = array();
            for ($i = 0; $i < $n; $i++) {
                $colors[] = $this->rand_color($colval, $i);
            }

            $retval = array(
                "labels" => $this->array_to_js_string($labels),
                "vals" => $this->array_to_js_number($vals),
                "biaya" => $this->array_to_js_number($biaya),
                "biaya2" => $this->array_to_js_number($biaya2),
                "colors" => $this->array_to_js_string($colors)
            );

            return $retval;
        }
    }

    function frame() {
        $data['js'] = [
            "modules/dashboard3/views/js_dashboard.js"
        ];
 
        $aa = $this->input->post('ruas');
        $bb = $this->input->post('bujt');
        $cc = $this->input->post('tah');
        
        
        $a='';
        $b='';
        $c='';
      
        if($aa !=''){
            $a=" a.id_ruas='$aa' and ";
            $data['ruas_s']=$aa;
        }
        if($bb !=''){
           $b=" a.kd_bujt='$bb' and ";
           $data['bujt_s']=$bb;
        }
        if($cc !=''){
            $thang = $cc;
             $c=" a.tahun='$thang' and ";
        }else{
            $thang=$this->session->konfig_tahun_ang;
            $c=" a.tahun='$thang' and ";
        }
        
        $data['tahuns']=$thang;
        $wh=$a.$b.$c;
        // die();
        $group = $this->session->users['id_user_group_real'];
        $ks = $this->session->users['kd_bujt'];
        // $kdt=$this->session->konfig_kd_tahapan;
        $thang=$this->session->konfig_tahun_ang;

       // echo $ks; die();

        $where = '';
        $is_bujt = '';
        //   if ($ks !== '') {
             //   echo $group; die();

             if($group == 3)
             {
                //  echo 'bujt'; die();
                 $where = ' a.kd_bujt ='.$ks;
                 $is_bujt = ',a.kd_bujt';
             }
             else
             {
                $where = ' 1=1';
                //  echo 'admin'; die();
                
             }
        //   }
         

        $data['chart']['ruas'] = $this->chart_simple_bar(
                "select 
                    (CASE
                        WHEN a.nilai_investasi is NULL THEN 0
                        ELSE a.nilai_investasi
                    END) as total ,
                    (CASE
                          WHEN a.nilai_ppjt_awal is NULL THEN 0
                          ELSE a.nilai_ppjt_awal
                      END)  as total2
                    ,a.nm_ruas as nama
                    ,count(a.id) as jumlah
                    ,a.tahun
                    $is_bujt
                from 
                (
                select id,id_ruas, nilai_investasi, nilai_ppjt_awal, nm_ruas, nm_seksi, tahun, kd_bujt
                from v_aset_jalan_tol
                ) a
                where $wh $where
                group by 
                    a.nilai_investasi  
                    ,a.nilai_ppjt_awal 
                    ,a.nm_ruas 
                    ,a.id
                    ,a.tahun
                    $is_bujt
                ", 0);
 
        
        
                $data['chart']['jenisjbt'] = $this->chart_simple_bar(
                    "select 
                        coalesce(sum(a.nilai_perolehan),0) as total
                        ,coalesce(sum(a.kuantitas),0) as jumlah 
                        ,a.nm_jnsjembatan as nama
                        ,a.tahun
                        $is_bujt
                    from
                    (
                    select x.nilai_perolehan, x.kuantitas, y.nm_jnsjembatan, y.tahun, x.kd_bujt,x.id_ruas
                    from v_aset_jembatan x
                    right join aset_r_jns_jembatan y on x.kd_jnsjembatan = y.kd_jnsjembatan and x.tahun = y.tahun
                    ) a
                    where $wh $where
                    group by 
                        a.nm_jnsjembatan
                        ,a.tahun 
                        $is_bujt
                    ", 0);        


                    $data['chart']['jenisbgn'] = $this->chart_simple_bar(
                        "select 
                            coalesce(sum(a.nilai_perolehan),0) as total
                            ,coalesce(sum(a.kuantitas),0) as jumlah 
                            ,a.nm_jnsbangun as nama
                            ,a.tahun 
                            $is_bujt 
                        from
                        (
                        select z.nilai_perolehan, z.kuantitas, x.nm_jnsbangun, x.tahun, z.kd_bujt,z.id_ruas
                        from v_aset_bangunan z 
                        left join aset_r_jnsbangunan x on z.kd_jnsbangun = x.kd_jnsbangun and z.tahun = x.tahun
                        ) a
                        where $wh $where
                        group by 
                            a.nm_jnsbangun 
                            ,a.tahun 
                            $is_bujt
                        ", 0);  



                        $data['chart']['jenisalat'] = $this->chart_simple_bar(
                            "select 
                                coalesce(sum(a.nilai_perolehan),0) as total
                                ,coalesce(sum(a.kuantitas),0) as jumlah
                                ,a.peralatan as nama
                                ,a.tahun
                                $is_bujt
                            from
                            (
                            select z.nilai_perolehan, z.kuantitas, x.peralatan, x.tahun, z.kd_bujt,z.id_ruas
                            from v_aset_peralatan z
                            left join aset_r_peralatan x on z.id_alat = x.id_alat and z.kd_jnsalat = x.kd_jnsalat and x.tahun = z.tahun
                            ) a
                            where $wh  $where
                            group by 
                                a.peralatan 
                                ,a.tahun 
                                $is_bujt
                            ", 0);  

                

      

        $this->load->view('v_dashboard_balai', $data);
    }

    public function index() {
//	return; //ruslan, 28 des 2018, skip dulu

        $title = "Jumlah Usulan Per Balai";
        $data = array();


        $this->template->set('title', $title);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }

}
