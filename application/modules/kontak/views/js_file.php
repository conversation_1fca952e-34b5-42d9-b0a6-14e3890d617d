<script>
   

    function updateForm(){
        event.preventDefault()
        var kontak = $('#summernote').summernote('code');
        // kontak = btoa(kontak)
        var objmasterdetail = {
            
            "kontak" : kontak, 
            "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"
            
            
        };

         console.log(kontak);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("kontak/update_form") ?>";
            $.post(url, params).done(function (data) {
                // alert('Data Berubah')
                Swal.fire(
                            'Data Berubah!',
                            'Data Berhasil diubah.',
                            'success'
                        )
                data=JSON.parse(data)
                $('#divShow').html(data.data)
                $('#summernote').html(data.data)
                // console.log(data.data)
                $('#btnEdit').css('display','block')
                $('#divShow').css('display','block')
                $('#btnSimpan').css('display','none')
                $('#divSummernote').css('display','none')

            })
            .fail(function () {
            alert("error");
            });
     
    }

    function edit() {
        $('#btnEdit').css('display','none')
        $('#divShow').css('display','none')
        $('#btnSimpan').css('display','block')
        $('#divSummernote').css('display','block')
    }


    $(document).ready(function() {
        $('#summernote').summernote({
            
        });
    });

    

</script>