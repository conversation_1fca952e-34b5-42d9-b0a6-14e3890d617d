<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Kontak extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "Edit Kontak";
        $js_file = $this->load->view('kontak/js_file', '', true);
        $kontak = $this->db->get('landing_page')->row_array();
        
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "jv_script" => $js_file,
            "data" => $kontak
        );
        
        $this->load->view('index', $data);
    }

    

    function update_form() {
        // $this->wgisitia->handle_removed($param);
        $param = $this->input->post('formData',false);
        
        $sql = "update landing_page set kontak = ? WHERE id = ? ";
        $this->db->query($sql, array($param["kontak"], 1));
        // echo "<pre>";
        // print_r (html_escape($param["kontak"]));
        // echo "</pre>";
        
        // $data_detail = [ 
        //     // "kontak" => $param["kontak"], 
        //     "kontak" => htmlentities($param["kontak"]), 
        // ];
        // $data_detail = $this->db->escape($param["kontak"]);
        // $this->db->where('id', 1);
        // $this->db->update('landing_page', $data_detail);

        echo json_encode(array("status" => TRUE,"data"=>$param["kontak"]));
    }

    


}
