<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>

 <div class="page-body">
    <!-- Server Side Processing table start -->
    <div class="card">
        <div class="card-header">
            <h5><?=$title;?></h5>
        </div>
        <div class="card-block">
        <div class="row">    
                <div class="col-sm-12">
                    <!-- <input type="text" id="plugins4_q" placeholder="Cari grup user" class="form-control fa fa-search" style="width:50%;"> -->
                    <!-- <button type="button" class="btn btn-primary waves-effect waves-light add" onclick="dtTambahRow();">Tambah Neraca Kabupaten
                    </button> -->
                </div>
                <div class="col-md-5" style="margin-top:20px;display:inline">
                        <label style="font-size:15pt;">Provinsi</label>
                        <select id="ikd_prov"onchange="filterChange('prov')" name="ikd_prov" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                </div>
                
                
                <div class="col-md-5" style="margin-top:20px;display:inline">
                        <label style="font-size:15pt;">Tahun</label>
                        <select id="itahun_data"onchange="filterChange('prov')" name="itahun_data" class="bootstrap-select form-control" data-live-search="true">
                            <option value="">Semua Tahun</option>
                            <option value="0">Tidak Ada Tahun</option>
                            <?php
                                for ($i=date('Y'); $i > 2001; $i--) { 
                                    
                                    echo '<option value="'.$i.'">'.$i.'</option>';
                                }
                            ?>
                        </select>
                </div>
                
            </div>
            <br/>
            <div class="dt-responsive table-responsive">
                <table id="dt-server-processing" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Provinsi</th>
                            <th>Ketersediaan Data</th>
                            <th>Tahun Data</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <!-- Server Side Processing table end -->
</div>   
<?php echo $jv_script; ?>
<?php echo $modal_tambah; ?>
<!-- <?php echo $modal_download; ?> -->
<?php echo $modal_edit; ?>
