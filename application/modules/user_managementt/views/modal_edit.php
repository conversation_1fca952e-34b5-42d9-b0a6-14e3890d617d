<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!-- Slide Right Modal -->
<style>
    .invisible{}
</style>
<div class="modal fade" id="modal-edit" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Tambah/Edit User</h4>
                    <button type="button" class="btn-close"
                        data-bs-dismiss="modal"
                        aria-label="Close">
                        <span
                            aria-hidden="true"></span>
                    </button>
                </div>
                <div class="modal-body">
                    <form role="form" id="frm-edit"> 
                    <input type="hidden" id="modeform"/>
                    <input type="hidden" id="nama" name="nama"/>
                    <label for="exampleInputPassword1">Jenis login</label>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" onchange="xjnsLog(1)" type="radio" value="1" name="xjnsLogin" id="xjnsLogin1" checked>
                                <label class="form-check-label" for="xjnsLogin1">
                                    Internal
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" onchange="xjnsLog(2)" type="radio" value="2" name="xjnsLogin" id="xjnsLogin2" >
                                <label class="form-check-label" for="xjnsLogin2">
                                    SSO
                                </label>
                            </div>
                        </div>
                        <div class="form-group" id="xform-peran">
                            <label for="exampleInputPassword1">Peran</label>
                            <select  class="form-control bootstrap-select" onchange="xperanChange($(this).val())" id="xid_user_groups" name="xid_user_groups">
                            </select>
                        </div>
                    <div class="form-group" id="xform-kanwil">
                        <label for="exampleInputPassword1">Kantor Wilayah</label>
                        <select  class="form-control" onchange="xprovChange($(this).val())" id="xkd_prov" name="xkd_prov"> 
                        </select>
                    </div>
                    <div class="form-group" id="xform-kantah">
                        <label for="exampleInputPassword1">Kantor Pertanahan</label>
                        <select  class="form-control" onchange="xkabkotChange($(this).val())" id="xkd_kabkot" name="xkd_kabkot"> 
                        </select>
                    </div>
                                
                        
                        
                        <div class="form-group" style="display:none;">
                            <label for="exampleInputPassword1">ID User</label>
                            <input type="text" class="form-control" id="xid_user" name="xid_user" placeholder="">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">Nama user</label>
                            <input type="text" class="form-control" id="xusername" placeholder="" name="xusername">
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1">Nama Login/NIP</label>
                            <input type="text" class="form-control" id="xuserlogin" placeholder="" name="xuserlogin">
                        </div>
                        <div class="form-group int">
                            <label for="exampleInputPassword1">Email</label>
                            <input type="text" class="inp form-control" id="xemail" name="xemail" placeholder="">
                        </div>
                        <div class="form-group ">
                            <label for="password">Password</label>
                            <input type="password" class="inp form-control" id="xpassword" placeholder="" name="xpassword">
                        </div>
                        <div class="form-group ">
                            <label for="repassword">Konfirmasi password</label>
                            <input type="password" class="inp form-control" id="xrepassword" placeholder="" name="xrepassword">
                        </div>
                    </form>
                </div>
                        <!-- /.box-body -->
                <div class="modal-footer">
                <button type="button"
                    class="btn btn-default waves-effect "
                    data-bs-dismiss="modal">Close</button>
                <button type="button" onclick="updateForm()" 
                    class="btn btn-primary waves-effect waves-light ">Simpan</button>
            </div>
                </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->
