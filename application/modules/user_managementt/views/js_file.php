<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var xhrdata = null;
    var table = null;

    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";

    function clear_input() {
        $("#frm-tambah :input").val("");
        $("#frm-edit :input").val("");
    }

    function listing() {
        table = $('#dt-server-processing').DataTable({
            "draw": 0,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>user_managementt/ssp_paket","data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
                // d.thang = $('#fthang').val();
                
            }},
            "columnDefs": [
                {
                    "aTargets": [0],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[2];
                    }
                },
                {
                    "aTargets": [2],
                    "mRender": function (data, type, full) {
                        return full[3];
                    }
                },
                {
                    "aTargets": [3],
                    "mRender": function (data, type, full) {
                        return full[4];
                    }
                },
                // {
                //     "aTargets": [4],
                //     "mRender": function (data, type, full) {
                //         return full[7];
                //     }
                // },
                // {
                //     "aTargets": [5],
                //     "mRender": function (data, type, full) {
                //         return full[1];
                //     }
                // },
                // {
                //     "aTargets": [6],
                //     "mRender": function (data, type, full) {
                //         return full[3];
                //     }
                // },
                // {
                //     "aTargets": [7],
                //     "mRender": function (data, type, full) {
                //         return full[9];
                //     }
                // },
                // {
                //     "aTargets": [7],
                //     "mRender": function (data, type, full) {
                //         return full[];
                //     }
                // },
                // {
                //     "aTargets": [9],
                //     "mRender": function (data, type, full) {
                //         return full[10];
                //     }
                // },
                {
                    "aTargets": [4],
                    "mRender": function (data, type, row) {
                        var id = row[0];
                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "Edit",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "Hapus",
                            "</button>",
                            // "<button onclick= cek('" + id + "') class='btn btn-warning btn-xs'>",
                            // "Upload",
                            // "</button>",
                            //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                // "paginate": {
                //     "first": "<i class='fast backward ui icon'></i>",
                //     "last": "<i class='fast forward ui icon'></i>",
                //     "next": "<i class='step forward ui icon'></i>",
                //     "previous": "<i class='step backward ui icon'></i>"
                // },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }

    function dtDeleteRow(id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

        // var url = "<?php echo base_url(); ?>" + "pemegang_saham/ajax_delete/" + id + "/" + yearnow;
        var url = "<?php echo base_url(); ?>" + "user_managementt/ajax_delete/" + id;
        var params = {"formData":{}, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        $.post(url, params)
                .done(function (data) {

                var table = $('#dt-server-processing').DataTable();
                table.ajax.reload();
                })
                .fail(function () {
                alert("error");
                })
        }
    }


    // function convertTo(datastring) {
    //     var str = datastring;
    //     var s1 = str.replace('{','[');
            
    //     return eval(s1.replace('}',']'));
    // }

    function dtEditRow(id) {
        event.preventDefault()
    //   function start(callback) { 
          
          data_selected = xhrdata.data.filter(x => x[0] == id)[0];
  
          $('#xid').val(id);
    //   }
      

    //   function a(){
          if(role == 1){ 
            // initComboboxUserGroupEdit('xid_user_groups',data_selected[5]);

          }else{
            refreshComboboxOutput('xid_user_groups', 23, 'tahun',thangs, role);

          }

    //   };

    //   function b(){


          $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
          
          });

    
         

          $('#xusername').val(data_selected[2]);
          $('#xuserlogin').val(data_selected[9]);
          $('#xpassword').val('');
          $('#xrepassword').val('');
          $('#xemail').val(data_selected[3]);
          $('#xid_user').val(data_selected[0]);

          $('#xkd_prov').val(data_selected[7]).selectpicker('refresh');

          provSelect(data_selected[7])
          if(data_selected[5] == 7 || data_selected[5] == 8){
            kabkotSelect(data_selected[7],data_selected[8])
        }
        
        if(data_selected[5] <= 4){
            $('#xjnsLogin1').prop('checked',true)
            satkerSelect(1,data_selected[5])
            $('.int').css('display','block')
            $('.sso').css('display','none')
            $('#xform-kanwil').css('display','none')
            $('#xform-kantah').css('display','none')
        }else{
            $('#xjnsLogin2').prop('checked',true)
            $('.int').css('display','none')
            $('.sso').css('display','block')
            $('#xform-kanwil').css('display','block')
            $('#xform-kantah').css('display','block')
            satkerSelect(2,data_selected[5])

          }
          $("#modal-edit").modal("show");
          
    //   }

    //    $.when($.ajax(start())).then(b());



    }
    
  


    function dtTambahRow() {
        clear_input();

        $('#modal-tambah').modal('show');
        // $('#frm-tambah').trigger('reset');
            // alert('ok');
        //    initCombobox('id_user_groups', 23);
        //    initComboboxUserGroup('id_user_groups');
        //    initCombobox('kd_kabkot', 20);


            // $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
            //     refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
            // });
        jnsLog(1)
        $('#jnsLogin1').prop('checked',true)
        $(".decformat").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;
            // format number
            $(this).val(function (index, value) {
            return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            });
        });

        
       

    }

    function simpanForm() {

        if($( "#frm-tambah" ).valid() === true){
            if($( "#password" ).val() != $( "#repassword" ).val()){
                alert('Konfirmasi Password Tidak Sama');
                return false;
            }
            var xobj_usulan = {
                "username" : $( "#username" ).val(),
                "userlogin" : $( "#userlogin" ).val(), 
                "email" : $( "#email" ).val(), 
                "password" : $( "#password" ).val(), 
                "id_user_groups" : $( "#id_user_groups" ).val(), 
                "kd_prov" : $( "#kd_prov" ).val(), 
                "kd_kabkot" : $( "#kd_kabkot" ).val(), 
                "nama" : $( "#nama" ).val(), 
                "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"
            };

            console.log(xobj_usulan);
            // return false;

            
            var url = "<?php echo base_url(); ?>" + "user_managementt/save_form";
            var params = {"formData": xobj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                
            
            var table = $('#dt-server-processing').DataTable();
            table.ajax.reload();
            $('#modal-tambah').modal('hide');

            })
            .fail(function () {
            alert("error");
            });
        } else {
            alert("Data gagal disimpan, harap periksa informasi di setiap field isian");
            $(".error").css("color","red");
        }
    }

    function updateForm(){
        var id = $("#xid_user").val();
        var data_selected = xhrdata.data.filter(x => x[0] == id)[0];
        if ($('#xjnsLogin1').is(':checked')) {
            var jnsLogin = 1;
        } else {
            var jnsLogin = 2;
        }

        var nama 
        if (jnsLogin==2) {
            if ($('xid_user_groups').val() == 9 && $('xid_user_groups').val() == 10) {
                nama = $('#xkd_prov option:selected').text();
                
            }
            nama = $('#xkd_kabkot option:selected').text();
        }else{
            nama = $( "#xusername" ).val()
        }
        if($( "#frm-edit" ).valid() === true){
            var objmasterdetail = {
                    "id": id,
                    "username" : $( "#xusername" ).val(),
                    "userlogin" : $( "#xuserlogin" ).val(), 
                    "email" : $( "#xemail" ).val(), 
                    "password" : $( "#xpassword" ).val(), 
                    "id_user_groups" : $( "#xid_user_groups" ).val(), 
                    "kd_prov" : $( "#xkd_prov" ).val(), 
                    "kd_kabkot" : $( "#xkd_kabkot" ).val(), 
                    "nama" : nama, 
                    "jnsLogin" : jnsLogin,
                    "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"


            };
             console.log(objmasterdetail);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("user_managementt/update_form") ?>";
            $.post(url, params).done(function (data) {
                if (data == '1'){
                    alert('Data Sudah Ada');
                } else {
                    var table = $('#dt-server-processing').DataTable();
                    table.ajax.reload();
                    $('#modal-edit').modal('hide');
                }
            })
            .fail(function () {
            alert("error");
            });
        }else{
            alert("Data gagal diupdate, silahkan lihat informasi pada setiap field isian untuk memeriksa kesalahan entri data anda");
        $(".error").css("color","red");
        }
    }
    
    // function bind_combo_induk(thang, selval){
    //     var data = get_induk(thang);
    //     var objthang = JSON.parse(data);
    //     $("#kdinduk").empty();
    //     $('#kdinduk').append("<option value='' >" + "--Pilih--" + "</option>");
    //     for(var i=0; i<= objthang.length-1; i++){
    //         $("#kdinduk").append("<option value="+objthang[i].kdsatker+">"+objthang[i].nmsatker+"</option>");
    //     }
    //     if (selval){
    //         $("#kdinduk").val(selval);
    //     }
    // }
    
    // function get_induk(thang){
    //     var x = null;
    //     $.ajax({
    //         type: "GET",
    //         async:false,
    //         url : "<?php echo base_url('user_managementt/get_induk/') ?>" + thang,
    //         success: function(response){
    //             x = response;
    //         },
    //         failure: function(errMsg) {
    //             alert(errMsg);
    //         }
    //     });
    //     return x;
    // }

   
    // function dtdetail(id){
    //     //console.log(xhrdata);

    //     var data_selected = xhrdata.data.filter(x => x[9] === id)[0];  //46

    //     var waydata = {
    //         thang: $("#fthang").val(),
    //         ibukota: data_selected[2],
    //         kd_prov:data_selected[0],
    //         kd_prov_bps: data_selected[3],
    //         kd_prov_irmsv3: data_selected[4],
    //         kd_prov_krisna:data_selected[5],
    //         kd_prov_rams: data_selected[6],
    //         kd_prov_rkakl: data_selected[7],
    //         kd_prov_sipro: data_selected[8],
    //         nama_prov: data_selected[1]

    //     }

    //     way.set('formData', waydata);
    //     $('#modalTitle').text('Detail');
    //     // $('#modeform').val('edit');
    //     $("input").prop('disabled', true);
    //     $("#hid").hide("slow");
    //     $("#hida").hide("slow");
    //     $('#modal-tambah').modal('show');
    // }




   

    function close_alert() {
        $("#alert_information").css({display: "none"});
    }
    
    // function bind_combo_thang(selval){
    //     var data = get_thang();
    //     var objthang = JSON.parse(data);
    //     for(var i=0; i<= objthang.length-1; i++){
    //         $("#fthang").append("<option value="+objthang[i].thang+">"+objthang[i].uraian+"</option>");
    //     }
    //     if (selval){
    //         $("#fthang").val(selval);
    //     }
    // }
    
    // function get_thang(){
    //     var x = null;
    //     $.ajax({
    //         type: "GET",
    //         async:false,
    //         url : "<?php echo base_url('user_managementt/get_thang') ?>",
    //         success: function(response){
    //             x = response;
    //         },
    //         failure: function(errMsg) {
    //             alert(errMsg);
    //         }
    //     });
    //     return x;
    // }
    // function dtUpload(id) {
    //     // alert(id)
    //     $("#id_up").val(id);
    //     var tab = $('#table_id2').DataTable();
    //     tab.destroy()
    //     $('#table_id2 td').empty();
    //     listing_attachment2(id);
    //     $("#modal-download").modal("show");
    // }



    var table_attachment = null;
    function listing_attachment2(id) {

                table_attachment = $('#table_id2').DataTable({

                "draw": 0,
                "columnDefs": [{"orderable": true, "targets": [0]}],
                "order": [[0, "desc"]], 
                "processing": true,
                "serverSide": true,
                "ajax": {
                    type: "POST",
                    url: "<?php echo base_url(); ?>user_managementt/ssp_attachment",
                    data: function (d) {
                        d.id = id;
                       // d.role = role;
                        d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    }
                },
                "columnDefs": [
                    { 
                        "aTargets": [0],
                        "mRender": function (data, type, full) {
                            var ico_class = get_extentsion_file(full[0]);
                            var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp";
                            var subs_img=full[0].substr(0,6);
                            if(ico_class == "feather icon-image"){
                                html_icon="<a target='_blank' class='fancybox' rel='group' href='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'><img class='img-responsive' style='width:40%;' src='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'></a>";
                            }
                            return html_icon+"<br>"+full[3];
                            // alert("ASd")
                        } 
                    },
                    {
                        "aTargets": [1],
                        "mRender": function (data, type, full) {
                            console.log("full attachment");
                            console.log(full);
                            var data_full_attachment = full[0];
                            var dire = data_full_attachment.substr(0, 6);
                            var html_button = [
                                "<a target='_blank' class='btn btn-primary' href='<?php echo base_url(); ?>uploads/" + dire + "/" + data_full_attachment + "'><i class='feather icon-download'></i></a>",
                                "<a target='_blank' class='btn btn-danger' onclick=hapus_lampiran('" + full[1] + "')> <i class='feather icon-trash-2'></i></a>",
                            ].join("\n");
                            return html_button;
                        }
                    }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
            });
            table_attachment.on('xhr', function () {
                xhrdata1 = table_attachment.ajax.json();
                console.log(xhrdata1);
            });

            $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        }

    // }


    //     function get_extentsion_file(file) {
    // var extension = file.substr((file.lastIndexOf('.') + 1));
    // switch (extension) {
    // case 'jpg':
    //         case 'png':
    //         case 'PNG':
    //         case 'jpeg':
    //         case 'gif':
    //         case 'JPG':
    //         return 'feather icon-image'; // There's was a typo in the example where
    // break; // the alert ended with pdf instead of gif.
    // case 'zip':
    //         case 'rar':
    //         //alert('was zip rar');
    //         return 'feather icon-archive';
    //         break;
    // case 'pdf':
    //     return 'feather icon-file-text';
    // case 'xlsx':
    //     return 'feather icon-file-text';
    // break;
    // default:
    // return 'feather icon-file-text';

    // }
    // }

    function hapus_lampiran(id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

            var url = "<?php echo base_url(); ?>" + "user_managementt/hps_lampiran/" + id;
            var params = {"formData": {}, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params)
                    .done(function (data) {
                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload()
                        var tab = $('#table_id2').DataTable();
                        tab.ajax.reload();
                        ;
                    })
                    .fail(function () {
                        alert("error");
                    })
        }
    }
    

    $(document).ready(function () {
        // bind_combo_thang(thangs);
            listing();
        

            // $('#id_bujt, #id_ruas, #id_jnsdana, #id_refbank, #id_pt').select2({
            //     dropdownParent: $('#modal-tambah'),
            //     // width: 'resolve',
            //     theme: 'classic'
            // });

            // $('#xid_bujt, #xid_ruas, #xid_jnsdana, #xid_refbank, #xid_pt').select2({
            //     dropdownParent: $('#modal-edit'),
            //     theme: 'classic',
            // });

            // $('#xid_refbank, #xid_pt').select2({
            //     tags: true
            // });
            $('#submit').submit(function (e) {
            e.preventDefault();
            var file = new FormData;
            $.ajax({
                url: '<?php echo base_url(); ?>user_managementt/up',
                type: "post",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                    $("#judul").val('')
                    $("#filess").val('')
                    $("#kate").val("#");
                 var tab = $('#table_id2').DataTable();
                tab.ajax.reload();
                tlist_paket.ajax.reload();
                }
            });
        });

            $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker(); 
            });


            $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker();
            });

            $('body div#modal-edit.modal').one('hide.bs.modal', function (e) { 
                $('#xid_user_groups').empty();
                $('#xid_user_groups').selectpicker('refresh');
            });


           

       
        });


        function initComboboxUserGroup(divname,id) {


            url = WGI_APP_BASE_URL + "lookup/fieldlookUserGroup";
            // return false;
            wgiAjaxCache(url, function(ajaxdata) {
                jdata = JSON.parse(ajaxdata);
                $('#' + divname).empty();
                $('#' + divname).append(new Option("--Pilih--", ""));
                $.each(jdata, function(i, el) {
                    // $('#' + divname).append(new Option(el.nama,el.id_user_group));
                    $('#' + divname).append('<option value="' + el.id_user_group+ ','+el.id_sub_user_group+'">' + el.nama+ '</option>');
                });
                $('#' + divname).selectpicker('refresh')
            });
        }

        
        
        function initComboboxUserGroupEdit(divname,id) {
            url = WGI_APP_BASE_URL + "lookup/fieldlookUserGroup";
            // return false;
            wgiAjaxCache(url, function(ajaxdata) {
                jdata = JSON.parse(ajaxdata);
                $('#' + divname).empty();
                $('#' + divname).append(new Option("--Pilih--", ""));
                $.each(jdata, function(i, el) {
                    var sel = '';
                    if (el.id_user_group == id) {
                        sel = 'selected';
                        // $('#' + divname).append(new Option(el.nama,el.id_user_group));
                    }
                    $('#' + divname).append('<option '+sel+' value="' + el.id_user_group+ ','+el.id_sub_user_group+'">' + el.nama+ '</option>');
                });
                $('#' + divname).selectpicker('refresh')
            });
        }

    function jnsLog(v) {
        if (v == 2) {
            $('.int').css('display','none')
            $('.sso').css('display','block')
            $('.int').val('')
        } else {
            $('.sso').css('display','none')
            $('.int').css('display','block')
        }
        $.get("<?php echo base_url(); ?>user_managementt/getSatker/"+v, function(data) {
            data = JSON.parse(data)
                $('#id_user_groups').empty();
                $('#id_user_groups').append(new Option("--Pilih--", ""));
                $.each(data, function(i, el) {
                    $('#id_user_groups').append('<option value="' + el.id_user_group+'">' + el.nama+ '</option>');
                });
                $('#id_user_groups').selectpicker('refresh')
        });
        $('.inp').val('')
        $('#kd_prov').html('')
        $('#nama').html('')
        $('#kd_prov').selectpicker('refresh')
        $('#kd_kabkot').html('')
        $('#kd_kabkot').selectpicker('refresh')
        
    }
    
    function peranChange(v) {

        $('.inp').val('')
        $('#nama').html('')
        $('#kd_prov').html('')
        $('#kd_prov').selectpicker('refresh')
        $('#kd_kabkot').html('')
        $('#kd_kabkot').selectpicker('refresh')

        
        if (v == 7 || v == 8) {
            $.get("<?php echo base_url(); ?>user_managementt/getProv/", function(data) {
            data = JSON.parse(data)
                $('#kd_prov').empty();
                $('#kd_prov').append(new Option("--Pilih--", ""));
                $.each(data, function(i, el) {
                    $('#kd_prov').append('<option value="' + el.kode_prov+'">' + el.nama_kantor+ '</option>');
                });
                $('#kd_prov').selectpicker('refresh')
            });
            $('#form-kanwil').css('display','block')
            $('#form-kantah').css('display','block')

        }else if (v == 9 || v == 10){
            $.get("<?php echo base_url(); ?>user_managementt/getProv/", function(data) {
                data = JSON.parse(data)
                    $('#kd_prov').empty();
                    $('#kd_prov').append(new Option("--Pilih--", ""));
                    $.each(data, function(i, el) {
                        $('#kd_prov').append('<option nama-kantor="'+el.nama_kantor+'" value="' + el.kode_prov+'">' + el.nama_kantor+ '</option>');
                    });
                    $('#kd_prov').selectpicker('refresh')
            });
            $('#form-kanwil').css('display','block')
            $('#form-kantah').css('display','none')
        }else{
            $('#form-kanwil').css('display','none')
            $('#form-kantah').css('display','none')
        }
    }

    function provChange(v) {

        $.get("<?php echo base_url(); ?>user_managementt/getKabkot/"+v, function(data) {
            data = JSON.parse(data)
            $('#kd_kabkot').empty();
            $('#kd_kabkot').append(new Option("--Pilih--", ""));
            $.each(data, function(i, el) {
                var sel = '';
                if (el.kode_kab_kota == v) {
                    sel = 'selected';
                    // $('#' + divname).append(new Option(el.nama,el.id_user_group));
                }
                $('#kd_kabkot').append('<option '+sel+' value="' + el.kode_kab_kota+'">' + el.nama_kantor+ '</option>');
            });
            $('#kd_kabkot').selectpicker('refresh')
        });
        var nama_kantor = $('#kd_kabkot option:selected').text();
        $('#nama').val(nama_kantor)
    
    }
    function kabkotChange(v) {

       
        var nama_kantor = $('#kd_kabkot option:selected').text();
        $('#nama').val(nama_kantor)
    
    }

    function provSelect(v) {

        $.get("<?php echo base_url(); ?>user_managementt/getProv/", function(data) {
            data = JSON.parse(data)
            $('#xkd_prov').empty();
            $('#xkd_prov').append(new Option("--Pilih--", ""));
            $.each(data, function(i, el) {
                var sel = '';
                if (el.kode_prov == v) {
                    sel = 'selected';
                }
                $('#xkd_prov').append('<option '+sel+' value="' + el.kode_prov+'">' + el.nama_kantor+ '</option>');
            });
            $('#xkd_prov').selectpicker('refresh')
        });
        var nama_kantor = $('#kd_prov option:selected').text();
        $('#nama').val(nama_kantor)

    }
    function kabkotSelect(prov,kab) {

        $.get("<?php echo base_url(); ?>user_managementt/getKabkot/"+prov, function(data) {
            data = JSON.parse(data)
            $('#xkd_kabkot').empty();
            $('#xkd_kabkot').append(new Option("--Pilih--", ""));
            $.each(data, function(i, el) {
                var sel = '';
                if (el.kode_kab_kota == kab) {
                    sel = 'selected';
                    // $('#' + divname).append(new Option(el.nama,el.id_user_group));
                }
                $('#xkd_kabkot').append('<option '+sel+' value="' + el.kode_kab_kota+'">' + el.nama_kantor+ '</option>');
            });
            $('#xkd_kabkot').selectpicker('refresh')
        });
        var nama_kantor = $('#kd_prov option:selected').text();
        $('#nama').val(nama_kantor)

    }
    function satkerSelect(v,satker) {

        $.get("<?php echo base_url(); ?>user_managementt/getSatker/"+v, function(data) {
            data = JSON.parse(data)
            $('#xid_user_groups').empty();
            $('#xid_user_groups').append(new Option("--Pilih--", ""));
            $.each(data, function(i, el) {
                if (el.id_user_group == satker) {
                    $('#xid_user_groups').append('<option selected value="' + el.id_user_group+'">' + el.nama+ '</option>');
                }else{

                    $('#xid_user_groups').append('<option value="' + el.id_user_group+'">' + el.nama+ '</option>');
                }
            });
            $('#xid_user_groups').selectpicker('refresh')
        });

    }

    function xjnsLog(v) {
        if (v == 2) {
            $('.int').css('display','none')
            $('.int').val('')
        } else {
            $('.int').css('display','block')
        }
        $.get("<?php echo base_url(); ?>user_managementt/getSatker/"+v, function(data) {
            data = JSON.parse(data)
                $('#xid_user_groups').empty();
                $('#xid_user_groups').append(new Option("--Pilih--", ""));
                $.each(data, function(i, el) {
                    $('#xid_user_groups').append('<option value="' + el.id_user_group+'">' + el.nama+ '</option>');
                });
                $('#xid_user_groups').selectpicker('refresh')
        });
        $('.inp').val('')
        $('#xkd_prov').html('')
        $('#xnama').html('')
        $('#xkd_prov').selectpicker('refresh')
        $('#xkd_kabkot').html('')
        $('#xkd_kabkot').selectpicker('refresh')
        
    }
    
    function xperanChange(v) {
        $('.inp').val('')
        $('#xnama').html('')
        $('#xkd_prov').html('')
        $('#kd_prov').selectpicker('refresh')
        $('#xkd_kabkot').html('')
        $('#xkd_kabkot').selectpicker('refresh')

        
        if (v == 7 || v == 8) {
            $.get("<?php echo base_url(); ?>user_managementt/getProv/", function(data) {
            data = JSON.parse(data)
                $('#xkd_prov').empty();
                $('#xkd_prov').append(new Option("--Pilih--", ""));
                $.each(data, function(i, el) {
                    $('#xkd_prov').append('<option value="' + el.kode_prov+'">' + el.nama_kantor+ '</option>');
                });
                $('#xkd_prov').selectpicker('refresh')
            });
            $('#xform-kanwil').css('display','block')
            $('#xform-kantah').css('display','block')

        }else if (v == 9 || v == 10){
            $.get("<?php echo base_url(); ?>user_managementt/getProv/", function(data) {
                data = JSON.parse(data)
                    $('#xkd_prov').empty();
                    $('#xkd_prov').append(new Option("--Pilih--", ""));
                    $.each(data, function(i, el) {
                        $('#xkd_prov').append('<option nama-kantor="'+el.nama_kantor+'" value="' + el.kode_prov+'">' + el.nama_kantor+ '</option>');
                    });
                    $('#xkd_prov').selectpicker('refresh')
            });
            $('#xform-kanwil').css('display','block')
            $('#xform-kantah').css('display','none')
        }else{
            $('#xform-kanwil').css('display','none')
            $('#xform-kantah').css('display','none')
        }
    }

    function xprovChange(v) {

        $.get("<?php echo base_url(); ?>user_managementt/getKabkot/"+v, function(data) {
            data = JSON.parse(data)
            $('#xkd_kabkot').empty();
            $('#xkd_kabkot').append(new Option("--Pilih--", ""));
            $.each(data, function(i, el) {
                var sel = '';
                if (el.kode_kab_kota == v) {
                    sel = 'selected';
                    // $('#' + divname).append(new Option(el.nama,el.id_user_group));
                }
                $('#xkd_kabkot').append('<option '+sel+' value="' + el.kode_kab_kota+'">' + el.nama_kantor+ '</option>');
            });
            $('#xkd_kabkot').selectpicker('refresh')
        });
        var nama_kantor = $('#xkd_kabkot option:selected').text();
        $('#xnama').val(nama_kantor)
    
    }
    function xkabkotChange(v) {

       
        var nama_kantor = $('#xkd_kabkot option:selected').text();
        $('#xnama').val(nama_kantor)
    
    }

</script>


