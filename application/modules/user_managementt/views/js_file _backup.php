<script>
var sessid_user_group = "<?php echo $this->session->users['id_user_group_real']; ?>";
var xhrdata=null;
    var html_string=[
               "<table id='table_id' class='display' >",
                    "<thead>",
                    "<tr>",
                        "<th><div style='width:180px;'>Action</div></th>",
                        "<th><div style='width:180px;'>Nama User</div></th>",
                        "<th><div style='width:180px;'>Peran</div></th>",                          
                    "</tr>",
                    "</thead>",
                    "<tfoot>",
                        "<tr>",
                            "<th>&nbsp;</th>",
                            "<th>&nbsp;</th>",
                            "<th>&nbsp;</th>",
                        "</tr>",
                    "</tfoot>",
                "</table>",
                ].join("\n");
    function clear_input(){
        $("#formData :input").val("");
    }
    var table=null;
    /**
           $columns = array(  
            array('db' => 'username','dt'=> 0),           
            array('db' => 'peran','dt'=> 1), 
            array('db' => 'id_user_group','dt' => 2), 
            array('db' => 'email','dt' => 3),  
            array('db' => 'kode_satker','dt' => 4), 
            array('db' => 'nama_satker','dt' => 5),
            array('db' => 'kd_dapil','dt' => 6),
            array('db' => 'nama_dapil','dt' => 7),
            array('db' => 'kd_fraksi','dt' => 8),
            array('db' => 'nama_fraksi','dt' =>9),
            array('db' => 'id_sub_user_group','dt' =>10),
            array('db' => 'id_user','dt' =>11)         
        );
     **/
    function listing(){
            table = $('#table_id').DataTable({
                "createdRow": function (row, data, index) {
                    $('td', row).eq(2).addClass('invisible');
                    $('td', row).eq(4).addClass('invisible');
                    $('td', row).eq(6).addClass('invisible');
                    $('td', row).eq(8).addClass('invisible');
                    $('td', row).eq(10).addClass('invisible');
                    
                    $('td', row).eq(11).addClass('background-solid');
                    $('td', row).eq(11).addClass('center-element');
                    $('td', row).eq(12).addClass('invisible');
                },
                "draw": 0,
                "order": [[2, "asc"]],
                "processing": true,
                "serverSide": true,
                "ajax": { type: "POST", url: "///user_management/ssp"},
                "aoColumnDefs": [
                    {
                        "aTargets": [11],
                        "mRender": function (data, type, full) {
                                     //console.log(full[12]+"full data");
                                     //alert(full.kd_prov+'kd_prov');
                                      var html_button=[
                                            "<button onclick= dtEditRow('"+data+"','"+full[13]+"') class='btn btn-primary btn-xs'>",
                                                 "<i class='fa fa-pencil'>",
                                                 "</i>",
                                            "</button>",
                                            "<button onclick= dtDeleteRow('"+data+"') class='btn btn-danger btn-xs'>",
                                                 "<i class='fa fa-trash'>",
                                                 "</i>",
                                            "</button>",
                                        ].join("\n");
                            return "<center class='center-element'>"+html_button+"</center>";
                        }
                    }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "Memuat...",
                    "processing": "Memroses...",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
                ,
                "pagingType": "full_numbers",
                "columnDefs": [ 
                    {
                        "targets": [ 2 ],
                        "visible": true,
                    }
                    ],
                "pageLength": 10,
                "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            scrollY:        "450px",
            scrollX:        true,
            scrollCollapse: true,
            fixedColumns:   {
                leftColumns: 0,
                //rightColumns: 1
                },
            "responsive": true
            });
           
            table.on( 'xhr', function () {
                xhrdata = table.ajax.json();
        //        console.log('xhr data: ' );
                console.log(xhrdata);
            });
        //});

        $(".display").addClass( "table table-bordered table-striped js-dataTable-full-pagination" );
         
        }

         //extrackttree(43);
        function extrackttree(id_sub_group){
        url = "///user_management/get_parent/"+id_sub_group;
        $.get(url).done(function (data) {
             //console.log("extract tree");      
             console.log(data);
                    
        })
        .fail(function () {
            alert("error");
        })
        .always(function () {
            //alert("finished");
        });     

        }


     function bind_data_html_edit(data_html,id_user_group){
        //(data_html);
        //$("#form-group-container").empty();
        //console.log(data_html);
        var label_value="";
        //alert(id_user_group);
        if(id_user_group==0){
            label_value="Peran";
        }
        //Group DPR 
        if(id_user_group==40){
            label_value="Komisi";
        }

        //Group Binamarga 
        if(id_user_group==2){
            label_value="";
        }

         //Group Binamarga 
        if(id_user_group==54){
            label_value="Direktorat  Kompetensi";
        }

        //Group Binamarga 
        if(id_user_group==9){
            label_value="Sub Direktorat  Kompetensi";
        }
        

         var html_form_group=[
            "<div class='form-group'>",
            "<label for='exampleInputPassword1'>"+label_value+"</label>",
            data_html,
            "</div>",
        ].join("\n");
        console.log("---appneded html---")
        console.log(html_form_group);
         $("#form-group-container").append(html_form_group);
       }
    var html_first_element="";
    function refresh_children(element){
        var id_sub_user_group=element.value;
        //alert(id_sub_user_group);
       
        //alert(data_clone);
            //alert($("#kode_satker").text());
          //$("#kode_satker").parent().remove();
          $("#kode_satker").empty();
          //alert(22);
        if(id_sub_user_group==71){
            bind_lookup_provinsi()
        }else{  
            if(id_sub_user_group!="20"){

                    url = "<?php echo base_url(); ?>user_management/getchildrens/"+id_sub_user_group;
                    remove_all_sibling_bellow($(':focus').parents().index());
                    $.get(url).done(function (data) {
                        var myobj =JSON.parse(data);
                        var full_select=[];
                        for(var i=0; i<=myobj.length-1; i++){
                            var html_children="<option value='"+myobj[i].id_user_group+"'>"+myobj[i].nama+"</option>";
                             full_select.push(html_children);
                        }
                        //console.log("%%% html childrens %%%")
                        //console.log(full_select.join(""));
                        var html_select=[
                        "<select onchange='refresh_children(this)' id='sel_edit_0' class='form-control'>",
                            full_select.join(""),
                        "</select>"
                        ];
                        if(myobj.length != 0){
                            bind_data_html_edit(html_select.join(""),id_user_group);
                        }
                    });
             }else{
                    //var data_clone=$("#form-group-container .form-group:eq(0)" ).html();
                    //bind_lookup_satker();
                    $("#form-group-container").empty();
                    //bind_lookup_satker();
                    //$("#form-group-container").prepend(data_clone);
                    //$("#form-group-container").prepend(html_first_element)
                    //alert(data_clone);
                    var data_focus=$(":focus").val();
                    // //mendapatkan kode satker mode tambah
                     initComboboxUserGroupSatker("kode_satker", 43,data_focus);


             }
         }
         //end provinsi 
          //mendapatkan kode satker mode edit balai
          //alert($("#sel_edit_1").val());
         var head_element=$("#form-group-container select:eq(0)").val();
         if(head_element==65){
             var satker_parent=$("#form-group-container select:eq(1)").val();
             if($("#sel_edit_0").length){
                 initComboboxUserGroupSatker("kode_satker", 43,satker_parent);
             }
         }

         //mendapatkan kode satker mode edit pusat
          //alert($("#sel_edit_1").val());
         if(head_element==2){
            //alert($("#form-group-container select:eq(2)").val());
           
             //alert("satker parent bbb ->"+ satker_parent);
             //alert($("#form-group-container select:eq(2)").length);
             var satker_parent=-1;
             if($("#form-group-container select:eq(2)").length !=0){
                 var satker_parent=$("#form-group-container select:eq(2)").val();
             }else{
                 var satker_parent=$("#form-group-container select:eq(1)").val();
             }
             if($("#sel_edit_0").length !=0){
                 initComboboxUserGroupSatker("kode_satker", 43,satker_parent);
             }
         }
       
    }

    /**
          $columns = array(  
            array('db' => 'username','dt'=> 0),           
            array('db' => 'peran','dt'=> 1), 
            array('db' => 'id_user_group','dt' => 2), 
            array('db' => 'email','dt' => 3),  
            array('db' => 'kode_satker','dt' => 4), 
            array('db' => 'nama_satker','dt' => 5),
            array('db' => 'kd_dapil','dt' => 6),
            array('db' => 'nama_dapil','dt' => 7),
            array('db' => 'kd_fraksi','dt' => 8),
            array('db' => 'nama_fraksi','dt' =>9),
            array('db' => 'id_sub_user_group','dt' =>10),
            array('db' => 'id_user','dt' =>11)         
        );
    **/

    function dtEditRow(id,xkd_prov){
        $("input").val("");
         $("select").val("");
        var data_selected = xhrdata.data.filter(x=>   x[11]==id  )[0];
        $("#email").val(data_selected[3]);
        $("#id_user").val(id);
        $("#password").val("");
        var kode_satker=data_selected[4];
        var nama_satker=data_selected[5];
        var xid_sub_group=data_selected[2];
        var xnama_dapil=data_selected[7];
        var xnama_fraksi=data_selected[9];
        var xparent=data_selected[10];
       
        if(xparent==40){
            $("#form-group-dpr").removeClass("invisible")
            $("#form-group-satker").addClass("invisible")
            updateCombobox("kd_dapil", 1, xnama_dapil);
            updateCombobox("kd_fraksi", 2,xnama_fraksi);
        }
       if(data_selected[3]!=null){
            $("#form-peran").hide();
         
                url = "<?php echo base_url(); ?>user_management/get_parent/"+xid_sub_group;
                $.get(url).done(function (data) {
                    var myobj=JSON.parse(data).reverse();                  
                    var html_form_group=[
                        "<div class='form-group'>",
                            "<label for='exampleInputPassword1'>Nama User</label>",
                            "<input class='form-control' id='username' placeholder='' type='text'>",
                        "</div>",
                    ].join("\n");
                     $("#form-group-container").empty();

                    for (var i=0; i<= myobj.length-1; i++){
                        var html_select=[
                            "<select onchange='refresh_children(this)' id=sel_edit_"+i+" class='form-control select-edit'>",
                                "<option value="+myobj[i].id_user_group+" selected='selected'>",
                                    myobj[i].nama,
                                 "</option>",
                            "</select'>",
                        ].join("\n");
                         $("#fullselect").val("");
                        bind_data_html_edit(html_select,myobj[i].id_sub_user_group);
                        url = "<?php echo base_url(); ?>user_management/getlistparrents/"+myobj[i].id_sub_user_group;
                        var full_select=[];
                        jQuery.ajaxSetup({async:false});
                        $.get(url).done(function (data) {
                            "--data to prepend--"
                            var myobj =JSON.parse(data);                           
                             for(var i2=0; i2<= myobj.length-1; i2++){
                                var html_options=[
                                    "<option value="+myobj[i2].id_user_group+">",
                                        myobj[i2].nama,
                                     "</option>",
                                ].join("\n");

                               full_select.push(html_options);
                             }
                        });
                         $("#sel_edit_"+i).append(full_select.join(""));
                    }

                     var yparent=$("#sel_edit_0").val();
                     if(yparent==65 || yparent==2){
                      var xid_user_group_satker=$("#sel_edit_1").val();                    
                      updateComboboxSatker("kode_satker", 43,nama_satker,xid_user_group_satker);  
                    } 
                    
                      
                    //alert($("#sel_edit_0").val());
                    var kdprov="";
                    if(yparent==71){
                        //alert(2);
                        bind_lookup_provinsi_by_id(xkd_prov);
                    }

                   

                })
                .fail(function () {
                    alert("error");
                })
                .always(function () {
                    //alert("finished");
                }); 
                //alert($("#sel_edit_1").val());
                
                if($('#sel_edit_1').length){
                    //updateComboboxSatker("kode_satker", 43,kode_satker,$("#sel_edit_1").val()); 
                    updateComboboxSatker("kode_satker", 43,nama_satker,$("#sel_edit_1").val()); 
                }

                
       }

    
        //updateCombobox('kd_prov',25,data_selected[4]);
        
        //way.set('formData', waydata);
        //$("#id_user").val(data_selected[4]);
       
        
        $("#username").val(data_selected[0]);
        $('#modalTitle').text('Edit Data');
        $('#modeform').val('edit');
        $('#modal-tambah').modal('show');
    }

    //tambah dan edit data
    function dtTambahRow() {
        clear_input();
        //console.log('nambah row');
        //initTreeCombobox();
          $("#form-peran").show();
        initTreeCombobox('id_user_group',27);
        $("#form-group-container").empty();
        $('#modalTitle').text('Tambah Data');
        $('#modeform').val('tambah');
        $('#modal-tambah').modal('show');
    }


    function dtDeleteRow(id) {
        
        if (confirm("Apakah anda yakin akan menghapus data ini?")) {
            url = "<?php echo base_url() ?>/user_management/deleteform";
                $.post(url, {id:id}).done(function (data) {
                    table.ajax.reload();
                     $("#alert-content").empty();
                     $("#alert-content").append(" <p>Delete data suksess");
                    $("#alert_information").css({display: "block"});
                    setTimeout(close_alert, 3000);     
                })
                .fail(function () {
                    alert("error");
                })
                .always(function () {
                    //alert("finished");
                });        
        } 

       
    }
    //untuk keperluan refresh mencegah error karena konflik element 
    function destroylisting(){
         var table = $('#table_id').DataTable();
         table.clear();
         table.draw();
         //listing();
    
         var tables = $.fn.dataTable.fnTables(true);

         $(tables).each(function () {
             $(this).dataTable().fnDestroy();
         });
         $('#table_id').remove();
     $("#table-container").append(html_string);
    }

    function refreshComboForm(element){
         var value=element.value;
         refreshCombobox('id_kabkot',27,"kd_prov",value);
    }
    
   //xx
   function bind_data_html(data_html){
    //(data_html);
    //$("#form-group-container").empty();
    //console.log(data_html);
    console.log("data html")
    console.log(data_html);
     var html_form_group=[
        "<div class='form-group'>",
        "<label for='exampleInputPassword1'>"+" "+"</label>",
        data_html,
        "</div>",
    ].join("\n");
     $("#form-group-container").append(html_form_group);
   }

    //selalu letakan kode ini paling bawah
     
    function remove_all_sibling_bellow(indeks){
        //alert(indeks);
       for ( var i=0; i<= $("#form-group-container .form-group").length-1; i++ ){
            //alert(i);
            if(i > indeks){
                //alert("igreater than index");
                //$("#form-group-container .form-group").eq(i).remove();
                $("#form-group-container .form-group").eq(i).remove();
            }
            if(indeks==0){
                $('#form-group-container .form-group').not(':first').remove();
                //.not(':first').remove()
            }
       }
    }
    //getChildren


    function bind_lookup_satker(){
               $.ajax({
                url:"<?php echo base_url(); ?>user_management/get_satker",
                type:"GET",
                success:function(data){
                    var obj=JSON.parse(data);
                     var array_select=[];
                     for (var i=0; i<=obj.length-1; i++){
                     var html_option=[
                            "<option value='"+obj[i].kdsatker+"'>",
                                obj[i].nmsatker,
                            "</option>",
                        ].join("\n");
                        array_select.push(html_option);
                    }
                        $("#form-group-container").empty();
                        // $("#form-group-container").append("<label>Pilih Satker</label>")
                        var html_select= "<select id='kode_satker' class='form-control'>"+array_select.toString()+"</select>";
                         bind_data_html(html_select);
                         $("#kode_satker").prepend("<option selected>--Pilih--</option>")

                }

                });  
    }
    
    function bind_lookup_provinsi(){
               $.ajax({
                url:"<?php echo base_url(); ?>user_management/get_provinsi",
                type:"GET",
                success:function(data){
                    var obj=JSON.parse(data);
                     var array_select=[];
                     for (var i=0; i<=obj.length-1; i++){
                     var html_option=[
                            "<option value='"+obj[i].kd_prov_irmsv3+"'>",
                                obj[i].nama_prov,
                            "</option>",
                        ].join("\n");
                        array_select.push(html_option);
                    }
                        //$("#form-group-container").empty();                     
                        var html_select= "<select id='kd_prov' class='form-control'>"+array_select.toString()+"</select>";
                         bind_data_html(html_select);
                         $("#kd_prov").prepend("<option selected>--Pilih Provinsi--</option>")

                }

                });  
    }
    
    function bind_lookup_provinsi_by_id(kd_prov){
               $.ajax({
                url:"<?php echo base_url(); ?>user_management/get_provinsi",
                type:"GET",
                success:function(data){
                    var obj=JSON.parse(data);
                     var array_select=[];
                     for (var i=0; i<=obj.length-1; i++){
                        var html_option="";
                        if(kd_prov==obj[i].kd_prov_irmsv3){
                           html_option=[
                                   "<option selected value='"+obj[i].kd_prov_irmsv3+"'>",
                                       obj[i].nama_prov,
                                   "</option>",
                               ].join("\n");
                        }else{
                          html_option=[
                                   "<option value='"+obj[i].kd_prov_irmsv3+"'>",
                                       obj[i].nama_prov,
                                   "</option>",
                               ].join("\n"); 
                        }
                        array_select.push(html_option);
                    }
                        //$("#form-group-container").empty();                     
                        var html_select= "<select id='kd_prov' class='form-control'>"+array_select.toString()+"</select>";
                         bind_data_html(html_select);
                         //$("#kd_prov").prepend("<option selected>--Pilih Provinsi--</option>")

                }

                });  
    }

    function getChildren(element){

        var id_user_group=element.value;
        $("#form-group-dpr").addClass("invisible");
        $("#form-group-satker").removeClass("invisible");
        
        if(id_user_group ==71){
           bind_lookup_provinsi();
        }else{
            //remove lookup provinsi
            if(id_user_group ==20){
                bind_lookup_satker();

            }else{
                     //remove_all_sibling_bellow($(':focus :parent').index());
                        $("#form-group-container").empty();
                        //ajax get childrem
                        $.ajax({
                        url:"<?php echo base_url(); ?>registration/list_children/" + id_user_group,
                        type:"GET",
                        success:function(data){
                                //console.log(data.data);
                                bind_data_html(data.data);
                                $('#id_sub_user_group').on('change',function(){
                                  // alert("111");
                                var data_focus=$(':focus').val();//id_user_group
                                initComboboxUserGroupSatker("kode_satker", 43,data_focus);
                                    remove_all_sibling_bellow($(':focus').parents().index());
                                let id_sub_user_group = $('#id_sub_user_group').val();
                                 //$("#form-group-dpr").addClass("invisible");
                                //non dpr
                                if(id_user_group != 40){


                                        if(id_sub_user_group != null || id_sub_user_group != false || id_sub_user_group != " " || id_sub_user_group != 0){
                                            $.ajax({
                                            url:"<?php echo base_url(); ?>registration/list_last_group/" + id_sub_user_group + '/' + 0,
                                            type:"GET",
                                                success:function(data){
                                                     bind_data_html(data.data);
                                                     $('#id_sub_sub_user_group').on('change',function(){
                                                        //console.log($(':focus :parent').index());
                                                        //remove_all_sibling_bellow($(':focus :parent').prevAll().length;
                                                        //alert($(':focus :parent').prevAll().length);
                                                       //alert($(':focus').val());
                                                       ///$(':focus').val()
                                                        //
                                                        //remove_all_sibling_bellow($(':focus :parent').index()+1);old


                                                         //alert(data_focus);
                                                         //initComboboxUserGroupSatker("kode_satker", 43,data_focus);
                                                        remove_all_sibling_bellow($(':focus').parents().index());
                                                        let id_sub_sub_user_group= $('#id_sub_sub_user_group').val();
                                                        $.ajax({
                                                            url:"<?php echo base_url(); ?>registration/list_last_group/" + id_sub_sub_user_group + '/' + 0 + '/set',
                                                            type:"GET",
                                                            success:function(data){
                                                                 bind_data_html(data.data);
                                                            }

                                                        });
                                                     });


                                                }       
                                            }); 
                                         }

                                    }else{

                                            $("#form-group-dpr").removeClass("invisible");
                                            $("#form-group-satker").addClass("invisible");
                                            initCombobox("kd_dapil", 1);
                                            initCombobox("kd_fraksi", 2);


                                    }
                                });

                            }
                        });

            }
        }
        //end lookup provinsi
    }

     function simpanForm(){

        var mode = $('#modeform').val();
        //alert(mode);
        var url;
        if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>user_management/addform";
        } else if (mode == 'edit') {
            url = "<?php echo base_url(); ?>user_management/editform";
            //data.id_sub_user_group = $("#id_user_group").val();
        };
        
        //mengambil sub group terjauh
        var numItems = $('#form-group-container select').length;
        var id_sub_user_group=$("#form-group-container select").eq(numItems-2).val();
        var id_user_group=$("#form-group-container select").eq(numItems-1).val();

        var data={
            "id_user"           :$("#id_user").val(),
            "email"             :$("#email").val(),
            "username"          :$("#username").val(),
            "password"          :$("#password").val(),
            "id_sub_user_group" :id_sub_user_group,
            "id_user_group"     :id_user_group
        }
        //dpr
         if($("#id_user_group").val()==40 ){
             data.id_user_group = $("#form-group-container select").eq(0).val();
             data.id_sub_user_group = $("#id_user_group").val();
             data.kd_fraksi     = $("#kd_fraksi").val();
             var xkd_dapil      = $("#kd_dapil").val();
             data.kd_dapil = xkd_dapil.split("|")[0];
         }

         // satker
         if($("#id_user_group").val()==65){
             data.id_sub_user_group = $("#id_user_group").val(); 
         }
         
         // pemda
         if($("#id_user_group").val()==71){
             data.kd_prov = $("#kd_prov").val();
             data.id_user_group = $("#id_user_group").val(); 
         }
       
        if($("#kode_satker").val()!= "" && $("#kode_satker").val()!= -1){
            data.kode_satker = $("#kode_satker").val();
            data.id_sub_user_group=$("#id_user_group").val();
        }

         if (mode == 'edit') {
             
            if($("#sel_edit_0").val()==71){
                data.kd_prov = $("#kd_prov").val();           
                data.id_user_group = $("#sel_edit_0").val(); 
            }
            
            if($("#id_user_group").val()==65 ){
             data.id_sub_user_group = $("#sel_edit_0").val();
            
            }

        
             if($("#id_user_group").val()==20){
                 data.id_sub_user_group = $("#sel_edit_0").val(); 
             }
           
            if($("#kode_satker").val()!= "" && $("#kode_satker").val()!= -1){
                data.kode_satker = $("#kode_satker").val();
                data.id_sub_user_group=$("#id_user_group").val();
            }
        }
        //alert(data.kd_prov+"abcdefghxxx")
        var params = {"formData": data};
        console.log(params);
        var repassword=$("#repassword").val();
        var password=$("#password").val();
        var isPasswordMatch=true;
        if(password != repassword){
            isPasswordMatch=false;
            alert("Password tidak terkonfirmasi dengan benar harap cek kembali")
        }
       
        if($("#username").val() == ""|| $("#email").val() == "" || id_user_group=="" || isPasswordMatch==false){
            alert("Simpan data gagal!, Harap koreksi/lengkapi semua kolom isian..");
        }else{
               $.post(url, params)
                .done(function (data) {   
                    table.ajax.reload();
                    $("#modal-tambah").modal("hide"); 
                     $("#alert-content").empty();
                    $("#alert-content").append(" <p>Simpan data suksess");
                    $("#alert_information").css({display: "block"});
                    setTimeout(close_alert, 3000);                   
                })
                .fail(function () {
                    alert("error");
                })
                .always(function () {

            });
        }
       
    }

    $(document).ready(function(){
       listing();
       $("#table_id").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
       //$("#table_id td").addClass("invisible");
       initCombobox('id_user_group',27); //digunakan untuk combo box dengan nomenklatur 
    });

 function setComboVal(divname, newval) {
    if (newval != '') {
        $('#'+divname).val(newval);
    }
}

function close_alert() {
    $("#alert_information").css({display: "none"});

}
//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
//selindes: selected value
function updateCombobox(divname, refindex, selvalue) {
    url = "<?php echo base_url(); ?>lookup/fieldlook/" + refindex;
    //alert(selvalue+"yyyyy");
    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#'+divname).empty();
        $.each(jdata, function (i, el) {
           

            if(selvalue==el.val){
                 $('#'+divname).append( new Option(el.val,el.id,"selected") );
            }else{
               $('#'+divname).append( new Option(el.val,el.id) );  
            }

        });                    
       
        // if (selvalue != ''){
        //     $('#'+divname).val(selvalue)
        // }
    })
    .fail(function () {
        alert("error");
    })
    .always(function () {
        // alert("finished");
    });   
}


//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
//selindes: selected value
function updateComboboxSatker(divname, refindex, selvalue,id_user_group) {
    //alert("XXXXX"+selvalue+"XXXXX");
    if(id_user_group != "--pilih--"){
        url = "<?php echo base_url(); ?>lookup/fieldlookusergroupsatker/" + refindex+"/"+id_user_group;

        $.get(url).done(function (data) {
            jdata = JSON.parse(data);
            $('#'+divname).empty();
            $.each(jdata, function (i, el) {
                $('#'+divname).append( new Option(el.val,el.id) );
                //alert(i);
                if(selvalue==el.val){
                     //$('#'+divname).append( new Option(el.val,el.id,"selected") );
                     //alert(i);
                     $('#'+divname).append("<option selected value="+jdata(i).kode_satker+">"+selvalue+"</option>")
                }else{
                   $('#'+divname).append( new Option(el.val,el.id) );  
                }
            });                    

            //if (selvalue != '') $('#'+divname).val(selvalue)
        })
        .fail(function () {
            alert("error");
        })
        .always(function () {
            // alert("finished");
        });  
    } 
}


function updateComboboxtree(divname, refindex, selvalue,selrvalue) {
    url = "<?php echo base_url(); ?>lookup/fieldlooktree/" + refindex;
    alert(selvalue);
    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#'+divname).empty();
        $.each(jdata, function (i, el) {
            $('#'+divname).append( new Option(el.val,el.id) );
        });                    

        //if (selvalue != '') $('#'+divname).val(selvalue)
        if (selvalue != '') $('#'+divname).append("<option value="+selrvalue+" selected>"+selvalue+"</option>")
    })
    .fail(function () {
        alert("error");
    })
    .always(function () {
        // alert("finished");
    });   
}


//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
 function initComboboxUserGroupSatker(divname, refindex,id_user_group) {
        url = "<?php echo base_url('/lookup/fieldlookusergroupsatker/'); ?>" + refindex+'/'+id_user_group;

        $.get(url).done(function (data) {
            jdata = JSON.parse(data);
            $('#'+divname).empty();
            $('#'+divname).append( new Option("--Pilih--",-1,"selected") );
            $.each(jdata, function (i, el) {
                $('#'+divname).append( new Option(el.val,el.id) );
            });                    

            //if (selvalue != '') $('#'+divname).val(selvalue)
        })
        .fail(function () {
            alert("error");
        })
        .always(function () {
            // alert("finished");
        });   
    }

//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
 function initCombobox(divname, refindex) {
        url = "<?php echo base_url('/lookup/fieldlook/'); ?>" + refindex;

        $.get(url).done(function (data) {
            jdata = JSON.parse(data);
            $('#'+divname).empty();
            $('#'+divname).append( new Option("--Pilih--",-1) );
            $.each(jdata, function (i, el) {
                $('#'+divname).append( new Option(el.val,el.id) );
            });                    

            //if (selvalue != '') $('#'+divname).val(selvalue)
        })
        .fail(function () {
            alert("error");
        })
        .always(function () {
            // alert("finished");
        });   
    }
//reff_index= table_name
 function refreshCombobox(divname, refindex,refresh_field,refresh_value) {
    url = "<?php echo base_url('/lookup/refreshlook'); ?>/" + refindex+"/"+refresh_field+"/"+refresh_value;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
        $('#'+divname).empty();
        $('#'+divname).append( new Option("--Pilih--",-1) );
        $.each(jdata, function (i, el) {
            $('#'+divname).append( new Option(el.val,el.id) );
        });                    

        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
    .fail(function () {
        alert("error");
    })
    .always(function () {
        // alert("finished");
    });   
}


//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
var html_select_peran=[
    "<div class='form-group' id='form-peran'>",
    "<label for='exampleInputPassword1'>Peran</label>",
    "<select onchange='getChildren(this)' class='form-control' id='id_user_group'>",
        "<option>--Pilih--</option>",
    "</select>",
 "</div>",
].join("\n");
 function initTreeCombobox(divname, refindex) {
        url = "<?php echo base_url('/lookup/fieldlooktree'); ?>/" + refindex;
        //pindah dari mmode edit ke mode tambah
       
        //alert(divname);

        $.get(url).done(function (data) {

            jdata = JSON.parse(data);
            // $("#form-group-container").empty("");
            // $("#form-group-container").append(html_select_peran);
            $('#'+divname).empty();
            $('#'+divname).prepend("<option disabled selected value=-1 >--Pilih--</option>");
            //$('#'+divname).text("---Pilih---");
            $.each(jdata, function (i, el) {
                //alert(el.val);
                $('#'+divname).append( new Option(el.val,el.id) );
            });                    

            //if (selvalue != '') $('#'+divname).val(selvalue)
        })
        .fail(function () {
            alert("error");
        })
        .always(function () {
            // alert("finished");
        });   
    }



//validation
var BaseFormValidation = function() {
  
    // Init Material Forms Validation, for more examples you can check out https://github.com/jzaefferer/jquery-validation
    var initValidationMaterial = function(){
        jQuery('.js-validation-material').validate({
            ignore: [],
            errorClass: 'help-block text-right animated fadeInDown',
            errorElement: 'div',
            errorPlacement: function(error, e) {
                jQuery(e).parents('.form-group > div').append(error);
            },
            highlight: function(e) {
                var elem = jQuery(e);

                elem.closest('.form-group').removeClass('has-error').addClass('has-error');
                elem.closest('.help-block').remove();
            },
            success: function(e) {
                var elem = jQuery(e);

                elem.closest('.form-group').removeClass('has-error');
                elem.closest('.help-block').remove();
            },
            rules: {
                'val-username2': {
                    required: true,
                    minlength: 3
                },
                'val-email2': {
                    required: true,
                    email: true
                },
                'val-password2': {
                    required: true,
                    minlength: 5
                },
                'val-confirm-password2': {
                    required: true,
                    equalTo: '#val-password2'
                },
                'val-select22': {
                    required: true
                },
                'val-select2-multiple2': {
                    required: true,
                    minlength: 2
                },
                'val-suggestions2': {
                    required: true,
                    minlength: 5
                },
                'val-skill2': {
                    required: true
                },
                'val-currency2': {
                    required: true,
                    currency: ['$', true]
                },
                'val-website2': {
                    required: true,
                    url: true
                },
                'val-phoneus2': {
                    required: true,
                    phoneUS: true
                },
                'val-digits2': {
                    required: true,
                    digits: true
                },
                'val-number2': {
                    required: true,
                    number: true
                },
                'val-range2': {
                    required: true,
                    range: [1, 5]
                },
                'val-terms2': {
                    required: true
                }
                ,
                'username': {
                    required: true,
                }
                ,
                'email': {
                    required: true,
                    email: true
                }
                 ,
                'id_user_group': {
                    required: true,
                }
            },
            messages: {
                'val-username2': {
                    required: 'Please enter a username',
                    minlength: 'Your username must consist of at least 3 characters'
                },
                'val-email2': 'Please enter a valid email address',
                'val-password2': {
                    required: 'Please provide a password',
                    minlength: 'Your password must be at least 5 characters long'
                },
                'val-confirm-password2': {
                    required: 'Please provide a password',
                    minlength: 'Your password must be at least 5 characters long',
                    equalTo: 'Please enter the same password as above'
                },
                'val-select22': 'Please select a value!',
                'val-select2-multiple2': 'Please select at least 2 values!',
                'val-suggestions2': 'What can we do to become better?',
                'val-skill2': 'Please select a skill!',
                'val-currency2': 'Please enter a price!',
                'val-website2': 'Please enter your website!',
                'val-phoneus2': 'Please enter a US phone!',
                'val-digits2': 'Please enter only digits!',
                'val-number2': 'Please enter a number!',
                'val-range2': 'Please enter a number between 1 and 5!',
                'val-terms2': 'You must agree to the service terms!'
                },'id_kabkot2': {
                    required: 'Please enter a username',
                    minlength: 'Your username must consist of at least 3 characters'
                },
                'username': {
                    required: 'Please enter a Username'
                },
                'id_user_group': {
                    required: 'Please Select a ID Usergroup'
                },
                'email': 'Please enter a valid email address',
        });
    };

    return {
        init: function () {
            // Init Bootstrap Forms Validation

            // Init Material Forms Validation
            initValidationMaterial();

            // Init Validation on Select2 change
            jQuery('.js-select2').on('change', function(){
                jQuery(this).valid();
            });
        }
    };
}();

// Initialize when page loads
jQuery(function(){ BaseFormValidation.init(); });

</script>