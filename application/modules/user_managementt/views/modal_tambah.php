<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!-- Slide Right Modal -->
<style>
    .invisible{}
</style>
<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Tambah/Edit User</h4>
                            <button type="button" class="btn-close"
                                data-bs-dismiss="modal"
                                aria-label="Close">
                                <span
                                    aria-hidden="true"></span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form role="form" id="frm-tambah"> 
                            <input type="hidden" id="modeform"/>
                            <input type="hidden" id="nama" name="nama"/>
                                <div class="form-group row" id="form-peran">
                                    <label for="exampleInputPassword1"><PERSON>is login</label>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" onchange="jnsLog(1)" type="radio" name="jnsLogin" id="jnsLogin1" checked>
                                            <label class="form-check-label" for="jnsLogin1">
                                                Internal
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">

                                        <div class="form-check">
                                            <input class="form-check-input" onchange="jnsLog(2)" type="radio" name="jnsLogin" id="jnsLogin2" >
                                            <label class="form-check-label" for="jnsLogin2">
                                                SSO
                                            </label>
                                        </div>
                                    </div>
                                <div class="form-group" id="form-peran">
                                    <label for="exampleInputPassword1">Peran</label>
                                    <select  class="form-control" onchange="peranChange($(this).val())" id="id_user_groups" name="id_user_groups"> 
                                    </select>
                                </div>
                                <div class="form-group sso" id="form-kanwil">
                                    <label for="exampleInputPassword1">Kantor Wilayah</label>
                                    <select  class="form-control" onchange="provChange($(this).val())" id="kd_prov" name="kd_prov"> 
                                    </select>
                                </div>
                                <div class="form-group sso" id="form-kantah">
                                    <label for="exampleInputPassword1">Kantor Pertanahan</label>
                                    <select  class="form-control" onchange="kabkotChange($(this).val())" id="kd_kabkot" name="kd_kabkot"> 
                                    </select>
                                </div>
                                
                                <div class="form-group" style="display:none;">
                                    <label for="exampleInputPassword1">ID User</label>
                                    <input type="text" class="form-control" id="id_user" placeholder="">
                                </div>
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Nama user</label>
                                    <input type="text" class="inp form-control" id="username" placeholder="" name="username">
                                </div>
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Nama Login/NIP</label>
                                    <input type="text" class="inp form-control" id="userlogin" placeholder="" name="userlogin">
                                </div>
                                <div class="form-group int">
                                    <label for="exampleInputPassword1">Email</label>
                                    <input type="text" class="inp form-control" id="email" name="email" placeholder="">
                                </div>
                                <div class="form-group ">
                                    <label for="password">Password</label>
                                    <input type="password" class="inp form-control" id="password" placeholder="" name="password">
                                </div>
                                <div class="form-group">
                                    <label for="repassword">Konfirmasi password</label>
                                    <input type="password" class="inp form-control" id="repassword" placeholder="" name="repassword">
                                </div>
                            </form>
                        </div>
                        <!-- /.box-body -->
                <div class="modal-footer">
                <button type="button"
                    class="btn btn-default waves-effect "
                    data-bs-dismiss="modal">Close</button>
                <button type="button" onclick="simpanForm()" 
                    class="btn btn-primary waves-effect waves-light ">Simpan</button>
            </div>
                </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->
