<!-- <script src="https://cdn.datatables.net/1.11.6/js/jquery.dataTables.min.js"></script> -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/0.5.0-beta4/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.4/jspdf.min.js"></script>

<script>

    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var xhrdata = null;
    var table = null;
    var dataTableData
    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";

    function clear_input() {
        $("#frm-tambah :input").val("");      
    }

    function listing() {
        
        table = $('#dt-server-processing').DataTable({
            "draw": 0,
            scrollY: '300px', // Set the desired height here
            scrollX: true,    // Enable horizontal scrolling if needed
            scrollCollapse: true,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[0, "asc"]],
            "processing": false,
            "serverSide": false,
            "ajax": { type: "POST", url: "<?php echo base_url();?>monitoring_pertimbangan_pkp3t/ssp_paket_interop","data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>",
                d.kd_prov = $('#ikd_prov').val(),
                d.kd_kabkot = $('#ikd_kabkot').val(),
                d.tahun_data = $('#itahun_data').val()
                
            }},
            
            // "columnDefs": [
            //     {
            //         "aTargets": [0],
            //         "mRender": function (data, type, full,meta) {
            //             // console.log(full)
            //             return meta.row + meta.settings._iDisplayStart + 1;
            //         }
            //     },
            //     {
            //         "aTargets": [1],
            //         "mRender": function (data, type, full) {
            //             return 'a. '+full[3]+'<br><hr> b. '+full[4]
                       
            //         }
            //     },
            //     {
            //         "aTargets": [2],
            //         "mRender": function (data, type, full) {
            //             return '(1) '+full[9]+'<br><hr> (2) '+full[10]+'<br><hr> (3) '+full[11];
                       
            //         }
            //     },
            //     {
            //         "aTargets": [3],
            //         "mRender": function (data, type, full) {
            //             return 'a) '+full[7]+'<br><hr> b) '+full[6]+'<br><hr> c) '+full[5];
                        
            //         }
            //     },
            //     {
            //         "aTargets": [4],
            //         "mRender": function (data, type, full) {
            //             return 'a. '+full[15]+'<br><hr> b. - <br>'
                        
            //         }
            //     },
            //     {
            //         "aTargets": [5],
            //         "mRender": function (data, type, full) {
            //             return '1. <br><hr> 2.<br><hr> 3.';
            //         }
            //     },
            //     {
            //         "aTargets": [6],
            //         "mRender": function (data, type, full) {
            //             if (full[18] == 'Proses') {
            //                 return '<span class="badge badge-success">'+full[18]+'</span>'
            //             } else if (full[18] == 'Selesai'){
            //                 return '<span class="badge badge-danger">'+full[18]+'</span>'
                   
            //             } else if (full[18] == 'Ditutup'){
            //                 return '<span class="badge badge-danger">'+full[18]+'</span>'
            //             }else{
            //                 return '<span class="badge badge-danger">'+full[18]+'</span>'
            //             };
            //         }
            //     },
                
            // ],
            columns: [
                { data: null ,render: function(data, type, row,index) {
                        return index.row+1
                }},
                { data: null ,render: function(data, type, row,index) {
                    var str = 'a. '+row.pemohon+'<br><hr> b. -'
                        return str.replace('null','-')
                }},
                { data: null ,render: function(data, type, row) {
                    var str = '1) '+row.nomorrisalah+'<br><hr> 2) '+row.tanggalrisalah+'<br><hr> 3) '+row.luasdimohon
                        return str.replace('null','-')
                }},
                { data: null ,render: function(data, type, row) {
                    var str = 'a) '+row.namakabupaten+'<br><hr> b) '+row.namakecamatan+'<br><hr> c) '+row.namadesa
                        return str.replace('null','-')
                }},
                { data: null ,render: function(data, type, row) {
                    var str = '- <br><hr> -'
                        return str.replace('null','-')
                }},
                { data: null ,render: function(data, type, row) {
                    var str = '-'
                        return str.replace('null','-')
                }},
                // Add more columns as needed
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                // "paginate": {
                //     "first": "<i class='fast backward ui icon'></i>",
                //     "last": "<i class='fast forward ui icon'></i>",
                //     "next": "<i class='step forward ui icon'></i>",
                //     "previous": "<i class='step backward ui icon'></i>"
                // },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }

    function dtDeleteRow(id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

        // var url = "<?php echo base_url(); ?>" + "pemegang_saham/ajax_delete/" + id + "/" + yearnow;
        var url = "<?php echo base_url(); ?>" + "monitoring_pertimbangan_pkp3t/ajax_delete/" + id;
        var params = {"formData":{}, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        $.post(url, params)
                .done(function (data) {

                var table = $('#dt-server-processing').DataTable();
                table.ajax.reload();
                })
                .fail(function () {
                alert("error");
                })
        }
    }


 

    function dtEditRow(id) {
      
      function start(callback) { 
          $("#modal-edit").modal("show");
          
          data_selected = xhrdata.data.filter(x => x[0] == id)[0];
          
          $("#xkd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', this.value);
                console.log(this.value)
            });
  
          $('#xid').val(id);
      }
      

      function a(){
          if(role == 1){ 
              initCombobox('xkd_prov', 19);
              initCombobox('xkd_status', 22);
            //   initCombobox('xkd_kabkot', 20);
            // console.log(data_selected[8]);
              refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', data_selected[8]);
            //   refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', data_selected[8]);

          }else{
              // refreshCombobox4('xid_ruas', 0, 'bujt', data_selected[20]);

          }

      };

      function b(){


          $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
            // console.log(data_selected[1])
              $('#xkd_prov').val(data_selected[6]).selectpicker('refresh');      
              $('#xkd_status').val(data_selected[5]).selectpicker('refresh');      
          });

          
          $(".decformat").keyup(function (event) {
              if (event.which >= 37 && event.which <= 40)
                  return;
              // format number
              $(this).val(function (index, value) {
              return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
              });
          });

         

          $('#xtahun_data').val(data_selected[2]);
          $('#xluas').val(data_selected[4]);
      }

       $.when($.ajax(start())).then(a()).then(b());
    }

    function refreshSelectpicker() {
        // removeSelectpicker('kd_kabkot')
        $('select').selectpicker('refresh')
    }
   
    function dtTambahRow() {

        const myTimeout = setTimeout(refreshSelectpicker, 100);
        $('#modal-tambah').modal('show');
        // $('#frm-tambah').trigger('reset');
            clear_input()
           initCombobox('kd_prov', 19);
           initCombobox('kd_kabkot', 20);
           initCombobox('kd_status', 22);


            $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
            });

        $(".decformat").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;
            // format number
            $(this).val(function (index, value) {
            return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            });
        });

        
       

    }

    function simpanForm() {

        if($( "#frm-tambah" ).valid() === true){
            var xobj_usulan = {
                "kd_kabkot" : $( "#kd_kabkot" ).val(),
                "kd_prov" : $( "#kd_prov" ).val(), 
                "kd_status" : $( "#kd_status" ).val(), 
                "tahun_data" : $( "#tahun_data" ).val(), 
                "luas" : $( "#luas" ).val(), 
                "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"
            };

            
            var url = "<?php echo base_url(); ?>" + "monitoring_pertimbangan_pkp3t/save_form";
            var params = {"formData": xobj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                
            
            var table = $('#dt-server-processing').DataTable();
            table.ajax.reload();
            $('#modal-tambah').modal('hide');

            })
            .fail(function () {
            alert("error");
            });
        } else {
            alert("Data gagal disimpan, harap periksa informasi di setiap field isian");
            $(".error").css("color","red");
        }
    }

    function updateForm(){
        var id = $("#xid").val();
        var data_selected = xhrdata.data.filter(x => x[0] == id)[0];

        if($( "#frm-edit" ).valid() === true){

            var objmasterdetail = {
                    "id": id,
                    "kd_kabkot" : $( "#xkd_kabkot" ).val(),
                    "kd_prov" : $( "#xkd_prov" ).val(), 
                    "kd_status" : $( "#xkd_status" ).val(), 
                    "tahun_data" : $( "#xtahun_data" ).val(), 
                    "luas" : $( "#xluas" ).val(), 
                    "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"


            };
        //  console.log(objmasterdetail);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("monitoring_pertimbangan_pkp3t/update_form") ?>";
            $.post(url, params).done(function (data) {
                if (data == '1'){
                    alert('Data Sudah Ada');
                } else {
                    var table = $('#dt-server-processing').DataTable();
                    table.ajax.reload();
                    $('#modal-edit').modal('hide');
                }
            })
            .fail(function () {
            alert("error");
            });
        }else{
            alert("Data gagal diupdate, silahkan lihat informasi pada setiap field isian untuk memeriksa kesalahan entri data anda");
        $(".error").css("color","red");
        }
    }

    function convertTo(datastring) {
        var str = datastring;
        var s1 = str.replace('{','[');
            
        return eval(s1.replace('}',']'));
    }
    
    function bind_combo_induk(thang, selval){
        var data = get_induk(thang);
        var objthang = JSON.parse(data);
        $("#kdinduk").empty();
        $('#kdinduk').append("<option value='' >" + "--Pilih--" + "</option>");
        for(var i=0; i<= objthang.length-1; i++){
            $("#kdinduk").append("<option value="+objthang[i].kdsatker+">"+objthang[i].nmsatker+"</option>");
        }
        if (selval){
            $("#kdinduk").val(selval);
        }
    }
    
    function get_induk(thang){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('monitoring_pertimbangan_pkp3t/get_induk/') ?>" + thang,
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }




    
    function dtdetail(id){
        //console.log(xhrdata);

        var data_selected = xhrdata.data.filter(x => x[9] === id)[0];  //46

        var waydata = {
            thang: $("#fthang").val(),
            ibukota: data_selected[2],
            kd_prov:data_selected[0],
            kd_prov_bps: data_selected[3],
            kd_prov_irmsv3: data_selected[4],
            kd_prov_krisna:data_selected[5],
            kd_prov_rams: data_selected[6],
            kd_prov_rkakl: data_selected[7],
            kd_prov_sipro: data_selected[8],
            nama_prov: data_selected[1]

        }

        way.set('formData', waydata);
        $('#modalTitle').text('Detail');
//        $('#modeform').val('edit');
        $("input").prop('disabled', true);
	$("#hid").hide("slow");
	$("#hida").hide("slow");
        $('#modal-tambah').modal('show');
    }




   

    function close_alert() {
        $("#alert_information").css({display: "none"});
    }
    
    function bind_combo_thang(selval){
        var data = get_thang();
        var objthang = JSON.parse(data);
        for(var i=0; i<= objthang.length-1; i++){
            $("#fthang").append("<option value="+objthang[i].thang+">"+objthang[i].uraian+"</option>");
        }
        if (selval){
            $("#fthang").val(selval);
        }
    }
    
    function get_thang(){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('monitoring_pertimbangan_pkp3t/get_thang') ?>",
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    function dtUpload(id) {
        // alert(id)
        $("#id_up").val(id);
        var tab = $('#table_id2').DataTable();
        tab.destroy()
        $('#table_id2 td').empty();
        listing_attachment2(id);
        $("#modal-download").modal("show");
    }



    var table_attachment = null;
    function listing_attachment2(id) {

        // var data_selected = jalanjson.data.filter(x => x[0] == id)[0];
     //   var xiduser = role;
        // var xthang = data_selected[1];
        // if ($.fn.dataTable.isDataTable('#table_id2')) {

        //     table_attachment = $('#table_id2').DataTable();
        // } else {

            table_attachment = $('#table_id2').DataTable({

                "draw": 0,
                "columnDefs": [{"orderable": true, "targets": [0]}],
                "order": [[0, "desc"]], 
                "processing": true,
                "serverSide": true,
                "ajax": {
                    type: "POST",
                    url: "<?php echo base_url(); ?>monitoring_pertimbangan_pkp3t/ssp_attachment",
                    data: function (d) {
                        d.id = id;
                       // d.role = role;
                        d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    }
                },
                "columnDefs": [
                    { 
                        "aTargets": [0],
                        "mRender": function (data, type, full) {
                            var ico_class = get_extentsion_file(full[0]);
                            var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp";
                            var subs_img=full[0].substr(0,6);
                            if(ico_class == "feather icon-image"){
                                html_icon="<a target='_blank' class='fancybox' rel='group' href='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'><img class='img-responsive' style='width:40%;' src='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'></a>";
                            }
                            return html_icon+"<br>"+full[3];
                            // alert("ASd")
                        } 
                    },
                    {
                        "aTargets": [1],
                        "mRender": function (data, type, full) {
                            console.log("full attachment");
                            console.log(full);
                            var data_full_attachment = full[0];
                            var dire = data_full_attachment.substr(0, 6);
                            var html_button = [
                                "<a target='_blank' class='btn btn-primary' href='<?php echo base_url(); ?>uploads/" + dire + "/" + data_full_attachment + "'><i class='feather icon-download'></i></a>",
                                "<a target='_blank' class='btn btn-danger' onclick=hapus_lampiran('" + full[1] + "')> <i class='feather icon-trash-2'></i></a>",
                            ].join("\n");
                            return html_button;
                        }
                    }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
            });
            table_attachment.on('xhr', function () {
                xhrdata1 = table_attachment.ajax.json();
                console.log(xhrdata1);
            });

            $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        }

    // }


        function get_extentsion_file(file) {
    var extension = file.substr((file.lastIndexOf('.') + 1));
    switch (extension) {
    case 'jpg':
            case 'png':
            case 'PNG':
            case 'jpeg':
            case 'gif':
            case 'JPG':
            return 'feather icon-image'; // There's was a typo in the example where
    break; // the alert ended with pdf instead of gif.
    case 'zip':
            case 'rar':
            //alert('was zip rar');
            return 'feather icon-archive';
            break;
    case 'pdf':
        return 'feather icon-file-text';
    case 'xlsx':
        return 'feather icon-file-text';
    break;
    default:
    return 'feather icon-file-text';

    }
    }

    function hapus_lampiran(id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

            var url = "<?php echo base_url(); ?>" + "monitoring_pertimbangan_pkp3t/hps_lampiran/" + id;
            var params = {"formData": {}, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params)
                    .done(function (data) {
                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload()
                        var tab = $('#table_id2').DataTable();
                        tab.ajax.reload();
                        ;
                    })
                    .fail(function () {
                        alert("error");
                    })
        }
    }
    

    $(document).ready(function () {
        // bind_combo_thang(thangs);
        // $.get("<?php echo base_url(); ?>monitoring_pertimbangan_pkp3t/getYear/", function(data) {
        //     data = JSON.parse(data)
        //     $('#itahun_data').html(data);
        //     $('#itahun_data').selectpicker('refresh')
        //     console.log(data)
        // });
        $.get("<?php echo base_url(); ?>monitoring_pertimbangan_pkp3t/getProvInterop/", function(data) {
            data = JSON.parse(data)
            $('#ikd_prov').html(data);
            $('#ikd_prov').selectpicker('refresh')
            console.log('getYear')
        });
        // initCombobox('ikd_prov', 19);
        setTimeout(function() {
            $('#ikd_prov').selectpicker();
            $('#itahun_data').selectpicker();
        }, 100);
            // listing();
            $('.numberonly').keypress(function (e) {    
                var charCode = (e.which) ? e.which : event.keyCode    

                if (String.fromCharCode(charCode).match(/[^0-9]/g))    

                    return false;                        

            }); 
            $('.float-number').keypress(function(event) {
                if ((event.which != 46 || $(this).val().indexOf('.') != -1) && (event.which < 48 || event.which > 57)) {
                    event.preventDefault();
                }
            });

            // $('#id_bujt, #id_ruas, #id_jnsdana, #id_refbank, #id_pt').select2({
            //     dropdownParent: $('#modal-tambah'),
            //     // width: 'resolve',
            //     theme: 'classic'
            // });

            // $('#xid_bujt, #xid_ruas, #xid_jnsdana, #xid_refbank, #xid_pt').select2({
            //     dropdownParent: $('#modal-edit'),
            //     theme: 'classic',
            // });

            // $('#xid_refbank, #xid_pt').select2({
            //     tags: true
            // });
            $('#submit').submit(function (e) {
            e.preventDefault();
            var file = new FormData;
            $.ajax({
                url: '<?php echo base_url(); ?>monitoring_pertimbangan_pkp3t/up',
                type: "post",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                    $("#judul").val('')
                    $("#filess").val('')
                    $("#kate").val("#");
                 var tab = $('#table_id2').DataTable();
                tab.ajax.reload();
                tlist_paket.ajax.reload();
                }
            });
        });

            $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker(); 
            });


            $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker();
            });
            // initCombobox('ikd_prov', 19);
            setTimeout(loadData,500);
            // $("#ikd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
            //     refreshSelectboot2('ikd_kabkot', 20, 'kd_prov', this.value);
            // });

            $('#dt-server-processing').on('click', 'tr', function () {
                // var table = $('#dt-server-processing').DataTable();
                // var row = table.row( this ).data();
                // console.log(data);
                var tr = $(this).closest('tr');
                var row = table.row(tr);
        
                if (row.child.isShown()) {
                    // This row is already open - close it
                    row.child.hide();
                    tr.removeClass('shown');
                } else {
                    // console.log(row.data()[0])
                    var kdProv = row.data()[0];
                    var tahun = row.data()[3];
                    var layer = null;
                    // Open this row
                    $.get("<?php echo base_url(); ?>monitoring_pertimbangan_pkp3t/getKab/" + kdProv+'/'+tahun+'/'+layer, function(data) {
                        data = JSON.parse(data)
                        // series = data;
                    //    console.log(data['html']) 
                    
                        row.child(data['html']).show();
                        tr.addClass('shown');
                    });
                }
            })
       
            // table.on('init.dt', function() {
            //     // Get the data from the DataTable and store it in a JavaScript variable
            //     dataTableData = table.rows().data().toArray();
                
            //     // Now, 'dataTableData' contains the data from the DataTable
            //     // console.log(dataTableData);
            // });

        });

        function filterChange(val) {
            loadData()
            $('#dt-server-processing').dataTable().fnClearTable();
            $('#dt-server-processing').dataTable().fnDestroy();
            if(val != 'nochange'){

                $.get("<?php echo base_url(); ?>monitoring_pertimbangan_pkp3t/getKabkotInterop/"+val, function(data) {
                    data = JSON.parse(data)
                    $('#ikd_kabkot').html(data);
                    $('#ikd_kabkot').selectpicker('refresh')
                    // listing()
                });
            }else{
                listing()
            }
                
        }
        function loadData(params) {
            $('#ikd_prov').selectpicker();
            $('#ikd_kabkot').selectpicker();
            
        }

        function expline() {
            
            // Create a new jsPDF instance
            var doc = new jsPDF({
                orientation: 'landscape', // Set the orientation to 'landscape'
                unit: 'mm', // Set the unit to millimeters (you can change it to other units if needed)
                format: [297, 210] // Set custom landscape dimensions (width, height)
            });
            // Add content to the PDF
            doc.rect(20, 20, 10, 30); // (x, y, width, height)
            doc.rect(20, 20, 20, 30); // (x, y, width, height)
            doc.text('No', 20, 25); // Add text inside the bordered area
            doc.text('No', 30, 25); // Add text inside the bordered area
            // Generate the PDF as a data URI
            var pdfDataUri = doc.output('datauri');

            // Create a Blob from the data URI
            var pdfBlob = dataURItoBlob(pdfDataUri);

            // Create a URL for the Blob
            var pdfUrl = URL.createObjectURL(pdfBlob);

            // Open the PDF in a new tab
            window.open(pdfUrl, '_blank');
        }
        function exp() {
            var kd_prov = '00';
            var kd_kabkot = '00';
            var tahun_data = '00';
            if ($('#ikd_kabkot').val() != '') {
                kd_kabkot = $('#ikd_kabkot').val()
            }
            if ($('#itahun_data').val() != '') {
                tahun_data = $('#itahun_data').val()
            }
            var urlToOpen = "<?php echo base_url(); ?>monitoring_pertimbangan_pkp3t/export_pdf/"+kd_kabkot+'/'+tahun_data;

            // Open the URL in a new tab
            window.open(urlToOpen, '_blank');
        }
        function expassass() {
            // Define the HTML element to convert
            // console.log(dataTableData)
            var str = '<tr>'+
                            '<th rowspan="4" width="10%">No</th>'+
                            '<th>ProvinsiSubjek PTP PKKPR Nonberusaha</th>'+
                            '<th>PTP</th>'+
                            '<th>Letak</th>'+
                            '<th>Kegiatan</th>'+
                            '<th>PKKPR Nonberusaha</th>'+
                            '<th rowspan="4">Keterangan</th>'+
                        '</tr>'+
                        '<tr>'+
                            '<th>a. Pemohon</th>'+
                            '<th>(1) No PTP</th>'+
                            '<th>a) Kab/Kota</th>'+
                            '<th rowspan="3">Rencana kegiatan</th>'+
                            '<th>1. No KKPR</th>'+
                        '</tr>'+
                        '<tr>'+
                            '<th rowspan="2">b. Bertindak Atas Nama</th>'+
                            '<th>(2) Tanggal PTP</th>'+
                            '<th>b) Kecamatan</th>'+
                            '<th>2. Tanggal KKPR</th>'+
                        '</tr>'+
                        '<tr>'+
                            '<th>(3) Luas (m2)</th>'+
                            '<th>c) Kelurahan/Desa</th>'+
                            '<th>3. Luas (m2)</th>'+
                        '</tr>'
            var no =0
            $.each(dataTableData, function(index, value) {
                if (index < 10) {
                    no++
                    str += '<tr>'+
                            '<td rowspan="3">'+no+'</td>'+
                            '<td>a. '+value[7]+'</td>'+
                            '<td>(1) '+value[15]+'</td>'+
                            '<td>a) '+value[22]+'</td>'+
                            '<td rowspan="3">'+value[10]+'</td>'+
                            '<td>1. -</td>'+
                        '</tr>'+
                        '<tr>'+
                            '<td rowspan="2">b. - </td>'+
                            '<td>(2) '+value[16]+'</td>'+
                            '<td>b) '+value[21]+'</td>'+
                            '<td>2. -</td>'+
                        '</tr>'+
                        '<tr>'+
                            '<td>(3) '+value[18]+'</td>'+
                            '<td>c) '+value[20]+'</td>'+
                            '<td>3. 0</td>'+
                        '</tr>'
                }
            });
            $('#tabExport').html(str)
            exportPdf()
            // convertTableToPDF()
        }

        function exportPdf() {
            var element = document.getElementById('exports');

            // Create a new jsPDF instance
            // var doc = new jsPDF();
            var doc = new jsPDF({
                orientation: 'landscape', // 'portrait' (default) or 'landscape'
                unit: 'mm', // 'mm' (default), 'pt', 'cm', or 'in'
                format: 'a4' // 'a4', 'letter', or [width, height]
            });
            // Use html2canvas to capture the HTML element as an image
            html2canvas(element).then(function(canvas) {
                // Convert the canvas image to a data URL
                var imgData = canvas.toDataURL('image/png');

                // Add the image to the PDF
                doc.addImage(imgData, 'PNG', 30, 10, 230, 0);

                // Save or display the PDF
                doc.save('html_to_pdf.pdf');
            });
        }

        function convertTableToPDFs() {
            // Create a new jsPDF instance
            var doc = new jsPDF({
                orientation: 'landscape', // 'portrait' (default) or 'landscape'
                unit: 'mm', // 'mm' (default), 'pt', 'cm', or 'in'
                format: 'a4' // 'a4', 'letter', or [width, height]
            });

            // Define the HTML table element
            var table = document.getElementById('exports');

            // Function to add a new page and reset the Y position
            function addNewPage() {
                doc.addPage();
                yPosition = 10; // Reset Y position to the top of the page
            }

            // Initialize Y position for content placement
            var yPosition = 10;

            // Function to capture table as an image and add it to the PDF
            function captureTableAsImage() {
                html2canvas(table, {
                    onrendered: function(canvas) {
                        var imgData = canvas.toDataURL('image/png');
                        doc.addImage(imgData, 'PNG', 10, yPosition, 0, 0);
                        yPosition += canvas.height; // Update Y position
                        if (yPosition > 270) {
                            addNewPage(); // Start a new page if content exceeds the page height
                        }
                        checkAndSavePDF(); // Check if there are more pages
                    }
                });
            }

            // Function to check and save the PDF
            function checkAndSavePDF() {
                if (yPosition <= 270) {
                    doc.save('table_to_multi_page.pdf'); // Save the PDF
                } else {
                    captureTableAsImage(); // Continue capturing content on new pages
                }
            }

            // Start capturing the table content
            captureTableAsImage();
        }

        

</script>

