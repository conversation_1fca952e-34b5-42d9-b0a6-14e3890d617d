<script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/wayjs/way.js"></script>
<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var xhrdata = null;
    var table = null;


    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";

    function clear_input() {
        $("#formData :input").val("");
    }

    function listing() {
        table = $('#dt-server-processing').DataTable({
            "draw": 0,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>reff_bujt/ssp_paket","data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
                // d.thang = $('#fthang').val();
                
            }},
            "columnDefs": [
                {
                    "aTargets": [0],
                    "mRender": function (data, type, full) {
                        return full[0];
                    }
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
                },

                {
                    "aTargets": [2],
                    "mRender": function (data, type, full) {
                        return full[2];
                    }
                },

                {
                    "aTargets": [3],
                    "mRender": function (data, type, full) {
                        return full[3];
                    }
                },
                
                {
                    "aTargets": [4],
                    "mRender": function (data, type, full) {
                        return full[4];
                    }
                },
                
                {
                    "aTargets": [5],
                    "mRender": function (data, type, row) {
                        var id = row[0];
                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "Edit",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "Hapus",
                            "</button>",                            
                            //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                // "paginate": {
                //     "first": "<i class='fast backward ui icon'></i>",
                //     "last": "<i class='fast forward ui icon'></i>",
                //     "next": "<i class='step forward ui icon'></i>",
                //     "previous": "<i class='step backward ui icon'></i>"
                // },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }
    
    function bind_combo_induk(thang, selval){
        var data = get_induk(thang);
        var objthang = JSON.parse(data);
        $("#kdinduk").empty();
        $('#kdinduk').append("<option value='' >" + "--Pilih--" + "</option>");
        for(var i=0; i<= objthang.length-1; i++){
            $("#kdinduk").append("<option value="+objthang[i].kdsatker+">"+objthang[i].nmsatker+"</option>");
        }
        if (selval){
            $("#kdinduk").val(selval);
        }
    }
    
    function get_induk(thang){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('reff_bujt/get_induk/') ?>" + thang,
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }

    function dtDeleteRow(id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

        // var url = "<?php echo base_url(); ?>" + "pemegang_saham/ajax_delete/" + id + "/" + yearnow;
        var url = "<?php echo base_url(); ?>" + "reff_bujt/ajax_delete/" + id;
        var params = {"formData":{}, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        $.post(url, params)
                .done(function (data) {

                var table = $('#dt-server-processing').DataTable();
                table.ajax.reload();
                })
                .fail(function () {
                alert("error");
                })
        }
    }

    function dtEditRow(id) {

        function start(callback) { 
            $("#modal-edit").modal("show");
            // $('.tbhItem').text('Edit Laporan Keuangan');
            // $("#frm-edit").validate().resetForm();
            $('#frm-edit').trigger('reset');

            // id_paket = id;
            data_selected = xhrdata.data.filter(x => x[0] == id)[0];
            ib = data_selected[0];
            nb = data_selected[1];
            al = data_selected[2];
            tlp = data_selected[3];
            fax = data_selected[4];

            // console.log(data_selected[3]);
    
            $('#xid').val(id);
        }
        

        // function a(){
        //     if(role == 1){ 
        //         initCombobox('xid_ruas', 0);
        //         initCombobox('xid_bujt', 2);
        //         //initCombobox('xkategori', 10);
        //     }else{
        //         refreshCombobox4('xid_ruas', 0, 'bujt', data_selected[20]);

        //     }

        // };
 
        function b(){
            $('#xid_kd_bujt').val(ib);
            $('#xnama_bujt').val(nb);
            $('#xalamat').val(al);
            $('#xno_tlp').val(tlp);
            $('#xno_fax').val(fax);
        }

         $.when($.ajax(start())).then(b());
    }


    function dtTambahRow() {
        $('#frm-tambah').trigger('reset');
        // initCombobox('id_refbank', 2);
        // $("#id_bujt").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        //         refreshSelectboot('id_ruas', 0, 'bujt', this.value);
        // });
      
        // $("#id_ruas").change(function () {
        //     var idruas = this.value;
        //     refreshCombobox('id_bujt', 0, 'id_ruas', idruas);
        // });
        // $(".decformat").keyup(function (event) {
        //     if (event.which >= 37 && event.which <= 40)
        //         return;
        //     // format number
        //     $(this).val(function (index, value) {
        //     return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        //     });
        // });

        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
        $('#modal-tambah').modal('show');
        // $('.tbhItem').text('Tambah');
    }

    function simpanForm() {
    
        if($( "#frm-tambah" ).valid() === true){
            var xobj_usulan = {
                "nama_bujt" : $("#nama_bujt").val(),
                "alamat" : $("#alamat").val(),
                "no_tlp" : $("#no_tlp").val(),
                "no_fax" : $("#no_fax").val(),
                "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"
            };

            
            var url = "<?php echo base_url(); ?>" + "reff_bujt/save_form";
            var params = {"formData": xobj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
            
            var table = $('#dt-server-processing').DataTable();
            table.ajax.reload();
            $('#modal-tambah').modal('hide');

            })
            .fail(function () {
            alert("error");
            });
        } else {
            alert("Data gagal disimpan, harap periksa informasi di setiap field isian");
            $(".error").css("color","red");
        }
    }

function updateForm(){
    var id = $("#xid").val();
    var data_selected = xhrdata.data.filter(x => x[0] == id)[0];

    if($( "#frm-edit" ).valid() === true){

        var objmasterdetail = {
                "id": id,
                "kd_bujt" : $("#xid_kd_bujt").val(),
                "nama_bujt" : $("#xnama_bujt").val(),
                "alamat" : $("#xalamat").val(),
                "no_tlp" : $("#xno_tlp").val(),
                "no_fax" : $("#xno_fax").val(),
                "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"


        };
    //  console.log(objmasterdetail);
        var params = {"formData": objmasterdetail, 
            "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        var url = "<?php echo base_url("reff_bujt/update_form") ?>";
        $.post(url, params).done(function (data) {
            if (data == '1'){
                alert('Data Sudah Ada');
            } else {
                var table = $('#dt-server-processing').DataTable();
                table.ajax.reload();
                $('#modal-edit').modal('hide');
            }
        })
        .fail(function () {
        alert("error");
        });
    }else{
        alert("Data gagal diupdate, silahkan lihat informasi pada setiap field isian untuk memeriksa kesalahan entri data anda");
    $(".error").css("color","red");
    }
}


    function close_alert() {
        $("#alert_information").css({display: "none"});
    }
    
    function bind_combo_thang(selval){
        var data = get_thang();
        var objthang = JSON.parse(data);
        for(var i=0; i<= objthang.length-1; i++){
            $("#fthang").append("<option value="+objthang[i].thang+">"+objthang[i].uraian+"</option>");
        }
        if (selval){
            $("#fthang").val(selval);
        }
    }
    
    function get_thang(){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('reff_bujt/get_thang') ?>",
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }


    function dtUpload(id) {
        // alert(id)
        $("#id_up").val(id);
        var tab = $('#table_id2').DataTable();
        tab.destroy()
        $('#table_id2 td').empty();
        listing_attachment2(id);
        $("#modal-download").modal("show");
    }



    var table_attachment = null;
    function listing_attachment2(id) {

        // var data_selected = jalanjson.data.filter(x => x[0] == id)[0];
     //   var xiduser = role;
        // var xthang = data_selected[1];
        // if ($.fn.dataTable.isDataTable('#table_id2')) {

        //     table_attachment = $('#table_id2').DataTable();
        // } else {

            table_attachment = $('#table_id2').DataTable({

                "draw": 0,
                "columnDefs": [{"orderable": true, "targets": [0]}],
                "order": [[0, "desc"]], 
                "processing": true,
                "serverSide": true,
                "ajax": {
                    type: "POST",
                    url: "<?php echo base_url(); ?>reff_bujt/ssp_attachment",
                    data: function (d) {
                        d.id = id;
                       // d.role = role;
                        d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    }
                },
                "columnDefs": [
                    { 
                        "aTargets": [0],
                        "mRender": function (data, type, full) {
                            var ico_class = get_extentsion_file(full[0]);
                            var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp";
                            var subs_img=full[0].substr(0,6);
                            if(ico_class == "feather icon-image"){
                                html_icon="<a target='_blank' class='fancybox' rel='group' href='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'><img class='img-responsive' style='width:40%;' src='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'></a>";
                            }
                            return html_icon+"<br>"+full[3];
                            // alert("ASd")
                        } 
                    },
                    {
                        "aTargets": [1],
                        "mRender": function (data, type, full) {
                            console.log("full attachment");
                            console.log(full);
                            var data_full_attachment = full[0];
                            var dire = data_full_attachment.substr(0, 6);
                            var html_button = [
                                "<a target='_blank' class='btn btn-primary' href='<?php echo base_url(); ?>uploads/" + dire + "/" + data_full_attachment + "'><i class='feather icon-download'></i></a>",
                                "<a target='_blank' class='btn btn-danger' onclick=hapus_lampiran('" + full[1] + "')> <i class='feather icon-trash-2'></i></a>",
                            ].join("\n");
                            return html_button;
                        }
                    }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
            });
            table_attachment.on('xhr', function () {
                xhrdata1 = table_attachment.ajax.json();
                console.log(xhrdata1);
            });

            $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        }

    // }


        function get_extentsion_file(file) {
    var extension = file.substr((file.lastIndexOf('.') + 1));
    switch (extension) {
    case 'jpg':
            case 'png':
            case 'PNG':
            case 'jpeg':
            case 'gif':
            case 'JPG':
            return 'feather icon-image'; // There's was a typo in the example where
    break; // the alert ended with pdf instead of gif.
    case 'zip':
            case 'rar':
            //alert('was zip rar');
            return 'feather icon-archive';
            break;
    case 'pdf':
        return 'feather icon-file-text';
    case 'xlsx':
        return 'feather icon-file-text';
    break;
    default:
    return 'feather icon-file-text';

    }
    }

    function hapus_lampiran(id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

            var url = "<?php echo base_url(); ?>" + "reff_bujt/hps_lampiran/" + id;
            var params = {"formData": {}, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params)
                    .done(function (data) {
                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload()
                        var tab = $('#table_id2').DataTable();
                        tab.ajax.reload();
                        ;
                    })
                    .fail(function () {
                        alert("error");
                    })
        }
    }
    $(document).ready(function () {
        // bind_combo_thang(thangs);
        listing();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");

        $(".si-close").click(function(){
            $("input").prop('disabled', false);
        });
        
        $('#fthang').change( function() {
            table.draw();
        });

        $('#submit').submit(function (e) {
            e.preventDefault();
            var file = new FormData;
            $.ajax({
                url: '<?php echo base_url(); ?>reff_bujt/up',
                type: "post",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                    $("#judul").val('')
                    $("#filess").val('')
                    $("#kate").val("#");
                 var tab = $('#table_id2').DataTable();
                tab.ajax.reload();
                tlist_paket.ajax.reload();
                }
            });
        });

        $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) { 
            $(this).find('div.modal-content select').selectpicker(); 
        });

        $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
            $(this).find('div.modal-content select').selectpicker();
        });
        
        
        
    });

</script>