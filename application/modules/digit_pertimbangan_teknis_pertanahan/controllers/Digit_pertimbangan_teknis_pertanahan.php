<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");
use \Shapefile\Shapefile;
use \Shapefile\ShapefileException;
use \Shapefile\ShapefileReader;
use \diversen\gps;

class Digit_pertimbangan_tek<PERSON>_per<PERSON>han extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $satker = $this->session->users['kd_bujt'];
        $title = "Digitasi PTP Izin Lokasi Provinsi";
        $this->db->where('table_name','ptp_kkppr_stran');
        $datas['column'] = $this->db->get('spatial.list_column')->result();
        $menu=explode('/',$this->input->post('uri'));
        $menus = $menu[4] == '' ? $menu[3] : $menu[4];
        $menu = str_replace('digit_' ,'',$menus);

        $this->db->where('url',$menu);
        $layer= $this->db->get('v_tema_peta')->result();

        $datas['column'] = $this->db->get('spatial.list_column')->result();
        $popup = $this->load->view('digit_pertimbangan_teknis_pertanahan/popup', $datas, true);
        $datas['popup'] = json_encode($popup);
        $js_wms = $this->load->view('digit_pertimbangan_teknis_pertanahan/js_wms', $datas, true);
        $datas['js_wms'] = $js_wms;
        
        $js_file = $this->load->view('digit_pertimbangan_teknis_pertanahan/js_file', '', true);
        $modal_tambah = $this->load->view('digit_pertimbangan_teknis_pertanahan/modal_tambah', '', true);
        $modal_edit = $this->load->view('digit_pertimbangan_teknis_pertanahan/modal_edit', '', true);
        $modal_download = $this->load->view('digit_pertimbangan_teknis_pertanahan/modal_download', '', true);
        $modal_digitasi = $this->load->view('digit_pertimbangan_teknis_pertanahan/modal_digitasi', $datas, true);
        $modal_digitasi_edit = $this->load->view('digit_pertimbangan_teknis_pertanahan/modal_digitasi_edit', '', true);
        $modal_history = $this->load->view('digit_pertimbangan_teknis_pertanahan/modal_history', '', true);
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        
        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_digitasi" => $modal_digitasi,
            "modal_digitasi_edit" => $modal_digitasi_edit,
            "modal_download" => $modal_download,
            "modal_history" => $modal_history,
            "title" => $title,
            "js_wms" => $js_wms,
            "jv_script" => $js_file,
            "popup" => $datas['popup'],
            "menu" => str_replace('digit_','',@$menus),
            "layer" => $layer,
        );
        
        $this->load->view('index', $data);
    }

    
    
    public function ssp_paket() {
      


       
        $primaryKey = 'gid'; //test        
        $kd_prov=@$this->input->post("kd_prov",true);
        $kd_kabkot=@$this->input->post("kd_kabkot",true);
        $tahun_data=@$this->input->post("tahun_data",true);
        $layer=@$this->input->post("layer",true);

        $where="";
        if (!empty($kd_prov)) {
            $where .="kdppum = '".$kd_prov."'";
            if (!empty($kd_kabkot)) {
                $where .=" and kdpkab = '".$kd_kabkot."'";
            }     
        } 
        
        if ($layer == 'ptpil_prov') {
            if ($tahun_data != '#') {
                if($where == ""){

                    $where .=" thnkgt = '".$tahun_data."'";
                }else{
                    $where .=" and thnkgt = '".$tahun_data."'";

                }
            } 
            $table = 'spatial.digit_ptpil_prov';

            $columns = array(
                array('db' => 'gid' , 'dt' => 0),
                array('db' => 'wadmkd' , 'dt' => 1),
                array('db' => 'wadmkc' , 'dt' => 2),
                array('db' => 'wadmkk' , 'dt' => 3),
                array('db' => 'wadmpr' , 'dt' => 4),
                array('db' => 'kuasaptp' , 'dt' => 5),
                array('db' => 'subjekptp' , 'dt' => 6),
                array('db' => 'niboss' , 'dt' => 7),
                array('db' => 'noptp' , 'dt' => 8),
                array('db' => 'tglptp' , 'dt' => 9),
                array('db' => 'thnkgt' , 'dt' => 10),
                array('db' => 'luasha' , 'dt' => 11),
                array('db' => 'geom' , 'dt' => 12),
                array('db' => 'kdppum' , 'dt' => 13),
                array('db' => 'kdpkab' , 'dt' => 14),
                array('db' => 'lokasi' , 'dt' => 15),
                array('db' => 'rcnkgt' , 'dt' => 16),
                array('db' => 'kdkbli' , 'dt' => 17),
                array('db' => 'kbli' , 'dt' => 18),
                array('db' => 'jptp' , 'dt' => 19),
                array('db' => 'hslptp' , 'dt' => 20),
                array('db' => 'luasm2' , 'dt' => 21),
            );
        } else if($layer == 'ptpil_prov_tk'){
            if ($tahun_data != '#') {
                $where .=" and thndata = '".$tahun_data."'";
            } 
            $table = 'spatial.digit_ptpil_prov_tk';

            $columns = array(
                array('db' => 'gid' , 'dt' => 0),
                array('db' => 'wadmkd' , 'dt' =>1),
                array('db' => 'wadmkc' , 'dt' =>2),
                array('db' => 'wadmkk' , 'dt' =>3),
                array('db' => 'wadmpr' , 'dt' =>4),
                array('db' => 'kuasaptp' , 'dt' =>5),
                array('db' => 'subjekptp' , 'dt' =>6),
                array('db' => 'niboss' , 'dt' =>7),
                array('db' => 'noptp' , 'dt' =>8),
                array('db' => 'tglptp' , 'dt' =>9),
                array('db' => 'thnkgt' , 'dt' =>10),
                array('db' => 'luasha' , 'dt' =>11),
                array('db' => 'x' , 'dt' =>12),
                array('db' => 'y' , 'dt' =>13),
                array('db' => 'geom' , 'dt' =>14),
                array('db' => 'kdpkab' , 'dt' =>15),
                array('db' => 'kdppum' , 'dt' =>16),
                array('db' => 'lokasi' , 'dt' =>17),
                array('db' => 'rcnkgt' , 'dt' =>18),
                array('db' => 'kdkbli' , 'dt' =>19),
                array('db' => 'kbli' , 'dt' =>20),
                array('db' => 'jptp' , 'dt' =>21),
                array('db' => 'hslptp' , 'dt' =>22),
                array('db' => 'luasm2' , 'dt' =>23),
            );
        }
        
        

        

         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);

        
    }

    public function ssp_paket_hist($id,$layer) {
  
       if ($layer == 'ptpil_prov') {
           $table = 'spatial.v_ptpil_prov_hist';
       } else {
           $table = 'spatial.v_ptpil_prov_tk_hist';
       }
       
        $primaryKey = 'gid'; //test        
        $where="gid =".$id;
        
        $columns = array(
            array('db' => 'gid', 'dt' => 0),
            array('db' => 'proses', 'dt' => 1),
            array('db' => 'oleh' , 'dt'  =>2) , 
            array('db' => 'waktu' , 'dt'  =>3) , 
            array('db' => 'id_hist' , 'dt'  =>4) , 
        );

        

         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);

        
    }


    
    public function ajax_delete($id,$layer) {
        if ($layer == 'ptpil_prov') {
            $table = 'spatial.ptpil_prov';
        } else {
            $table = 'spatial.ptpil_prov_tk';
        }
        $this->db->where('gid', $id);
        $this->db->delete($table);
        
        
        // $this->M_model->delete_gis_by_id('dok_pt_tnh_timbul',$arr);
        // $this->M_model->delete_by_id('dok_neraca','kd_kabkot', $id);
        echo json_encode(array("status" => TRUE));
    }

    public function insertShp()
    {
        
        
        $geom = json_decode($this->input->post('geom', TRUE));
        $created_by = $this->session->userdata('users')['id_user'];
        $data = [
            'wadmkd' => $this->input->post('wadmkd' ,true),
            'wadmkc' => $this->input->post('wadmkc' ,true),
            'wadmkk' => $this->input->post('wadmkk' ,true),
            'wadmpr' => $this->input->post('wadmpr' ,true),
            'kuasaptp' => $this->input->post('kuasaptp' ,true),
            'subjekptp' => $this->input->post('subjekptp' ,true),
            'niboss' => $this->input->post('niboss' ,true),
            'noptp' => $this->input->post('noptp' ,true),
            'tglptp' => $this->input->post('tglptp' ,true),
            'thnkgt' => $this->input->post('thnkgt' ,true),
            'kdppum' => $this->input->post('kd_prov' ,true),
            'kdpkab' => $this->input->post('kd_kabkot' ,true),
            'lokasi' => $this->input->post('lokasi' ,true),
            'rcnkgt' => $this->input->post('rcnkgt' ,true),
            'kdkbli' => $this->input->post('kdkbli' ,true),
            'kbli' => $this->input->post('kbli' ,true),
            'jptp' => $this->input->post('jptp' ,true),
            'hslptp' => $this->input->post('hslptp' ,true),
            'luasm2' => str_replace('.','',$this->input->post('luasm2', true)) == '' ? 0 : str_replace('.','',$this->input->post('luasm2' ,true)),
            'luasha' => str_replace('.','',$this->input->post('luasha' ,true)) == '' ? 0 : str_replace('.','',$this->input->post('luasha' ,true)),
            'created_by' => $created_by
        ];
        
        // echo "<pre>";
        // print_r ($data);
        // echo "</pre>";exit();
        $str='';
        $type='';
        $table='';
        // if ($geom != '' ) {
        //     $type = $geom->geometry->type;
        //     if($type == 'Polygon'){
        //             $str .="(";
        //             foreach ($geom->geometry->coordinates[0] as $k => $v) {
        //                 $str .=' '.$v[0].' '.$v[1].',';
        //             }
        //             $str = substr($str, 0, -1);
        //             $str .="),";
                    
        //         $str = substr($str, 0, -1);
        //         $geom = 'Polygon('.$str.')';
        //         $data['geom'] = $geom;
        //         $data['luas_ha'] = $this->input->post('luas_ha', true);
        //         $table = 'spatial.ptpil_prov';
                
        //     }else if($type == 'Point'){
        
        //         $geom = 'POINT('.$geom->geometry->coordinates[0].' '.$geom->geometry->coordinates[1].')';
        //         $data['geom'] = $geom;

        //         $data['luas'] = $this->input->post('luas', true);
        //         $data['x'] = $this->input->post('x', true);
        //         $data['y'] = $this->input->post('y', true);
        //         $data['elevation'] = $this->input->post('elevation', true);
        //         $table = 'spatial.ptpil_prov_tk';
        //     }
        // }
        if ($geom != '' ) {
            $type = @$geom->geometry->type;
           
            if(!$type == ''){
                if($type == 'Polygon'){
                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->features[0]->geometry->coordinates[0];
                        
                    }
                        $str .="(";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[0].' '.$v[1].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .="),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'Polygon('.$str.')';
                    $data['geom'] = $geom;                    
                    $table = 'spatial.ptpil_prov';

                }else if($type == 'Point'){
                    $geom = 'POINT('.$geom->geometry->coordinates[0].' '.$geom->geometry->coordinates[1].')';
                    $data['geom'] = $geom;
    
                    $data['x'] = str_replace('.','',$this->input->post('x', true));
                    $data['y'] = str_replace('.','',$this->input->post('y', true));
                    $table = 'spatial.ptpil_prov_tk';
                }
            }else{
                $type = @$geom->features[0]->geometry->type;
                if($type == 'Polygon'){
                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->features[0]->geometry->coordinates[0];
                        
                    }
                        $str .="(";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[1].' '.$v[0].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .="),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'Polygon('.$str.')';
                    $data['geom'] = $geom;
                    $table = 'spatial.ptpil_prov';

                }
            }
            
        }
        $gid = $this->input->post('gid', TRUE);

        
        // echo "<pre>";
        // print_r ($data);
        // echo "</pre>";exit();
        
        
        // if($gid=='' ){
            $this->db->insert($table, $data);
        //     echo json_encode(['sts' => 'sukses']);
        // }else{
        //     $this->db->where('gid', $gid);
        //     $this->db->update($table, $data);
        //     echo json_encode(['sts' => 'sukses']);
            
        // }
        $table = str_replace('spatial.','',$table);
        
        // echo "<pre>";
        // print_r ($table);
        // echo "</pre>";exit();
        
        $gid = $this->db->query('SELECT last_value FROM spatial.'.$table.'_gid_seq')->row_array()['last_value'];            
        $this->save_image($gid,$table);

    }

    public function save_image($gid,$table) {
        
   
    
        if (is_array($_FILES)) {
            
            
            foreach ($_FILES as $key => $value) {
                if($_FILES[$key]['name'] != ''){

                    $allowed_type = array('image/jpeg'=>1,'image/png'=>1,);
                    $filetype = mime_content_type($_FILES[$key]['tmp_name']);
                    if (@$allowed_type[$filetype]) {
                        $nama_dir = FCPATH . 'uploads/foto_digit/'.$table.'/'.date('Ym').'/'.$gid;
                        $nama_file = date('Ymdhis').'_'.$_FILES[$key]['name'];
                        if (is_dir($nama_dir)) {
                            
                        } else {
                            $oldmask = umask(0);
                            mkdir($nama_dir, 0777, true);
                            umask($oldmask);
                        }
                            $sourcePath = $_FILES[$key]['tmp_name'];
                            $up = move_uploaded_file($sourcePath, $nama_dir.'/'.$nama_file);
                            $created_by = $this->session->userdata('users')['id_user'];
                            $data = [ 
                                "gid" => $gid, 
                                "filename" => $nama_file, 
                                "filepath" => 'uploads/foto_digit/'.$table.'/'.date('Ym').'/'.$gid, 
                                "created_by" => $created_by,
                                "created_at" => date('Y-m-d H:i:s'),
                                "updated_at" => date('Y-m-d H:i:s'),
                            ];
                            $this->db->insert('dok_'.$table,$data);
                    }else{
                        echo json_encode(['status' => false ,'sts' => 'fail', 'msg' => 'Type FIle Salah atatu File Rusak!']);
                        exit();
                }
            }
            
            }

        }
            echo json_encode(array("status" => TRUE));
    }

    public function updateShp()
    {
        $id = $this->input->post('xgid', TRUE);
        $geom = json_decode($this->input->post('xgeom', TRUE));
        $created_by = $this->session->userdata('users')['id_user'];
        
        $data = [
            'wadmkd' => $this->input->post('xwadmkd' ,true),
            'wadmkc' => $this->input->post('xwadmkc' ,true),
            'wadmkk' => $this->input->post('xwadmkk' ,true),
            'wadmpr' => $this->input->post('xwadmpr' ,true),
            'kuasaptp' => $this->input->post('xkuasaptp' ,true),
            'subjekptp' => $this->input->post('xsubjekptp' ,true),
            'niboss' => $this->input->post('xniboss' ,true),
            'noptp' => $this->input->post('xnoptp' ,true),
            'tglptp' => $this->input->post('xtglptp' ,true),
            'thnkgt' => $this->input->post('xthnkgt' ,true),
            'kdppum' => $this->input->post('xkd_prov' ,true),
            'kdpkab' => $this->input->post('xkd_kabkot' ,true),
            'lokasi' => $this->input->post('xlokasi' ,true),
            'rcnkgt' => $this->input->post('xrcnkgt' ,true),
            'kdkbli' => $this->input->post('xkdkbli' ,true),
            'kbli' => $this->input->post('xkbli' ,true),
            'jptp' => $this->input->post('xjptp' ,true),
            'hslptp' => $this->input->post('xhslptp' ,true),
            'luasm2' => str_replace('.','',$this->input->post('xluasm2', true)) == '' ? 0 : str_replace('.','',$this->input->post('xluasm2' ,true)),
            'updated_by' => $created_by
        ];
        
       
        
        $str='';
 
        if ($geom != '' ) {
            $type = @$geom->geometry->type;
            
           
            if(!$type == ''){
                if($type == 'Polygon' || $type=='MultiPolygon'){
                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->features[0]->geometry->coordinates[0];
                        
                    }
                        $str .="(";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[0].' '.$v[1].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .="),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'Polygon('.$str.')';
                    $data['geom'] = $geom;
                    $data['luasha'] = str_replace('.','',$this->input->post('xluasha', true));
                    $table = 'spatial.ptpil_prov';

                }else if($type == 'Point'){
                    $geom = 'POINT('.$geom->geometry->coordinates[0].' '.$geom->geometry->coordinates[1].')';
                    $data['geom'] = $geom;
    
                    $data['x'] = str_replace('.','',$this->input->post('xx', true));
                    $data['y'] = str_replace('.','',$this->input->post('xy', true));
                    $table = 'spatial.ptpil_prov_tk';
                }
            }else{
                
                $type2 = $geom->type;
                $type = @$geom->features[0]->geometry->type;
                if($type == 'Polygon' || $type=='MultiPolygon'  ){

                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->features[0]->geometry->coordinates[0];
                        
                    }
                        $str .="(";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[0].' '.$v[1].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .="),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'Polygon('.$str.')';
                    $data['geom'] = $geom;
                    $table = 'spatial.ptpil_prov';
                    $dok = 'ptpil_prov';
                   
                }elseif($type2=='MultiPolygon'){
                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->coordinates[0][0];
                        
                    }
                        $str .="((";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[0].' '.$v[1].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .=")),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'MultiPolygon('.$str.')';
                    $data['geom'] = $geom;
                    $table = 'spatial.ptpil_prov';
                    $dok = 'ptpil_prov';
                }else if($type == 'Point'){
                    
                    
                    $geom = 'POINT('.$geom->features[0]->geometry->coordinates[0].' '.$geom->features[0]->geometry->coordinates[1].')';
                    $data['geom'] = $geom;
                    $data['x'] = str_replace('.','',$this->input->post('xx', true));
                    $data['y'] = str_replace('.','',$this->input->post('xy', true));
                    $table = 'spatial.ptpil_prov_tk';
                    $dok = 'ptpil_prov_tk';

                   
                }

            }
            
        }

        
   
        

        
        
        if (!empty($data)) {
            $this->db->where('gid', $id);
            $this->db->update($table, $data);
            $this->db->select('last_value');
            $last_hist = $this->db->get($table.'_id_hist_seq')->row_array()['last_value'];
            
            $this->db->where('id_hist', $last_hist);
            $this->db->update($table.'_hist', ['updated_by' => $created_by]);
            // echo $this->db->last_query();
            $this->save_image($id,$dok);
            $del = $this->input->post('deleted_foto',TRUE);
            if (!empty($del)) {
                foreach ($del as $key => $value) {
                    $this->db->where('id_dok', $value);
                    $this->db->delete('dok_'.$dok);
                }
            }
            // echo json_encode(['sts' => 'sukses']);

            exit();
        }
        echo json_encode(['sts' => 'sukses']);

    }

    public function uploadShp()
    {

        $layer = explode('&&',$this->input->post('layer', TRUE));
    
        $tabel=[
                'PTPIL' => 'ptpil_prov',
                'PTPILTK' => 'ptpil_prov_tk'
        ];
        //mencari kolom dari tabel yang sesuai 
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name', $tabel[$layer[2]]);
        $column = $this->db->get('v_column')->result();
        // echo "<pre>";
        // print_r ($column);
        // echo "</pre>"; exit();
        //mengambil data file upload
        $file = file_get_contents($_FILES['filess']['tmp_name']);
        $exp = explode('.',$_FILES['filess']['name']);
        $name= $exp[0];
        $ext=$exp[1];

        $prov = $this->M_model->getInisialProv($this->input->post('ukd_prov', TRUE));
        // echo $this->db->last_query();
        
        $kdprov = $this->input->post('ukd_prov', TRUE);
        $kdkab = $this->input->post('ukd_kabkot', TRUE);
        $kabkot = $this->M_model->getInisialKabkot($this->input->post('ukd_kabkot', TRUE));
        // print_r($prov);
        $nm_kabkot=str_replace(' ','_',$kabkot['nama_kabkot']);
        $nm_prov=str_replace(' ','_',$prov['nama_prov']);
        $tahun_data = $this->input->post('utahun_data', TRUE);
        
        
        $sourcePath = $_FILES['filess']['tmp_name'];
        $nama_dir = FCPATH . 'upload_shp/';
        $nama_file=$name.'.'.$ext;
        if (!is_dir($nama_dir)) {
            
            mkdir($nama_dir,0777,true);
        }
        move_uploaded_file($sourcePath, $nama_dir.$nama_file);
        chmod($nama_dir.$nama_file,0777);
        // if (file_exists('upload_shp/bangai_kepu.shp')) {
        $zip = new ZipArchive();
        // $source_file = $_FILES['filess']["tmp_name"];
        // open the zip file to extract
        // echo '';
        if ($zip->open($nama_dir.$nama_file) !== true) {
            echo "Could not unzip file";
            exit;
        }

        // place in the temp folder
        if ($zip->extractTo($nama_dir) !== true) {
            $zip->close();
            echo "Could not extract files to $nama_dir folder";
            exit;
        }
        $zip->close();
        $shpname = $nama_dir.$name.'.shp';

        try {
            // Open Shapefile
            // $Shapefile = new ShapefileReader('upload_shp/kepulauan.shp');
            // $Shapefile = new ShapefileReader($file);
            $Shapefile = new ShapefileReader($nama_dir.$name.'.shp');
            // Read all the records
            $data=[];
            while ($Geometry = $Shapefile->fetchRecord()) {
              
                
                // echo "<pre>";
                // print_r ($Geometry->getWKT());
                // echo "</pre>";exit();
                

                $isiDatas=[];
                $cekwil=[];
                foreach ($Geometry->getDataArray() as $key => $value) {
                    $k=strtolower($key);
                    $value = str_replace('*' ,0,$value);
                    $isiDatas[$k]=$value;
                    // echo $k.'/'.strtolower($value)."/".$tahun_data."<pre>";
                    if($k=='kdppum' && $value != $kdprov){
                        echo json_encode(['sts' => "gagal", 'msg' => 'Provinsi Tidak Sama Dengan Data yang Dipilih']);
                        exit();
                    }
                    if($k=='kdpkab' && $value != $kdkab){
                        echo json_encode(['sts' => "gagal", 'msg' => 'Kabkupaten/kota Tidak Sama Dengan Data yang Dipilih']);
                        exit();
                     
                    }
                    if($k=='tahun_data' && $value != $tahun_data){
                        echo json_encode(['sts' => "gagal", 'msg' => 'Tahun Tidak Sama Dengan Data yang Dipilih']);
                        exit();
                    }
                }

                foreach ($isiDatas as $k => $v ) {
                        
                    foreach ($column as $k2 => $v2) {
                        
                        //  pencocokan kolom geojson dengan kolom tabel
                        if ($v2->column_name == $k && $k != 'gid') {
                            $isiData[$k]=$v;
                        }
                    }
                }

                $isiData['geom'] = $Geometry->getWKT();
                array_push($data,$isiData);

            }
            
            // echo "<pre>";
            // print_r ($data);
            // echo "</pre>";exit();
            
            foreach ($data as $key => $value) {
                unset($value['gid']);
                unset($value['geom']);
                $this->db->delete("spatial.".$tabel[$layer[2]], $value);
            }
            
            $this->db->insert_batch('spatial.'.$tabel[$layer[2]], $data);
            // echo $this->db->last_query();
            

            // $data = [
            //     'judul_dok'=>$layer[0],
            //     'nm_dok'=>$layer[0],
            //     'path'=>$nama_dir.$nama_file,
            //     'kdpkab'=>$kdkab,
            //     'id_layer'=>$layer[3],
            //     'tahun_data'=>$tahun_data
            // ];

            // $this->db->insert('dok_ptpil',$data);
            foreach (glob($nama_dir.'/'.$name.'*') as $filename) {
                unlink($filename);
            }
            echo json_encode(['sts' => "success"]);
            

        } catch (ShapefileException $e) {
            // Print detailed error information
            // echo "Error Type: " . $e->getErrorType()
            //     . "\nMessage: " . $e->getMessage()
            //     . "\nDetails: " . $e->getDetails();
            // switch ($e->getErrorType()) {
            //     case Shapefile::ERR_GEOM_RING_AREA_TOO_SMALL:
            //     case Shapefile::ERR_GEOM_RING_NOT_ENOUGH_VERTICES:
            //     case Shapefile::ERR_GEOM_POLYGON_OPEN_RING:
                    require_once(FCPATH."env.php");
                    $shpname = str_replace(array(' ','(',')'),array('\ ','\(','\)'),$shpname);
                    $str = 'ogr2ogr -update -append -f "PostgreSQL" PG:"host='.WGI_DB_LRS_HOST.' port='.WGI_DB_LRS_PORT.' user='.WGI_DB_LRS_USER.' dbname='.WGI_DB_LRS_DB.' password='.WGI_DB_LRS_PWD.'" -nln spatial.'.$tabel[$layer[2]].' '.$shpname.' -progress -nlt MULTIPOLYGON';
                    $arr = [
                            'kdppum' => $kdprov,
                            'kdpkab' => $kdkab,
                            'tahun_data' =>$tahun_data
                        ];
                    $this->db->delete("spatial.".$tabel[$layer[2]], $arr);
                    
                    exec($str,$a,$b);
                    foreach (glob($nama_dir.'/'.$name.'*') as $filename) {
                        unlink($filename);
                    }
                    if (@$a[0] == '0...10...20...30...40...50...60...70...80...90...100 - done.') {
                        echo json_encode(['sts' => "success"]);
    
                    }else{
                        echo json_encode(['sts' => "gagal", 'msg' => 'Gagal Simpan Data']);
                    }
                    
                    // echo "<pre>";
                    // print_r ($a);
                    // echo "</pre>";
                    
                    // echo "<pre>";
                    // print_r ($b);
                    // echo "</pre>";
                    
                    // echo $str;
                    
                    // echo json_encode(['sts' => "gagal", 'msg' => 'Beberapa Type Data Geometry Invalid, Mohon Perbaiki Terlebih Daluhu.']);
                        // exit();
            // }
        }
    }

    function inp() {
        $filename = '/var/www/html/atrpgt/uploads/simbologi.csv';
        // echo $filename;exit();
        if (($handle = fopen($filename, 'r')) !== false) {
            $data = [];
            $num = 1;
            // $file = fopen("contacts.csv","r");
       
            $c=0;
            $child = '';
            while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                $c++;
                if ($c > 4) {
                    
                    if($row[0] == $num){
                        $num++;
                        $child = $row[2];
                        $datas=[
                                    'layer' => $row[2],
                                    'jenis_geom' => $row[3],
                                    'jenis_simbologi' => $row[4],
                                    'rgb_fill' => $row[5],
                                    'rgb_outline' => $row[6],
                                    'hex_fill' => $row[7],
                                    'hex_outline' => $row[8],
                                    'ket' => $row[9]
                        ];
                        $data[$child][0] = $datas;
                        // array_push($data)                    
                    }else{
                        $datas=[
                            'layer' => $data[$child][0]['layer'],
                            'jenis_geom' => $data[$child][0]['jenis_geom'],
                            'jenis_simbologi' => $row[4],
                            'rgb_fill' => $row[5],
                            'rgb_outline' => $row[6],
                            'hex_fill' => $row[7],
                            'hex_outline' => $row[8],
                            'ket' => $row[9]
                ];
                        array_push($data[$child],$datas);
                        
                        // echo "<pre>";
                        // print_r ($row);
                        // echo "</pre>";exit();
                        
                    }
                   
                }
                
                
                
                
                
            }
            foreach ($data as $key => $value) {
                $this->db->insert_batch('simbologi', $value);
                
            }

            // Now $data contains the CSV data
            // You can process it as needed
        } else {
            echo "Error opening $filename";
        }
    }

    
    function getFoto($id,$table) {
        $this->db->where('gid', $id);        
        $datas = $this->db->get($table)->result();
        $html = $this->load->view('digit_pertimbangan_teknis_pertanahan/foto_edit', ['data' => $datas], true);
        
        echo json_encode($html);
    }

}