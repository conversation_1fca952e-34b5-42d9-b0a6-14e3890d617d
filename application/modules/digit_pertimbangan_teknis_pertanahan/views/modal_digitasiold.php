

<div class="modal " id="modDigitasi" tabindex="-1">
  <div class="modal-dialog modal-fullscreen">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Digitasi</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal" id="frm-tambah">
            <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
            <input type="hidden" name="wadmpr" id="wadmpr">
            <input type="hidden" name="wadmkk" id="wadmkk">
            <div class="row">
                <div class="col-sm-12 col-md-12 col-xs-12">
                    <div class="card" id="divMap" style="">
                    <!-- <button type="button" onclick="calculateArea()">get</button> -->
                        <div id="map2" style="height: 80vh;width: 100%"></div>
                    </div>
                </div>
             </div>
            <div class="row">
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label"   for="kd_prov">Provinsi</label>
                        <div class="col-md-12">
                        <select id="kd_prov" name="kd_prov" required onchange="prov_change()" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="kd_kabkot">Kab/Kota</label>
                        <div class="col-md-12">
                            <select id="kd_kabkot" required name="kd_kabkot" class="bootstrap-select form-control" data-live-search="true">
                            </select>
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="tahun_data">tahun_data</label>  
                        <div class="col-md-12">
                            <select id="tahun_data" required name="tahun_data" class="bootstrap-select form-control" data-live-search="true">
                            <?php 
                                $now = date('Y');
                                for ($i=$now; $i >= 1990 ; $i--) { 
                                    echo '<option value="'.$i.'">'.$i.'</option>';
                                }
                            ?>
                            </select>
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="pemohon">Pemohon</label>  
                        <div class="col-md-12">
                        <input id="pemohon" name="pemohon" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="nama_perus">Nama Perus</label>  
                        <div class="col-md-12">
                        <input id="nama_perus" name="nama_perus" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="nib">NIB</label>  
                        <div class="col-md-12">
                        <input id="nib" name="nib" type="text" placeholder="" class="form-control input-md numeric">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="koordinat">Koordinat</label>  
                        <div class="col-md-12">
                        <input id="koordinat" name="koordinat" type="text" placeholder="" class="form-control input-md numeric">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="alamat_prs">Alamat Perusahaan</label>  
                        <div class="col-md-12">
                        <input id="alamat_prs" name="alamat_prs" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="peruntukan">Peruntukan</label>  
                        <div class="col-md-12">
                        <input id="peruntukan" name="peruntukan" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="alamat_ptk">Alamat PTK</label>  
                        <div class="col-md-12">
                        <input id="alamat_ptk" name="alamat_ptk" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="qname50">qname50</label>  
                        <div class="col-md-12">
                        <input id="qname50" name="qname50" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="qname100">qname100</label>  
                        <div class="col-md-12">
                        <input id="qname100" name="qname100" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="no_berkas">No Berkas</label>  
                        <div class="col-md-12">
                        <input id="no_berkas" name="no_berkas" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="tg_risalah">Tanggal Risalah</label>  
                        <div class="col-md-12">
                        <input id="tg_risalah" name="tg_risalah" type="date" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="nomor_ptp">Nomor PTP</label>  
                        <div class="col-md-12">
                        <input id="nomor_ptp" name="nomor_ptp" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="tgl_ptp">Tanggal PTP</label>  
                        <div class="col-md-12">
                        <input id="tgl_ptp" name="tgl_ptp" type="date" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="luas_ptp">Luas PTP</label>  
                        <div class="col-md-12">
                        <input id="luas_ptp" name="luas_ptp" type="text" placeholder="" class="numeric form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="no_ilok">No Ilok</label>  
                        <div class="col-md-12">
                        <input id="no_ilok" name="no_ilok" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="tgl_ilok">Tanggal Ilok</label>  
                        <div class="col-md-12">
                        <input id="tgl_ilok" name="tgl_ilok" type="date" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="luas_ilok">Luas Ilok</label>  
                        <div class="col-md-12">
                        <input id="luas_ilok" name="luas_ilok" type="text" placeholder="" class="numeric form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="posisi">Posisi</label>  
                        <div class="col-md-12">
                        <input id="posisi" name="posisi" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="ket">Ket</label>  
                        <div class="col-md-12">
                        <input id="ket" name="ket" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="info">Info</label>  
                        <div class="col-md-12">
                        <input id="info" name="info" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="klas">klas</label>  
                        <div class="col-md-12">
                        <input id="klas" name="klas" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="luas_ha">Luas ha</label>  
                        <div class="col-md-12">
                        <input id="luas_ha" name="luas_ha" type="text" placeholder="" class="numeric form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="lokasi">Lokasi</label>  
                        <div class="col-md-12">
                        <input id="lokasi" name="lokasi" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="rencana_ke">Rencana Ke</label>  
                        <div class="col-md-12">
                        <input id="rencana_ke" name="rencana_ke" type="text" placeholder="" class="form-control input-md numeric">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="kode_kbli">Kode KBLI</label>  
                        <div class="col-md-12">
                        <input id="kode_kbli" name="kode_kbli" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="kbli">KBLI</label>  
                        <div class="col-md-12">
                        <input id="kbli" name="kbli" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="jns_ptp">Jenis PTP</label>  
                        <div class="col-md-12">
                        <input id="jns_ptp" name="jns_ptp" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="luas_m2">luas m2</label>  
                        <div class="col-md-12">
                        <input id="luas_m2" name="luas_m2" type="text" placeholder="" class="numeric form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="geom"></label>  
                        <div class="col-md-12">
                        <input id="geom" name="geom" type="hidden" placeholder="" class="form-control input-md float-number">
                        
                        </div>
                    </div>
            </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="btn-close" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" onclick="$('#frm-tambah').submit()" class="btn btn-primary">Save changes</button>
            </form>
            </div>
    </div>
  </div>
</div>


<script>
    $(document).ready(function () {
     
        $('#frm-tambah').submit(function (e) {
            getPolygon()
            var selectedOption = $("#kd_prov option:selected");
            $('#wadmpr').val(selectedOption.text());
             selectedOption = $("#kd_kabkot option:selected");
            $('#wadmkk').val(selectedOption.text());
            // return false
            e.preventDefault();
            var file = new FormData(this);
            $.ajax({
                    // url: '<?php echo base_url(); ?>digit_pertimbangan_stranas/up',
                    url: '<?php echo base_url(); ?>digit_pertimbangan_teknis_pertanahan/insertShp',
                    type: "post",
                    data: file,
                    processData: false,
                    contentType: false,
                    cache: false,
                    async: false,
                    success: function (data) {
                        // $("select").val('--pilih--');
                        // $("select").selectpicker("refresh");
                        data=JSON.parse(data)
                        console.log(data.sts)
                        if(data.sts=='gagal'){
                            swal.close()
                            Swal.fire(
                                'Gagal!',
                                data.msg,
                                'error'
                            )
                        }else{

                            $("#filess").val('')
                            var table = $('#dt-server-processing').DataTable();
                            table.ajax.reload();
                            swal.close()
                            Swal.fire(
                                'Sukses!',
                                'Data Tersimpan!',
                                'success'
                            )
                            $('#btn-close').click()
                            clearAllLayer
                        }
                        
                    },error: function (jqXHR, exception) {
                        // console.log(jqXHR);
                        swal.close()
                        Swal.fire(
                            'Gagal!',
                            'Data Gagal Tersimpan!',
                            'error'
                        )
                    }
                });
        });
    });
    var map
    var drawnPolygons
    var drawnItems 
    $('#modDigitasi').on('shown.bs.modal', function (e) {
        $('#geom').val('')
         var mapContainer = document.getElementById('map2');

        if (mapContainer && mapContainer.classList.contains('leaflet-container')) {
            drawnItems.clearLayers();
        }else{
            map = L.map('map2').setView([-2.7521401146517785, 116.07226320582281], 5);
            var gl = L.mapboxGL({
                style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
            }).addTo(map);
          
            
            map.pm.addControls({  
                position: 'topleft',  
                drawCircleMarker: false,
                rotateMode: false,
                drawCircle:false,
                drawText:false,
                cutPolygon:false,
                drawPolygon:true,
                drawMarker:false,
                drawPolyline:false,
                drawRectangle:true,
                dragMode:false,

            }); 

        
            drawnPolygons = [];
            drawnItems = new L.FeatureGroup().addTo(map);

            map.on('pm:create', function (e) {
                var layer = e.layer;
                var latlngs = layer.getLatLngs();
                drawnItems.addLayer(layer);
                // Generate a unique ID for the new polygon
                var newPolygonId = Date.now();

                // Save the new polygon along with its ID to the array
                drawnPolygons.push({ id: newPolygonId, latlngs: latlngs });

                // Get the GeoJSON of all drawn polygons (including the edited ones)
                // console.log(drawnPolygons);
                var geojson = getMultiPolygonGeoJSON(drawnPolygons);
                getPolygon()

            });

           
        }

    
    })  

  

    function getPolygons(params) {
        var layers = L.PM.Utils.findLayers(map);
        var polygonsArray = [];
        var polygons = layers
            // Loop through the drawn polygons and convert each one to GeoJSON Polygon
        for (var i = 0; i < polygons.length; i++) {
            var latlngs = layers[i].getLatLngs()[0];
            // var lu=L.GeometryUtil.geodesicArea(layers[i].getLatLngs());
            var coordinates = [];
            // console.log(layers[i].getLatLngs()[0])
            for (var j = 0; j < latlngs.length; j++) {
                coordinates.push(latlngs[j].lng+' '+latlngs[j].lat);
            }

            // Push the first point again to close the polygon
            coordinates.push(coordinates[0]);

            polygonsArray.push([coordinates]);
        }
        // Create a GeoJSON MultiPolygon from the individual polygons
        
        multiPolygon = 'MULTIPOLYGON '+JSON.stringify(polygonsArray).replace(/\[/g, '(').replace(/\]/g, ')').replace(/\"/g, '')
        $('#geom').val(multiPolygon)
    }

    function getMultiPolygonGeoJSON(polygons) {
        return false
    // Create an array to store the individual polygons
    var polygonsArray = [];

    // Loop through the drawn polygons and convert each one to GeoJSON Polygon
    for (var i = 0; i < polygons.length; i++) {
        var latlngs = polygons[i].latlngs[0];
        var coordinates = [];

        // Convert the latlngs to GeoJSON coordinates
        for (var j = 0; j < latlngs.length; j++) {
            coordinates.push(latlngs[j].lng+' '+latlngs[j].lat);
        }

        // Push the first point again to close the polygon
        coordinates.push(coordinates[0]);

        polygonsArray.push([coordinates]);
    }

    // Create a GeoJSON MultiPolygon from the individual polygons
    var geojson = {
        type: 'MultiPolygon',
        coordinates: polygonsArray,
    };

    return polygonsArray;
    }

    function getPolygon() {
        var layers = L.PM.Utils.findLayers(map);
        var group = L.featureGroup();
        layers.forEach((layer)=>{
            group.addLayer(layer);
            var latt = layer.getLatLngs()[0]
            // var areas = L.GeometryUtil.geodesicArea(latt)
        });
        shapes = group.toGeoJSON();
        $('#geom').val(JSON.stringify(shapes))
        // console.log(shapes)
    }

    function shoelaceArea(coords) {
    var area = 0;
    var numPoints = coords.length;

    for (var i = 0; i < numPoints; i++) {
        var j = (i + 1) % numPoints;
        area += (coords[j].lat + coords[i].lat) * (coords[j].lng - coords[i].lng);
    }
    return Math.abs(area / 2);
}
    function calculatePolygonArea(latLngs) {
    var area = 0;
    var len = latLngs.length;
    for (var i = 0; i < len; i++) {
        var j = (i + 1) % len;
        var lat1 = latLngs[i].lat;
        var lng1 = latLngs[i].lng;
        var lat2 = latLngs[j].lat;
        var lng2 = latLngs[j].lng;

        area += (lng2 - lng1) * (lat2 + lat1);
    }
    console.log(area)
    return Math.abs(area) / 2;
}
    function clearAllLayer() {
        map.eachLayer(function(layer){
            if (layer._path != null) {
                layer.remove()
            }
        });
    }
</script>