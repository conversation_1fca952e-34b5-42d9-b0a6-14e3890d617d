<script>

	$(document).ready(function() {
	var ajaxurl="<?php echo base_url()."/usulan/list_usulan"; ?>";
    var table =$('#table_id').DataTable( {
        "ajax": ajaxurl,
        "columns": [
            { "data": "nama_paket" },
            { "data": "output" },
            { "data": "total_pagu" },
			{
            "targets": -1,
            "data": null,
            "defaultContent": "<button class='btn btn-primary btn-xs'>Show</button><button class='btn btn-primary btn-xs'>Edit</button><button class='btn btn-primary btn-xs'>Delete</button>"
			}
        ]		
    });
	 $('#table_id tbody').on( 'click', 'button', function () {
        var data = table.row( $(this).parents('tr') ).data();
        alert( data[1] +"'s salary is: "+ data[1] );
    } );
	});
	
	function tambah(){
		
		$("#modal-tambah").modal("show");
		
		 var navListItems = $('div.setup-panel div a'),
            allWells = $('.setup-content'),
            allNextBtn = $('.nextBtn');

    allWells.hide();

    navListItems.click(function (e) {
        e.preventDefault();
        var $target = $($(this).attr('href')),
                $item = $(this);

        if (!$item.hasClass('disabled')) {
            navListItems.removeClass('btn-primary').addClass('btn-default');
            $item.addClass('btn-primary');
            allWells.hide();
            $target.show();
            $target.find('input:eq(0)').focus();
        }
    });

    allNextBtn.click(function(){
        var curStep = $(this).closest(".setup-content"),
            curStepBtn = curStep.attr("id"),
            nextStepWizard = $('div.setup-panel div a[href="#' + curStepBtn + '"]').parent().next().children("a"),
            curInputs = curStep.find("input[type='text'],input[type='url']"),
            isValid = true;

        $(".form-group").removeClass("has-error");
        for(var i=0; i<curInputs.length; i++){
            if (!curInputs[i].validity.valid){
                isValid = false;
                $(curInputs[i]).closest(".form-group").addClass("has-error");
            }
        }

        if (isValid)
            nextStepWizard.removeAttr('disabled').trigger('click');
    });

    $('div.setup-panel div a.btn-primary').trigger('click');

	}
	
</script>