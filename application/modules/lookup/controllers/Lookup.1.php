<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Lookup extends MY_Controller {

//    private $fldconfig = array(
//        0 => ['r_bas', 'kd_jns_belanja', 'keterangan'],
//        1 => ['r_dapil', 'kd_dapil', 'nama_dapil'],
//        2 => ['r_fraksi', 'kd_fraksi', 'nama_fraksi'],
//        3 => ['r_isu_strategis', 'kd_isu', 'uraian'],
//        4 => ['r_jns_pekerjaan', 'id_pekerjaan', 'uraian'],
//        5 => ['r_kegiatan', 'kd_kegiatan', 'nama_kegiatan'],
//        6 => ['r_komisi', 'kd_komisi', 'nama_komisi'],
//        7 => ['r_komisi', 'kd_komisi', 'nama_komisi'],
//        8 => ['r_komponen', 'kd_komponen', 'nama_komponen'],
//        9 => ['r_kppn', 'kd_kppn', 'nama_kanwil'],
//        10 => ['r_nosp', 'no_sp', 'kode_satker'],
//        11 => ['r_ppk', 'kode_ppk', 'nama_ppk'],
//        12 => ['r_program', 'kd_program', 'nama_program'],
//        13 => ['r_renstra', 'id_renstra', 'nilai'],
//        14 => ['r_sasaran_pembangunan_nasional', 'kd_sasaran_pembangunan', 'uraian'],
//        15 => ['r_sasaran_strategis', 'kd_sasaran_strategis', 'uraian'],
//        16 => ['r_satker', 'kode_satker', 'nama_satker'],
//        17 => ['r_sub_komponen', 'kd_sub_komponen', 'nama_sub_komponen'],
//        18 => ['r_sub_output', 'kd_sub_output', 'nama_sub_output'],
//        19 => ['r_sumberdana', 'id_sumberdana', 'uraian'],
//        20 => ['r_target', 'id_target', 'program'],
//        21 => ['r_thang', 'thang', 'uraian'],
//        22 => ['r_unit', 'kd_unit', 'nama_unit'],
//        23 => ['ref_jenis_kontrak', 'id_jenis_kontrak', 'jenis_kontrak'],
//        24 => ['ref_kab_kota', 'id_kabkot', 'kab_kota'],
//        25 => ['provinsi', 'kd_prov', 'nama_prov'],
//        26 => ['ref_satuan', 'id_satuan', 'nama_satuan'],
//        27 => ['user_group', 'id_user_group', 'nama'],
//        28 => ['r_thang', 'thang', 'uraian'], //data baru harus ditambahkan di paling bawah
//        29 => ['r_tipe_usulan', 'id', 'keterangan'],
//        30 => ['r_output', 'kd_output', 'nama_output'],
//        31 => ['v_ruas', 'no_ruas', 'linkname'],
//        32 => ['v_jembatan', 'no_jembatan', 'nama_jembatan'],
//        33 => ['module', 'kode_module', 'nama_module']
//    );

    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();
        $this->load->library('template');
    }

    //nama_tabel,primary_key,field_nilai
//    public function index($id) {
//        
//    }

    function fieldlook($id) {
       $fldconfig = $this->config->item("reff_table");
        
        /*         * *pringatan** */
        /*         * data baru harus ditambahkan di paling bawah* */

        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0];
        $query = $this->db->query($sql);

        //echo $this->db->last_query();

        $data = $query->result_array();
        echo json_encode($data);
    }

    function fieldlookarr($id) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0];
        $query = $this->db->query($sql);

        $data = $query->result_array();
        echo "laskdjflaksjdf";
        $retdata = array();
        foreach($data as $item) {
            $retdata[$item['id']] = $item['val'];
            //$retdata[] = $item;
            //$retdata[$item->id] = $item->val;
        }
        
//        print_r($retdata);
        
        return $retdata;
    }
    
    function fieldlooktree($id) {

        $fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where id_sub_user_group=0";
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    //id = table_name, refresh id= value who want to refresh //refresh_field  nama field yang akan di refresh
    //for example kode_provinsi ->merefresh data kota yang sesuai dengan kode provinsi yang bersangkutan
    function refreshlook($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlook2($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );


        $refval = explode('::', $refresh_value)[1];

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . " = '" . $refval . "'";
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }
    
     function refreshlook3($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );


        $refval = explode('::', $refresh_value)[0];

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . " = ". $refval;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

}
