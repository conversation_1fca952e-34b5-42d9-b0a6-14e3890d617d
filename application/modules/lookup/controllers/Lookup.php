<?php

defined('BASEPATH') OR exit('No direct script access allowed');
//savdsaghgdaggdafsgh tomy

include_once APPPATH . "/vendor/autoload.php";
Shapefile\ShapefileAutoloader::register();
// Import classes
use \Shapefile\Shapefile;
use \Shapefile\ShapefileException;
use \Shapefile\ShapefileReader;
use Shapefile\ShapefileWriter;
use Shapefile\Geometry\Point;
use Shapefile\Geometry\Polygon;
use Shapefile\Geometry\Linestring;

use ShapeFile\Geometry\GeometryCollection;
use ShapeFile\ShapeType;
use ShapeFile\ShapeTypes;


class Lookup extends MY_Controller {

    private $fldconfig = array(
              0 => ['v_ruas_bujt', 'id_ruas', 'ruas'],
              1 => ['ref_investor', 'id_investor', 'nama_perusahaan'],
              2 => ['aset_r_bujt', 'kd_bujt', 'nama_bujt'],
              3 => ['jns_asuransi', 'id_jnsasuransi', 'nm_jnsasuransi'],
              4 => ['jns_objek', 'id_jnsalba', 'nm_jnsobjek'],
              5 => ['r_ppjt_status', 'id_statppjt', 'status_ppjt'],
              6 => ['grup_sdm', 'id_grupsdm', 'nm_sdm'],
              7 => ['jns_struktur', 'id_jstruktur', 'nm_jstruktur'],
              8 => ['jns_lap_konstruksi', 'id_jnslapkons', 'nm_jnslapkons'],
              9 => ['jabatan', 'id_posisi', 'nm_jabatan'],
              10 => ['jns_lapkeu', 'id_jnslapkeu', 'nm_jnslapkeu'],
              11 => ['v_bank', 'id_refbank', 'nm_bank'],
              12 => ['ref_seksi', 'id_seksi', 'nm_seksi'],
              13 => ['ref_golkd', 'id_gol', 'keterangan'],
              14 => ['v_ruas_bujt', 'bujt', 'nama_bujt'],
              15 => ['r_tahun', 'tahun', 'tahun'],
              16 => ['ref_statpenuh', 'id_status', 'nm_status'],
              17 => ['jns_dana', 'id_jnsdana', 'nm_jnsdana'],
              18 => ['ref_pt', 'id_pt', 'nama_perusahaan'],
              19 => ['aset_r_provinsi', 'kd_prov', 'nama_prov'],
              20 => ['aset_r_kabkota', 'kd_kabkot', 'nama_kabkot'],
              21 => ['aset_r_kecamatan', 'kd_camat', 'nama_camat'],
              22 => ['tb_status', 'kd_status', 'nm_status'],
              23 => ['aset_user_group', 'id_user_group', 'nama'],
              24 => ['aset_users', 'id_user', 'nama'],
              25 => ['aset_r_kelurahan', 'kd_lurah', 'nama_lurah'],
              26 => ['spatial.ptp_tnh_timbul', 'gid', 'pemohon'],
              27 => ['r_legenda', 'id', 'text'],
              28 => ['kategori_foto', 'id', 'kategori'],
              29 => ['kategori_vidio', 'id', 'kategori'],
              30 => ['aset_r_kelurahan', 'kd_lurah', 'nama_lurah'],
              31 => ['r_format', 'kd_format', 'keterangan'],
              32 => ['r_subjek', 'id_subjek', 'nama_subjek'],
              33 => ['r_temanalisis', 'id_tema', 'nama_tema'],
            //   22 => ['v_', 'kd_status', 'nm_status'],
        //    4 => ['aset_r_merk', 'nm_merk', 'keterangan'],
        //    5 => ['aset_r_kondisi', 'kondisi', 'keterangan'],
        //    6 => ['aset_r_provinsi', 'kd_prov', 'nama_prov'],
        //    7 => ['aset_r_kabkota', 'kd_kabkot', 'nama_kabkot'],
        //    8 => ['aset_r_kecamatan', 'kd_camat', 'nama_camat'],
        //    10 => ['aset_r_statusjln', 'id_status', 'status'],
        //    11 => ['aset_r_jnsbangunan', 'kd_jnsbangun', 'nm_jnsbangun'],
        //    12 => ['aset_r_jns_jembatan', 'kd_jnsjembatan', 'nm_jnsjembatan'],
        //    13 => ['aset_r_jembatan', 'id_jembatan', 'nm_jembatan'],
        //    15 => ['aset_r_bujt', 'kd_bujt', 'nama_bujt'],
        //    16 => ['aset_r_jnsbmn', 'kd_jnsbmn', 'nm_jnsbmn'],
        //    17 => ['aset_r_ruas', 'id_ruas', 'nm_ruas'],
        //    18 => ['aset_r_perolehan', 'id_aset_perolehan', 'nama_perolehan'],
        //    19 => ['aset_r_bentuk_aset', 'id_bentuk_aset', 'nama_aset'],
//         0 => ['r_bas', 'kd_jns_belanja', 'keterangan'],
//         1 => ['r_dapil', 'kd_dapil', 'nama_dapil'],
//         2 => ['r_fraksi', 'kd_fraksi', 'nama_fraksi'],
//         3 => ['r_isu_strategis', 'kd_isu', 'uraian'],
//         4 => ['r_jns_pekerjaan', 'id_pekerjaan', 'uraian'],
// //    5 => ['r_kegiatan', 'kd_kegiatan', 'nama_kegiatan'],
//         5 => ['v_kegiatan', 'kdgiat', 'nmgiat'],
//         6 => ['r_komisi', 'kd_komisi', 'nama_komisi'],
//         7 => ['r_komisi', 'kd_komisi', 'nama_komisi'],
// //    8 => ['r_komponen', 'kd_komponen', 'nama_komponen'],
//         //    8 => ['v_komponen', 'kdkmpnen', 'nmkmpnen', 'kdgiat', 'kdoutput', 'kdsoutput'],
//         9 => ['r_kppn', 'kd_kppn', 'nama_kanwil'],
//         10 => ['r_nosp', 'no_sp', 'kode_satker'],
//         11 => ['r_ppk', 'kode_ppk', 'nama_ppk'],
// //            12=>['r_program', 'kd_program', 'nama_program'],
//         12 => ['v_program', 'kdprogram', 'nmprogram'],
//         13 => ['r_renstra', 'id_renstra', 'nilai'],
//         14 => ['r_sasaran_pembangunan_nasional', 'kd_sasaran_pembangunan', 'uraian'],
//         15 => ['r_sasaran_strategis', 'kd_sasaran_strategis', 'uraian'],
//         16 => ['r_satker', 'kode_satker', 'nama_satker'],
//         17 => ['r_sub_komponen', 'kd_sub_komponen', 'nama_sub_komponen'],
// //    18 => ['r_sub_output', 'kd_sub_output', 'nama_sub_output'],
//         18 => ['v_komponen', 'kdkmpnen', 'nmkmpnen', 'kdgiat', 'kdoutput', 'kdsoutput'],
//         19 => ['r_sumberdana', 'id_sumberdana', 'uraian'],
//         20 => ['r_target', 'id_target', 'program'],
//         21 => ['r_thang', 'thang', 'uraian'],
//         //22=>['r_unit','kd_unit', 'nama_unit'],
//         22 => ['v_unit', 'kdunit', 'nmunit'],
//         23 => ['ref_jenis_kontrak', 'id_jenis_kontrak', 'jenis_kontrak'],
//         24 => ['ref_kab_kota', 'id_kabkot', 'kab_kota'],
//         25 => ['provinsi', 'kd_prov_irmsv3', 'nama_prov'],
//         26 => ['ref_satuan', 'id', 'nama_satuan'],
//         27 => ['user_group', 'id_user_group', 'nama'],
//         28 => ['aset_r_periode', 'tahun', 'keterangan'], //data baru harus ditambahkan di paling bawah
//         29 => ['r_tipe_usulan', 'id', 'keterangan'],
// //    30 => ['r_output', 'kd_output', 'nama_output'],
//         30 => ['v_output', 'kdoutput', 'nmoutput'],
//         31 => ['module', 'kode_module', 'nama_module'],
//         //32 => ['v_jembatan', 'id_jembatan', 'nama_jembatan'],
//         32 => ['v_ruas_jembatan', 'id_jembatan', 'nama_jembatan'],
//         33 => ['v_link_satker', 'no_ruas', 'linkname'],
//         34 => ['v_module', 'kode_module', 'nama_module'],
//         35 => ['v_segmen_ruas', 'no_segment', 'no_segment'],
//         36 => ['v_gbkpk', 'kdgbkpk', 'nmgbkpk'],
//         37 => ['v_akun', 'kdakun', 'nmakun'],
//         38 => ['v_sdana', 'nmsdana', 'nmsdana'],
//         39 => ['v_output', 'sat'],
//         40 => ['v_jembatan', 'lon'],
//         41 => ['v_jembatan', 'lat'],
//         42 => ['r_treatment', 'id', 'kd_treatment'],
//         43 => ['v_usergroup_satker', 'kode_satker', 'nama_satker'],
//         44 => ['v_sub_output', 'kdsoutput', 'nmsoutput', 'kdgiat', 'kdoutput'],
//         45 => ['v_user', 'id_user', 'username'],
//         46 => ['v_satker_kppn', 'kdkppn', 'nmkppn'],
//         47 => ['v_ppk_satker', 'kd_ppk', 'nama_ppk'],
//         48 => ['v_link_satker', 'linkid', 'linkname'],
//         49 => ['ref_kab_kota', 'kd_kab_irmsv3', 'kab_kota'],
//         50 => ['ref_harga_sat', 'kdkmpnen', 'hargasat'],
//         51 => ['v_balai_prov', 'kd_satker_balai', 'nmsatker'],
//         52 => ['v_satker_fisik', 'kdsatker', 'nmsatker'],
//         53 => ['v_satker_non_fisik', 'kdsatker', 'nmsatker'],
//         54 => ['r_satker_ppk', 'id_ppk', 'nama_ppk'],
//         55 => ['provinsi', 'kd_prov', 'nama_prov'],
//         56 => ['v_ruas_jembatan', 'id_jembatan', 'nama_jembatan'],
//         57 => ['r_kws_dalam_wps', 'distinct(kws_kode)', 'kws_nama'],
//         58 => ['r_kws_dalam_wps', 'distinct(subkawasan_nama)', 'subkawasan_nama'],
//         59 => ['r_kws_dalam_wps', 'distinct(wps_kode)', 'wps_nama'],
//         60 => ['v_prov_satker', 'distinct(kd_prov)', 'nama_prov'],
//         61 => ['view_kabkota', 'id_kabkot', 'kab_kota'],
//         62 => ['view_kabkota', 'id_kabkot', 'kab_kota'],
//         63 => ['v_satker', 'kdsatker', 'nmsatker'],
//         64 => ['v_prov_satker', 'distinct(kdlokasi)', 'nama_prov'],
//         65 => ['v_jnsban', 'kdsdana', 'nmjnsban'],
//         66 => ['v_api_verifikator', 'id_user_group', 'nama'],
//         67 => ['v_prov_satker', 'distinct(kd_prov_irmsv3)', 'nama_prov'],
//         68 => ['r_truas', 'id', 'deskripsi'],
//         69 => ['r_ruas', 'distinct(KD_PPK)', 'KD_PPK'],
//         70 => ['v_ref_ruas', 'KODE_RUAS', 'nama_ruas'],
//         71 => ['v_ref_kabkota', 'distinct(kd_kab_ruas)', 'kab_kota'],
//         72 => ['v_ref_ruas', 'ROUTEID', 'SURVEY_LEN'],
//         73 => ['v_register', 'register', 'nmdonor'],
//         74 => ['v_ref_komponen_usulan_fsk', 'kdkmpnen', 'nmkmpnen'],
//         75 => ['r_truas', 'id', 'deskripsi'],
//         76 => ['v_blokir', 'kdblokir', 'nmblokir'],
//         77 => ['v_ref_sspn', 'kode_sspn', 'uraian'],
//         78 => ['v_ref_sasaran_strategis', 'kd_sasaran_strategis', 'uraian'],
//         79 => ['v_ref_outcome', 'outcome_id', 'nama'],
//         80 => ['v_ref_sspp', 'kode_sspp', 'uraian'],
//         81 => ['v_ref_sskp', 'kode_sskp', 'uraian'],
//         82 => ['v_satuan', 'satkeg', 'satkeg'],
//         83 => ['provinsi', 'kd_prov_rkakl', 'nama_prov'],
//         84 => ['v_jembatan', 'no_jembatan', 'nama_jembatan'],
//         85 => ['v_prov_satker', 'kdsatker', 'nmsatker'],
//         86 => ['r_vendor', 'id_vendor', 'nmvendor'],
//         87 => ['v_balai_prov', 'distinct(kd_satker_balai)', 'nmsatker'],
//         //88 => ['v_output_kondisi', 'distinct(kdgiat)', 'nmgiat'],
//         //89 => ['v_output_kondisi', 'distinct(kro)', 'nmoutput'],
//         88 => ['v_nomenklatur', 'distinct(kdgiat)', 'nmgiat'],
//         89 => ['v_nomenklatur', 'distinct(kro)', 'nmoutput'],
//         90 => ['v_soutput_kondisi', 'distinct(kdsoutput)', 'nmsoutput'],
//         91 => ['v_nomenklatur', 'distinct(kdprogram)', 'nmprogram'],
//         92 => ['v_komponen_fisik', 'kdkmpnen', 'nmkmpnen'],
//         93 => ['v_usulan_dpr', 'distinct(id_user_entry)', 'pengentri_desc'],
//         94 => ['v_usulan_pemda', 'distinct(id_user_entry)', 'pengentri_desc'],
//         95 => ['v_usulan_dprd', 'distinct(id_user_entry)', 'pengentri_desc'],
//         96 => ['v_usulan_akademis', 'distinct(id_user_entry)', 'pengentri_desc'],
//         97 => ['v_usulan_kl', 'distinct(id_user_entry)', 'pengentri_desc'],
//         98 => ['v_usulan_diskresi', 'distinct(id_user_entry)', 'pengentri_desc'],
//         99 => ['v_ref_truas', 'id', 'deskripsi'],
//         100 => ['r_wilayah_prov', 'kd_prov_rkakl', 'nama_prov'],
//         101 => ['v_output_sub_output', 'nmsoutput', 'nmsoutput'],
//         102 => ['ref_kab_kota', 'kd_kab_rkakl', 'kab_kota'],
//         103 => ['v_usulan_dpr', 'distinct(id_pengusul)', 'pengusul_desc'],
//         104 => ['v_usulan_pemda', 'distinct(id_pengusul)', 'pengusul_desc'],
//         105 => ['v_usulan_dprd', 'distinct(id_pengusul)', 'pengusul_desc'],
//         106 => ['v_usulan_akademis', 'distinct(id_pengusul)', 'pengusul_desc'],
//         107 => ['v_usulan_kl', 'distinct(id_pengusul)', 'pengusul_desc'],
//         108 => ['v_usulan_diskresi', 'distinct(id_pengusul)', 'pengusul_desc'],
//         109 => ['v_ref_ruas_pok', 'KODE_RUAS', 'nama_ruas'],
//         110 => ['r_tematik', 'kd_tematik', 'tematik'],
//         111 => ['r_uraian_tematik', 'id', 'uraian'],
//         112 => ['r_biaya', 'kd_biaya', 'biaya'],
//         113 => ['r_rc', 'kd_rc', 'uraian']
    );

    public function __construct() {
        parent::__construct();
        $this->load->helper('url');
        $this->load->helper(array('form', 'url', 'file','download'));

        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();
        $this->load->library('template');
        $this->load->helper('dtssp');

        // $this->thangwhere = " where tahun=".$this->session->konfig_tahun_ang;
        // $this->thang = " and tahun=".$this->session->konfig_tahun_ang;

        // $thangR = $this->session->konfig_tahun_ang - 1;
        // $this->thangwhereRev = " where tahun=". $thangR;
        // $this->thangRev = " and tahun=". $thangR;

        $this->thangwhere = " where 1=1";
        $this->thang = " and 1=1";
 
        $thangR = $this->session->konfig_tahun_ang - 1;
        $this->thangwhereRev = " where 1=1";
        $this->thangRev = " and 1=1";
    }

    function fieldlook($id, $rev = 1) {
        $fldconfig = $this->fldconfig;

        if ($rev === 1){
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0];
        } else {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0]. $this->thangwhereRev;
        }

        $query = $this->db->query($sql);
        
        $data = $query->result_array();

        echo json_encode($data);
    }
    function fieldlookthang($id, $rev = 1) {
        $fldconfig = $this->fldconfig;
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0];


        $query = $this->db->query($sql);

        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlookall($id,$key) {
        $fldconfig = $this->fldconfig;
        if($key=='undefined')
        {
          $cari='';
        }else{
          $cari=$key;
        }
        $sql = "select top 10 " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0]." where nmdonor like '%$cari%'". $this->thang;
        $query = $this->db->query($sql);

        $data = $query->result_array();

        echo json_encode($data);
    }


    function fieldlookSearch($id) {
        $like = $this->input->get("p");


        $fldconfig = $this->fldconfig;
//        if($like==0)
//        {
//            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0];
//        }
//        else
//        {
            //  $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $fldconfig[$id][2] . " like '%$like%'". $this->thang;
//        }

        $sql = "select " . $fldconfig[$id][1] . " as id, " . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where lower(" . $fldconfig[$id][2] . ") like '%$like%'";


        $query = $this->db->query($sql);

        // echo $this->db->last_query();

        $data = $query->result_array();
        echo json_encode($data);
    }

    //id=table_id,xid=where column ,paramid=where parameter for condition
    function fieldlookbyid($id, $xcolumn, $paramid) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $xcolumn . "=" . $paramid. $this->thang;
        $query = $this->db->query($sql);

        $data = $query->result_array();
        echo json_encode($data);
    }

    function fieldlook2($id, $refindex, $param1, $param2) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where kdgiat= " . $param1 ."and kdoutput=".$param2. $this->thang;
        $query = $this->db->query($sql);

        $data = $query->result_array();
        echo json_encode($data);
    }

    function fieldlookusergroupsatker($id, $id_user_group) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where id_usergroup =" . $id_user_group. $this->thang;
        $query = $this->db->query($sql);

        $data = $query->result_array();
        echo json_encode($data);
    }

    function setinput($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->fldconfig;

        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $refval = explode('::', $refresh_value);

        $reffld = explode('::', $refresh_field);

        $sql = "select " . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'";

        //echo $sql;
        $query = $this->db->query($sql);
        $data = $query->row_array();
        echo json_encode($data);
    }

    function setinput3($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->fldconfig;

        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $refval = explode('::', $refresh_value);

        $reffld = explode('::', $refresh_field);

        $sql = "select " . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'";

//        echo $sql;
        $query = $this->db->query($sql);
        $data = $query->row_array();
        echo json_encode($data);
    }

    function setinput2($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->fldconfig;

        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][1] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . " = " ."'".$refresh_value."'". $this->thang;

        echo $sql;
        $query = $this->db->query($sql);
        $data = $query->row_array();
        echo json_encode($data);
    }

    function setinput4($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->fldconfig;

        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . " = " . $refresh_value. $this->thang;

        //echo $sql;
        $query = $this->db->query($sql);
        $data = $query->row_array();
        echo json_encode($data);
    }

    function fieldlookarr($id) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0]. $this->thangwhere;
        $query = $this->db->query($sql);

        $data = $query->result_array();
//        echo "laskdjflaksjdf";
        $retdata = array();
        foreach ($data as $item) {
            $retdata[$item['id']] = $item['val'];
        }

        return $retdata;
    }

    function fieldlooktree($id) {

        $fldconfig = $this->fldconfig;

//        $fldconfig = $this->config->item("reff_table");

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where id_sub_user_group=0". $this->thang;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlookSearch() {

        $like = $this->input->get('q');
        $id = $this->input->get('ix');
        $fld = $this->input->get('f');
        $vl = $this->input->get('v');

        $fldconfig = $this->fldconfig;


        //echo '-->'.$refresh_value;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $fld . "=" . $vl ." and ". $fldconfig[$id][2] ." like '%$like%'";

        //echo $sql;

        $query = $this->db->query($sql);

        //echo $this->db->last_query();


        $data = $query->result_array();
        echo json_encode($data);
    }

    //id = table_name, refresh id= value who want to refresh //refresh_field  nama field yang akan di refresh
    //for example kode_provinsi ->merefresh data kota yang sesuai dengan kode provinsi yang bersangkutan
    function refreshlook($id, $refresh_field, $refresh_value = -99, $rev = 1) {

        $fldconfig = $this->fldconfig;


        //echo '-->'.$refresh_value;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        if ($rev === 1){
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value. $this->thang;
        } else {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value. $this->thangRev;
        }

        //echo $sql;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlookVchar($id, $refresh_field, $refresh_value, $rev = 1) {

        $fldconfig = $this->fldconfig;


        //echo '-->'.$refresh_value;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        if ($rev === 1){
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "='" . $refresh_value . "'";
        } else {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value. $this->thangRev;
        }

        //echo $sql;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }


    function refreshlookorder($id,$order,$asc,  $refresh_field,$refresh_value = -99, $rev =1) {
        $fldconfig = $this->fldconfig;
        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " order by ". $order ." ". $asc;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }
    function initlookorder($id, $order,$asc) {
        $fldconfig = $this->fldconfig;
        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . $this->thangwhere ." order by ". $order ." ". $asc;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    //id = table_name, refresh id= value who want to refresh //refresh_field  nama field yang akan di refresh
    //for example kode_provinsi ->merefresh data kota yang sesuai dengan kode provinsi yang bersangkutan
    function refreshlookstringid($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->fldconfig;


        //echo '-->'.$refresh_value;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "='" . $refresh_value."'". $this->thang;

        //echo $sql;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlook2($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->fldconfig;
//        $fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );


        $refval = explode('::', $refresh_value)[1];

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . " = '" . $refval . "'". $this->thang;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlook3($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->fldconfig;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );
//        echo $refresh_value;

        $refval = explode('::', $refresh_value)[1];

        $sql = "select " . $fldconfig[$id][2] . " as id," . $fldconfig[$id][1] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . " = '" . $refval. $this->thang . "' order by " . $fldconfig[$id][1] . " asc";

        //echo $sql;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlookSTA($id, $refresh_value) {

        $fldconfig = $this->fldconfig;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );
//        echo $refresh_value;

        //$refval = explode('::', $refresh_value)[1];

        $sql = "select " . $fldconfig[$id][2] . " from " . $fldconfig[$id][0] . " where " . $fldconfig[$id][1] . " = '" . $refresh_value . "'". $this->thang;



        //echo $sql;
        $query = $this->db->query($sql);
        $data = $query->row();


        //print_r($data);

        $stars = array(array('id' => 0, 'val' => 0));



        $minsta = 0;

        $maxsta = $data->SURVEY_LEN * 1000;


        while ($minsta < $maxsta) {
            $minsta += 100;

            $st['id'] = $minsta;
            $st['val'] = $minsta;

            $stars[] = $st;
        }



        //print_r($realdata);

        echo json_encode($stars);



        //$data = $query->result_array();
        //echo json_encode($data);
    }



    function refreshlook4($id, $refresh_field, $refresh_value, $rev = 1) {

        $fldconfig = $this->fldconfig;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );
//        echo $refresh_value;

        $refval = explode('::', $refresh_value);

        $reffld = explode('::', $refresh_field);

        $count = sizeof($reffld);

        if ($rev === 1){
            if ($count == 4) {
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "' and " . $reffld[3] . " = '" . $refval[3] . "'". $this->thang;
            } else if ($count == 3) {
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "'". $this->thang;
            } else if ($count == 2) {
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'". $this->thang;
            }
        } else {
            if ($count == 4) {
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "' and " . $reffld[3] . " = '" . $refval[3] . "'". $this->thangRev;
            } else if ($count == 3) {
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "'". $this->thangRev;
            } else if ($count == 2) {
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'". $this->thangRev;
            }
        }

        //echo $sql;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlook4or($id, $refresh_field, $refresh_value, $rev = 1) {

        $fldconfig = $this->fldconfig;
        $refval = explode('::', $refresh_value);
        $reffld = explode('::', $refresh_field);
        $count = sizeof($reffld);
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' or " . $reffld[1] . " = '" . $refval[1] . "'". $this->thang;

        // if ($rev === 1){
        //     if ($count == 4) {
        //         $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' or " . $reffld[1] . " = '" . $refval[1] . "' or " . $reffld[2] . " = '" . $refval[2] . "' or " . $reffld[3] . " = '" . $refval[3] . "'". $this->thang;
        //     } else if ($count == 3) {
        //         $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' or " . $reffld[1] . " = '" . $refval[1] . "' or " . $reffld[2] . " = '" . $refval[2] . "'". $this->thang;
        //     } else if ($count == 2) {
            
        //     }
        // } else {
        //     if ($count == 4) {
        //         $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' or " . $reffld[1] . " = '" . $refval[1] . "' or " . $reffld[2] . " = '" . $refval[2] . "' or " . $reffld[3] . " = '" . $refval[3] . "'". $this->thangRev;
        //     } else if ($count == 3) {
        //         $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' or " . $reffld[1] . " = '" . $refval[1] . "' or " . $reffld[2] . " = '" . $refval[2] . "'". $this->thangRev;
        //     } else if ($count == 2) {
        //         $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' or " . $reffld[1] . " = '" . $refval[1] . "'". $this->thangRev;
        //     }
        // }
        //echo $sql;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }
    function refreshlook4thang($id, $refresh_field, $refresh_value, $rev = 1) {

        $fldconfig = $this->fldconfig;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );
//        echo $refresh_value;

        $refval = explode('::', $refresh_value);

        $reffld = explode('::', $refresh_field);
        $count = sizeof($reffld);

        if($refval[2]=='DPR' or $refval[2]=='PEMDA' or $refval[2]=='KL' or $refval[2]=='AKADEMIS' or $refval[2]=='DISKRESI'){

          if($refval[2]=='DPR'){
          $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where userlogin like '%A-%' AND " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'";
        }else {

          $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'";

        }
        }else{
        if ($rev === 1){
            if ($count == 4) {
              if($refval[3]==='-'){
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "' and " . $reffld[3] . " = '" . $refval[3] . "'";

              }else{
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and ((" . $reffld[2] . " = '" . $refval[2] . "' and kd_kabkot_pemda = '-') or " . $reffld[3] . " = '" . $refval[3] . "')";

              }
            } else if ($count == 3) {
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "'";
            } else if ($count == 2) {

                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'";
            }
        } else {
            if ($count == 4) {

                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "' and " . $reffld[3] . " = '" . $refval[3] . "'";
            } else if ($count == 3) {
                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "'";
            } else if ($count == 2) {

                $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'";
            }
        }
      }

        //echo $sql;
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }


    function get_ruas_from_balai($id, $refresh_field, $refresh_value) {

        /* Load External Controller */
//        $controller = "c";
//        $method = "child_satker_is_pjn";
//
//        include($controller . '.php');
//        $get = new $controller();
//        $get->$method();
//        $this->load->module("rolemgr");
//        $data = $this->rolemgr->child_satker_is_pjn();
//
//        echo $data;
        $data_received = Modules::run('rolemgr/rolemgr/child_satker_is_pjn', $data);

//        echo $data_received;
//        die();

        $fldconfig = $this->fldconfig;

        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $refval = explode('::', $refresh_value);
        $count = sizeof($refval);

        for ($i = 0; $i < $count; $i++) {
            $sql .= "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value[i]. $this->thang;
            $sql .= " union ";
        }


        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function fieldlookdpr($id) {
        $fldconfig = $this->fldconfig;

        $sql = "select id_user as id,username as val from users  where  id_user_group=47 and id_sub_user_group=40". $this->thang;
        $query = $this->db->query($sql);

        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshRegAuto(){


    }
    function refreshlookor($id, $refresh_field, $refresh_value, $refresh_field2, $refresh_value2) {

        $fldconfig = $this->fldconfig;


        //echo '-->'.$refresh_value;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value ." or " . $refresh_field2 . "=" . $refresh_value2. $this->thang  ;

        //echo $sql;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }
    function refreshlookchar($id, $refresh_field, $refresh_value) {

        $fldconfig = $this->fldconfig;


        //echo '-->'.$refresh_value;
        //$fldconfig = $this->config->item("reff_table");
        //format data sample
        // $config['reff_table'] =array(
        //     0=>['r_bas', 'kd_jns_belanja','keterangan'],
        //     1=>['r_dapil', 'kd_dapil', 'nama_dapil']
        //         );

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "='" . $refresh_value."'". $this->thang;

        //echo $sql;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlookkws($id, $refresh_field, $refresh_value, $wps) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value . " and wps_kode = " . $wps. $this->thang;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlooksub($id, $refresh_field, $refresh_value, $wps, $kws) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value . " and wps_kode = " . $wps . " and kws_kode = '$kws'". $this->thang;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlookcontidion($id, $refresh_field, $refresh_value, $condition, $valuecondition) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value . " and ".$condition." = '" . $valuecondition . "'". $this->thang;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlookkabkota($id, $refresh_field, $refresh_value) {
        $fldconfig = $this->fldconfig;

        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $refresh_field . "=" . $refresh_value . " and STATUS = 'O' OR STATUS = 'P' OR STATUS = 'K'". $this->thang;

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function fieldlookWithoutThang($id) {
        $fldconfig = $this->fldconfig;
        $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0];
        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }

    function refreshlook6WithoutThang($id, $refresh_field, $refresh_value) {
        $fldconfig = $this->fldconfig;
        $refval = explode('::', $refresh_value);
        $reffld = explode('::', $refresh_field);
        $count = sizeof($reffld);

        if ($count == 6) {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "' and " . $reffld[3] . " = '" . $refval[3] . "' and " . $reffld[4] . " = '" . $refval[4] . "' and " . $reffld[5] . " = '" . $refval[5] . "'";
        } else if ($count == 5) {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "' and " . $reffld[3] . " = '" . $refval[3] . "' and " . $reffld[4] . " = '" . $refval[4] . "'";
        } else if ($count == 4) {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "' and " . $reffld[3] . " = '" . $refval[3] . "'";
        } else if ($count == 3) {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "' and " . $reffld[2] . " = '" . $refval[2] . "'";
        } else if ($count == 2) {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "' and " . $reffld[1] . " = '" . $refval[1] . "'";
        } else {
            $sql = "select " . $fldconfig[$id][1] . " as id," . $fldconfig[$id][2] . " as val from " . $fldconfig[$id][0] . " where " . $reffld[0] . " = '" . $refval[0] . "'";
        }

        $query = $this->db->query($sql);
        $data = $query->result_array();
        echo json_encode($data);
    }


    // ============================================ Sukmana ==================================================

    
    function fieldlooktematik($id,$par, $rev = 1) {
        $fldconfig = $this->fldconfig;
        
        $this->db->select('row_number() over(order by vt.id_layer) as no,vt.nama_layer,vt.id_layer,vt.url,vt.alias');
        $this->db->where('vt.url', $par);
        $this->db->order_by('vt.id_layer', 'asc');

        $d = $this->db->get('v_tematik vt')->result();
        // echo $this->db->last_query();
    
        

        // return $d;
        // echo $this->db->last_query();
        echo json_encode($d);
    }

        
    function fieldlookModule($id,$par, $rev = 1) {

        $this->db->select('module');
        $dok = $this->db->get('download_dokumen')->result();
        $doks=[];
        foreach ($dok as $key => $value) {
            array_push($doks,$value->module);
        }

        // if(!empty($doks)){
        //     $this->db->where_not_in('url', $doks);
        // }
        $this->db->select('vt.url,vt.nama_module');
        $this->db->group_by('vt.nama_module,vt.url');
        $this->db->order_by('nama_module', 'asc');
        
        $d = $this->db->get('v_tematik vt')->result();

        echo json_encode($d);
    }
    
    
    
    public function fieldlookUserGroup()
    {
        $d = $this->db->get('aset_user_group')->result();
        
        echo json_encode($d);
        
    }

    public function tes()
    {
        echo 'tes';
    }

    public function templateShp($var,$kdppum,$kdpkab,$tahun)
    {
        
        $nama_dir = FCPATH . 'upload_shp/template_shp/';
        $this->db->select('wadmkk');
        $this->db->where('kdpkab', $kdpkab);
        $this->db->where('kdppum', $kdppum);
        $kab = $this->db->get('spatial.batas_admin_indonesia')->row_array();
        $kab = str_replace(' ','_',$kab['wadmkk']);
        $this->db->select('nama_layer');
        $this->db->where('alias', $var);
        $lay = $this->db->get('r_layer')->row_array();
        $rep=[' ','(',')'];
        $lay = str_replace($rep,'_',$lay['nama_layer']);
        // ========================== create sendri ===========
        $table=[
                'NPGT' => 'npgt_prov',
                'LBS' => 'lahanbakusawah_prov',
                'A' => 'npgt_kabkot_a',
                'G' => 'npgt_kabkot_g',
                'Q' => 'npgt_kabkot_q',
                'O' => 'npgt_kabkot_o',
                'W' => 'npgt_kabkot_w',
                'GQ' => 'npgt_kabkot_gq',
                'N' => 'npgt_kabkot_n',
                'V' => 'npgt_kabkot_v',
                'HP' => 'npgt_kabkot_h_polyline',
                'K' => 'npgt_kabkot_k_polyline',
                'H' => 'npgt_kabkot_h_polygon',
                'NPGTPKB' => 'npgt_perkebunan',
                'TNBH' => 'tanahnegara_prov_tnbh',
                'TNBK' => 'tanahnegara_prov_tnbk',
                'TNTK' => 'tanahnegara_prov_tntk',
                'MPPT' => 'mppt_prov',
                'WPPN' => 'wp3wt_penataan_perbatasan',
                'WPP' => 'wp3wt_penataan_pesisir',
                'WPK' => 'wp3wt_penataan_pulau_kecil',
                'WPPT' => 'wp3wt_penataan_wilayah_tertentu',
                'WPPK' => 'wp3wt_ppk_point',
                'WPPK2' => 'wp3wt_ppk_polygon',
                'WPPKT' => 'wp3wt_ppkt',
                'WPPTT' => 'wp3wt_tnh_timbul',
                'IPPTP' => 'ptp_p3t',
                'PTPIL' => 'ptpil_prov',
                'P3T' => 'ptp_pk_p3t',
                'PTPILTK' => 'ptpil_prov_tk',
                'KT' => 'kemampuan_tanah',
                'RTRW' => 'rtrw',
                'PTPNB' => 'ptp_kppr_non_berusaha', 
                'PTPKB' => 'ptp_kppr_berusaha',
                'PTPKS' => 'ptp_kppr_stranas',
                'PSRPTT' => 'ptp_tnh_timbul',
                'NPGTKK' => 'npgt_kabkota',
                'NPGTKPY' => 'npgt_kec_polygon',
                'PT' => 'landuse_indonesia',
        ];

        // echo @$table[$var];exit();

        if (@$table[$var]) {
            // echo 'masuk';
 
            
            
            $not_in = ['gid','id','created_by','created_at','updated_by','updated_at','created_bysso','updated_bysso','objectid','shape_leng','shape_area'];
            $this->db->where('table_schema', 'spatial');
            $this->db->where('table_name', $table[$var]);
            $this->db->where_not_in('column_name', $not_in);
            $column = $this->db->get('v_column')->result();
            
            $str ="select ";
            foreach ($column as $key => $value) {
            
                $arr = ['kdppum','kdpkab','wadmpr','wadmkk','geom','gid','tahun_data'];
                if((!in_array($value->column_name, $arr , TRUE))){
                $str.= "null as ".$value->column_name.", ";
                }
                
                if((in_array($value->column_name, $arr , TRUE))){
                    if($value->column_name == 'geom'){
                        $str .=  "b.".$value->column_name.", ";
                    }elseif($value->column_name == 'gid'){

                    }elseif($value->column_name == 'tahun_data'){
                        $str .= $tahun." as tahun_data, ";
                    }else{
                        $str .=  "b.".$value->column_name.", ";
                    }
                }
                
            }

            $str = mb_substr($str, 0, -1);
            $str[-1] = PHP_EOL;
            $str1=$str;
            $str .= " from spatial.".$table[$var]." a
            join spatial.batas_admin_indonesia b  on a.kdpkab = b.kdpkab and a.kdppum = a.kdppum 
            where b.kdppum  = '".$kdppum."' and b.kdpkab = '".$kdpkab."' limit 10";
            $cek = $this->db->query($str)->num_rows();
            // echo $this->db->last_query();
            
            if($cek == 0){
                // echo 'sini';
                if (!strpos($str1, 'b.geom')) {
                    // String Not Found
                    $str1 .= ",b.geom ";
                }
                $str1 .= " from spatial.batas_admin_indonesia b   
                where b.kdppum  = '".$kdppum."' and b.kdpkab = '".$kdpkab."' limit 10";
                $str = $str1;
            }
            
            // echo "<pre>";
            // print_r ($cek);
            // echo "</pre>";
            
            // echo $str;exit();
        }else{
            // echo 'keluar';
            $table[$var] = 'template_universal';
            
            $str = "select kdppum,kdpkab,wadmpr,wadmkk,'".$tahun."' as tahun_data,geom from spatial.batas_admin_indonesia    
            where kdppum  = '".$kdppum."' and kdpkab = '".$kdpkab."' limit 10";
        }
    //   echo $str;exit();
        // $Shapefile = new ShapefileWriter($nama_dir.'npgt_prov.shp');
        // ============== hapus file lama ==============
        $lay = str_replace('/','_',$lay);
        $nama = strtolower($lay.'_'.$kab.'_'.$tahun);
        $cekFiles = glob($nama_dir.$nama.".*");
        if(!empty($cekFiles)){
            foreach ($cekFiles as $key => $value) {
                unlink($value);
            }
        }
    
        
        $nama = $var == 'P3T' ? 'pk_p3t'.'_'.$kab.'_'.$tahun : $nama;
        $nama = $var == 'PSRPTT' ? 'ptp_tanah_timbul'.'_'.$kab.'_'.$tahun : $nama;
        // echo $str;exit();
        // =================== create shp baru =============
        $exec =  'pgsql2shp -f '.FCPATH .'/upload_shp/template_shp/'.$nama.' -h '.WGI_DB_LRS_HOST.' -p '.WGI_DB_LRS_PORT.' -u '.WGI_DB_LRS_USER.' -P '.WGI_DB_LRS_PWD.' '.WGI_DB_LRS_DB.' "'.$str.'" ';
        // echo 'pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/'.$nama.' -h '.WGI_DB_LRS_HOST.' -p '.WGI_DB_LRS_PORT.' -u '.WGI_DB_LRS_USER.' -P '.WGI_DB_LRS_PWD.' '.WGI_DB_LRS_DB.' "'.$str.'" ';

        exec($exec,$a,$b);
        
    
        // ============== chmod file ==============
        $files = glob($nama_dir.$nama.".*");
        foreach ($files as $key => $value) {
            chmod($value, 0777);
        }


        // ============== proses ziping file =========
        
        $zipname = $nama_dir.$nama.'.zip';
        
        $zip = new ZipArchive;
        $zip->open($zipname, ZipArchive::CREATE);
        foreach ($files as $file) {
            $new_filename = substr($file,strrpos($file,'/') + 1);
            // echo $file;
            $zip->addFile($file,$new_filename);
        }
        $zip->close();

        //=== download file ===
        $file_content = file_get_contents($zipname);
        $cekFiles = glob($nama_dir.$nama.".*");
        if(!empty($cekFiles)){
            foreach ($cekFiles as $key => $value) {
                unlink($value);
            }
        }
        force_download($nama.'.zip',$file_content);
  
        
       exit();
        try {
            $cekFiles = glob($nama_dir."npgt_prov.*");
            if(!empty($cekFiles)){
                foreach ($cekFiles as $key => $value) {
                    unlink($value);
                }
            }
            // Open Shapefile
            $Shapefile = new ShapefileWriter($nama_dir.'npgt_prov.shp');
            
            // Set shape type
            $Shapefile->setShapeType(Shapefile::SHAPE_TYPE_POLYGON);
            // pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/npgt_prov -h ************ -p 4932 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0"
            // pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/npgt_prov -h ************ -p 4980 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0"
            // ogr2ogr -f "/var/www/html/atrpgt/upload_shp/template_shp/npgt_prov" mydata.shp PG:"host=************ user=wgi dbname=pgtdb password=pgDevDIAwan port=4932" "gis_npgt_prov"
            // pgsql2shp -f D:\Webgis\ -h ************ -p 4932 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0"
            
            // D:\Webgis\
            
            // shell_exec (pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/npgt_prov -h ************:4932 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0");
            // pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/npgt_prov -h ************ -p 4932 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0"
            // Create field structure
            // $Shapefile->addNumericField('ID', 10);
            // $Shapefile->addCharField('DESC', 25);
            foreach ($column as $key => $value) {
                $num = ['bigint', 'integer' , 'numeric'];
                if (in_array($value->data_type, $num) ) {
                    $Shapefile->addNumericField($value->column_name,10);
                }elseif ($value->data_type == 'character varying') {
                    $Shapefile->addCharField($value->column_name,254);
                }
            }

            
            // echo "<pre>";
            // print_r ($Shapefile->getFields());
            // echo "</pre>";
            
            // exit();
           
            // Finalize and close files to use them
            $Shapefile = null;
            $files = glob($nama_dir."npgt_prov.*");
            foreach ($files as $key => $value) {
                chmod($value, 0777);
            }
            $zipname = $nama_dir.'npgt_prov.zip';
            $zip = new ZipArchive;
            $zip->open($zipname, ZipArchive::CREATE);
            foreach ($files as $file) {
                $new_filename = substr($file,strrpos($file,'/') + 1);
                // echo $new_filename;
              $zip->addFile($file,$new_filename);
            }
            $zip->close();
            chmod($zipname, 0777);
            force_download($zipname, null);

        } catch (ShapefileException $e) {
            // Print detailed error information
            echo "Error Type: " . $e->getErrorType()
                . "\nMessage: " . $e->getMessage()
                . "\nDetails: " . $e->getDetails();
        }

    }

    public function templateShpKec($var,$kdppum,$kdpkab,$tahun)
    {
        
        $nama_dir = FCPATH . 'upload_shp/template_shp/';
        $this->db->select('wadmkk');
        $this->db->where('kdpkab', $kdpkab);
        $this->db->where('kdppum', $kdppum);
        $kab = $this->db->get('spatial.batas_admin_indonesia')->row_array();
        $kab = str_replace(' ','_',$kab['wadmkk']);
        $this->db->select('nama_layer');
        $this->db->where('alias', $var);
        $lay = $this->db->get('r_layer')->row_array();
        $rep=[' ','(',')'];
        $lay = str_replace($rep,'_',$lay['nama_layer']);
        // ========================== create sendri ===========
        $table=[
                'NPGTKCA' => 'npgt_kec_a',
                'NPGTKCG' => 'npgt_kec_g',
                'NPGTKCQ' => 'npgt_kec_q',
                'NPGTKCO' => 'npgt_kec_o',
                'NPGTKCW' => 'npgt_kec_w',
                'NPGTKCGQ' => 'npgt_kec_gq',
                'NPGTKCN' => 'npgt_kec_n',
                'NPGTKCV' => 'npgt_kec_v',
                'NPGTKCH' => 'npgt_kec_h_polyline',
                'NPGTKCK' => 'npgt_kec_k_polyline',
                'NPGTKCHG' => 'npgt_kec_h_polygon',
        ];

    

        if (@$table[$var]) {
            $this->db->where('table_schema', 'spatial');
            $this->db->where('table_name', $table[$var]);
            $column = $this->db->get('v_column')->result();

            $str ="select ";
            foreach ($column as $key => $value) {
            
                $arr = ['kdppum','kdpkab','wadmpr','wadmkk','geom','gid','tahun_data'];
                if((!in_array($value->column_name, $arr , TRUE))){
                $str.= "null as ".$value->column_name.", ";
                }
                
                if((in_array($value->column_name, $arr , TRUE))){
                    if($value->column_name == 'geom'){
                        $str .=  "b.".$value->column_name.", ";
                    }elseif($value->column_name == 'gid'){

                    }elseif($value->column_name == 'tahun_data'){
                        $str .= $tahun." as tahun_data, ";
                    }else{
                        $str .=  "b.".$value->column_name.", ";
                    }
                }
                
            }

            // $str = mb_substr($str,0,-1);
            $str = mb_substr($str, 0, -1);
            $str[-1] = PHP_EOL;
            $str1=$str;
            $str .= " from spatial.".$table[$var]." a
            join spatial.batas_admin_indonesia b  on a.kdpkab = b.kdpkab and a.kdppum = a.kdppum 
            where b.kdppum  = '".$kdppum."' and b.kdpkab = '".$kdpkab."' limit 10";
            $cek = $this->db->query($str)->num_rows();
            if($cek == 0){
                $str1 .= " from spatial.batas_admin_indonesia b   
                where b.kdppum  = '".$kdppum."' and b.kdpkab = '".$kdpkab."' limit 10";
                $str = $str1;
            }

            // echo $str;
            // exit();

        }else{
            $table[$var] = 'template_universal';
            
            $str = "select kdppum,kdpkab,wadmpr,wadmkk,'".$tahun."' as tahun_data,geom from spatial.batas_admin_indonesia    
            where kdppum  = '".$kdppum."' and kdpkab = '".$kdpkab."' limit 10";
        }
        // echo $str;
        // exit();
        
        // echo $str;
        // exit();
        // $Shapefile = new ShapefileWriter($nama_dir.'npgt_prov.shp');
        // ============== hapus file lama ==============
        $nama = strtolower($lay.'_'.$kab.'_'.$tahun);
        $cekFiles = glob($nama_dir.$nama.".*");
        // if(!empty($cekFiles)){
        //     foreach ($cekFiles as $key => $value) {
        //         unlink($value);
        //     }
        // }
        // echo $nama;
        // exit();
        // =================== create shp baru =============
        $exec =  'pgsql2shp -f '.FCPATH .'/upload_shp/template_shp/'.$nama.' -h localhost -p 5432 -u wgi -P pgDevDiAwan pgtdb "'.$str.'" ';
 
        shell_exec($exec);
        
        // exit();
        // ============== chmod file ==============
        $files = glob($nama_dir.$nama.".*");
        foreach ($files as $key => $value) {
            chmod($value, 0777);
        }


        // ============== proses ziping file =========
        
        $zipname = $nama_dir.$nama.'.zip';
        
        $zip = new ZipArchive;
        $zip->open($zipname, ZipArchive::CREATE);
        foreach ($files as $file) {
            $new_filename = substr($file,strrpos($file,'/') + 1);
            // echo $file;
            $zip->addFile($file,$new_filename);
        }
        $zip->close();

        //=== download file ===
        // force_download($zipname,null);
        $file_content = file_get_contents($zipname);
        $cekFiles = glob($nama_dir.$nama.".*");
        if(!empty($cekFiles)){
            foreach ($cekFiles as $key => $value) {
                unlink($value);
            }
        }
        force_download($nama.'.zip',$file_content);
  
        
       exit();
        try {
            $cekFiles = glob($nama_dir."npgt_prov.*");
            if(!empty($cekFiles)){
                foreach ($cekFiles as $key => $value) {
                    unlink($value);
                }
            }
            // Open Shapefile
            $Shapefile = new ShapefileWriter($nama_dir.'npgt_prov.shp');
            
            // Set shape type
            $Shapefile->setShapeType(Shapefile::SHAPE_TYPE_POLYGON);
            // pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/npgt_prov -h ************ -p 4932 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0"
            // pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/npgt_prov -h ************ -p 4980 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0"
            // ogr2ogr -f "/var/www/html/atrpgt/upload_shp/template_shp/npgt_prov" mydata.shp PG:"host=************ user=wgi dbname=pgtdb password=pgDevDIAwan port=4932" "gis_npgt_prov"
            // pgsql2shp -f D:\Webgis\ -h ************ -p 4932 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0"
            
            // D:\Webgis\
            
            // shell_exec (pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/npgt_prov -h ************:4932 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0");
            // pgsql2shp -f /var/www/html/atrpgt/upload_shp/template_shp/npgt_prov -h ************ -p 4932 -u wgi -P pgDevDiAwan pgtdb "select * from spatial.npgt_prov limit 0"
            // Create field structure
            // $Shapefile->addNumericField('ID', 10);
            // $Shapefile->addCharField('DESC', 25);
            foreach ($column as $key => $value) {
                $num = ['bigint', 'integer' , 'numeric'];
                if (in_array($value->data_type, $num) ) {
                    $Shapefile->addNumericField($value->column_name,10);
                }elseif ($value->data_type == 'character varying') {
                    $Shapefile->addCharField($value->column_name,254);
                }
            }

            
            // echo "<pre>";
            // print_r ($Shapefile->getFields());
            // echo "</pre>";
            
            // exit();
           
            // Finalize and close files to use them
            $Shapefile = null;
            $files = glob($nama_dir."npgt_prov.*");
            foreach ($files as $key => $value) {
                chmod($value, 0777);
            }
            $zipname = $nama_dir.'npgt_prov.zip';
            $zip = new ZipArchive;
            $zip->open($zipname, ZipArchive::CREATE);
            foreach ($files as $file) {
                $new_filename = substr($file,strrpos($file,'/') + 1);
                // echo $new_filename;
              $zip->addFile($file,$new_filename);
            }
            $zip->close();
            chmod($zipname, 0777);
            force_download($zipname, null);

        } catch (ShapefileException $e) {
            // Print detailed error information
            echo "Error Type: " . $e->getErrorType()
                . "\nMessage: " . $e->getMessage()
                . "\nDetails: " . $e->getDetails();
        }

    }

    public function caraUpload(Type $var = null)
    {
        
        // $nama_dir = FCPATH . 'upload_shp/template_shp/cara_upload_data_pgt.pdf';
        // force_download($nama_dir, null);
        $tampilan = $this->load->view('petunjuk', null, true);
        echo json_encode(['data' =>$tampilan]);

    } 
    
    public function viewDokumen($var)
    {
        // $var ='analisa_fisik_tanah';
        $this->db->select('module,judul_dok,path');
        $this->db->where('module', $var);
        $data = $this->db->get('download_dokumen')->result();
        
        
        if(empty($data)){
            echo json_encode(['sts' => 'kosong']);

            exit();
        }
        $data =$data[0];
        $tampilan = $this->load->view('dokumen', $data, true);
        echo json_encode(['data' =>$tampilan,'sts'=> 'ada']);

    }


    // ============================================ Sukmana ==================================================

    public function getByProv($prov='12')
    {
        $arr = [
                'Sungai Polyline (H)' => '',
                'Perubahan Penggunaan Tanah (GQ)' => '',
                'Kesesuaian Penggunaan Tanah Terhadap RTRW (N)' => '',
                'Ketersediaan Tanah (V)' => '',
                'Jalan (K)' => '',
                'Sungai Polygon (H)' => '',
                'Rencana Tata Ruang Wilayah Kabupaten/Kota (W)' => '',
                'Gambaran Umum Penguasaan Tanah (O)' => '',
                'Penggunaan Tanah terakhir (Q)' => '',
                'Penggunaan Tanah tahun lama (G)' => '',
                'Administrasi(A)' => '',
                'Kesesuaian Penggunaan Tanah Terhadap RTRW (N)' => '',
                'Administrasi(A)' => '',
                'Penggunaan Tanah terakhir (Q)' => '',
                'Sungai Polyline (H)' => '',
                'Gambaran Umum Penguasaan Tanah (O)' => '',
                'Sungai Polygon (H)' => '',
                'Jalan (K)' => '',
                'Ketersediaan Tanah (V)' => '',
                'Rencana Tata Ruang Wilayah Kabupaten/Kota (W)' => '',
                'Monitoring Perubahan Penggunaan Tanah' => '',
                'Kemampuan Tanah' => '',
                'Lahan Baku Sawah' => '',
                'NPGT Perkebunan' => '',
                'NPGT Provinsi' => '',
                'PTP IL Provinsi Point' => '',
                'PTP IL Provinsi Polygon' => '',
                'PTP Penyelenggaraan Kebijakan Penggunaan & Pemanfaatan Tanah' => '',
                'PTP P KKPR Berusaha' => '',
                'PTP P KKPR Non Berusaha' => '',
                'PTP P KKPR Stranas' => '',
                'WP3WT Tanah Timbul' => '',
                'WP3WT Penataan Pulau-Pulau Kecil (Polygon)' => '',
                'WP3WT Penataan Pulau-Pulau Kecil (Point)' => '',
                'WP3WT Penataan Pulau-Pulau Kecil Terluar' => '',
                'Tanah Negara Bekas Hak' => '',
                'Tanah Negara Bekas Kawasan' => '',
                'Tanah Kritis' => '',
                'RTRW' => '',
                'Ketersediaan Data Jawa Bagian Selatan' => '',
                'NPGT Industri' => '',
                'NPGT Perumahan' => '',
                'NPGT Kecamatan' => '',
                'PTP IPPT Provinsi' => '',
                'PSRP Tanah Timbul' => '',
                'WP3WT Penataan Pulau Kecil' => '',
                'WP3WT Penataan Perbatasan' => '',
                'WP3WT Penataan Pesisir' => '',
                'WP3WT Penataan Wilayah Tertentu' => '',
                'Wilayah Administrasi Desa' => '',
                'Wilayah Administrasi Kecamatan' => '',
        ];
        $tema =$this->db->get('v_tema_peta')->result();
        $list_view = $this->db->get('v_list_view')->result();
        $list=[];
        foreach ($list_view as $key => $value) {
            array_push($list,$value->table_name);
        }
        
        $data=[];
        foreach ($tema as $key => $value) {
           
            if (!array_key_exists($value->parent_tema,$data)) {
                    $data[$value->parent_tema] = [];
            }

        }
        
        
        
        // $data = [];
        foreach ($tema as $key => $value) {
           
            if (in_array($value->v_dasboard,$list)) {
                $this->db->where('kdppum', $prov);
                $this->db->select('kdppum,wadmpr,tahun_data');
                $get = $this->db->get($value->v_dasboard)->result();
                if (!empty($get)) {
                    // $childs=[];
                    // foreach ($get as $k2 => $v2) {
                    //     $this->db->where('tahun_data', $v2->tahun_data);
                    //     $this->db->where('kdppum', $v2->kdppum);
                        
                    //     $child = $this->db->get($value->v_dasboard.'_drill')->result();
                    //     $kab=[];
                    //     foreach ($child as $k3 => $v3) {
                    //         // array_push($kab,$v3->wadmkk);
                    //         $a=['nama' => $v3->wadmkk,'jml'=>$v3->jml];
                    //         array_push($kab,$a);
                    //     }
                    //     $get[$k2]->jml_kab = count($kab);   
                    //     $get[$k2]->list_kab = $kab;   
                    // }
                    $gets=[
                            'nama_module' => $value->nama_module, 
                            'nama_layer' => $value->nama_layer,
                            'data' => $get,
                            ];
                    array_push($data[$value->parent_tema],$gets);
                }
            }

        }
        $data;
        foreach ($data as $key => $value) {
            if(count($value) == 0){
                unset($data[$key]);
                // echo $key;
            }
        }
        
        echo "<pre>";
        print_r ($data);
        echo "</pre>";
        
        
         
    }

    public function downloadShp($layer,$kdpkab,$tahun)
    {
        $table=[
                'spatial.v_d_npgt_kabkot_a' => 'npgt_kabkot_a',
                'spatial.v_d_npgt_kabkot_o' => 'npgt_kabkot_o',
                'spatial.v_d_npgt_kabkot_k_polyline' => 'npgt_kabkot_k_polyline',
                'spatial.v_d_kemampuan_tanah' => 'kemampuan_tanah',
                'spatial.v_d_npgt_kabkot_n' => 'npgt_kabkot_n',
                'spatial.v_d_npgt_kabkot_v' => 'npgt_kabkot_v',
                'spatial.v_d_lahanbaku' => 'lahanbakusawah_prov',
                'spatial.v_d_mppt' => 'mppt_prov',
                'spatial.v_d_npgt_perkebunan' => 'npgt_perkebunan',
                'spatial.v_d_npgt_prov' => 'npgt_prov',
                'spatial.v_d_ptp_tnh_timbul' => 'ptp_tnh_timbul',
                'spatial.v_d_ptpil_prov_tk' => 'ptpil_prov_tk',
                'spatial.v_d_ptpil_prov' => 'ptpil_prov',
                'spatial.v_d_ptp_p3t' => 'ptp_p3t',
                'spatial.v_d_ptp_non_berusaha' => 'ptp_kppr_non_berusaha',
                'spatial.v_d_ptp_berusaha' => 'ptp_kppr_berusaha',
                'spatial.v_d_ptp_stranas' => 'ptp_kppr_stranas',
                'spatial.v_d_ptp_pk_p3t' => 'ptp_pk_p3t',
                'spatial.v_d_npgt_kabkot_g' => 'npgt_kabkot_g',
                'spatial.v_d_npgt_kabkot_q' => 'npgt_kabkot_q',
                'spatial.v_d_npgt_kabkot_gq' => 'npgt_kabkot_gq',
                'spatial.v_d_pgtl_rtrw' => 'rtrw',
                'spatial.v_d_npgt_kabkot_w' => 'npgt_kabkot_w',
                'spatial.v_d_npgt_kabkot_h_polygon' => 'npgt_kabkot_h_polygon',
                'spatial.v_d_npgt_kabkot_h_polyline' => 'npgt_kabkot_h_polyline',
                'spatial.v_d_tn_tntk' => 'tanahnegara_prov_tntk',
                'spatial.v_d_tn_tnbh' => 'tanahnegara_prov_tnbh',
                'spatial.v_d_tn_tnbk' => 'tanahnegara_prov_tnbk',
                'spatial.v_d_wp3wt_penataan_perbatasan' => 'wp3wt_penataan_perbatasan',
                'spatial.v_d_wp3wt_penataan_pesisir' => 'wp3wt_penataan_pesisir',
                'spatial.v_d_wp3wt_penataan_pulau_kecil' => 'wp3wt_penataan_pulau_kecil',
                'spatial.v_d_wp3wt_ppk_point' => 'wp3wt_ppk_point',
                'spatial.v_d_wp3wt_ppk_polygon' => 'wp3wt_ppk_polygon',
                'spatial.v_d_wp3wt_ppkt' => 'wp3wt_ppkt',
                'spatial.v_d_wp3wt_penataan_wilayah_tertentu' => 'wp3wt_penataan_wilayah_tertentu',
                'spatial.v_d_wp3wt_tnh_timbul' => 'wp3wt_tnh_timbul',
                'spatial.v_d_pgtl_desa' => 'batas_administrasi_desa',
                'spatial.v_d_pgtl_kecamatan' => 'batas_administrasi_kecamatan',
                'spatial.v_d_npgt_kabkota' => 'npgt_kabkota',
                'spatial.v_d_npgt_kec' => 'npgt_kec_polygon',
                'spatial.v_d_landuse_indonesia' => 'landuse_indonesia'

        ];

        $nama_download = [
             'spatial.v_d_npgt_kabkot_a' => 'NPGT Kabkot Administrasi A'  ,
             'spatial.v_d_npgt_kabkot_o' => 'NPGT Kabkot Gambaran Umum Penguasaan Tanah O'  ,
             'spatial.v_d_npgt_kabkot_k_polyline' => 'NPGT Kabkot Jalan K'  ,
             'spatial.v_d_kemampuan_tanah' => 'Kemampuan Tanah'  ,
             'spatial.v_d_npgt_kabkot_n' => 'NPGT Kabkot Kesesuaian Penggunaan Tanah Terhadap RTRW N'  ,
             'spatial.v_d_npgt_kabkot_v' => 'NPGT Kabkot Ketersediaan Tanah V'  ,
             'spatial.v_d_lahanbaku' => 'Lahan Baku Sawah'  ,
             'spatial.v_d_mppt' => 'Monitoring Perubahan Penggunaan Tanah'  ,
             'spatial.v_d_npgt_perkebunan' => 'NPGT Perkebunan'  ,
             'spatial.v_d_npgt_prov' => 'NPGT Provinsi'  ,
             'spatial.v_d_ptp_tnh_timbul' => 'PSRP Tanah Timbul'  ,
             'spatial.v_d_ptpil_prov_tk' => 'PTP IL Provinsi Point'  ,
             'spatial.v_d_ptpil_prov' => 'PTP IL Provinsi Polygon'  ,
             'spatial.v_d_ptp_p3t' => 'PTP IPPT Provinsi'  ,
             'spatial.v_d_ptp_non_berusaha' => 'PTP P KKPR Non Berusaha'  ,
             'spatial.v_d_ptp_berusaha' => 'PTP P KKPR Berusaha'  ,
             'spatial.v_d_ptp_stranas' => 'PTP P KKPR Stranas'  ,
             'spatial.v_d_ptp_pk_p3t' => 'PTP PK P3T'  ,
             'spatial.v_d_npgt_kabkot_g' => 'NPGT Kabkot Penggunaan Tanah tahun lama G'  ,
             'spatial.v_d_npgt_kabkot_q' => 'NPGT Kabkot Penggunaan Tanah terakhir Q'  ,
             'spatial.v_d_npgt_kabkot_gq' => 'NPGT Kabkot Perubahan Penggunaan Tanah GQ'  ,
             'spatial.v_d_pgtl_rtrw' => 'PGT Lainya RTRW'  ,
             'spatial.v_d_npgt_kabkot_w' => 'NPGT Kabkot Rencana Tata Ruang Wilayah Kabupaten/Kota W'  ,
             'spatial.v_d_npgt_kabkot_h_polygon' => 'NPGT Kabkot Sungai Polygon H'  ,
             'spatial.v_d_npgt_kabkot_h_polyline' => 'NPGT Kabkot Sungai Polyline H'  ,
             'spatial.v_d_tn_tntk' => 'Tanah Kritis'  ,
             'spatial.v_d_tn_tnbh' => 'Tanah Negara Bekas Hak'  ,
             'spatial.v_d_tn_tnbk' => 'Tanah Negara Bekas Kawasan'  ,
             'spatial.v_d_wp3wt_penataan_perbatasan' => 'WP3WT Penataan Perbatasan'  ,
             'spatial.v_d_wp3wt_penataan_pesisir' => 'WP3WT Penataan Pesisir'  ,
             'spatial.v_d_wp3wt_penataan_pulau_kecil' => 'WP3WT Penataan Pulau Kecil'  ,
             'spatial.v_d_wp3wt_ppk_point' => 'WP3WT Penataan Pulau-Pulau Kecil Point'  ,
             'spatial.v_d_wp3wt_ppk_polygon' => 'WP3WT Penataan Pulau-Pulau Kecil Polygon'  ,
             'spatial.v_d_wp3wt_ppkt' => 'WP3WT Penataan Pulau-Pulau Kecil Terluar'  ,
             'spatial.v_d_wp3wt_penataan_wilayah_tertentu' => 'WP3WT Penataan Wilayah Tertentu'  ,
             'spatial.v_d_wp3wt_tnh_timbul' => 'WP3WT Tanah Timbul'  ,
             'spatial.v_d_pgtl_desa' => 'PGT Lainya Wilayah Administrasi Desa'  ,
             'spatial.v_d_pgtl_kecamatan' => 'PGT Lainya Wilayah Administrasi Kecamatan'  ,
             'spatial.v_d_npgt_kabkota' => 'NPGT Kabkota',
             'spatial.v_d_npgt_kec' => 'NPGT Kecamatan' ,
             'spatial.v_d_landuse_indonesia' => 'Penggunaan Tanah'


        ];

        // // echo $layer;
        // echo $table[$layer];
        // echo $nama_download[$layer];
        $nama=strtolower(str_replace(' ','_', $nama_download[$layer]));
        $layer= $table[$layer];
        $this->db->select('wadmkk');
        $this->db->where('kdpkab', $kdpkab);
        $kab = $this->db->get('spatial.batas_admin_indonesia')->row_array()['wadmkk'];
        $kab = str_replace(' ','_',$kab);
        
        
        $nama_dir = FCPATH . 'upload_shp/template_shp/';
       
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'tahun_data');
        $cek = $this->db->get('information_schema.columns')->row_array();
        
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'thndata');
        $cek2 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'thnkeg');
        $cek3 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'thn_data');
        $cek4 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'thnkgt');
        $cek5 = $this->db->get('information_schema.columns')->row_array();
   
        
        if(!empty($cek)){
            $param = $cek['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null ";
                $nama = strtolower($nama.'_'.$kab);
            }

        }elseif(!empty($cek2)){
            $param = $cek2['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null ";
                $nama = strtolower($nama.'_'.$kab);
            }
        }elseif(!empty($cek3)){
            $param = $cek3['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null";
                $nama = strtolower($nama.'_'.$kab);
            }
        }elseif(!empty($cek4)){
            $param = $cek4['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null";
                $nama = strtolower($nama.'_'.$kab);
            }
        }elseif(!empty($cek5)){
            $param = $cek5['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null";
                $nama = strtolower($nama.'_'.$kab);
            }
        }

       $not_in = ['gid','id','created_by','created_at','updated_by','updated_at','created_bysso','updated_bysso','objectid'];
       $this->db->select('column_name');
       $this->db->where('table_schema', 'spatial');
       $this->db->where('table_name',$layer);
       $this->db->where_not_in('column_name', $not_in);
       $select = $this->db->get('information_schema.columns')->result();

        
       $str_select = '';
        if (!empty($select)) {
            foreach ($select as $key => $value) {
                $str_select .= $value->column_name.',';
            }
            $str_select = substr($str_select, 0, -1);
        } else {
            $str_select .= "*";
        }
 
        
        
        $sql = "select ".$str_select."  from spatial.".$layer." where ".$where_tahun." and kdpkab = '".$kdpkab."'";
        // $sql .= ' limit 10';
 

        require_once(FCPATH."env.php");
        $exec =  'pgsql2shp -f '.FCPATH .'/upload_shp/template_shp/'.$nama.' -h '.WGI_DB_LRS_HOST.' -p '.WGI_DB_LRS_PORT.' -u '.WGI_DB_LRS_USER.' -P '.WGI_DB_LRS_PWD.' pgtdb "'.$sql.'" ';
        // shell_exec($exec);
        // echo $exec;exit();
        exec($exec,$a,$b);
        // echo $exec;
        // exit();
        
        // echo "<pre>";
        // print_r ($a);
        // echo "</pre>";
        // echo "<pre>";
        // print_r ($b);
        // echo "</pre>";
        // exit();
        $files = glob($nama_dir.$nama.".*");
        foreach ($files as $key => $value) {
            // chmod($value, 0777);
        }
        echo $nama_dir.$nama;
        // ============== proses ziping file =========
        
        $zipname = $nama_dir.$nama.'.zip';
        
        $zip = new ZipArchive;
        $zip->open($zipname, ZipArchive::CREATE);
        foreach ($files as $file) {
            $new_filename = substr($file,strrpos($file,'/') + 1);
            // echo $file;
            $zip->addFile($file,$new_filename);
        }
        $zip->close();

        // //=== download file ===
        $file_content = file_get_contents($zipname);
        $cekFiles = glob($nama_dir.$nama.".*");
        if(!empty($cekFiles)){
            foreach ($cekFiles as $key => $value) {
                // unlink($value);
            }
        }
        $nama =strtoupper(str_replace('_',' ',$nama));
        force_download($nama.'.zip',$file_content);
  
        
    }

    public function downloadGeo($layer,$kdpkab,$tahun)
    {
        
        
        
        $table=[
                'spatial.v_d_npgt_kec_a' => 'npgt_kec_a',
                'spatial.v_d_npgt_kec_o' => 'npgt_kec_o',
                'spatial.v_d_npgt_kec_k_polyline' => 'npgt_kec_k_polyline',
                'spatial.v_d_kemampuan_tanah' => 'kemampuan_tanah',
                'spatial.v_d_npgt_kec_n' => 'npgt_kec_n',
                'spatial.v_d_npgt_kec_v' => 'npgt_kec_v',
                'spatial.v_d_lahanbaku' => 'lahanbakusawah_prov',
                'spatial.v_d_mppt' => 'mppt_prov',
                'spatial.v_d_npgt_perkebunan' => 'npgt_perkebunan',
                'spatial.v_d_npgt_prov' => 'npgt_prov',
                'spatial.v_d_ptp_tnh_timbul' => 'ptp_tnh_timbul',
                'spatial.v_d_ptpil_prov_tk' => 'ptpil_prov_tk',
                'spatial.v_d_ptpil_prov' => 'ptpil_prov',
                'spatial.v_d_ptp_p3t' => 'ptp_p3t',
                'spatial.v_d_ptp_non_berusaha' => 'ptp_kppr_non_berusaha',
                'spatial.v_d_ptp_berusaha' => 'ptp_kppr_berusaha',
                'spatial.v_d_ptp_stranas' => 'ptp_kppr_stranas',
                'spatial.v_d_ptp_pk_p3t' => 'ptp_pk_p3t',
                'spatial.v_d_npgt_kec_g' => 'npgt_kec_g',
                'spatial.v_d_npgt_kec_q' => 'npgt_kec_q',
                'spatial.v_d_npgt_kec_gq' => 'npgt_kec_gq',
                'spatial.v_d_pgtl_rtrw' => 'rtrw',
                'spatial.v_d_npgt_kec_w' => 'npgt_kec_w',
                'spatial.v_d_npgt_kec_h_polygon' => 'npgt_kec_h_polygon',
                'spatial.v_d_npgt_kec_h_polyline' => 'npgt_kec_h_polyline',
                'spatial.v_d_tn_tntk' => 'tanahnegara_prov_tntk',
                'spatial.v_d_tn_tnbh' => 'tanahnegara_prov_tnbh',
                'spatial.v_d_tn_tnbk' => 'tanahnegara_prov_tnbk',
                'spatial.v_d_wp3wt_penataan_perbatasan' => 'wp3wt_penataan_perbatasan',
                'spatial.v_d_wp3wt_penataan_pesisir' => 'wp3wt_penataan_pesisir',
                'spatial.v_d_wp3wt_penataan_pulau_kecil' => 'wp3wt_penataan_pulau_kecil',
                'spatial.v_d_wp3wt_ppk_point' => 'wp3wt_ppk_point',
                'spatial.v_d_wp3wt_ppk_polygon' => 'wp3wt_ppk_polygon',
                'spatial.v_d_wp3wt_ppkt' => 'wp3wt_ppkt',
                'spatial.v_d_wp3wt_penataan_wilayah_tertentu' => 'wp3wt_penataan_wilayah_tertentu',
                'spatial.v_d_wp3wt_tnh_timbul' => 'wp3wt_tnh_timbul',
                'spatial.v_d_pgtl_desa' => 'batas_administrasi_desa',
                'spatial.v_d_pgtl_kecamatan' => 'batas_administrasi_kecamatan',
                'spatial.v_d_npgt_kabkota' => 'npgt_kabkota',
                'spatial.v_d_npgt_kec' => 'npgt_kec_polygon',

        ];

        $nama_download = [
             'spatial.v_d_npgt_kec_a' => 'NPGT Kecamatan Administrasi A'  ,
             'spatial.v_d_npgt_kec_o' => 'NPGT Kecamatan Gambaran Umum Penguasaan Tanah O'  ,
             'spatial.v_d_npgt_kec_k_polyline' => 'NPGT Kecamatan Jalan K'  ,
             'spatial.v_d_kemampuan_tanah' => 'Kemampuan Tanah'  ,
             'spatial.v_d_npgt_kec_n' => 'NPGT Kecamatan Kesesuaian Penggunaan Tanah Terhadap RTRW N'  ,
             'spatial.v_d_npgt_kec_v' => 'NPGT Kecamatan Ketersediaan Tanah V'  ,
             'spatial.v_d_lahanbaku' => 'Lahan Baku Sawah'  ,
             'spatial.v_d_mppt' => 'Monitoring Perubahan Penggunaan Tanah'  ,
             'spatial.v_d_npgt_perkebunan' => 'NPGT Perkebunan'  ,
             'spatial.v_d_npgt_prov' => 'NPGT Provinsi'  ,
             'spatial.v_d_ptp_tnh_timbul' => 'PSRP Tanah Timbul'  ,
             'spatial.v_d_ptpil_prov_tk' => 'PTP IL Provinsi Point'  ,
             'spatial.v_d_ptpil_prov' => 'PTP IL Provinsi Polygon'  ,
             'spatial.v_d_ptp_p3t' => 'PTP IPPT Provinsi'  ,
             'spatial.v_d_ptp_non_berusaha' => 'PTP P KKPR Non Berusaha'  ,
             'spatial.v_d_ptp_berusaha' => 'PTP P KKPR Berusaha'  ,
             'spatial.v_d_ptp_stranas' => 'PTP P KKPR Stranas'  ,
             'spatial.v_d_ptp_pk_p3t' => 'PTP PK P3T'  ,
             'spatial.v_d_npgt_kec_g' => 'NPGT Kecamatan Penggunaan Tanah tahun lama G'  ,
             'spatial.v_d_npgt_kec_q' => 'NPGT Kecamatan Penggunaan Tanah terakhir Q'  ,
             'spatial.v_d_npgt_kec_gq' => 'NPGT Kecamatan Perubahan Penggunaan Tanah GQ'  ,
             'spatial.v_d_pgtl_rtrw' => 'PGT Lainya RTRW'  ,
             'spatial.v_d_npgt_kec_w' => 'NPGT Kecamatan Rencana Tata Ruang Wilayah Kabupaten/Kota W'  ,
             'spatial.v_d_npgt_kec_h_polygon' => 'NPGT Kecamatan Sungai Polygon H'  ,
             'spatial.v_d_npgt_kec_h_polyline' => 'NPGT Kecamatan Sungai Polyline H'  ,
             'spatial.v_d_tn_tntk' => 'Tanah Kritis'  ,
             'spatial.v_d_tn_tnbh' => 'Tanah Negara Bekas Hak'  ,
             'spatial.v_d_tn_tnbk' => 'Tanah Negara Bekas Kawasan'  ,
             'spatial.v_d_wp3wt_penataan_perbatasan' => 'WP3WT Penataan Perbatasan'  ,
             'spatial.v_d_wp3wt_penataan_pesisir' => 'WP3WT Penataan Pesisir'  ,
             'spatial.v_d_wp3wt_penataan_pulau_kecil' => 'WP3WT Penataan Pulau Kecil'  ,
             'spatial.v_d_wp3wt_ppk_point' => 'WP3WT Penataan Pulau-Pulau Kecil Point'  ,
             'spatial.v_d_wp3wt_ppk_polygon' => 'WP3WT Penataan Pulau-Pulau Kecil Polygon'  ,
             'spatial.v_d_wp3wt_ppkt' => 'WP3WT Penataan Pulau-Pulau Kecil Terluar'  ,
             'spatial.v_d_wp3wt_penataan_wilayah_tertentu' => 'WP3WT Penataan Wilayah Tertentu'  ,
             'spatial.v_d_wp3wt_tnh_timbul' => 'WP3WT Tanah Timbul'  ,
             'spatial.v_d_pgtl_desa' => 'PGT Lainya Wilayah Administrasi Desa'  ,
             'spatial.v_d_pgtl_kecamatan' => 'PGT Lainya Wilayah Administrasi Kecamatan'  ,
             'spatial.v_d_npgt_kabkota' => 'NPGT Kabkota', 
             'spatial.v_d_npgt_kec' => 'NPGT Kecamatan' 

        ];

        // // echo $layer;
        // echo $table[$layer];
        // echo $nama_download[$layer];
        // exit();
        $nama=strtolower(str_replace(' ','_', $nama_download[$layer]));
        $layer= $table[$layer];
        $this->db->select('wadmkk');
        $this->db->where('kdpkab', $kdpkab);
        $kab = $this->db->get('spatial.batas_admin_indonesia')->row_array()['wadmkk'];
        $kab = str_replace(' ','_',$kab);
        
        
        $nama_dir = FCPATH . 'upload_shp/template_shp/';
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'tahun_data');
        $cek = $this->db->get('information_schema.columns')->row_array();
        
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'thndata');
        $cek2 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'thnkeg');
        $cek3 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'thn_data');
        $cek4 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$layer);
        $this->db->where('column_name', 'thnkgt');
        $cek5 = $this->db->get('information_schema.columns')->row_array();
   
        
        if(!empty($cek)){
            $param = $cek['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null ";
                $nama = strtolower($nama.'_'.$kab);
            }

        }elseif(!empty($cek2)){
            $param = $cek2['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null ";
                $nama = strtolower($nama.'_'.$kab);
            }
        }elseif(!empty($cek3)){
            $param = $cek3['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null";
                $nama = strtolower($nama.'_'.$kab);
            }
        }elseif(!empty($cek4)){
            $param = $cek4['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null";
                $nama = strtolower($nama.'_'.$kab);
            }
        }elseif(!empty($cek5)){
            $param = $cek5['column_name'];
            if($tahun != 'null'){
                $where_tahun=$param." = '".$tahun."'";
                $nama = strtolower($nama.'_'.$kab.'_'.$tahun);
            }else{
                $where_tahun=$param." is null";
                $nama = strtolower($nama.'_'.$kab);
            }
        }

        
        $sql = "select * from spatial.".$layer." where ".$where_tahun." and kdpkab = '".$kdpkab."'";
        // echo $sql;exit();
        // $sql .= ' limit 10';
        $sqlGeojson = 'SELECT json_build_object(\'type\', \'FeatureCollection\', \'features\',
         json_agg(st_asgeojson(t.*)::json)) AS json_build_object
        FROM ( '.$sql.') t;';

       
    
       
        require_once(FCPATH."env.php");
        $exec =  'pgsql2shp -f '.FCPATH .'/upload_shp/template_shp/'.$nama.' -h '.WGI_DB_LRS_HOST.' -p '.WGI_DB_LRS_PORT.' -u '.WGI_DB_LRS_USER.' -P '.WGI_DB_LRS_PWD.' pgtdb "'.$sql.'" ';
        // shell_exec($exec);
        exec($exec,$a,$b);
        // echo $nama;

        // exit();
        $textGeojson='';
        try {
            // Open Shapefile
            $Shapefile = new ShapefileReader(FCPATH .'/upload_shp/template_shp/'.$nama);
            
            // Read all the records
            $str= '{"type" : "FeatureCollection", "features" : [';
            while ($Geometry = $Shapefile->fetchRecord()) {
                // Skip the record if marked as "deleted"
                if ($Geometry->isDeleted()) {
                    continue;
                }
                
                 // Print Geometry as an Array
                // print_r($Geometry->getArray());
                
                // echo "<pre>";
                // print_r ($Geometry->getArray());
                // echo "</pre>";
                
                
                // Print Geometry as WKT
                // print_r($Geometry->getWKT());
                // exit();
                // Print Geometry as GeoJSON
                // print_r($Geometry->getGeoJSON());
                
                // Print DBF data
                
                $text  = '{"type": "Feature", "geometry":'.$Geometry->getGeoJSON().',"properties":';
                    
                    // echo "<pre>";
                    // print_r ($text);
                    // echo "</pre>";
                    
                $data = $Geometry->getDataArray();
                $a=[];
                foreach ($data as $key => $value) {
                    $k = strtolower($key);
                    $a[$k]=$value;   
                }
                $text .=json_encode($a); 
                
                // echo "<pre>";
                // print_r ($a);
                // echo "</pre>";exit();
                $str .= $text.'},';
                
                // print_r(json_encode($a));
                
            }

            $str = substr($str,0,-1);
            $str .= ']}';

            
            $textGeojson=$str;
        
        } catch (ShapefileException $e) {
            // Print detailed error information
            echo "Error Type: " . $e->getErrorType()
                . "\nMessage: " . $e->getMessage()
                . "\nDetails: " . $e->getDetails();
        }

        $fileGeo =  FCPATH .'/upload_shp/template_shp/'.$nama.'.geojson';
        // echo "<pre>";
        // print_r ($textGeojson);
        // echo "</pre>";
        $cekFiles = glob($nama_dir.$nama.".*");
        if(!empty($cekFiles)){
            foreach ($cekFiles as $key => $value) {
                unlink($value);
            }
        }
        $content = $textGeojson;
        $fp = fopen($fileGeo,"wb");
        fwrite($fp,$content);
        fclose($fp);
        
        $files = glob($nama_dir.$nama.".*");
        foreach ($files as $key => $value) {
            chmod($value, 0777);
        }
        // ============== proses ziping file =========
        
        $zipname = $nama_dir.$nama.'.zip';
        
        $zip = new ZipArchive;
        $zip->open($zipname, ZipArchive::CREATE);
        foreach ($files as $file) {
            $new_filename = substr($file,strrpos($file,'/') + 1);
            // echo $file;
            // if()
            $zip->addFile($fileGeo,$new_filename);
        }
        $zip->close();
        // //=== download file ===
        $file_content = file_get_contents($zipname);
        
        $nama =strtoupper(str_replace('_',' ',$nama));
        force_download($nama.'.zip',$file_content);
  
        
    }

    
    
    
    public function downloadShpKec($layer,$kdpkab,$tahun)
    {
        $table=[
                'spatial.v_d_npgt_kec_a' => 'npgt_kec_a',
                'spatial.v_d_npgt_kec_o' => 'npgt_kec_o',
                'spatial.v_d_npgt_kec_w' => 'npgt_kec_w',
                'spatial.v_d_npgt_kec_h_polygon' => 'npgt_kec_h_polygon',
                'spatial.v_d_npgt_kec_h_polyline' => 'npgt_kec_h_polyline',
                'spatial.v_d_npgt_kec_k_polyline' => 'npgt_kec_k_polyline',
                'spatial.v_d_npgt_kec_n' => 'npgt_kec_n',
                'spatial.v_d_npgt_kec_q' => 'npgt_kec_q',
                'spatial.v_d_npgt_kec_v' => 'npgt_kec_v',
                
        ];

        $nama_download = [
             'spatial.v_d_npgt_kec_a' => 'NPGT Kec Administrasi A'  ,
             'spatial.v_d_npgt_kec_o' => 'NPGT Kec Gambaran Umum Penguasaan Tanah O'  ,
             'spatial.v_d_npgt_kec_k_polyline' => 'NPGT Kec Jalan K'  ,
             'spatial.v_d_npgt_kec_n' => 'NPGT Kec Kesesuaian Penggunaan Tanah Terhadap RTRW N'  ,
             'spatial.v_d_npgt_kec_v' => 'NPGT Kec Ketersediaan Tanah V'  ,
             'spatial.v_d_npgt_kec_q' => 'NPGT Kec Penggunaan Tanah terakhir Q'  ,
             'spatial.v_d_npgt_kec_w' => 'NPGT Kec Rencana Tata Ruang Wilayah Kabupaten/Kota W'  ,
             'spatial.v_d_npgt_kec_h_polygon' => 'NPGT Kec Sungai Polygon H'  ,
             'spatial.v_d_npgt_kec_h_polyline' => 'NPGT Kec Sungai Polyline H'  ,
             
        ];

            $nama=strtolower(str_replace(' ','_', $nama_download[$layer]));
            $layer= $table[$layer];
        
        $this->db->select('wadmkk');
        $this->db->where('kdpkab', $kdpkab);
        $kab = $this->db->get('spatial.batas_admin_indonesia')->row_array()['wadmkk'];
        $kab = str_replace(' ','_',$kab);
        $nama_dir = FCPATH . 'upload_shp/template_shp/';
        $namanya = strtolower($nama.'_'.$kab.'_'.$tahun);
        if($tahun == 'null'){
            $where_tahun = ' tahun_data is null ';
            $namanya = strtolower($nama.'_'.$kab);
        }else{
            $where_tahun = " tahun_data = '".$tahun."' ";
            $namanya = strtolower($nama.'_'.$kab.'_'.$tahun);

        }
        $nama = $namanya;
        $sql = "select * from spatial.".$layer." where ".$where_tahun." and kdpkab = '".$kdpkab."'";
        // echo $sql;exit();
        $exec =  'pgsql2shp -f '.FCPATH .'/upload_shp/template_shp/'.$nama.' -h localhost -p 5432 -u wgi -P pgDevDiAwan pgtdb "'.$sql.'" ';
        // shell_exec($exec);
        // echo $sql;
        
 
        exec($exec,$a,$b);
        $files = glob($nama_dir.$nama.".*");
        foreach ($files as $key => $value) {
            chmod($value, 0777);
        }
        echo $nama_dir.$nama;
        // ============== proses ziping file =========
        
        $zipname = $nama_dir.$nama.'.zip';
        
        $zip = new ZipArchive;
        $zip->open($zipname, ZipArchive::CREATE);
        foreach ($files as $file) {
            $new_filename = substr($file,strrpos($file,'/') + 1);
            // echo $file;
            $zip->addFile($file,$new_filename);
        }
        $zip->close();

        // //=== download file ===
        $file_content = file_get_contents($zipname);
        $cekFiles = glob($nama_dir.$nama.".*");
        if(!empty($cekFiles)){
            foreach ($cekFiles as $key => $value) {
                unlink($value);
            }
        }
        $nama =strtoupper(str_replace('_',' ',$nama));
        force_download($nama.'.zip',$file_content);
  
        
    }


    public function offDisclaimer(Type $var = null)
    {
        $this->session->set_userdata('disclaimer', ['disclaimer' => 'off']); 
    }

    public function getLanding()
    {
        $data = $this->db->get('landing_page')->row_array();
        echo json_encode($data);
    }
    public function getUserManual()
    {
        $role =  $this->session->userdata('users')['id_user_group'];
        if ($role == 7 || $role == 8) {
            $this->db->where('jabatan', 3);
        }
        if ($role == 9 || $role == 10) {
            $this->db->where('jabatan', 2);
        }
        $datas = $this->db->get('user_manual')->result();
        $data=[];
        $a = '<tr>
                <th>No</th>
                <th>Nama</th>
                <th>Jenis</th>
                <th>Aksi</th>
            </tr>';
        foreach ($datas as $key => $value) {
            $jns = $value->jns == 1 ? 'pdf':'Vidio';
            $no = $key+1;
            $a.= '<tr><td>'.$no.'</td><td>'.$value->name.'</td><td>'.$jns.'</td><td><button type="button" class="btn btn-sm btn-primary" onclick="downloadUM('.$value->id.')">Download</button></td><tr>';
        }
        array_push($data,$a);
        echo json_encode($data);
    }

    function downloadUM($id) {
        $this->db->where('id', $id);
        $data = $this->db->get('user_manual')->row_array();
        
        echo "<pre>";
        print_r ($data);
        echo "</pre>";
        $file_content = file_get_contents($data['path']);
        $fileParts = explode('.', $data['path']);
        $ext = end($fileParts);
        $nama =strtoupper(str_replace('_',' ',$data['name']));
        force_download($nama.'.'.$ext,$file_content);
        
    }

    public function getYear($layer)
    {
        $this->db->select('tahun_data');
        $this->db->where('tahun_data is not null');
        $this->db->group_by('tahun_data');
        $this->db->order_by('tahun_data', 'desc');
        
        $data= $this->db->get($layer)->result();
        $str = '<option value="">Semua Tahun</option>';
        foreach ($data as $key => $value) {
            if ($value->tahun_data == '') {
                // $str .='<option value="null">Tidak Ada Tahun</option>';
            }else{
                $str .='<option value="'.@$value->tahun_data.'">'.@$value->tahun_data.'</option>';
            }         
        }
        echo json_encode(['year'=>$str]);
       
    
    }

    function getProvKab($kdkab) {
        $kdProv= substr($kdkab,0,2);

        $this->db->where('kd_prov', $kdProv);
        $prov = $this->db->get('aset_r_provinsi')->row_array();
        
        $this->db->where('kd_kabkot', $kdkab);
        $kab = $this->db->get('aset_r_kabkota')->row_array();
        
        
        echo json_encode(['prov' => $prov,'kab' => $kab]);
        
    }

    function getChildHistory($id,$view) {
        $this->db->where('id_hist', $id);
        $data = $this->db->get('spatial.'.$view)->row_array();
        $skip = ['created_by','created_at','updated_by','updated_at','operation	','id_hist','gid','objectid','operation','proses','oleh','waktu'];
        $arr = [
                'kd_bidang' =>'Kode Bidang', 
                'nib' =>'NIB', 
                'sk' =>'SK', 
                'tanggal_sk' =>'Tanggal SK', 
                'ket' =>'Keterangan',  
                'aprogram' =>'', 
                'prof_ktp' =>'',  
                'prof_riil' =>'',  
                'jml_kk' =>'Jumlah KK', 
                'shape_leng' =>'Shape Leng', 
                'shape_area' =>'Shape Area', 
                'wadmkd' =>'Nama Desa', 
                'wadmkc' =>'Nama Kecamatan', 
                'wadmkk' =>'Nama Kota/Kabupaten', 
                'wadmpr' =>'Nama Provinsi',  
                'bdn_hukum' =>'Badan Hukum',  
                'rcnkeg' =>'Rencana Kegiatan', 
                'noptp' =>'Nomor PTP', 
                'luas_ha' =>'Luas Ha', 
                'luasm2' =>'Luas M2', 
                'jptp' =>'Jenis PTP', 
                'tglptp' =>'Tanggal PTP', 
                'hslptp' =>'Hasil PTP',  
                'kdpkab' =>'Kode Kabupaten', 
                'kdppum' =>'Kode Provinsi', 
                'thndata' =>'Tahun Data', 
                'namprh' =>'Nama Perusahaan ', 
                'kdkbli' =>'Kode KBLI', 
                'kbli' =>'KBLI', 
            ];
        $str = '<table style="width:100%" class="table">';
        foreach ($data as $key => $value) {
            if (!in_array($key, $skip)) {
                $title = array_key_exists($key, $arr) ? $arr[$key] : ucfirst($key);
                if($title != '' && $value != ''){
                    if($key == 'geom'){
                        $value = substr($value,0,90).'....))';
                    }
                    $str .= '<tr style="width:100%;border-bottom :1px solid gray">'.
                    '<td style="width:25%"><b>'.$title.'</b></td>'.
                    '<td>: '.$value.'</td>'.
                    '</tr>';
                }
            }
        }
        $str .= '</table>';
        echo json_encode($str);
    }

    
    public function ssp_paket_hist($kdkab,$view,$tahun='') {
      
        $table = 'spatial.'.$view;
        $primaryKey = 'gid'; //test     

        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$view);
        $this->db->where('column_name', 'tahun_data');
        $cek = $this->db->get('information_schema.columns')->row_array();
        
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$view);
        $this->db->where('column_name', 'thndata');
        $cek2 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$view);
        $this->db->where('column_name', 'thnkeg');
        $cek3 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$view);
        $this->db->where('column_name', 'thn_data');
        $cek4 = $this->db->get('information_schema.columns')->row_array();
        $this->db->select('column_name');
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name',$view);
        $this->db->where('column_name', 'thnkgt');
        $cek5 = $this->db->get('information_schema.columns')->row_array();
        
        $where="kdpkab ='".$kdkab."'";

        
        
        
        if(!empty($cek)){
            $param = $cek['column_name'];
            if($tahun != 'null'){
                $where="kdpkab ='".$kdkab."' and ".$param." = '".$tahun."'";
            }else{
                $where="kdpkab ='".$kdkab."' and ".$param." is null ";
            }

        }elseif(!empty($cek2)){
            $param = $cek2['column_name'];
            if($tahun != 'null'){
                $where="kdpkab ='".$kdkab."' and ".$param." = '".$tahun."'";
            }else{
                $where="kdpkab ='".$kdkab."' and ".$param." is null ";
            }
        }elseif(!empty($cek3)){
            $param = $cek3['column_name'];
            if($tahun != 'null'){
                $where="kdpkab ='".$kdkab."' and ".$param." = '".$tahun."'";
            }else{
                $where="kdpkab ='".$kdkab."' and ".$param." is null";
            }
        }elseif(!empty($cek4)){
            $param = $cek4['column_name'];
            if($tahun != 'null'){
                $where="kdpkab ='".$kdkab."' and ".$param." = '".$tahun."'";
            }else{
                $where="kdpkab ='".$kdkab."' and ".$param." is null";
            }
        }elseif(!empty($cek5)){
            $param = $cek5['column_name'];
            if($tahun != 'null'){
                $where="kdpkab ='".$kdkab."' and ".$param." = '".$tahun."'";
            }else{
                $where="kdpkab ='".$kdkab."' and ".$param." is null";
            }
        }
        
        
        $columns = array(
            array('db' => 'gid', 'dt' => 0),
            array('db' => 'proses', 'dt' => 1),
            array('db' => 'oleh' , 'dt'  =>2) , 
            array('db' => 'waktu' , 'dt'  =>3) , 
            array('db' => 'id_hist' , 'dt'  =>4) , 
        );

        

         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);

        
    }

    function fieldlookSelectProv($table,$id,$val)  {
        $this->db->where($id, $val);
        $data = $this->db->get($table)->result();
        echo json_encode($data);
        
    }
    function fieldlookSelect($table,$id,$val,$text)  {
        $this->db->select('*,'.$text.' as text');
        $this->db->where($id, $val);
        $data = $this->db->get($table)->result();
        echo json_encode($data);
        
    }

    function monitoringSHp()  {

        // try {
            
            // 
            $jsonData = $this->input->post('json',true);  
            // $jsonData = file_get_contents('assets/data.geojson');
            // echo json_encode($jsonData);
            // exit();
          $data = json_decode($jsonData, true);
      
            
          
          
          $polygonCoords = $data['data_pertek']['data_geo']['coordinates'][0];
          $coords_array = $data;
          
          $files = glob("uploads/".$data['id_proyek'].".*");
          foreach ($files as $key => $value) {
              unlink($value);
          }
          

          // Open Shapefile
          $Shapefile = new ShapefileWriter('uploads/'.$data['id_proyek'].'.shp');
          
          // Set shape type
          $Shapefile->setShapeType(Shapefile::SHAPE_TYPE_POLYGON);
          
          $Polygon = new Polygon();
          
              // Create a Linestring (ring) for the polygon
              $Linestring = new Linestring();
              foreach ($polygonCoords as $coords) {
                // Add points to the Linestring
                $Linestring->addPoint(new Point($coords[1], $coords[0]));
              }
              foreach ($coords_array as $i => $coords) {
                if ($i != 'data_pertek' && $i != 'data_geo' && $i != 'data_validasi' ) {
                  $Shapefile->addCharField($i, 100);
                }
              }
                  
              foreach ($coords_array['data_validasi'] as $i =>  $c) {
                $i = strtolower($i) == 'id_proyek' ? 'id_pr' : $i;
                $i = strtolower($i) == 'id_proyek_lokasi' ? 'id_prl' : $i;
                // echo $i.' |';
                
                $Shapefile->addCharField($i, 100);
              }
              foreach ($coords_array['data_pertek'] as $i =>  $c) {
                  if ($i != 'data_pnbp' && $i != 'data_geo' && $i != 'id_proyek' && $i != 'id_proyek_lokasi' && $i != 'nama_daerah') {
                    // echo $i.' | ';
                  $i = strtolower($i) == 'id_proyek' ? 'id_pr' : $i;
                  $i = strtolower($i) == 'id_proyek_lokasi' ? 'id_prl' : $i;
                  $Shapefile->addCharField($i, 100);

                }
              }
              
              // Add the Linestring to the Polygon as the outer boundary
              $Polygon->addRing($Linestring);
              foreach ($coords_array as $i => $co) {
                if ($i != 'data_pertek' && $i != 'data_geo' && $i != 'data_validasi' ) {
                  $i = substr(@$i,0,10);  
                  $Polygon->setData($i, $co);
                }
              }
              foreach ($coords_array['data_validasi'] as $i =>  $c) {
                if ($i != 'data_pnbp' && $i != 'data_geo'  && $i != 'nama_daerah') {
                  $i = strtolower($i) == 'id_proyek' ? 'id_pr' : $i;
                  $i = strtolower($i) == 'id_proyek_lokasi' ? 'id_prl' : $i;
                  $i = substr(@$i,0,10);  
                  $Polygon->setData($i, $c);
                }
              }
              foreach ($coords_array['data_pertek'] as $i =>  $c) {
                if ($i != 'data_pnbp' && $i != 'data_geo'  && $i != 'id_proyek' && $i != 'id_proyek_lokasi' ) {
                  $i = substr(@$i,0,10);  
                //   echo $i.' | ';
                  $Polygon->setData($i, $c);
                }
              }
              
      
              $Shapefile->writeRecord($Polygon);
                $Shapefile = null;
              

              $files = glob("uploads/".$data['id_proyek'].".*");
              foreach ($files as $key => $value) {
                  chmod($value, 0777);
              }


                // ============== proses ziping file =========
                
                // $zipname = 'uploads/'.$data['id_proyek'].'.zip';
                // $zip = new ZipArchive;
                // if ($zip->open($zipname, ZipArchive::CREATE | ZipArchive::OVERWRITE) === true) {
                //     foreach ($files as $file) {
                //         $new_filename = basename($file); // Get the filename without the directory path
                //         if ($zip->addFile($file, $new_filename) === false) {
                //             echo "Failed to add file: $file";
                //         }
                //     }
                //     $zip->close(); 
                //     echo "ZIP file created successfully.";
                // } else {
                //     echo "Failed to create ZIP file.";
                // }




                //=== download file ===
                // $file_content = file_get_contents($zipname);
                // $cekFiles = glob("uploads/".$data['id_proyek'].".*");
                // if(!empty($cekFiles)){
                //     foreach ($cekFiles as $key => $value) {
                //         unlink($value);
                //     }
                // }
                // force_download($data['id_proyek'].'.zip',$file_content);
                $nama_dir = 'uploads/';
                $nama = $data['id_proyek'];
                $files = glob($nama_dir.$nama.".*");
                foreach ($files as $key => $value) {
                    chmod($value, 0777);
                }
                // ============== proses ziping file =========
                
                $zipname = $nama_dir.$nama.'.zip';
                $zip = new ZipArchive;
                $zip->open($zipname, ZipArchive::CREATE);
                foreach ($files as $file) {
                    $new_filename = substr($file,strrpos($file,'/') + 1);
                    // echo $file;
                    $zip->addFile($file,$new_filename);
                }
                $zip->close();
        
                // //=== download file ===
                $file_content = file_get_contents($zipname);
                $cekFiles = glob($nama_dir.$nama.".*");
                if(!empty($cekFiles)){
                    foreach ($cekFiles as $key => $value) {
                        // unlink($value);
                    }
                }
                $nama =strtoupper(str_replace('_',' ',$nama));
                // force_download($nama.'.zip',$file_content);
                force_download($data['id_proyek'].'.zip',$file_content);
                
                
                
              
            //   echo "<pre>";
            //   print_r ($data);
            //   echo "</pre>";exit();
            // }
            
          // Finalize and close files to use them
      
    //   } catch (ShapefileException $e) {
    //       // Print detailed error information
    //       echo "Error Type: " . $e->getErrorType()
    //           . "\nMessage: " . $e->getMessage()
    //           . "\nDetails: " . $e->getDetails();
    //   }

    }

    function monitoringShpDownload($v)  {
        $file_content = file_get_contents('uploads/'.$v.'.zip');
        force_download($v.'.zip',$file_content);

        
    }

    function unlinkShp($v) {
        $cekFiles = glob('uploads/'.$v.".*");
        if(!empty($cekFiles)){
            foreach ($cekFiles as $key => $value) {
                unlink($value);
            }
        }
    }

    function getParentMenu($menu='') {
        $menu = $menu == 'frame' ? 'dashboard4/frame' : $menu;  
        $this->db->select('kode_module, url, parent');
        $this->db->where('url', $menu);
        $data = $this->db->get('aset_module')->row_array();

        if ($data['parent'] == '#') {
            $this->session->unset_userdata('menu');
            $sesMenu = array(
                'menu' => $data['kode_module'],
            );
            $this->session->set_userdata($sesMenu);
            echo json_encode($data);
        }else{
            
            $data = $this->cek_parent($data['parent']);
            $this->session->unset_userdata('menu');
            $sesMenu = array(
                'menu' => $data['kode_module'],
            );
            $this->session->set_userdata($sesMenu);
            echo json_encode($data);
        }

    }

    function cek_parent($kd) {
        $this->db->where('kode_module', $kd);
        $data = $this->db->get('aset_module')->row_array();
        if ($data['url'] == '#') {
            return $data;
        }else{
            return $this->cek_parent($data['parent']);
        }
    }

    public function getUltimateParent($id) {
        $this->db->select('id, name, parent_id');
        $this->db->from('your_table');
        $this->db->where('id', $id);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $row = $query->row_array();
            if ($row["parent_id"] != 0) {
                // If the record has a parent, recursively get its parent
                return $this->getUltimateParent($row["parent_id"]);
            } else {
                // If the record has no parent, it is the ultimate parent
                return $row;
            }
        } else {
            return false;
        }
    }

    function get_metadata($prov,$tematik) {
        

        $this->db->where('url', $tematik);
        $tema = $this->db->get('v_tematik')->row_array();
        if (!empty($tema)) {
            $this->db->where('kdppum', $prov);
            $this->db->where('id_tematik', $tema['id_layer']);
            $data = $this->db->get('metadata')->row_array();
            echo json_encode($data);
        } else {
            echo json_encode([]);
        }
        
    }

    function get_metadata_select($prov,$tematik) {
        

        $this->db->where('v_dasboard', $tematik);
        $tema = $this->db->get('v_tematik')->row_array();
        if (!empty($tema)) {
            $this->db->where('kdppum', $prov);
            $this->db->where('id_tematik', $tema['id_layer']);
            $data = $this->db->get('metadata')->row_array();
            echo json_encode($data);
        } else {
            echo json_encode([]);
        }
        
    }
}