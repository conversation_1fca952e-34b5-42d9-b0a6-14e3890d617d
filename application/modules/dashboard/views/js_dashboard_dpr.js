< script type = "text/javascript" > ﻿

    'use strict'

var dtable = null;

$(document).ready(function() {



    $("body").niceScroll({
        cursorcolor: "#ddd",
        cursorwidth: 5,
        cursorborderradius: 0,
        cursorborder: 0,
        scrollspeed: 50,
        autohidemode: true,
        zindex: 9999999
    });


    //  var colors_q12 = [
    //    [
    //      'rgba(166,206,227,0.8)',
    //      'rgba(31,120,180,0.8)',
    //      'rgba(178,223,138,0.8)',
    //      'rgba(51,160,44,0.8)',
    //      'rgba(251,154,153,0.8)',
    //      'rgba(227,26,28,0.8)',
    //      'rgba(253,191,111,0.8)',
    //      'rgba(255,127,0,0.8)',
    //      'rgba(202,178,214,0.8)',
    //      'rgba(106,61,154,0.8)',
    //      'rgba(255,255,153,0.8)',
    //      'rgba(177,89,40,0.8)'
    //    ],
    //    [
    //      'rgba(141,211,199,0.8)',
    //      'rgba(255,255,179,0.8)',
    //      'rgba(190,186,218,0.8)',
    //      'rgba(251,128,114,0.8)',
    //      'rgba(128,177,211,0.8)',
    //      'rgba(253,180,98,0.8)',
    //      'rgba(179,222,105,0.8)',
    //      'rgba(252,205,229,0.8)',
    //      'rgba(217,217,217,0.8)',
    //      'rgba(188,128,189,0.8)',
    //      'rgba(204,235,197,0.8)',
    //      'rgba(255,237,111,0.8)'
    //    ]
    //  ];


    //ambil hidden value jadi array JS
    var vlabels = JSON.parse($('#chart_prov_labels').val().replace(/'/g, '"'));
    var vcolors = JSON.parse($('#chart_prov_colors').val().replace(/'/g, '"'));
    var vvals = JSON.parse($('#chart_prov_vals').val().replace(/'/g, '"'));


    var ctx = document.getElementById("bar-chart").getContext('2d');
    var myChart = new Chart(ctx, {

        type: 'bar',
        data: {

            labels: vlabels,
            datasets: [{
                //label: 'Jumlah Usulan',
                data: vvals,
                backgroundColor: vcolors,
                borderColor: vcolors,
                borderWidth: 1
            }]
        },
        options: {
            legend: {
                display: false
            },
            scales: {
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    },
                    scaleLabel: {
                        display: true,
                        labelString: "Jumlah Usulan",

                    }
                }],
                xAxes: [{
                    // Change here
                    barPercentage: 0.1
                }]
            }
        }
    });


    var vlabels = JSON.parse($('#chart_fraksi_labels').val().replace(/'/g, '"'));
    var vcolors = JSON.parse($('#chart_fraksi_colors').val().replace(/'/g, '"'));
    var vvals = JSON.parse($('#chart_fraksi_vals').val()); //gak perlu di-replace karena val ini array of string



    var ctx2 = document.getElementById("bar-chart2").getContext('2d');
    var myChart2 = new Chart(ctx2, {
        type: 'pie',
        data: {
            labels: vlabels,
            datasets: [{
                label: 'Jumlah Usulan',
                data: vvals,
                backgroundColor: vcolors,
                borderColor: vcolors,
                borderWidth: 1
            }]
        },
        options: {



            onClick: function(evt) {

                var firstPoint = myChart2.getElementAtEvent(evt)[0];

                if (firstPoint) {
                    var lbl = myChart2.data.labels[firstPoint._index];
                    var val = myChart2.data.datasets[firstPoint._datasetIndex].data[firstPoint._index];
                    console.log('label: ' + lbl + ', val: ' + val);
                }


            },



            //},
            legend: {
                display: true
            },
            scales: {
                //        yAxes: [{
                //          ticks: {
                //            beginAtZero: true
                //          },
                //            scaleLabel: {//
                //              display: false,
                //              labelString: "Jumlah Usulan",
                //
                //            }
                //}]
            }
        }
    });


    var vlabels = JSON.parse($('#chart_dapil_labels').val().replace(/'/g, '"'));
    var vcolors = JSON.parse($('#chart_dapil_colors').val().replace(/'/g, '"'));
    var vvals = JSON.parse($('#chart_dapil_vals').val()); //gak perlu di-replace karena val ini array of string


    //  var ctx3 = document.getElementById("bar-chart3").getContext('2d');
    //  var myChart3 = new Chart(ctx3, {
    //    type: 'bar',
    //    data: {
    //      labels: vlabels,
    //      datasets: [{
    //        label: 'Jumlah Usulan',
    //        data: vvals,
    //        backgroundColor: vcolors,
    //        borderColor: vcolors,
    //        borderWidth: 1
    //}]
    //    },
    //    options: {
    //            legend: {
    //            display: false
    //},
    //      scales: {
    //        yAxes: [{
    //          ticks: {
    //            beginAtZero: true
    //},
    //            scaleLabel: {
    //              display: true,
    //              labelString: "Jumlah Usulan",

    //}
    //        }]
    //      }
    //    }
    //  });


    $('.ui.submit.button').on('click', function() {
        alert('hello');
    })

    $('.ui.submit.button').on('click', function() {
        alert('hello');
    })

    //modal page modals trigger settings
    $(".ui.modal.standard").modal("attach events", ".modalone", "show");
    $(".ui.modal.basic").modal("attach events", ".modaltwo", "show");
    $(".ui.modal.fullscreen").modal("attach events", ".modalthree", "show");
    $(".ui.modal.small").modal("attach events", ".modalfour", "show");
    $(".ui.modal.large").modal("attach events", ".modalfive", "show");
    $(".ui.modal.long").modal("attach events", ".modalsix", "show");
    $(".modaleffect").on("click", function() {
        var a = $(this).attr("data-value");
        $(".ui.modal.standard").modal("setting", "transition", a).modal("show");
    });
    $(".modalinverted").on("click", function() {
        $(".ui.modal.inverted").modal({
            inverted: true,
            blurring: false
        }).modal("show");
    });
    $(".modalblur").on("click", function() {
        $(".ui.modal.standard").modal({
            blurring: true,
            inverted: false
        }).modal("show");
    });
    $(".modalactionone").on("click", function() {

        console.log('step 1');

        if ($.fn.dataTable.isDataTable('.table')) {
            dtable = $('.table').DataTable();
        } else {
            dtable = $('.table').DataTable({
                scrollY: '50vh',
                scrollCollapse: true,
                "pagingType": "full_numbers_icon",
                order: [1, 'desc'],
            });
        }




        $(".ui.actionmodal").modal({
            closable: false,
            onDeny: function() {
                window.alert("OOooopps! You must read this..");
                return false;
            },
            onApprove: function() {
                // window.alert("Yess He is a Best Footballer in Holland now!");
            }
        }).modal("show");
        console.log('step 1c');
    });



});