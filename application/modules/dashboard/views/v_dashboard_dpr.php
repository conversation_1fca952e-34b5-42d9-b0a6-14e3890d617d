<html>

    <head>
        <!-- Standard Meta -->
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
        <link rel="image_src" type="image/jpeg" href="/images/logo.png" />
        <link rel="icon" href="img/favicon.ico" type="image/x-icon" />
        <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon" />
        <!-- Site Properities -->
        <meta name="generator" content="Visual Studio 2015" />
        <title>Charts - v2 | Golgi Admin</title>
        <meta name="description" content="Golgi Admin Theme" />
        <meta name="keywords" content="html5, ,semantic,ui, library, framework, javascript,jquery,admin,theme" />
        <!-- <link href="plugins/chartist/chartist.min.css" rel="stylesheet" /> -->
        <link href="<?php echo base_url() . 'assets/semantic/dist/semantic.css'; ?>" rel="stylesheet" />
        <link href="<?php echo base_url() . 'assets/semantic/css/main.min.css'; ?>" rel="stylesheet" />
        <style>
            .containerli {
                margin-top: 5px;
                margin-bottom: 5px;
            }

            .ui.segments>.segment {
                border: none;
            }  
            .yuh{
                float: left; 
                padding: 10px;
                color: white;
                font-weight: bold;
                font-size: 15px;
            }
        </style>


        <link href="<?php echo base_url() . 'assets/semantic/plugins/pacejs/pace.css'; ?>" rel="stylesheet" />

    </head>



    <body id="framebody" class="admin">

        <div class="pusher">
            <div class="full height">
                <!--Load Sidebar Menu In App.js loadhtml function-->
                <!-- <div class="toc"></div> -->
                <!--Load Sidebar Menu In App.js loadhtml function-->

                <div class="article">

                    <!--Load Navbar Menu In App.js loadhtml function-->
                    <!-- <div class="navbarmenu"></div> -->
                    <!--Load Navbar Menu In App.js loadhtml function-->
                    <!--Begin Container-->
                    <div class="containerli">
                        <div class="ui equal width left aligned padded grid stackable">

                            <div class="row">
                                <div class="column">

                                    <div class="field">
                                        <div class="yuh"><i class="fa fa-calendar"></i> Tahun Anggaran</div>    <!--                                            <label>Tahun Anggaran</label>-->
<!--                                        <form action="<?php echo base_url(); ?>dashboard/frame" method="post">
                                            <select id="thang1" name="tah" class="ui fluid dropdown" style="width:auto;margin-left: 100px;"  onchange="this.form.submit()">
                                                <option value="">--Pilih--</option>
                                                <option value="2018" <?php echo $b; ?>>2018</option>
                                                <option value="2019" <?php echo $s; ?>>2019</option>
                                                <option value="">Semua</option>
                                            </select>
                                        </form>-->
                                        <form action="<?php echo base_url(); ?>dashboard/frame" method="post">
											<input value="<?php echo $this->security->get_csrf_hash(); ?>" id="<?php echo $this->security->get_csrf_token_name(); ?>" name="<?php echo $this->security->get_csrf_token_name(); ?>" type="hidden">
                                            <select id="thang1" class="ui fluid dropdown" name="tah" style="width:auto;margin-left: 100px;" onchange="this.form.submit()">
                                                <?php
                                                $thang = $this->db->get('r_thang')->result_array();
                                                foreach ($thang as $t) {
                                                    if(!empty($tahuns))
                                                    {
                                                        if ($t['thang'] == $tahuns) {
                                                        $select = "selected";
                                                    }
                                                    else
                                                    {
                                                        $select="";
                                                    }
                                                    }
                                                    else
                                                    {
                                                        if($t['thang']==$this->session->konfig_tahun_ang){
                                                        $select = "selected";
                                                    }
                                                    else
                                                    {
                                                        $select="";
                                                    }
                                                    }
                                                    
                                                   
                                                    ?>

                                                    <option value="<?php echo $t['thang']; ?>" <?php echo $select;?> ><?php echo $t['uraian']; ?></option>
                                                            <?php
                                                        }
                                                        ?>
                                            </select>
                                                </div>
                                        </form>
                                        <div class="form-group">

                                        </div>
                                        <div class="ui segments">
                                            <div class="ui segment">
                                                <h4 id="hjumlahusulan" class="ui horizontal divider header"></h4>
                                            </div>

                                        </div>                                    


                                        <div class="ui segments">
                                            <div class ="ui segment">
                                                <h5 class="ui header">Usulan DPR Per Lokasi Kegiatan</h5>
                                            </div>
                                            <div class="ui segment">
                                                <canvas id="bar-chart"></canvas>
                                            </div>
                                        </div>
                                        <div class="ui segments">
                                            <div class="ui segment">
                                                <h5 class="ui header">Usulan DPR Per Jenis Kegiatan</h5>
                                            </div>
                                            <div class="ui segment">
                                                <canvas id="bar-chart2"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>



                                <!--                            <div class="row">
                                                                <div class="column">
                                                                    <div class="ui segments">
                                                                        <div class="ui segment">
                                                                            <h5 class="ui header">Usulan DPR Per Jenis Kegiatan</h5>
                                                                        </div>
                                                                        <div class="ui segment">
                                                                            <canvas id="bar-chart2"></canvas>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="column">
                                                                    <div class="ui segments">
                                                                        <div class="ui segment">
                                                                            <h5 class="ui header">Usulan DPR Per Dapil Pengusul</h5>
                                                                        </div>
                                                                        <div class="ui segment">
                                                                            <canvas id="bar-chart3"></canvas>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>-->

                            </div>
                        </div>
                        <!--Finish Container-->
                        <!--Load Footer Menu In App.js loadhtml function-->
                        <!-- <div class="footer"></div> -->
                        <!--Load Footer Menu In App.js loadhtml function-->
                    </div>
                </div>
            </div>


            <input type="hidden" id="chart_prov_labels" value="<?php echo $chart['prov']['labels']; ?>"/>
            <input type="hidden" id="chart_prov_colors" value="<?php echo $chart['prov']['colors']; ?>"/>
            <input type="hidden" id="chart_prov_vals"   value="<?php echo $chart['prov']['vals']; ?>"/>

            <input type="hidden" id="chart_fraksi_labels" value="<?php echo $chart['fraksi']['labels']; ?>"/>
            <input type="hidden" id="chart_fraksi_colors" value="<?php echo $chart['fraksi']['colors']; ?>"/>
            <input type="hidden" id="chart_fraksi_vals"   value="<?php echo $chart['fraksi']['vals']; ?>"/>

            <input type="hidden" id="chart_dapil_labels" value="<?php echo $chart['dapil']['labels']; ?>"/>
            <input type="hidden" id="chart_dapil_colors" value="<?php echo $chart['dapil']['colors']; ?>"/>
            <input type="hidden" id="chart_dapil_vals"   value="<?php echo $chart['dapil']['vals']; ?>"/>

            <script src="<?php echo base_url() . 'assets/semantic/js/jquery-2.1.4.min.js'; ?>"></script>
            <script src="<?php echo base_url() . 'assets/semantic/plugins/nicescrool/jquery.nicescroll.min.js'; ?>"></script>
            <script src="<?php echo base_url() . 'assets/semantic/js/chartjs/dist/Chart.bundle.min.js'; ?>"></script>
            <script src="<?php echo base_url() . 'assets/semantic/dist/semantic.min.js'; ?>"></script>
            <script data-pace-options='{ "ajax": false }' src="<?php echo base_url() . 'assets/semantic/plugins/pacejs/pace.js'; ?>"></script>

            <!--here is cumstom js loader (array of js located at base_path/asset/<js path & filename>)-->
            <?php
            foreach ($js as $j) {
                echo file_get_contents(APPPATH . $j);
            }
            ?>

            <script type="text/javascript">
                                    $(document).ready(function () {

                                        $('#hjumlahusulan').html('<i class="tag blue icon"></i>Total: <?php echo $totaldata; ?> usulan');

                                        //                console.log('frame ready');
                                        //
                                        //                console.log('start....');
                                        //                console.log(window.parent.$('#iframe-div').height());
                                        //                console.log('framebody height: ');
                                        //                var fheight = $('#framebody').height();
                                        //                var hmain = $(window).height();
                                        //                var hnav = window.parent.$('#header-navbar').height();
                                        //                console.log(hmain - hnav);
                                        //                console.log('tinggi browser ' + hmain);
                                        //                console.log('tinggi navbar ' + hnav);
                                        //                console.log('tinggi frame ' + hnav);
                                        //
                                        //                $('#framebody').css('height', hmain - hnav);
                                    });
            </script>


    </body>

</html>
