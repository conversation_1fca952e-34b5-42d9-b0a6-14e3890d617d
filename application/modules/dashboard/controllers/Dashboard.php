<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends MY_Controller {

    private $colors = [];

    public function __construct() {
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();

        $this->colors[0] = [
            'rgba(166,206,227,0.8)',
            'rgba(31,120,180,0.8)',
            'rgba(178,223,138,0.8)',
            'rgba(51,160,44,0.8)',
            'rgba(251,154,153,0.8)',
            'rgba(227,26,28,0.8)',
            'rgba(253,191,111,0.8)',
            'rgba(255,127,0,0.8)',
            'rgba(202,178,214,0.8)',
            'rgba(106,61,154,0.8)',
            'rgba(255,255,153,0.8)',
            'rgba(177,89,40,0.8)'
        ];

        $this->colors[1] = [
            'rgba(141,211,199,0.8)',
            'rgba(255,255,179,0.8)',
            'rgba(190,186,218,0.8)',
            'rgba(251,128,114,0.8)',
            'rgba(128,177,211,0.8)',
            'rgba(253,180,98,0.8)',
            'rgba(179,222,105,0.8)',
            'rgba(252,205,229,0.8)',
            'rgba(217,217,217,0.8)',
            'rgba(188,128,189,0.8)',
            'rgba(204,235,197,0.8)',
            'rgba(255,237,111,0.8)'
        ];

        $this->colors[2] = ['rgba(141,211,199,0.8)'];
    }

    private function rand_color($varian, $idx) {
        return $this->colors[$varian][$idx % sizeof($this->colors[$varian])];
    }

    private function array_to_js_string($a) {
        return "['" . rtrim(implode("','", $a), ',') . "']";
    }

    private function array_to_js_number($a) {
        return "[" . rtrim(implode(',', $a), ',') . "]";
    }

    //param: color variant
    private function chart_simple_bar($sql, $colval) {
        $res = $this->db->query($sql);
        if (!$res) {
            return null;
        } else {
            $ares = $res->result_array();
            $labels = array_column($ares, 'kelas');
            $vals = array_column($ares, 'jumlah');

            $n = sizeof($labels);
            $colors = array();
            for ($i = 0; $i < $n; $i++) {
                $colors[] = $this->rand_color($colval, $i);
            }

            $retval = array(
                "labels" => $this->array_to_js_string($labels),
                "vals" => $this->array_to_js_number($vals),
                "colors" => $this->array_to_js_string($colors)
            );

            return $retval;
        }
    }

    private function get_jml_usulan($thn) {
        $user=$this->session->users['id_user'];
        if (empty($thn)) {
            $a = $this->db->query("select count(id_usulan) as total from v_usulan_dpr")->result_array();
        } else {
            $a = $this->db->query("select count(id_usulan) as total from v_usulan_dpr where thang=$thn and id_user=".$user)->result_array();
        }

        return $a[0]['total'];
    }

    function frame() {
        $user=$this->session->users['id_user'];
        if (empty($this->session->users['kd_prov'])) {
            $prov=20;
        } else {
            $prov=$this->session->users['kd_prov'];
        }
        $taa = $this->session->konfig_tahun_ang;
        $data['js'] = [
            "modules/dashboard/views/js_dashboard_dpr.js"
        ];

        //   print_r($tah);
//        die();
        if (!empty($_POST['tah'])) {
            $tah = $_POST['tah'];
            $data['tahuns'] = $tah;
            $a = $this->db->query("select count(id_usulan) as total from v_usulan_dpr where thang=$tah and id_user=$user")->result_array();

            $b = $a[0]['total'];
            $data['totaldata'] = $b;

            $data['chart']['prov'] = $this->chart_simple_bar(
//                "select coalesce(a.lokasi, 'N/A') as kelas, count(*) as jumlah
//            from dbo.v_usulan_dpr a where thang=$tah and kd_prov_irms=$prov
//            group by a.lokasi
//            order by a.lokasi",
                    "select a.lokasi as kelas, count(*) as jumlah
            from dbo.v_usulan_dpr a where thang=$tah and id_user=$user
            group by a.lokasi
            order by a.lokasi",
                    0);

            $data['chart']['fraksi'] = $this->chart_simple_bar(
                    "select coalesce(a.ket_tipe_usulan, 'N/A') as kelas, count(*) as jumlah
            from dbo.v_usulan_dpr a where thang=$tah and id_user=$user
            group by a.ket_tipe_usulan
            order by a.ket_tipe_usulan",
                    1);

            $data['chart']['dapil'] = $this->chart_simple_bar(
                    "select coalesce(a.nama_dapil, 'N/A') as kelas, count(*) as jumlah
            from dbo.v_usulan_dpr a where thang=$tah and id_user=$user
            group by a.nama_dapil
            order by a.nama_dapil",
                    0);

            $this->load->view('v_dashboard_dpr', $data);
        } else {

            $a = $this->db->query("select count(id_usulan) as total from v_usulan_dpr where id_user=$user and thang=$taa ")->result_array();

            $b = $a[0]['total'];
            $data['totaldata'] = $b;

            $data['chart']['prov'] = $this->chart_simple_bar(
                    "select a.lokasi as kelas, count(*) as jumlah
            from dbo.v_usulan_dpr a where id_user=$user and thang=$taa
            group by a.lokasi
            order by a.lokasi",
                    0);

            $data['chart']['fraksi'] = $this->chart_simple_bar(
                    "select coalesce(a.ket_tipe_usulan, 'N/A') as kelas, count(*) as jumlah
            from dbo.v_usulan_dpr a  where id_user=$user and thang=$taa
            group by a.ket_tipe_usulan
            order by a.ket_tipe_usulan",
                    1);

            $data['chart']['dapil'] = $this->chart_simple_bar(
                    "select coalesce(a.nama_dapil, 'N/A') as kelas, count(*) as jumlah
            from dbo.v_usulan_dpr a where id_user=$user
            group by a.nama_dapil
            order by a.nama_dapil",
                    0);

            $this->load->view('v_dashboard_dpr', $data);
        }
    }

    public function index() {
        $title = "Dashboard Usulan DPR";
        $data = array();


        $this->template->set('title', $title);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }

}
