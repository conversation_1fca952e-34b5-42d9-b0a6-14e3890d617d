<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Info_ticker extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "Info Ticker";
        $js_file = $this->load->view('info_ticker/js_file', '', true);
        $kontak = $this->db->get('landing_page')->row_array();
        $modal_tambah = $this->load->view('info_ticker/modal_tambah', '', true);
        $modal_edit = $this->load->view('info_ticker/modal_edit', '', true);
        
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "jv_script" => $js_file,
            "data" => $kontak
        );
        
        $this->load->view('index', $data);
    }

    public function save_form() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $data_detail = [ 
            "text" => $this->input->post("formData")["text"] 
        ];
        $this->db->insert('ticker', $data_detail);


        echo json_encode(array("status" => TRUE));
    }


    function update_form() {
        // $this->wgisitia->handle_removed($param);
        $param = $this->input->post('formData', false);
        
        // echo "<pre>";
        // print_r ($this->input->post('formData', FALSE));
        // echo "</pre>";
        // strip_tags($_POST[''], '<p><a>');
        // exit();
        
        
        $data_detail = [ 
            "text" => $param["text"], 
        ];

        $this->db->where('id', $param["id"]);
        $this->db->update('ticker', $data_detail);

        echo json_encode(array("status" => TRUE,"data"=>$param["id"]));
    }

    
    public function ajax_delete($id) {

        $this->M_model->delete_by_id('ticker','id', $id);
        echo json_encode(array("status" => TRUE));
    }

    public function ssp_paket() {
      
        $table = 'ticker';
        $primaryKey = 'id'; //test        
         
        $columns = array(
            array('db' => 'id', 'dt' => 0),
            array('db' => 'text', 'dt' => 1),
        );
       

        datatable_ssp($table, $primaryKey, $columns);

        
    }


}
