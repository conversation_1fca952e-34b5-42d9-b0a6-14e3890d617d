<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!--
{ "data": "kd_prov" },
{ "data": "id_kabkot" },
{ "data": "kd_kab_kota_bpiw" },
{ "data": "kab_kota" },
{ "data": "kd_kab_irmsv3" },
{ "data": "kd_kab_rams" },
{ "data": "kd_kab_bps" },
{ "data": "kd_kab_rkakl" }
-->
 <!-- Slide Right Modal -->
 <div class="modal fade" id="modal-tambah-module" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Tambah Modul Baru</h4>
                <button type="button" class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close">
                    <span
                        aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body">
                    <!-- form start -->
                    <input type="hidden" id="modeform"/>
                    <form role="form" id="frm-tambah-module">
                      <div class="box-body">
                        <div class="col-xs-12">
                            <div class="form-group" style="display:none;">
                                <label for="exampleInputPassword1">ID User Group</label>
                                <input readonly type="text" class="form-control col-md-8" id="id_user_group" placeholder="">
                            </div>
                            <!--
                            <div class="form-group">
                                <label for="exampleInputPassword1">Kode Modul</label>
                                <input maxlength="3" type="text" class="form-control col-md-8" id="kode_module" placeholder="">
                            </div>
                            -->
                             <div class="form-group">
                                <label for="exampleInputPassword1">Nama Modul</label>
                                <input type="text" class="form-control col-md-8" id="nama_module" placeholder="">
                            </div>
                            <div class="form-group">
                                <input id="check_parent" onclick="setAsParent()" type="checkbox">&nbsp;Jadikan sebagai Level 1
                            </div>
                            <div class="form-group" id="container-jstree-7">
                                <label id="label-tree_7">Induk</label>
                                <div id="jstree_7">
                                       
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">Induk Terpilih</label>
                                 <input readonly type="text" class="form-control col-md-8" id="parent" placeholder="">
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">URL</label>
                                 <input type="text" class="form-control col-md-8" id="url" placeholder="">
                            </div>
                            <br>
                        </div>
                      </div>
                      <!-- /.box-body -->
                    </form>
                </div>
            <div class="modal-footer">
                <button type="button"
                    class="btn btn-default waves-effect "
                    data-bs-dismiss="modal">Close</button>
                <button type="button" onclick="simpanFormModule()" class="btn btn-primary waves-effect waves-light ">Simpan</button>
            </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->
