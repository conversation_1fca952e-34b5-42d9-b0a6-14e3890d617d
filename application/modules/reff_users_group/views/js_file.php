<script>
var html_jstree3 = [
    "<div id='jstree_3'>",
    "</div>"
].join("\n");

var html_jstree4 = [
    "<div id='jstree_4'>",
    "</div>"
].join("\n");

var html_jstree6 = [
    "<div id='jstree_6'>",
    "</div>"
].join("\n");

var html_jstree7 = [
    "<div id='jstree_7'>",
    "</div>"
].join("\n");


var html_jstree8 = [
    "<div id='jstree_8'>",
    "</div>"
].join("\n");

function get_data_user_group() {
    var x = null;
    $.ajax({
        type: "GET",
        url: "<?php echo base_url('/reff_users_group/treeview') ?>",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(data) {
            // console.log("--ajax data--");
            // console.log(data)
            x = data;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return x;
}

function init_jstree3() {
    //var harcodedata=[{"id":1,"parent":"#","text":"ADMINISTRATOR"},{"id":2,"parent":"#","text":"PUSAT"},{"id":3,"parent":"#","text":"SATKER FISIK"},{"id":4,"parent":87,"text":"SUBDIT PEMROGRAMAN"},{"id":5,"parent":87,"text":"SUBDIT ADPS"},{"id":6,"parent":87,"text":"SUBDIT KPSJ"}];
    //var harcodedata=[{"id":1,"parent":"#","text":"ADMINISTRATOR"},{"id":2,"parent":"#","text":"PUSAT"}];

    var data_context_menu = []; //akan dikirim di context menu
    var data_user_group = get_data_user_group();
    //var data_user_group=harcodedata;
    //document.write(JSON.stringify(data_user_group));
    $('#jstree_3').bind('loaded.jstree', function(e, data) {
            $("#jstree_3 a i").addClass("fa fa-user");

        }).on('open_node.jstree', function(e, data) {
            $("#jstree_3 a i").addClass("fa fa-user");
            $("#jstree_3 a i .jstree-checkbox").removeClass("fa fa-user");
        }).on('changed.jstree', function(e, data) {
            var i, j, r = [];
            for (i = 0, j = data.selected.length; i < j; i++) {
                r.push(data.instance.get_node(data.selected[i]).text);
            }
            $("#title-module").empty("")
            $("#title-module").text("List Module " + data.node.text);
            init_tree_module(data.node.id);
            //console.log(data.node.id);
            data_context_menu = data.node;
            $('#frm-edit #id_user_group').val(data.node.id);
            $('#frm-edit #nama').val(data.node.text);
            $("#frm-edit #id_sub_user_group").val(data.node.id_sub_user_group);
            $("#frm-edit-module #id_user_group").val(data.node.id);
            $("#jstree_3 .jstree-anchor  i:eq(1)").addClass("fa fa-user");
            get_alias(data.node.id);
        })
        .jstree({
            'core': {
                'data': data_user_group
            },
            types: {
                "root": {
                    "icon": "glyphicon glyphicon-plus"
                },
                "child": {
                    "icon": "glyphicon glyphicon-leaf"
                },
                "default": {}
            },
            "plugins": [
                "contextmenu",
                "search"
            ],
            'contextmenu': {
                items: {
                    "ccp": false,
                    "tambah_group_user": {
                        // The item label
                        "label": "Tambah grup Users",
                        "action": function() {
                            tambah();
                        }
                    },
                    "edit__group_user": {
                        // The item label
                        "label": "Edit",
                        "action": function() {
                            dtEditRow(data_context_menu);
                        }
                    },
                    "delete__group_user": {
                        // The item label
                        "label": "Hapus",
                        "action": function() {
                            dtDeleteRow(data_context_menu.id);
                        }
                    },
                    "tambah__module_user": {
                        // The item label
                        "label": "Buat Modul Baru",
                        "action": function() {
                            tambah_module(data_context_menu.id);

                        }
                    },
                    "link_ke_module": {
                        // The item label
                        "label": "Link ke Modul",
                        "action": function() {
                            akses_module(data_context_menu.id);

                        }
                    }
                }
            }
        });



}
var to = false;
$('#plugins4_q').keyup(function() {
    if (to) {
        clearTimeout(to);
    }
    to = setTimeout(function() {
        var v = $('#plugins4_q').val();
        $('#jstree_3').jstree(true).search(v);
    }, 250);
});

//var to = false;
$('#search_module').keyup(function() {
    if (to) {
        clearTimeout(to);
    }
    to = setTimeout(function() {
        var v = $('#search_module').val();
        $('#frm-akses-module #jstree_8').jstree(true).search(v);
    }, 250);
});

function tambah_module(id_user_group) {
    //alert("--tambah_module--");
    $("#frm-tambah-module input").val("");
    $("#frm-tambah-module #check_parent").attr("checked", false);
    $("#jstree_7").remove();
    $("#container-jstree-7").append(html_jstree7);

    init_tree_module_tambah();

    $("#frm-tambah-module #id_user_group").val(id_user_group);
    $("#modal-tambah-module").modal("show");
}

function akses_module(id_user_group) {
    $("#frm-akses-module input").val("");
    //$("#frm-akses-module #check_parent").attr("checked",false);
    $("#jstree_8").remove();
    $("#container-jstree-8").append(html_jstree8);

    init_tree_module_akses_module();

    $("#frm-akses-module #id_user_group").val(id_user_group);
    $("#modal-akses-module").modal("show");
}

function init_tambah() {
    //
    $("#frm-tambah #jstree_4").remove();
    $("#frm-tambah #container-jstree4").append(html_jstree4);
    $('#frm-tambah #jstree_4').bind('loaded.jstree', function(e, data) {
        $("#frm-tambah #jstree_4 a i").addClass("fa fa-user");

    }).on('open_node.jstree', function(e, data) {
        $("#frm-tambah #jstree_4 a i").addClass("fa fa-user");
    }).on('changed.jstree', function(e, data) {
        var i, j, r = [];
        for (i = 0, j = data.selected.length; i < j; i++) {
            r.push(data.instance.get_node(data.selected[i]).id);
        }

        $('#id_sub_user_group').val("");
        $('#id_sub_user_group').val(r.join(', '));
    }).jstree({
        'core': {
            'data': {
                "url": "<?php echo base_url('/reff_users_group/treeview') ?>",
                "dataType": "json" // needed only if you do not supply JSON headers
            }

        },
        types: {
            "root": {
                "icon": "glyphicon glyphicon-plus"
            },
            "child": {
                "icon": "glyphicon glyphicon-leaf"
            },
            "default": {}
        }

    });
}

function get_tree_module(id_user_group) {
    var x = null;
    $.ajax({
        //type: "GET",
        url: "<?php echo base_url('/reff_users_group/treeviewmodule/') ?>" + id_user_group,
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(data) {
            //console.log("--ajax data--");
            console.log(data)
            x = data;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return x;
}

function get_tree_module_all() {
    var x = null;
    $.ajax({
        //type: "GET",
        url: "<?php echo base_url('/reff_users_group/treeviewmoduleall') ?>",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function(data) {
            //console.log("--ajax data--");
            console.log(data)
            x = data;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return x;
}
//data_node dari user_group
function init_tree_module(id_user_group) {
    var data_context_menu = [];
    var data_tree_module = get_tree_module(id_user_group);
    $("#jstree_6").remove();
    $("#container-jstree-6").append(html_jstree6);
    $('#jstree_6').bind('loaded.jstree', function(e, data) {
        //$("#frm-tambah #jstree_4 a i").addClass("fa fa-user");
        $("#jstree_6 .jstree-anchor").addClass("jstree-checked");
    }).on('open_node.jstree', function(e, data) {
        $("#jstree_6 .jstree-anchor").addClass("jstree-checked");
    }).on('changed.jstree', function(e, data) {
        var i, j, r = [];

        for (i = 0, j = data.selected.length; i < j; i++) {
            r.push(data.instance.get_node(data.selected[i]).id);
        }
        $("#jstree_6 .jstree-anchor").addClass("jstree-checked");
        data_context_menu = data.node;
    }).jstree({
        'core': {
            'data': {
                "url": "<?php echo base_url('/reff_users_group/treeviewmodule/') ?>"+id_user_group,
                "dataType": "json" // needed only if you do not supply JSON headers
            }

        },
        types: {
            "root": {
                "icon": "glyphicon glyphicon-plus"
            },
            "child": {
                "icon": "glyphicon glyphicon-leaf"
            },
            "default": {}
        },
        "plugins": [
            "checkbox",
            "contextmenu",
        ],
        checkbox: {
            three_state: false, // to avoid that fact that checking a node also check others
            whole_node: false, // to avoid checking the box just clicking the node
            tie_selection: false // for checking without selecting and selecting without checking
        },
        'contextmenu': {
            items: {
                "ccp": false,
                "edit__group_module": {
                    // The item label
                    "label": "Edit",
                    "action": function() {
                        dtEditRowModule(data_context_menu);
                    }
                },
                "delete__group_module": {
                    // The item label
                    "label": "Lepas Hak Akses Module",
                    "action": function() {
                        dtDeleteRowModule(data_context_menu.id, id_user_group);
                    }
                }
            }
        }
    });
}

function init_tree_module_tambah() {
    //alert(id_user_group);
    // ajax demo
    var data_tree_module = get_tree_module_all();
    //$("#jstree_7").remove();
    //$("#container-jstree-7").append(html_jstree6);
    $('#jstree_7').bind('loaded.jstree', function(e, data) {

    }).on('open_node.jstree', function(e, data) {

    }).on('changed.jstree', function(e, data) {
        var i, j, r = [];
        for (i = 0, j = data.selected.length; i < j; i++) {
            r.push(data.instance.get_node(data.selected[i]).id);
        }
        $("#frm-tambah-module #parent").val("")
        $("#frm-tambah-module #parent").val(data.node.id)
    }).jstree({
        'core': {
            'data': data_tree_module

        },
        types: {
            "root": {
                "icon": "glyphicon glyphicon-plus"
            },
            "child": {
                "icon": "glyphicon glyphicon-leaf"
            },
            "default": {}
        },
        "plugins": ["dnd", "unique"],
        "unique": {

            "error_callback": function(n, p, f) {
                alert("Duplicate node `" + n + "` with function '" + f + "'!");

                // alert("Duplicate node `" + n + "` with function `" + f + "`!");

            }

        },
    });
}
var list_kode_module = [];

function init_tree_module_akses_module() {
    //alert(id_user_group);
    // ajax demo

    var data_tree_module = get_tree_module_all();
    $("#jstree_8").remove();
    $("#container-jstree-8").append(html_jstree8);
    $('#jstree_8').bind('loaded.jstree', function(e, data) {

    }).on('open_node.jstree', function(e, data) {

    }).on('changed.jstree', function(e, data) {
        var i, j, r = [];
        for (i = 0, j = data.selected.length; i < j; i++) {
            r.push(data.instance.get_node(data.selected[i]).id);
        }
        $("#frm-akses-module #url").val("");
        var url = get_url(data.node.id).url;
        var nama_module = get_url(data.node.id).nama_module;
        var kode_module = get_url(data.node.id).kode_module;
        $("#frm-akses-module #url").val(url);
        $("#frm-akses-module #nama_module").val(nama_module);
        $("#frm-akses-module #kode_module").val(kode_module);
    }).jstree({
        'core': {
            'data': data_tree_module,
            multiple: false,

        },
        types: {
            "root": {
                "icon": "glyphicon glyphicon-plus"
            },
            "child": {
                "icon": "glyphicon glyphicon-leaf"
            },
            "default": {}
        },
        "plugins": ["unique", "search", "checkbox"],
        checkbox: {
            three_state: false, // to avoid that fact that checking a node also check others
            whole_node: false, // to avoid checking the box just clicking the node
            tie_selection: false // for checking without selecting and selecting without checking
        },
        "unique": {
            "duplicate": function(name, counter) {
                alert('duplicate node added: ' + name);
                return name; // This would just return the duplicate name to use as the node is created
            }
        }
    }).on("check_node.jstree uncheck_node.jstree", function(e, data) {
        // alert(data.node.id + ' ' + data.node.text +
        //(data.node.state.checked ? ' CHECKED': ' NOT CHECKED'));
        //alert(data.node.state.checked);
        if (data.node.state.checked) {
            list_kode_module.push(data.node.id);
            //alert(data.node.id);
        } else {
            list_kode_module.push(data.node.id);
            remove_item(data.node.id)
        }
    });
}

function remove_item(search_term) {
    for (var i = list_kode_module.length - 1; i >= 0; i--) {
        if (list_kode_module[i] === search_term) {
            list_kode_module.splice(i, 1);
            //break;       //<-- Uncomment  if only the first term has to be removed
        }
    }
}

function onlyUnique(value, index, self) {
    return self.indexOf(value) === index;
}

function get_alias(id) {
    $.ajax({
        //type: "GET",
        url: "<?php echo base_url('/reff_users_group/get_alias?id=') ?>" + id,
        data: "",
        cache: false,
        success: function(data) {
            var result = null;
            // console.log("result");
            result = JSON.parse(data);
            //  console.log(result.alias);
            $("#frm-edit #alias").val(result.alias);
            $("#frm-edit #id_sub_user_group").val(result.id_sub_user_group);
        }
    });
}

function get_url(id) {
    var x = null;
    $.ajax({
        //type: "GET",
        url: "<?php echo base_url('/reff_users_group/get_url?id=') ?>" + id,
        data: "",
        async: false,
        cache: false,
        success: function(data) {
            var result = null;
            //alert(typeof data);
            console.log("result");
            result = JSON.parse(data);
            //result =$.parseJSON(data);
            // $("#frm-edit #alias").val(result.url);
            //console.log("ajax url")
            //console.log(result)
            x = result;
        }
    });
    return x;
}

function setAsParent(data) {

    if ($("#frm-tambah #check_parent").is(':checked')) {
        $("#frm-tambah #jstree_4").hide();
        $("#frm-tambah #id_sub_user_group").val(0);
    } else {
        $("#frm-tambah #jstree_4").show();
        $("#frm-tambah #id_sub_user_group").val("");
    }
    //edit
    if ($("#frm-edit #check_parent").is(':checked')) {
        $("#frm-edit #jstree_4").hide();
        $("#frm-edit #id_sub_user_group").val(0);
    } else {
        $("#frm-edit #jstree_4").show();
        $("#frm-edit #id_sub_user_group").val("");
    }

    //tambah module
    if ($("#frm-tambah-module #check_parent").is(':checked')) {
        $("#frm-tambah-module #jstree_7").hide();
        $("#frm-tambah-module #parent").val(null);
    } else {
        $("#frm-tambah-module #jstree_7").show();
        $("#frm-tambah-module #parent").val("");
    }
}

function simpanForm() {
    var mode = $('#modeform').val();
    var url;


    var nama = $("#frm-tambah #nama").val();
    var id_sub_user_group = $("#frm-tambah #id_sub_user_group").val();
    //var id_user_group       =$("#frm-tambah #id_user_group").val();
    var alias = $("#frm-tambah #alias").val();

    var data = {
        "nama": nama,
        "id_sub_user_group": id_sub_user_group,
        "alias": alias,
        "id_sub_sub_user_group": 0,
        "tahun": 2021

    }
    url = "<?php echo base_url(); ?>reff_users_group/addform";

    var params = {
        "formData": data,
        <?php echo $this->security->get_csrf_token_name(); ?>: '<?php echo
$this->security->get_csrf_hash(); ?>'
    };
    $.post(url, params)
        .done(function(data) {
            $("#jstree_3").remove();
            $("#container-jstree-3").append(html_jstree3);
            init_jstree3();
            init_tambah();
            $("#jstree_6").empty("");
            $("#alert-content").empty();
            $("#alert-content").append(" <p>simpan data suksess");
            $("#alert_information").css({
                display: "block"
            });
            setTimeout(close_alert, 3000);
        })
        .fail(function() {
            alert("error");
        })
        .always(function() {});
    $("#modal-tambah").modal("hide");

}


function simpanFormModule() {

    //var mode = $('#modeform').val();
    var urlx = "";

    //var kode_module         =$("#frm-tambah-module #kode_module").val();
    var nama_module = $("#frm-tambah-module #nama_module").val();
    var parent = $("#frm-tambah-module #parent").val();
    var url = $("#frm-tambah-module #url").val();
    var id_user_group = $("#frm-tambah-module #id_user_group").val();

    var data = {
        "nama_module": nama_module,
        "parent": parent,
        "url": url,
        "id_user_group": id_user_group
    }
    // console.log("--data module--");
    // console.log(data);
    urlx = "<?php echo base_url(); ?>reff_users_group/addformModule";
    //alert(7771);
    var params = {
        "formData": data,
        <?php echo $this->security->get_csrf_token_name(); ?>: '<?php echo
$this->security->get_csrf_hash(); ?>'
    };
    $.ajax({
        type: "POST",
        url: urlx,
        data: params,
        cache: false,
        success: function(data) {
            init_tree_module(id_user_group);
            $("#alert-content").empty();
            $("#alert-content").append(" <p>simpan data Modul suksess");
            $("#alert_information").css({
                display: "block"
            });
            setTimeout(close_alert, 3000);

        }
    });
    $("#modal-tambah-module").modal("hide");

}


//xxx
function update() {
    var mode = $('#modeform').val();
    var url;


    var nama = $("#frm-edit #nama").val();
    var id_sub_user_group = $("#frm-edit #id_sub_user_group").val();
    var id_user_group = $("#frm-edit #id_user_group").val();
    var alias = $("#frm-edit #alias").val();

    var data = {
        "nama": nama,
        "id_sub_user_group": id_sub_user_group,
        "id_user_group": id_user_group,
        "alias": alias
    }

    url = "<?php echo base_url(); ?>reff_users_group/editform";

    var params = {
        "formData": data
    };
    $.post(url, params)
        .done(function(data) {

            $('#jstree_3').remove();
            $('#container-jstree-3').append(html_jstree3);
            init_jstree3();
            /**
            $('#frm-edit #jstree_4').jstree(true).refresh();
            $('#frm-tambah #jstree_4').jstree(true).refresh();
            **/
            //$('#jstree_4').jstree(true).refresh();
            $("#alert-content").empty();
            $("#alert-content").append(" <p>Update data suksess");
            $("#alert_information").css({
                display: "block"
            });
            re_edit();
            setTimeout(close_alert, 3000);
        })
        .fail(function() {
            alert("error");
        })
        .always(function() {});
    $("#modal-edit").modal("hide");

}

function updateModule() {
    var xurl;
    var id_user_group = $("#frm-edit-module #id_user_group").val();
    var kode_module = $("#frm-edit-module #kode_module").val();
    var nama_module = $("#frm-edit-module #nama_module").val();
    var parent = $("#frm-edit-module #parent").val();
    var url = $("#frm-edit-module #url").val();
    //alert(parent);
    var data = {
        "kode_module": kode_module,
        "nama_module": nama_module,
        "parent": parent,
        "url": url
    }

    xurl = "<?php echo base_url(); ?>reff_users_group/editformmodule";

    var params = {
        "formData": data,
        <?php echo $this->security->get_csrf_token_name(); ?>: '<?php echo
$this->security->get_csrf_hash(); ?>'
    };
    $.post(xurl, params)
        .done(function(data) {
            init_tree_module(id_user_group);
            $("#alert-content").empty();
            $("#alert-content").append(" <p>Update data suksess");
            $("#alert_information").css({
                display: "block"
            });
            //re_edit();
            setTimeout(close_alert, 3000);
        })
        .fail(function() {
            alert("error");
        })
        .always(function() {});
    $("#modal-edit-module").modal("hide");
}

function assignModule() {
    // console.log('--list kode module--');
    // console.log(list_kode_module);

    var urlx = "";
    var id_user_group = $("#frm-akses-module #id_user_group").val();
    var kode_module = $("#frm-akses-module #kode_module").val();
    var nama_module = $("#frm-akses-module #nama_module").val();
    var url = $("#frm-akses-module #url").val();

    list_kode_module = list_kode_module.filter(onlyUnique);
    // console.log("---data--")
    // console.log(data)
    var data = {
        "kode_module": kode_module,
        "nama_module": nama_module,
        "id_user_group": id_user_group,
        "url": url,
        "list_kode_module": list_kode_module
    }
    console.log("cc--list kode module--cc")
    console.log(list_kode_module);
    //console.log(list_kode_module+"list k module");
    urlx = "<?php echo base_url('/reff_users_group/assign_modul2');?>";
    var params = {
        "formData": data,
        <?php echo $this->security->get_csrf_token_name(); ?>: '<?php echo
$this->security->get_csrf_hash(); ?>'
    };
    $.post(urlx, params)
        .done(function(data) {
            init_tree_module(id_user_group);
            $("#alert-content").empty();
            $("#alert-content").append(" <p>Update data suksess");
            $("#alert_information").css({
                display: "block"
            });
            setTimeout(close_alert, 3000);
        })
        .fail(function() {
            alert("error");
        })
        .always(function() {});
    $("#modal-akses-module").modal("hide");
}

function close_alert() {
    $("#alert_information").css({
        display: "none"
    });

}




function dtDeleteRow(id) {
    var parent = $("#frm-edit #id_sub_user_group").val();
    url = "<?php echo base_url(); ?>reff_users_group/deleteform";
    //id=$("#frm-edit #id_user_group").val();
    var r = confirm("Data yang anda pilih akan kami hapus!, apakah anda yakin mau melanjutkan");
    if (r == true) {
        $.post(url, {
                id: id,
                <?php echo $this->security->get_csrf_token_name(); ?>: '<?php echo
$this->security->get_csrf_hash(); ?>'
            }).done(function(data) {
                //alert(data.status);
                var xdata = JSON.parse(data);
                if (xdata.status != "have_childs") {

                    $("#alert-content").empty();
                    $("#alert-content").append(" <p>Delete data suksess");
                    $("#alert_information").css({
                        display: "block"
                    });
                    setTimeout(close_alert, 3000);
                    $('#jstree_3').remove();
                    $('#container-jstree-3').append(html_jstree3);
                    init_jstree3();

                } else {
                    alert("delete data gagal!, harap meghapus sub user terlebih dahulu");
                }

            })
            .fail(function() {
                alert("error");
            })
            .always(function() {
                //alert("finished");
            });
    }
}



function dtDeleteRowModule(kode_module, id_user_group) {
    var parent = $("#frm-edit #id_sub_user_group").val();
    url = "<?php echo base_url(); ?>reff_users_group/deleteformmodule";
    var data = {
        "kode_module": kode_module,
        "id_user_group": id_user_group
    }
    urlx = "<?php echo base_url('/reff_users_group/assign_modul');?>";
    var params = {
        "formData": data,
        <?php echo $this->security->get_csrf_token_name(); ?>: '<?php echo
$this->security->get_csrf_hash(); ?>'
    };
    var r = confirm("Data yang anda pilih akan kami hapus!, apakah anda yakin mau melanjutkan");
    if (r == true) {
        $.post(url, params).done(function(data) {
                xdata = JSON.parse(data);
                if (xdata.status != "have_childs") {
                    $("#alert-content").empty();
                    $("#alert-content").append(" <p>Delete data suksess");
                    $("#alert_information").css({
                        display: "block"
                    });
                    setTimeout(close_alert, 3000);
                    init_tree_module(id_user_group);

                } else {
                    alert("Modul gagal Dihapus! Harap menghapus sub module terlebih dahulu")
                }
            })
            .fail(function() {
                alert("error");
            })
            .always(function() {
                //alert("finished");
            });
    }
}

function re_edit() {
    $("#button-container").show();
    $("input").val("");
    $("checkbox").prop("checked", false);
    $("#field-container").css("display", "none");
}

$('#modal-edit').on('hidden.bs.modal', function() {
    // do something…
    re_edit();
})

function dtEditRow(data_context_menu) {
    //alert(1115555)
    $("#button-container").hide();
    $("#field-container").css("display", "block");
    // ajax demo
    get_alias(data_context_menu.id);
    $("#frm-edit #jstree_4").remove();
    $("#frm-edit #container-jstree-4").append(html_jstree4);
    $('#frm-edit #jstree_4').bind('loaded.jstree', function(e, data) {
        $("#frm-edit #jstree_4 a i").addClass("fa fa-user");

    }).on('open_node.jstree', function(e, data) {
        $("#frm-edit #jstree_4 a i").addClass("fa fa-user");
    }).on('changed.jstree', function(e, data) {
        var i, j, r = [];
        for (i = 0, j = data.selected.length; i < j; i++) {
            r.push(data.instance.get_node(data.selected[i]).id);
        }
        $('#frm-edit #frm-edit #id_sub_user_group').val("");
        $('#frm-edit #id_sub_user_group').val(r.join(', '));

    }).jstree({
        'core': {
            'data': {
                "url": "<?php echo base_url('/reff_users_group/treeview') ?>",
                "dataType": "json" // needed only if you do not supply JSON headers
            }

        },
        types: {
            "root": {
                "icon": "glyphicon glyphicon-plus"
            },
            "child": {
                "icon": "glyphicon glyphicon-leaf"
            },
            "default": {}
        }
    });

    $("#modal-edit").modal("show");
}


function dtEditRowModule(data_context_menu) {
    //console.log(data_context_menu.id);
    //$("#frm-edit-module input").val("");
    $('#frm-edit-module #jstree_8').bind('loaded.jstree', function(e, data) {


    }).on('open_node.jstree', function(e, data) {

    }).on('changed.jstree', function(e, data) {
        var i, j, r = [];
        for (i = 0, j = data.selected.length; i < j; i++) {
            r.push(data.instance.get_node(data.selected[i]).id);
        }
        var parent = null;
        parent = r.join(', ');
        if (parent == 0) {
            parent = null;
        }
        $('#frm-edit-module #parent').val("");
        $('#frm-edit-module #parent').val(parent);
        $('#frm-edit-module #parent_text').val(data.node.text);

    }).jstree({
        'core': {
            'data': {
                "url": "<?php echo base_url('/reff_users_group/treeviewmoduleall') ?>",
                "dataType": "json" // needed only if you do not supply JSON headers
            }

        },
        types: {
            "root": {
                "icon": "glyphicon glyphicon-plus"
            },
            "child": {
                "icon": "glyphicon glyphicon-leaf"
            },
            "default": {}
        }
    });

    $("#modal-edit-module #modalTitle").text("");
    $("#modal-edit-module #modalTitle").text("Edit Module " + data_context_menu.text);

    $("#frm-edit-module #kode_module").val(data_context_menu.id);
    $("#frm-edit-module #nama_module").val(data_context_menu.text);
    var parent = data_context_menu.parent;
    if (parent == null || parent == "#" || parent == "") {
        parent = "#";
    }

    $("#frm-edit-module #parent").val(parent);

    $("#modal-edit-module").modal("show");
    var url = get_url(data_context_menu.id).url;
    //alert(data_context_menu.parent);
    parent = data_context_menu.parent;
    if (parent != "#" || parent != null || parent != "" ) {
        var parent_text = get_url(parent).nama_module;
    } else {
        var parent_text = "User Level 1";
    }
    $("#frm-edit-module #parent_text").val("");
    $("#frm-edit-module #parent_text").val(parent_text);
    $("#frm-edit-module #url").val(url);
    //console.log(url);
}


$(document).ready(function() {
    init_jstree3();
});

$("#frm-tambah #id_user_group").keydown(function(e) {
    // Allow: backspace, delete, tab, escape, enter and .
    if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
        // Allow: Ctrl/cmd+A
        (e.keyCode == 65 && (e.ctrlKey === true || e.metaKey === true)) ||
        // Allow: Ctrl/cmd+C
        (e.keyCode == 67 && (e.ctrlKey === true || e.metaKey === true)) ||
        // Allow: Ctrl/cmd+X
        (e.keyCode == 88 && (e.ctrlKey === true || e.metaKey === true)) ||
        // Allow: home, end, left, right
        (e.keyCode >= 35 && e.keyCode <= 39)) {
        // let it happen, don't do anything
        return;
    }
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
        e.preventDefault();
    }
});

$("#frm-tambah-module #kode_module").keydown(function(e) {
    // Allow: backspace, delete, tab, escape, enter and .
    if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
        // Allow: Ctrl/cmd+A
        (e.keyCode == 65 && (e.ctrlKey === true || e.metaKey === true)) ||
        // Allow: Ctrl/cmd+C
        (e.keyCode == 67 && (e.ctrlKey === true || e.metaKey === true)) ||
        // Allow: Ctrl/cmd+X
        (e.keyCode == 88 && (e.ctrlKey === true || e.metaKey === true)) ||
        // Allow: home, end, left, right
        (e.keyCode >= 35 && e.keyCode <= 39)) {
        // let it happen, don't do anything
        return;
    }
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
        e.preventDefault();
    }
});

function tambah() {

    init_tambah();
    $("#modeform").val("tambah")
    $('#modalTitle').text('Tambah Data User Grup');
    $("#modal-tambah").modal("show");
}
</script>