<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!--style for modal wizard-->
<style>
	/* table{
      clear: both;
      table-layout: fixed;
      word-wrap:break-word;
    }
    .background-solid{
        background-color:#F9F9F9;
        opacity : '';
    } */
 </style>
<div class="page-body">
    <!-- Server Side Processing table start -->
    <div class="card">
        <div class="card-header">
            <h5><?=$title;?></h5>
        </div>
        <div class="card-block">
			<div class="row">
						<div class="col-sm-12">
							<div id="alert_information" style="display:none;" class="alert alert-warning alert-dismissable"></div>
							<div id="alert-content"></div>
						</div>						
						<div class="col-sm-12 col-xl-6" id="container-jstree-3">
							<h4 class="sub-title">Grup User</h4>
							<div class="col-sm-12">
								<!-- <input type="text" id="plugins4_q" placeholder="Cari grup user" class="form-control fa fa-search" style="width:50%;"> -->
								<button type="button" class="btn btn-primary waves-effect waves-light add" onclick="tambah();">Tambah Modul User
								</button>
							</div> 
							<br />
							<div id="jstree_3"></div>
						</div>
						<div id="container-jstree-6" class="col-sm-12 col-xl-6">
							<h4 class="sub-title">Modules</h4>
							<div id="jstree_6"></div>
						</div>
				</div>		
			</div>
		</div>
	</div>					
	<?php //echo $modal_filter; ?>
	<?php echo $modal_tambah; ?>
	<?php echo $modal_tambah_modul; ?>
	<?php echo $modal_akses_modul; ?>
	<?php echo $modal_edit_modul; ?>
	<?php echo $modal_edit; ?>
	<?php //echo $modal_view; ?>
	<?php echo $jv_script; ?>
