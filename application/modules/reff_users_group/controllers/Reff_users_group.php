<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Reff_users_group extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        
        // if( !isset($_SERVER['HTTP_REFERER'])) {
        //     redirect('page_not_found');
        // }
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        } elseif ($this->session->users['id_user_group'] != 1) {
            echo "<script>
                     alert('Aks<PERSON> !!');
                     history.go(-1);
                   </script>";
        }
        $this->thangusulan = $this->session->konfig_tahun_ang;
        $this->load->database();
        $this->load->helper('dtssp'); //datatable server side processing helper
        $this->thangwhere=" and tahun =".$this->session->konfig_tahun_ang;
      //  $this->thangwhere=" and tahun = 2021";
         $this->thang="where tahun =".$this->session->konfig_tahun_ang;
        //$this->thang=" where tahun = 2021";
    }

    function get_alias() {
        $id = $this->input->get("id");
        $query = $this->db->get_where('aset_user_group', array('id_user_group' => $id,'tahun' => $this->session->konfig_tahun_ang));
        $result = json_decode(json_encode($query->result()[0]));
        $xresult = json_encode($result);
        echo $xresult;
    }

    function get_url() {
        $id = $this->input->get("id");
        $query = $this->db->get_where('aset_module', array('kode_module' => $id,'tahun' => $this->session->konfig_tahun_ang));
        $result = json_decode(json_encode($query->result()[0]));
        $xresult = json_encode($result);
        echo $xresult;
    }
    function search_parent_not_exists(){
        $data_parent=$this->db->query("select parent from aset_module");

        $data_kode_module=$this->db->query("select kode_module from aset_module".$this->thang);
//        echo "<pre>";
//        //print_r($data_parent->result());
//        print_r(json_decode(json_encode($data_parent->result()), true));
//        echo "</pre>";

        $array_parent=json_decode(json_encode($data_parent->result()), true);

        $array_kode_module=json_decode(json_encode($data_kode_module->result()), true);

        //print_r(!empty(array_intersect($array_parent, $array_kode_module)));
       // print_r(array_intersect($array_parent, $array_kode_module));

//        echo "<pre>";
//        print_r($result);
//        echo "</pre>";
    }
    public function treeview() {
        $query = $this->db->get_where('aset_user_group',array('tahun' => $this->session->konfig_tahun_ang));
        $this->db->group_by('id_sub_user_group');
        $array = json_decode(json_encode($query->result()), True);
        $tree = [];
        foreach ($array as $key => $value) {
            $parent = $value["id_sub_user_group"];
            if ($value["id_sub_user_group"] == 0 || $value["id_sub_user_group"] == "") {
                $array[$key]['id_sub_user_group'] = "#";
                $parent = "#";
            }
            array_push($tree, array("id" => $value["id_user_group"],
                "parent" => $parent,
                "text" => $value["nama"],

            ));
        }
        echo json_encode($tree);
    }

    // public function alwaysCekParent() {
        //generate code to cek parent for each module
 

    public function treeviewmodule($id_user_group) {      

        $sql = "SELECT kode_module, parent, nama_module, id_user_group, nama_user
            FROM v_group_module
            WHERE parent = ? AND id_user_group = ?";
        $query = $this->db->query($sql, array('#', $id_user_group));
        $array = json_decode(json_encode($query->result()), True);
        $keys = array_keys($array);
        $lastKey = end($keys);
        $isLast = false;
        $tree = [];
        $json = '[';
        // print_r($array);die();
        foreach ($array as $key => $value) {
           
        $json .= '{';
        $json .= '"id": "'.$value["kode_module"].'",';    
        $json .= '"data": "'.$value["nama_module"].'",';
        $json .= '"text": "'.$value["nama_module"].'",';
        $json .= '"children": [';
        
        $sqlChild1 = "SELECT kode_module, parent, nama_module, id_user_group, nama_user
                      FROM v_group_module
                       WHERE parent != ? AND (parent = ? AND id_user_group = ?)";
        $query = $this->db->query($sqlChild1,array('#', $value["kode_module"], $id_user_group));

        // echo $this->db->last_query(); die();
        $array2 = $query->result_array();
        $keys2 = array_keys($array2);
        $lastKey2 = end($keys2);
        $isLast2 = false;
        foreach($array2 as $key2 => $value2) {

        $json .= '{';
        $json .= '"id": "'.$value2["kode_module"].'",';    
        $json .= '"data": "'.$value2["nama_module"].'",';
        $json .= '"text": "'.$value2["nama_module"].'",';

        $sqlChild2 = "SELECT kode_module, parent, nama_module, id_user_group, nama_user
                FROM v_group_module
                WHERE (parent != ? OR parent != ?) AND (parent = ? AND id_user_group = ?)";
        $query = $this->db->query($sqlChild2,array('#', $value2["parent"], $value2["kode_module"], $id_user_group));
        $array3 = $query->result_array();
        $keys3 = array_keys($array3);
        $lastKey3 = end($keys3);
        $isLast3 = false;
        $json .= '"children": [';
        foreach($array3 as $key3 => $value3) {

            $json .= '{';
            $json .= '"id": "'.$value3["kode_module"].'",';    
            $json .= '"text": "'.$value3["nama_module"].'"';

            if ($key3 === $lastKey3) {
                $isLast3 = true;
                $json .= '}';
    
            } else {
                $json .= '},';
            }
    
        }
        $json .= '],';
        $json .= '"state": "open"';
        if ($key2 === $lastKey2) {
            $isLast2 = true;
          $json .= '}';

        } else {
            $json .= '},';
        }


        }
        $json .= '],';
        $json .= '"state": "open"';
        if ($key === $lastKey) {
            $isLast = true;
          $json .= '}';

        } else {
            $json .= '},';
        }
        
        }
        $json .= ']';
        echo $json ;
    }

    public function treeviewmoduleall() {
        $sql = "select * from aset_module ". $this->thang;
        $query = $this->db->query($sql);
        $array = json_decode(json_encode($query->result()), True);
        $tree = [];
        foreach ($array as $key => $value) {
            $parent = $value["parent"];
            if ($value["parent"] == null || $value["parent"] == 0) {
                $array[$key]['parent'] = "#";
                $parent = "#";
            }
            array_push($tree, array("id" => $value["kode_module"],
                "parent" => (string)$parent,
                "text" => $value["nama_module"]
            ));
        }
        echo json_encode($tree);
    }

    public function addform() {
        $param = $this->input->post('formData');
        // $sql = "select max(id_user_group) AS lastid from aset_user_group".$this->thang;
        $sql = "select max(id_user_group) AS lastid from aset_user_group";
        $query = $this->db->query($sql);
        $max_id_usergroup = $query->result()[0]->lastid;
        $param["id_user_group"] = $max_id_usergroup + 1;
        $param["tahun"] = $this->session->konfig_tahun_ang;

        
        $res = $this->db->insert('aset_user_group', $param);
        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    public function addformModule() {
        $param = $this->input->post('formData');


        //insert ke tabel module
        $id_user_group = $param['id_user_group'];
        unset($param['id_user_group']);
        //unset($param['parent']);

        $sql = "select max(cast(kode_module as int)) AS lastid from aset_module ".$this->thang;
        $query = $this->db->query($sql);
        $maxmodule = $query->result()[0]->lastid;
        //print($maxmodule);
        //die();
        $param["kode_module"] = $maxmodule + 1;
        //$param["parent"] = NULL;
        if ($param["parent"] == "") {
            $param["parent"] = NULL;
        }

        // echo "<pre>";
        //     print_r($param);
        // echo "</pre>";
        // die();
        //$this->db->set('parent', NULL, false);
        // $this->db->set('thang', $this->session->konfig_tahun_ang, false);\
        $this->db->set('tahun', $this->session->konfig_tahun_ang);
        $res = $this->db->insert('aset_module', $param);

        $param_group_modules = array(
            "kode_module" => $param["kode_module"],
            "id_user_group" => (int)$id_user_group,
            "id_sub_user_group" => 0,
            "id_sub_sub_user_group" => 0,
          //   "thang" => $this->session->konfig_tahun_ang
        );
        //insert ke tabel grup_module

        //$this->db->set('thang', $this->session->konfig_tahun_ang, false);
        $this->db->set('tahun', $this->session->konfig_tahun_ang);
        $res1 = $this->db->insert('aset_group_modules', $param_group_modules);


       // if($res) {

      //  }

        if (!$res and ! $res1) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    function get_parent($kode_module) {
        $sql = "select parent,kode_module from aset_module where kode_module= '$kode_module'".$this->thangwhere;

        $query = $this->db->query($sql);

        //print($query->result()[0]->parent);
        //print_r($query->result()[0]);
        //return json_decode($query->result()[0]);
        return json_decode(json_encode($query->result()[0]), True);
        //return $query->result()[0];
    }

    public function assign_modul() {
        $param = $this->input->post('formData');


        //insert ke tabel module

        $id_user_group = $param['id_user_group'];
        $parent = $this->get_parent($param['kode_module'])["parent"];
        $array_parent = [];
        //echo $parent;
        $i = 0;
        $data_parent = array();
        //die();
        while ($parent != "") {

            if ($i == 0) {
                //echo 1;
                $data_parent = $this->get_parent($param['kode_module']);
                //echo $data_parent["kode_module"];
            } else {
                //print_r(expression)
                $data_parent = $this->get_parent($data_parent["parent"]);
                //print_r($data_parent["kode_module"]);
                $parent = $data_parent["parent"];
            }


            $param_group_modules = array(
                "kode_module" => $data_parent["kode_module"],
                "id_user_group" => $id_user_group,
                "id_sub_user_group" => 0,
                "id_sub_sub_user_group" => 0
            );
            //insert ke tabel grup_module
            $this->db->set('tahun', $this->session->konfig_tahun_ang, false);
            $res1 = $this->db->insert('aset_group_modules', $param_group_modules);


            $i ++;
        }

        if (!$res1) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    function assign_modul2() {
        $param = $this->input->post('formData');
        $id_user_group = $param['id_user_group'];
        //echo $param["list_kode_module"][0];die();
        $array_kode_module = [];
        foreach ($param["list_kode_module"] as $value) {
            $parent = $this->get_parent($value)["parent"];
            $array_parent = [];
            $i = 0;
            $data_parent = array();

            while ($parent != "") {

                if ($i == 0) {
                    $data_parent = $this->get_parent($value);
                } else {
                    $data_parent = $this->get_parent($data_parent["parent"]);
                }



                $parent = $data_parent["parent"];
                array_push($array_kode_module, $data_parent["kode_module"]);
                $i ++;
            }
            //echo 555; die();
        }
        if (empty($array_kode_module)) {
            array_push($array_kode_module, $param["list_kode_module"][0]);
        }
        $array_kode_module = array_unique($array_kode_module);
        //print_r($array_kode_module);
        //echo 7777;
        //die();
        foreach ($array_kode_module as $value) {
            $param_group_modules = array(
                "kode_module" => $value,
                "id_user_group" => $id_user_group,
                "id_sub_user_group" => 0,
                "id_sub_sub_user_group" => 0
            );
            //insert ke tabel grup_module
            $this->db->set('tahun', $this->session->konfig_tahun_ang, false);
            $res1 = $this->db->insert('aset_group_modules', $param_group_modules);
        }
    }

    //memeriksa apakah module sudah diinsert sebelumnya
    function check_kode_module_exist($id) {
        //$id=$this->input->get("id");
        $query = $this->db->get_where('aset_user_group', array('id_sub_user_group' => $id,'tahun' => $this->session->konfig_tahun_ang));
        //echo $query->num_rows();
        return $query->num_rows();
    }

    public function editform() {
        $param = $this->input->post('formData');
        $this->db->where('id_user_group', $param['id_user_group'],'tahun',$this->session->konfig_tahun_ang);
        $res = $this->db->update('aset_user_group', $param);


        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    public function editformmodule() {
        $param = $this->input->post('formData');
        if ($param["parent"] == "") {
            $param["parent"] == null;
        }
        $this->db->where('kode_module', $param['kode_module'],'tahun',$this->session->konfig_tahun_ang);
        $res = $this->db->update('aset_module', $param);


        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    function check_children($id) {
        //$id=$this->input->get("id");
        $query = $this->db->get_where('aset_user_group', array('id_sub_user_group' => $id,'tahun'=>$this->session->konfig_tahun_ang));
        //echo $query->num_rows();
        return $query->num_rows();
    }

    function check_children_module($id) {
        //$id=$this->input->get("id");
        $query = $this->db->get_where('aset_module', array('parent' => $id,'tahun'=>$this->session->konfig_tahun_ang));
        //echo $id."<br/>";
        //echo $query->num_rows();
        return $query->num_rows();
    }

    function check_children_module_by_2_params($parent, $id_user_group) {
        //$id=$this->input->get("id");
        $sql = "select a.nama_module,c.nama as nama_user,
                a.kode_module, a.parent
                from aset_module a
                left join aset_group_modules b on (a.kode_module = b.kode_module and a.tahun=b.tahun)
        left join aset_user_group c on (c.id_user_group = b.id_user_group and c.tahun=b.tahun)
        WHERE
        c.id_user_group=$id_user_group and a.parent='$parent' and a.tahun=".$this->session->konfig_tahun_ang;

        $query = $this->db->query($sql);

        return $query->num_rows();
    }

    public function deleteform() {

        $id = $this->input->post('id');
        $numberofchild = $this->check_children($id);
        if ($numberofchild == 0) {
            $this->db->where('id_user_group', $id,'tahun',$this->session->konfig_tahun_ang);
            $res = $this->db->delete('aset_user_group');
            if (!$res) {
                // if query returns null
                $msg = $this->db->_error_message();
                echo '{"status":"error", "msg":"' . $msg . '"}';
            } else {
                echo '{"status":"sukses"}';
            }
        } else {

            echo '{"status":"have_childs"}';
        }
    }

    public function deleteformmodule() {

        $param = $this->input->post('formData');

        // var_dump($param);
        // exit();
        // echo($param["kode_module"]."--".$param["id_user_group"]);die();
        $numberofchild = $this->check_children_module_by_2_params((string)$param["kode_module"], $param["id_user_group"]);

        if ($numberofchild == 0) {
            $this->db->where('id_user_group', $param["id_user_group"])->where('kode_module', (string)$param["kode_module"]);
            $res = $this->db->delete('aset_group_modules');
            if (!$res) {
                // if query returns null
                $msg = $this->db->_error_message();
                echo '{"status":"error", "msg":"' . $msg . '"}';
            } else {
                echo '{"status":"sukses"}';
            }
        } else {
            echo '{"status":"have_childs"}';
        }
    }

    public function index() {
        header("Access-Control-Allow-Origin: *");
        $data = array();
        /*         * keterangan parameter
          $this->template->load('default_layout', 'contents' , 'index', $data);
          1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
          2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
          3.index = nama view yang akan di load
         * */
        $title = "Modul User/Role";
        $js_file = $this->load->view('reff_users_group/js_file', '', true);
        $modal_filter = $this->load->view('reff_users_group/modal_filter', '', true);
        $modal_tambah = $this->load->view('reff_users_group/modal_tambah', '', true);
        $modal_tambah_modul = $this->load->view('reff_users_group/modal_tambah_modul', '', true);
        $modal_akses_modul = $this->load->view('reff_users_group/modal_akses_modul', '', true);
        $modal_edit_modul = $this->load->view('reff_users_group/modal_edit_modul', '', true);
        $modal_edit = $this->load->view('reff_users_group/modal_edit', '', true);
        $modal_view = $this->load->view('reff_users_group/modal_view', '', true);
        $data = array("modal_filter" => $modal_filter,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "modal_view" => $modal_view,
            "modal_akses_modul" => $modal_akses_modul,
            "modal_tambah_modul" => $modal_tambah_modul,
            "modal_edit_modul" => $modal_edit_modul,
            "title" => $title,
            "jv_script" => $js_file
        );
        // $this->template->set('title', $title);
        // $this->template->set('jv_script', $js_file);
        $this->load->view('index', $data);
    }

}
