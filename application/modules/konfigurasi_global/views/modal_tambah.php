<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!-- Slide Right Modal -->
<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-slideright">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="modalTitle" class="block-title"></h3>
                </div>
                <div class="block-content">
                    <!-- form start -->
                    <input type="hidden" id="modeform">
                    <form  id="frm-tambah" role="form" way-data="formData">
                        <div class="box-body">
                            <input type="hidden" id="id" name="id">            

                            <div class="col-xs-12">
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Kode Program</label>
                                    <input type="text" name="kd_program" id="kd_program" class="form-control">

                                </div>
                            </div>
                            <div class="col-xs-12">
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Kode Kegiatan</label>
                                    <input type="text" name="kd_kegiatan" id="kd_kegiatan" class="form-control">
                                </div>
                            </div>
                            <div class="col-xs-12">
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Kode Output</label>
                                    <input type="text" name="kd_output" id="kd_output" class="form-control">
                                </div>
                            </div>

                            <div class="col-xs-12" >
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Kode Suboutput</label>
                                    <input type="text" name="kd_sub_output" class="form-control" id="kd_sub_output">


                                </div>
                            </div>

                            <div class="col-xs-12" >
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Kode Komponen</label>
                                    <input type="text" name="kd_komponen" class="form-control" id="kd_komponen">


                                </div>
                            </div>
                            <div class="col-xs-12">
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Nama Program</label>
                                    <input type="text" name="nama_program" id="nama_program" class="form-control">
                                </div>
                            </div>
                            <div class="col-xs-12" >
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Nama Kegiatan</label>
                                    <input type="text" name="nama_kegiatan" class="form-control" id="nama_kegiatan">
                                </div>
                            </div>
                            <div class="col-xs-12" >
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Nama Output</label>
                                    <input type="text" name="nama_output" class="form-control" id="nama_output">


                                </div>
                            </div>
                            <div class="col-xs-12" >
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Nama Suboutput</label>
                                    <input type="text" name="nama_sub_output" class="form-control" id="nama_sub_output">


                                </div>
                            </div>
                            <div class="col-xs-12" >
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Nama Komponen</label>
                                    <input type="text" name="nama_komponen" class="form-control" id="nama_komponen">
                                </div>
                            </div>
                            <div class="col-xs-12" >
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Total</label>
                                    <input type="text" name="total" class="form-control" id="kdkl">


                                </div>
                            </div>
                            
                             <div class="col-xs-12" >
                                <div class="form-group">
                                    <label for="exampleInputPassword1">Total Volume</label>
                                    <input type="text" name="total_vol" class="form-control" id="kdkl">


                                </div>
                            </div>
                            
                        </div>
                        <!-- /.box-body -->
                    </form>
                </div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->
