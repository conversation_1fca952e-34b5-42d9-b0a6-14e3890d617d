<script>
     var yearnow = "<?php echo $this->session->konfig_tahun_ang; ?>";
    $( document ).ready(function() {
        //console.log( "ready!" );
        // refresh_import_sakti();
        refresh_tahap();
        // refresh_service_sipro()
        // refresh_service_krisna()
        
      //  updateCombobox('thang', 28, yearnow);
        //years('thang', yearnow);
     //   refresh_service_file()
    });
        
    function submit_form(){
        //$("#frm-pengaturan").submit();
        var checked_sipro = $('#status_service_sipro').is(':checked');
        var sipro = checked_sipro ? 1 : 0;
        var checked_krisna = $('#status_service_krisna').is(':checked');
        var krisna = checked_krisna ? 1 : 0;
        var checked_sakti = $('#status_import_sakti').is(':checked');
        var sakti = checked_sakti ? 1 : 0;
        
        var obj_form = {
            "tahun" : $('#tahun').val(),
            "semester" : $('#semester').val()
        };
        console.log(obj_form);
        var url = "<?php echo base_url("konfigurasi_global/simpan_pengaturan") ?>";
        var params = {"formData": obj_form, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        $.post(url, params).done(function (data) {
            $('#success').show();
            // refresh_import_sakti();
            refresh_tahap();
            // refresh_service_sipro();
            // refresh_service_krisna();
        }).fail(function () {
            
        });
    }

   function refresh_tahap(){
       //alert(1);
               var tahap=[
                           {text:"I",value:"1"},
                           {text:"II",value:"2"}
                         ];
        //console.log(tahap);
       var thang=$("#tahun").val();
       var url="<?php echo base_url('konfigurasi_global/get_tahapan/'); ?>"+thang;
       $.ajax({
        //type: "GET",
        url:url,
        data:"dummy",
        cache: false,
        success: function(data){
            console.log(data);
           //alert(data);
            $("#semester").empty();
            for(var i=0; i<= tahap.length-1; i++){
               if(data==tahap[i].value){
                   $("#semester").append("<option selected value="+tahap[i].value+">"+tahap[i].text+"</option>");
               }else{
                   $("#semester").append("<option value="+tahap[i].value+">"+tahap[i].text+"</option>");
               }
            }

        }
      });
    //        var urll="<?php echo base_url('konfigurasi_global/get_rd/'); ?>"+thang;
    //   $.ajax({
    //    //type: "GET",
    //    url:urll,
    //    data:"dummy",
    //    cache: false,
    //    success: function(datas){
    //       //alert(data);
    //        $("#rd").empty();
    //        for(var i=0; i<= rd.length-1; i++){
    //           if(datas==rd[i].value){
    //               $("#rd").append("<option selected value="+rd[i].value+">"+rd[i].text+"</option>");
    //           }else{
    //               $("#rd").append("<option value="+rd[i].value+">"+rd[i].text+"</option>");
    //           }
    //        }

    //    }
    //  });

    //   refresh_service_sipro()
    //   refresh_service_krisna()
    //   refresh_service_file()
      
   }

   function refresh_service_sipro(){
       var thang=$("#thang").val();
       var url="<?php echo base_url('konfigurasi_global/get_service_sipro/'); ?>"+thang;
       $.ajax({
        //type: "GET",
        url:url,
        data:"dummy",
        cache: false,
        success: function(data){
            //alert(data);
            if(data=='0'){
                //alert(000)
            $("#status_service_sipro").prop("checked", false);
            }else{
                $("#status_service_sipro").prop("checked", true);
            }

        }
      });
   }

      function refresh_service_krisna(){
       var thang=$("#thang").val();
       var url="<?php echo base_url('konfigurasi_global/get_service_krisna/'); ?>"+thang;
       $.ajax({
        //type: "GET",
        url:url,
        data:"dummy",
        cache: false,
        success: function(data){
            //alert(data);
            if(data=='0'){
                //alert(000)
            $("#status_service_krisna").prop("checked", false);
            }else{
                $("#status_service_krisna").prop("checked", true);
            }

        }
      });
   }

      function refresh_service_file(){
       var thang=$("#thang").val();
       var url="<?php echo base_url('konfigurasi_global/get_service_file/'.$this->session->konfig_tahun_ang); ?>";
       console.log(url);
       $.ajax({
        type: "GET",
        url:url,
        success: function(data){
            console.log(data);
            if(data==0){
                //alert(000)
            $("#file").val(data);
            }else{
                $("#file").val(data);
            }

        }
      });
   }
   
    function refresh_import_sakti(){
        var thang=$("#thang").val();
        var url="<?php echo base_url('konfigurasi_global/get_import_sakti/'); ?>"+thang;
        $.ajax({
            //type: "GET",
            url:url,
            data:"dummy",
            cache: false,
            success: function(data){
                if(data==='0'){
                    $("#status_import_sakti").prop("checked", false);
                }else{
                    $("#status_import_sakti").prop("checked", true);
                }
            }
        });
    }

   function years(divname, selvalue) {
       var date = new Date();
       var now = date.getFullYear();
       var year = parseInt(now);
       //var max = 1;
       //console.log(max);
       $('#' + divname).empty();
       $('#' + divname).append("<option value=''>--Pilih--</option>");
       for (var i = 0; i < 2; i++) {
           var html_option = [
               "<option value='" + (year + i) + "' >", (year + i), "</option>",
           ].join("\n");
           $('#' + divname).append(html_option);
       }
       if (selvalue !== '' || selvalue !== null){
           $('#' + divname).val(selvalue);
       }
   }

</script>
