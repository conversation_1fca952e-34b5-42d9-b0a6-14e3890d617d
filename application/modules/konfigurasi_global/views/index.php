<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!--style for modal wizard-->

<div class="block">
    <div class="block-header">
        <div class="col-md-6">
            <!-- Static Labels -->
            <div class="block">
                <div class="block-header">
                    <ul class="block-options">
                        <li>
                            <button type="button"><i class="si si-settings"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Konfigurasi Global</h3>
                </div>
                <div class="block-content block-content-narrow">
                    <?php //if ($this->session->flashdata('status')) { ?>
<!--                        <div class="alert alert-success alert-dismissible">
                            <a href="#" class="close" data-bs-dismiss="alert" aria-label="close">&times;</a>
                            <strong>Info!</strong> Update pengaturan Baru sukses
                        </div>-->
                    <?php //} ?>
                    <div class="alert alert-success alert-dismissible" id="success" style="display:none">
                        <a href="#" class="close" data-bs-dismiss="alert" aria-label="close">&times;</a>
                        <strong>Info!</strong> Update pengaturan Baru sukses
                    </div>

<!--                    <form id="frm-pengaturan" class="form-horizontal push-10-t" action="<?php echo base_url("konfigurasi_global/simpan_pengaturan"); ?>" method="post">-->
                    <div class="form-horizontal push-10-t">
                        <div class="form-group">
                            <div class="col-sm-9">
                                <div class="form-material">
                                    <select onchange="refresh_tahap()" class="form-control" name="tahun" id="tahun" name="material-select" size="1">
                                        <option>Pilih</option>
                                        <?php
                                        foreach ($tahun as $key => $values) {
                                            ?>
                                            <?php
                                            if ($values['tahun'] == $this->session->konfig_tahun_ang) {
                                                ?>
                                                <option selected value="<?php echo $values['tahun']; ?>"><?php echo $values['tahun']; ?></option>
                                            <?php } else { ?>
                                                <option value="<?php echo $values['tahun']; ?>"><?php echo $values['tahun']; ?></option>
                                                <?php
                                            }//end if
                                        }//end foreach
                                        ?>
                                    </select>
                                    <label for="material-select">Tahun</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="form-material">
                                    <select class="form-control" id="semester" name="semester" size="1">
                                        <option>Pilih</option>
                                    </select>
                                    <label for="material-select">Semester</label>

                                </div>
                            </div>
                        </div>
                        <!-- <div class="form-group">
                            <div class="col-sm-12">
                                <div class="form-material">
                                    <select class="form-control" id="rd" name="rd" size="1">
                                        <option>Pilih</option>
                                    </select>
                                    <label for="material-select">Revisi Dipa</label>
                                </div>
                            </div>
                        </div> -->

                        <!-- <div class="form-group">
                            <div class="col-sm-9">
                                <div class="form-material">
                                    <div class="row items-push">
                                        <input type="hidden" name="<?= $this->security->get_csrf_token_name(); ?>" value="<?= $this->security->get_csrf_hash(); ?>" style="display: none">

                                        <div class="col-xs-6">
                                            <label class="css-input switch switch-primary">
                                                <input name="status_service_sipro" id="status_service_sipro" type="checkbox" checked=""><span></span> Status Service SIPRO
                                            </label>
                                        </div>
                                        <div class="col-xs-6">
                                            <label class="css-input switch switch-primary">
                                                <input name="status_service_krisna" id="status_service_krisna" type="checkbox" checked=""><span></span> Status Service KRISNA
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->

                        <div class="form-group">
                            <div class="col-sm-9">
                                <button class="btn btn-sm btn-primary" onclick="submit_form()">Simpan Pengaturan</button>
                            </div>
                        </div>
                    <!--</form>-->
                    </div>
                </div>
            </div>
            <!-- END Static Labels -->
        </div>
    </div>

</div>
<?php echo $modal_filter; ?>
<?php echo $modal_tambah; ?>
