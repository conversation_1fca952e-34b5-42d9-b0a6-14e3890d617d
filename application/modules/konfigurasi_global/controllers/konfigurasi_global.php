<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Konfigurasi_global extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
//        $this->load->model('M_pagu_paket');
        $this->load->helper('dtssp');
    }

    public function index() {
        //echo $this->session->konfig_tahun_ang;
        //die();
//        header("Access-Control-Allow-Origin: *");
        $data = array();
        /*         * keterangan parameter
          $this->template->load('default_layout', 'contents' , 'index', $data);
          1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
          2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
          3.index = nama view yang akan di load
         * */
        $query = $this->db->query("select distinct thang FROM dbeplanningv4.dbo.sitia_config where thang != 0");
        $data_thang = json_decode(json_encode($query->result()), true);
        $title = "Konfigurasi Global";
        $js_file = $this->load->view('konfigurasi_global/js_file', '', true);
        $modal_filter = $this->load->view('konfigurasi_global/modal_filter', '', true);
        $modal_tambah = $this->load->view('konfigurasi_global/modal_tambah', '', true);
        $data = array("modal_filter" => $modal_filter,
            "modal_tambah" => $modal_tambah,
            "title" => $title,
            "thang" => $data_thang
        );
        $this->template->set('title', $title);
        $this->template->set('jv_script', $js_file);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }

    public function ssp() {
        $table = 'v_konfigurasi_global';
        $primaryKey = 'kd_komponen';
        $columns = array(
            array('db' => 'kd_program', 'dt' => 0),
            array('db' => 'kd_kegiatan', 'dt' => 1),
            array('db' => 'kd_output', 'dt' => 2),
            array('db' => 'kd_sub_output', 'dt' => 3),
            array('db' => 'nama_komponen', 'dt' => 4),
            array('db' => 'nama_program', 'dt' => 5),
            array('db' => 'nama_kegiatan', 'dt' => 6),
            array('db' => 'nama_output', 'dt' => 7),
            array('db' => 'nama_sub_output', 'dt' => 8),
            array('db' => 'total', 'dt' => 9),
            array('db' => 'total_vol', 'dt' => 10),
            array('db' => 'kd_komponen', 'dt' => 11),
        );

        datatable_ssp($table, $primaryKey, $columns);
    }

//    public function listing() {
//        $url = "http://localhost:12892/referensi/fraksi/listall";
//        echo $this->get_data_module($url);
//    }
    function aday()
    {
        echo "123323asdasdasdasdasdasdas asdasd ad a das das d asd asd ";
//        redirect("login/logout?stat=logout");
    }
    function simpan_pengaturan() {

        echo "ADIFAHMI";
        die();
        // echo $this->input->post("thang"); die();
        $data = array(
            'thang' => $this->input->post("thang"),
            'tahapan' => $this->input->post("tahapan"),
            'status_service_sipro' => $this->input->post("status_service_sipro"),
            'status_service_krisna' => $this->input->post("status_service_krisna"),

        );
        //print_r($data); die();
        $data_tahap = array('nilai' => $this->input->post("tahapan"));
        $this->db->where('thang', $this->input->post("thang"));
        $this->db->where('item', 'tahap');
        $this->db->update('dbeplanningv4.dbo.sitia_config', $data_tahap);

        if ($this->input->post("status_service_sipro") == "on") {
            $status_service_sipro = 1;
        } else {
            $status_service_sipro = 0;
        }

        if ($this->input->post("status_service_krisna") == "on") {
            $status_service_krisna = 1;
        } else {
            $status_service_krisna = 0;
        }
        //echo $this->input->post("status_service_krisna")."***input***";
        //echo $status_service_krisna."<br>";
        //echo $status_service_sipro."<br>";
        $data_status_service_sipro = array('nilai' => $status_service_sipro);
        $data_status_service_krisna = array('nilai' => $status_service_krisna);
        //die();


        $this->session->set_flashdata('status', '1');
        redirect("lo");
    }

    function get_tahapan($thang) {
        $sql = "select nilai from dbeplanningv4.dbo.sitia_config where item='tahap' and thang =" . $thang;
        $query = $this->db->query($sql);
        //print_r($query->result());
        $tahap = json_decode(json_encode($query->result()), true);
        print_r($tahap[0]["nilai"]);
        echo $tahap[0]['nilai'];
    }
    function get_rd($thang) {
        $sql = "select nilai from dbeplanningv4.dbo.sitia_config where item='rev_dipa' and thang =" . $thang;
        $query = $this->db->query($sql);
        //print_r($query->result());
        $tahap = json_decode(json_encode($query->result()), true);
        print_r($tahap[0]["nilai"]);
        echo $tahap[0]['nilai'];
    }

    function get_service_sipro($thang) {
        $sql = "select nilai from dbeplanningv4.dbo.sitia_config where item='status_sipro' and thang =" . $thang;
        $query = $this->db->query($sql);
        //print_r($query->result());
        $service_sipro = json_decode(json_encode($query->result()), true);
        //print_r($tahap[0]["nilai"]);
        echo $service_sipro[0]['nilai'];
    }

    function get_service_krisna($thang) {
        echo $_GET['thang'];
        die();
          echo 123;
        $sql = "select nilai from dbeplanningv4.dbo.sitia_config where item='status_krisna' and thang =" . $thang;
        $query = $this->db->query($sql);
        //print_r($query->result());
        $service_krisna = json_decode(json_encode($query->result()), true);
        //print_r($tahap[0]["nilai"]);
       //echo $service_krisna[0]['nilai'];

    }

    function get_service_file($thang) {
        $sql = "select nilai from dbeplanningv4.dbo.sitia_config where item='file_sbk' and thang =" . $thang;
        $query = $this->db->query($sql);
        //print_r($query->result());
        $service_file= json_decode(json_encode($query->result()), true);
        //print_r($tahap[0]["nilai"]);
        echo $service_file[0]['nilai'];
    }
    function tes()
    {
        echo "asdasdasd";
    }
    function get_response() {

        $url = '************:12890/testdata/listuser'; //url di set global

        $ch = curl_init($url);

        $header = [];
        $header[] = 'Content-type: application/json';
        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
        $header[] = "Cache-Control: no-cache";
        $header[] = "accept-encoding:*";
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);

        if (!$result) {
            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
            die("Koneksi Gagal");
        } else {
            return $result;
            //print_r($result);
        }
    }

}
