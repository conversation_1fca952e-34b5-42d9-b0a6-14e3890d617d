<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class M_foto extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "Foto";
        $js_file = $this->load->view('m_foto/js_file', '', true);
        $kontak = $this->db->get('landing_page')->row_array();
        $modal_tambah = $this->load->view('m_foto/modal_tambah', '', true);
        $modal_edit = $this->load->view('m_foto/modal_edit', '', true);
        
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "jv_script" => $js_file,
            "data" => $kontak
        );
        
        $this->load->view('index', $data);
    }
    function is_base64_encoded($string) {
        return base64_encode(base64_decode($string, true)) === $string;
}
    public function save_form() {
        
        if (is_array($_FILES)) {
            

            // $allowed_type = array('image/jpeg'=>1,'image/png'=>1);
			// $filetype = mime_content_type($_FILES['filess']['tmp_name']);

			// if (@$allowed_type[$filetype]) {
            // }else{
            //     echo json_encode(['status' => false ,'sts' => 'fail', 'msg' => 'Type FIle Salah atatu File Rusak!']);
            //     exit();
            // }

            $userfile = $_FILES['filess'];

            if(!empty($userfile['name'])) {
                $allowed_mime_types = array(
                        'image/jpeg', 
                        'image/pjpeg',
                        'image/png',  
                        'image/x-png'		
                    );
                    $filesizeLimit = 5000;
                    $sourcePath = $userfile['tmp_name'];
                    //echo $sourcePath; die();
                    $format = strtolower(pathinfo($userfile['name'], PATHINFO_EXTENSION));
                    $other_mime = mime_content_type($sourcePath);

                    // echo $other_mime; die();
                   

                    if ($format == 'py' || $format == 'php' || $format == 'php4' || $format == 'php5') {
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 2

                        ]);
                        return; // Stop execution if it's a file
                    }

                    $fileContent = file_get_contents($sourcePath, false, null, 0, 500); // Read first 500 bytes
                    $pythonPatterns = ['import', 'from', 'def', 'print'];
                       
                    foreach ($pythonPatterns as $pattern) {
                        if (strpos($fileContent, $pattern) !== false) {
                            echo json_encode([
                                'status' => false,
                                'msg' => 'Sorry, you are not allowed to upload this file type !!',
                                'sts' => 2
                            ]);
                            exit(); // Stop execution if Python code is detected
                        }
                    }

                    if ($this->is_base64_encoded($fileContent)) {
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 3
                        ]);
                        exit(); // Stop execution if Python code is detected
                    } 
                       
                    $putFile = false;
                    if (in_array($other_mime, $allowed_mime_types)) {
                        if(($userfile['size'] > $filesizeLimit) || ($userfile['size'] < $filesizeLimit)) {
                            $putFile = true;
                        } else {
                            $putFile = false;
                        }
                    }

                    if($putFile !== false) {
            
                        $id_kategori = $this->input->post('id_kategori', TRUE);
                        // $nama_dir = FCPATH . 'upload_shp';
                        $nama_dir = FCPATH . 'uploads/media/foto/'.$id_kategori;
                
                        $nama_file = date('Ymdhis').'_'.$_FILES['filess']['name'];
                        
                        if (is_dir($nama_dir)) {

                        } else {
                            $oldmask = umask(0);
                            mkdir($nama_dir, 0777, true);
                            umask($oldmask);
                        }

                        
                        // if (is_uploaded_file($_FILES['filess']['tmp_name'])) {
                            $sourcePath = $_FILES['filess']['tmp_name'];

                            $up = move_uploaded_file($sourcePath, $nama_dir.'/'.$nama_file);
                        
                        
                            $data = [ 
                                "id_kategori" => $id_kategori,
                                "url" => $nama_file, 
                                "path" => $nama_dir, 
                            ];

                            $this->db->insert('foto',$data);
                            echo json_encode(array("status" => TRUE));

                    }else{
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 4
                        ]);
                    }
            
            }
        }

    }


    function update_form() {
        
        $id_kategori = $this->input->post('xid_kategori', TRUE);
        $id = $this->input->post('xid', TRUE);

        if (is_array($_FILES)) {
            

            // $allowed_type = array('image/jpeg'=>1,'image/png'=>1,);
			// $filetype = mime_content_type($_FILES['xfiless']['tmp_name']);

			// if (@$allowed_type[$filetype]) {
            // }else{
            //     echo json_encode(['status' => false ,'sts' => 'fail', 'msg' => 'Type FIle Salah atatu File Rusak!']);
            //     exit();
            // }
            $userfile = $_FILES['xfiless'];

            if(!empty($userfile['name'])) {
                $allowed_mime_types = array(
                        'image/jpeg', 
                        'image/pjpeg',
                        'image/png',  
                        'image/x-png'		
                    );
                    $filesizeLimit = 5000;
                    $sourcePath = $userfile['tmp_name'];
                    //echo $sourcePath; die();
                    $format = strtolower(pathinfo($userfile['name'], PATHINFO_EXTENSION));
                    $other_mime = mime_content_type($sourcePath);

                    // echo $other_mime; die();
                   

                    if ($format == 'py' || $format == 'php' || $format == 'php4' || $format == 'php5') {
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 2

                        ]);
                        return; // Stop execution if it's a file
                    }

                    $fileContent = file_get_contents($sourcePath, false, null, 0, 500); // Read first 500 bytes
                    $pythonPatterns = ['import', 'from', 'def', 'print'];
                       
                    foreach ($pythonPatterns as $pattern) {
                        if (strpos($fileContent, $pattern) !== false) {
                            echo json_encode([
                                'status' => false,
                                'msg' => 'Sorry, you are not allowed to upload this file type !!',
                                'sts' => 2
                            ]);
                            exit(); // Stop execution if Python code is detected
                        }
                    }

                    if ($this->is_base64_encoded($fileContent)) {
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 3
                        ]);
                        exit(); // Stop execution if Python code is detected
                    } 
                       
                    $putFile = false;
                    if (in_array($other_mime, $allowed_mime_types)) {
                        if(($userfile['size'] > $filesizeLimit) || ($userfile['size'] < $filesizeLimit)) {
                            $putFile = true;
                        } else {
                            $putFile = false;
                        }
                    }

                    if($putFile !== false) {
            
                        $nama_dir = FCPATH . 'uploads/media/foto/'.$id_kategori;
                
                        $nama_file = date('Ymdhis').'_'.$_FILES['xfiless']['name'];
                        
                        if (is_dir($nama_dir)) {

                        } else {
                            $oldmask = umask(0);
                            mkdir($nama_dir, 0777, true);
                            umask($oldmask);
                        }

                        
                        // if (is_uploaded_file($_FILES['filess']['tmp_name'])) {
                            $sourcePath = $_FILES['xfiless']['tmp_name'];

                            $up = move_uploaded_file($sourcePath, $nama_dir.'/'.$nama_file);
                        
                        
                            $data = [ 
                                "id_kategori" => $id_kategori,
                                "path" => $nama_dir, 
                                "url" => $nama_file, 
                            ];
                            $this->db->where('id', $id);
                            $this->db->update('foto',$data);
                    }else{
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 4
                        ]);
                    }
                            

                        
                }else{
                    $data = [ 
                        "id_kategori" => $id_kategori,
                    ];
                    $this->db->where('id', $id);
                    $this->db->update('foto',$data);
                }
                // echo json_encode(array("status" => TRUE));
        }
            
    }

    
    public function ajax_delete($id) {

        $this->M_model->delete_by_id('foto','id', $id);
        echo json_encode(array("status" => TRUE));
    }

    public function ssp_paket() {
      
        $table = 'v_foto';
        $primaryKey = 'id'; //test        
         
        $columns = array(
            array('db' => 'id', 'dt' => 0),
            array('db' => 'url', 'dt' => 1),
            array('db' => 'id_kategori', 'dt' => 2),
            array('db' => 'kategori', 'dt' => 3),
        );
       

        datatable_ssp($table, $primaryKey, $columns);

        
    }


}
