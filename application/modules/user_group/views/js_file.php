<script>
    $(document).ready(function () {
        var thang = "<?php echo $this->session->konfig_tahun_ang;?>";
        updateComboboxAndSelectedWoThang('thang', 21, thang);
        dataTable(thang);

//        $("#form_id").validate({
//            rules: {
//                id: {
//                  required: true
//                },
//                desc: {
//                  required: true
//                }
//            }
//        });

    });

    function reset(){
        $("#id").val("");
        $("#id_sub_user_group").val("");
        $("#id_sub_sub_user_group").val("");
        $("#nama").val("");
        $("#alias").val("");
    }

    function dtTambah(){
        $('#modal_id').modal('show');
        reset();
        //$("#id").prop("disabled", false);
    }

    function dtEdit(el){
        $('#modal_id').modal('show');
        reset();
        //$("#id").prop("disabled", true);
        
        var id = el.split('_')[0] === 'null' ? '' : el.split('_')[0];
        var ids = el.split('_')[1] === 'null' ? '' : el.split('_')[1];
        var idss = el.split('_')[2] === 'null' ? '' : el.split('_')[2];
        var nama = el.split('_')[3] === 'null' ? '' : el.split('_')[3];
        var alias = el.split('_')[4] === 'null' ? '' : el.split('_')[4];

        $("#id").val(id);
        $("#id_sub_user_group").val(ids);
        $("#id_sub_sub_user_group").val(idss);
        $("#nama").val(nama);
        $("#alias").val(alias);
    }

    function dataTable(thang) {
        $("#table_id").DataTable({
            "destroy":true,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "deferRender": true,
            "ajax": {
                url: "<?php echo base_url(); ?>user_group/ssp",
                type: "POST",
                "iTotalRecords":  8500,
                "iTotalDisplayRecords": 7,
                "data": function (d) {
                    d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    d.thang = thang;
                }
            },
            "aoColumnDefs": [{
                "aTargets": [0],
                    "mRender": function (data, type, full) {
                        return full[0];
                    }
                },{
                "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
                },{
                "aTargets": [2],
                    "mRender": function (data, type, full) {
                        return full[4];
                    }
                },{
                "aTargets": [3],
                    "mRender": function (data, type, full) {
                        return full[2];
                    }
                },{
                "aTargets": [4],
                    "mRender": function (data, type, full) {
                        return full[3];
                    }
                },{
                "aTargets": [5],
                    "mRender": function (data, type, full) {
                        var html_button = [
                            '<button onclick="dtEdit(' + "'" + full[0] + '_' + full[1] + '_' + full[4] + '_' + full[2] + '_' + full[3] + "'" + ')" class="btn btn-primary btn-xs" data-toggle="tooltip" title="Edit"><i class="fa fa-pencil"></i></button>',
                            '<button onclick="dtDelete(' + "'" + full[0] + "'"+ ')" class="btn btn-danger btn-xs" data-toggle="tooltip" title="Hapus"><i class="fa fa-trash"></i></button>'
                        ].join("\n");
                        return html_button;
                    }
                }],
                "autoWidth": false,
                "columns": [
                    {}, {}, {}, {}, {}, {}
                ],
                "order": [[0, "asc"]],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                    "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan"
                }
        });
        $("#table_id").addClass("table table-bordered table-striped");
    }

    function autoClosingAlert(selector) {
        $("#"+selector+"").fadeTo(4000, 500).slideUp(500, function(){
            $("#"+selector+"").slideUp(500);
        });
    }

    function save(){
        if($( "#form_id" ).valid() === true){
            var obj = {
                "id" : $("#id").val(),
                "thang" : $("#thang").val(),
                "id_sub_user_group": $('#id_sub_user_group').val(),
                "id_sub_sub_user_group": $('#id_sub_sub_user_group').val(),
                "nama": $('#nama').val(),
                "alias": $('#alias').val()
            };

            var url = "<?php echo base_url("user_group/save") ?>";
            var params = {"formData": obj, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                if (data === '1'){
                    alert('Data Sudah Ada');
                } else if (data === '2') {
                    $("#success-text").text("Data berhasil diubah");
                    autoClosingAlert('success');
                    var list = $('#table_id').DataTable();
                    list.ajax.reload();
                    $('#modal_id').modal('hide');
                } else {
                    $("#success-text").text("Data berhasil disimpan");
                    autoClosingAlert('success');
                    var list = $('#table_id').DataTable();
                    list.ajax.reload();
                    $('#modal_id').modal('hide');
                }   
            }).fail(function () {
                alert("error");
            });
            $(".error").css("color","red");
        }
    }

    function dtDelete(el) {
        var thang = $("#thang").val();
        if (confirm('Yakin untuk menghapus data ini?')){
            var url = "<?php echo base_url(); ?>" + "user_group/delete";
            var obj_data = {
                "thang": thang,
                "reg": el
            };
            var params = {"formData":obj_data, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                var list = $('#table_id').DataTable();
                list.ajax.reload();
                $("#success-text").text("Data berhasil dihapus");
                autoClosingAlert('success');
            }).fail(function () {
                alert("error");
            });
        }
    }

    function changeFilter(){
        var thang = $("#thang").val();
        dataTable(thang);
    }

</script>