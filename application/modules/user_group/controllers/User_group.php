<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class User_group extends MY_Controller {
    
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();
        $this->load->helper('dtssp'); //datatable server side processing helper
        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index() {
        header("Access-Control-Allow-Origin: *");
        $title = "User Group";
        $js_file = $this->load->view('user_group/js_file', '', true);
        $data = array(
            "title" => $title,
        );
        $this->template->set('title', $title);
        $this->template->set('jv_script', $js_file);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }
    
    public function ssp() {
        $thang = $this->input->post('thang', TRUE);
        if (strlen($thang) > 0){
            $whe = "thang = $thang";
        } else {
            $whe = "thang is null";
        }
        $table = "dbeplanningv4.dbo.user_group";
        $primaryKey = 'CAST(thang AS varchar) + id_user_group';

        $columns = array(
            array('db' => 'id_user_group', 'dt' => 0),
            array('db' => 'id_sub_user_group', 'dt' => 1),
            array('db' => 'nama', 'dt' => 2),
            array('db' => 'alias', 'dt' => 3),
            array('db' => 'id_sub_sub_user_group', 'dt' => 4)
        );
                
        datatable_ssp($table, $primaryKey, $columns, $whe);
    }
    
    public function cek($thang) {
        $str_sql = "select MAX(id_user_group) as max
                    from dbeplanningv4.dbo.user_group
                    where thang = $thang";
        $query = $this->db->query($str_sql);
        $data = json_decode(json_encode($query->result()), true);
        return $data[0]['max'];
    }
    
    public function save() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        $thang = $this->input->post("formData")["thang"];
        $id = $this->input->post("formData")["id"];
        $ids = $this->input->post("formData")["id_sub_user_group"];
        $idss = $this->input->post("formData")["id_sub_sub_user_group"];
        $nama = $this->input->post("formData")["nama"];
        $alias = $this->input->post("formData")["alias"];
        
        if (strlen($id) > 0) {
            $data = [
                "id_sub_user_group" => strlen($ids) > 0 ? $ids : 0,
                "nama" => strlen($nama) > 0 ? $nama : NULL,
                "alias" => strlen($alias) > 0 ? $alias : NULL,
                "id_sub_sub_user_group" => strlen($idss) > 0 ? $idss : NULL
            ];
            $this->db->where(array('id_user_group' => $id, 'thang' => $thang));
            $this->db->update('dbeplanningv4.dbo.user_group', $data);
            echo '2';
        } else {
            $cek = $this->cek($thang);
            $data = [
                "thang" => $thang,
                "id_user_group" => intval($cek) + 1,
                "id_sub_user_group" => strlen($ids) > 0 ? $ids : 0,
                "nama" => strlen($nama) > 0 ? $nama : NULL,
                "alias" => strlen($alias) > 0 ? $alias : NULL,
                "id_sub_sub_user_group" => strlen($idss) > 0 ? $idss : NULL
            ];
            $this->db->insert('dbeplanningv4.dbo.user_group', $data);
            echo '0';
        }
    }
    
    public function delete(){
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        $array = array('thang' => $param['thang'], 'id_user_group' => $param['reg']);
        $this->db->where($array);
        $this->db->delete('dbeplanningv4.dbo.user_group');
    }

}
