<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Monitoring_pertimbangan_be<PERSON><PERSON><PERSON> extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
		$this->load->library('pdf');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $satker = $this->session->users['kd_bujt'];
        $title = "Report PTP KPPR Berusaha";

        $js_file = $this->load->view('monitoring_pertimbangan_berusaha/js_file', '', true);
        $modal_tambah = $this->load->view('monitoring_pertimbangan_berusaha/modal_tambah', '', true);
        $modal_edit = $this->load->view('monitoring_pertimbangan_berusaha/modal_edit', '', true);
        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "title" => $title,
            "jv_script" => $js_file
        );
        
        $this->load->view('index', $data);
    }
    public function getKab($kdProv,$tahun,$layer=null)
    {
        $layer='spatial.v_d_ptp_berusaha'; 
        $this->db->where('kdppum', $kdProv);
        $this->db->where('tahun_data', $tahun);
        $data = $this->db->get($layer.'_drill')->result();
            
        $str = '';
        $no = 0;
        foreach ($data as $key => $value) {
            if($no < 4){
                $str .= '<td>- '.$value->wadmkk.'<td>';
            }else{
                $str .= '<td>- '.$value->wadmkk.'<td></tr><tr><td style="width:100px"></td>';
                $no=-1;
            }
            $no++;
        }
        $str = substr($str, 0, -2);
        $html = '<table cellpadding="5" cellspacing="0" border="0" style="width:100%;padding-left:50px;table-layout: fixed;">
        <tr><td style="width:100px">List Kabupaten:</td>'.$str.'</tr></table>';
            
        echo json_encode(['html'=>$html]);
    }

    public function save_form() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
        $data_detail = [ 
            "kd_prov" => $this->input->post("formData")["kd_prov"], 
            "kd_status" => $this->input->post("formData")["kd_status"], 
            "luas" => $this->input->post("formData")["luas"], 
            "tahun_data" => $this->input->post("formData")["tahun_data"]
        ];


        // $this->db->set('created_at', 'now()', FALSE);
        $this->db->set('waktu', 'now()', FALSE);
        // $this->db->set('created_by', $this->session->users['id_user'], FALSE);
        // $this->db->set('updated_by', $this->session->users['id_user'], FALSE);
        $this->db->insert('tb_ptpil', $data_detail);


        echo json_encode(array("status" => TRUE));
    }

    function update_form() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
        
        // echo "<pre>";
        // print_r ($this->input->post("formData"));
        // echo "</pre>";exit();
        
        $data_detail = [ 
            "kd_prov" => $this->input->post("formData")["kd_prov"], 
            "kd_status" => $this->input->post("formData")["kd_status"], 
            "luas" => $this->input->post("formData")["luas"], 
            "tahun_data" => $this->input->post("formData")["tahun_data"]
        ];

        $this->db->set('waktu', 'now()', FALSE);
        // $this->db->set('updated_by', $this->session->users['id_user'], FALSE);

        $this->db->where('txtidtahap61', $this->input->post("formData")["id"]);
        $this->db->update('tb_ptpil', $data_detail);



        echo json_encode(array("status" => TRUE));
    }
    public function getYear($layer=null,$tahun=null)
    {
        // $layer=str_replace('v_d','vm',$layer);
        $layer = 'monitoring_ptp_berusaha';
        $this->db->select("SPLIT_PART(nomorberkas , '/', 2) AS  tahun_data");
        $this->db->group_by("SPLIT_PART(nomorberkas , '/', 2)");
        $datas = $this->db->get($layer)->result();
        
        $data = rsort($datas);
        $data=[];
        foreach ($datas as $key => $value) {
            array_push($data,$value->tahun_data);
        }

        $sel = in_array($tahun,$data)? '':'selected';
        // echo $sel;
        $str='<option '.$sel.' value="">Semua Tahun</option>';
        foreach ($data as $key => $value) {
            $text = $value == null ? 'Tidak Ada Tahun' : $value;
            $value = $value == null ? '0' : $value;
            $sel = $value == $tahun ? 'selected' : '';
            $str .= '<option '.$sel.' value="'.$value.'">'.$text.'</option>';
        }
        echo json_encode($str);
        
    }
    public function getProv($layer=null,$tahun=null)
    {
        // $layer=str_replace('v_d','vm',$layer);
        $layer = 'monitoring_ptp_berusaha';
        $this->db->select("wadmpr");
        $this->db->group_by("wadmpr");
        $this->db->order_by('wadmpr', 'asc');
        $datas = $this->db->get($layer)->result();
        
        $data=[];
        foreach ($datas as $key => $value) {
            array_push($data,$value->wadmpr);
        }

        // echo $sel;
        $str='<option  value="">Semua Provinsi</option>';
        foreach ($data as $key => $value) {
            $str .= '<option  value="'.$value.'">'.$value.'</option>';
        }
        echo json_encode($str);
        
    }
    public function getKabkot($prov=null)
    {
        // $layer=str_replace('v_d','vm',$layer);
        $prov = str_replace('%20',' ',$prov);
        $layer = 'monitoring_ptp_berusaha';
        $this->db->select("wadmkk");
        $this->db->group_by('wadmkk');
        $this->db->order_by('wadmkk', 'asc');
        $this->db->where('wadmpr', $prov);
        
        $datas = $this->db->get($layer)->result();
        
        $data=[];
        foreach ($datas as $key => $value) {
            array_push($data,$value->wadmkk);
        }

        // echo $sel;
        $str='<option  value="">Semua Kabupaten/Kota</option>';
        foreach ($data as $key => $value) {
            $str .= '<option  value="'.$value.'">'.$value.'</option>';
        }
        echo json_encode($str);
        
    }
    public function ssp_paket() {
      
        
    
        
        $table = 'monitoring_ptp_berusaha';
        $primaryKey = 'id'; //test        
        $kd_prov=@$this->input->post("kd_prov",true);
        $kd_kabkot=@$this->input->post("kd_kabkot",true);
        $tahun_data=@$this->input->post("tahun_data",true);

        $where="";
        if (!empty($kd_prov)) {
            $where .="wadmpr = '".$kd_prov."'";
        } 
        if (!empty($kd_kabkot)) {
            $where .=" and wadmkk = '".$kd_kabkot."'";
        } 
        if ($tahun_data != '') {
            if (!empty($kd_prov)) {
                $where .=" and nomorberkas like '%".$tahun_data."%'";
            }else{
                $where .="nomorberkas like '%".$tahun_data."%'";
            }
        } 
        $columns = array(
            array('db' => 'id', 'dt' => 0),
            array('db' => 'nomorberkas', 'dt' => 1),
            array('db' => 'jptp', 'dt' => 2),
            array('db' => 'pemohon', 'dt' => 3),
            array('db' => 'namprh', 'dt' => 4),
            array('db' => 'wadmkd', 'dt' => 5),
            array('db' => 'wadmkc', 'dt' => 6),
            array('db' => 'wadmkk', 'dt' => 7),
            array('db' => 'wadmpr', 'dt' => 8),
            array('db' => 'nomorrisalah', 'dt' => 9),
            array('db' => 'tanggalrisalah', 'dt' => 10),
            array('db' => 'luasm2', 'dt' => 11),
            array('db' => 'luassetuju', 'dt' => 12),
            array('db' => 'luastolak', 'dt' => 13),
            array('db' => 'gunatanah', 'dt' => 14),
            array('db' => 'rcnkeg', 'dt' => 15),
            array('db' => 'nomorperda', 'dt' => 16),
            array('db' => 'tahunperda', 'dt' => 17),
            array('db' => 'status', 'dt' => 18),
            array('db' => 'kbli', 'dt' => 19),
        );

        

         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);

        
    }
    public function ssp_paket_api() {
        
        $prov = str_replace('.','',$this->input->post('kd_prov',true));
        $tahun_data = $this->input->post('tahun_data',true);
        
      
        
        if($prov == '00'){

            $kolom=['pemohon','uraian_usaha','nib','nomor_izin','tgl_berlaku_izin','luas_ptp','kota','kecamatan','desa','uraian_usaha','nilai_investasi','kbli','nomor_kkpr','tgl_kkpr','luas_kppr','hat_i_persen','hat_i_meter','hat_ii_persen','hat_ii_meter','hat_iii_persen','hat_iii_meter','hat_iv_persen','hat_iv_meter','status_text','status',
                    ];
            $list_data = [];
            $data = ['kolom' =>$kolom , 'data' => $list_data];
            echo json_encode($data);
            exit();
        }
        $number = str_replace('.','',$this->input->post('kd_kabkot',true));
        $result = strval(str_pad($number, 10, '0', STR_PAD_RIGHT));
        
        $dataPost = [

            "level_daerah"=> 2, 
            "id_wilayah"=>[$result],
            "limit"=>1000
        ]; 
       
        $dataPost= json_encode($dataPost);
        $curl = curl_init();

        curl_setopt_array($curl, [
        CURLOPT_URL => "https://tataruang.atrbpn.go.id/oss_kkpr2/api-public/kkprData",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => $dataPost,
        CURLOPT_HTTPHEADER => [
            "API-Key: ".WGI_KEY_TARU,
            "Accept: */*",
            "Content-Type: application/json",
        ],
        ]);

        $response = curl_exec($curl);
        $response = json_decode($response);
        
        $datas = $response->data;


        $kolom=['pemohon','uraian_usaha','nib','nomor_izin','tgl_berlaku_izin','luas_ptp','kota','kecamatan','desa','uraian_usaha','nilai_investasi','kbli','nomor_kkpr','tgl_kkpr','luas_kppr','hat_i_persen','hat_i_meter','hat_ii_persen','hat_ii_meter','hat_iii_persen','hat_iii_meter','hat_iv_persen','hat_iv_meter','status_text','status',
        ];
        $list_data = [];
        foreach ($datas as $key => $value) {
        
            $pertek = $value->data_pertek;
            $jsonPer =null;
            if (!empty($pertek)) {
                $jsonPer = json_encode($value);
            };
            $a= [
                    // @$value->id_proyek ,
                    // @$value->data_pertek->nomor_izin,
                    '-',
                    '-',
                    @$value->data_validasi->nib ,
                    // @$pertek->nomor_ptp ,
                    // '-' ,
                    @$pertek->nomor_izin ,
                    @$pertek->tgl_berlaku_izin ,
                    @$value->luas_tanah ,
                    @$value->kota ,
                    @$value->kecamatan ,
                    @$value->desa ,
                    @$value->uraian_usaha ,
                    '-',
                    @$value->kbli,
                    @$pertek->nomor_kkpr ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    '-' ,
                    @$value->data_validasi->nama_status ,
                    @$value->data_validasi->status,
                    @$jsonPer
                ] ;
            $b =[];
            foreach ($a as $k => $v) {
                $v = $v == '' ? '-' : $v;
                array_push($b,$v);
            }
            if($tahun_data == '00' || $tahun_data == ''){
                array_push($list_data,$b);
                
            }else if($tahun_data != '00' && $tahun_data != ''){
                $nomor = @$value->data_pertek->nomor_izin;
                
                $position = strpos($nomor, $tahun_data);
                
                if ($position !== false) {
                    array_push($list_data,$b);
                } 
            }

        
        }
        $data = ['kolom' =>$kolom , 'data' => $list_data];
        echo json_encode($data);

        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
        echo "cURL Error #:" . $err;
        } else {
        // echo $response;
        }
        
    }
    function export_pdf_api($prov,$kab,$tahun_data=''){

        if($prov == '00'){

            $kolom=['pemohon','uraian_usaha','nib','nomor_izin','tgl_berlaku_izin','luas_ptp','kota','kecamatan','desa','uraian_usaha','nilai_investasi','kbli','nomor_kkpr','tgl_kkpr','luas_kppr','hat_i_persen','hat_i_meter','hat_ii_persen','hat_ii_meter','hat_iii_persen','hat_iii_meter','hat_iv_persen','hat_iv_meter','status_text','status',
                    ];
            $list_data = [];
            $data = ['kolom' =>$kolom , 'data' => $list_data];
            echo json_encode($data);
            exit();
        }
        $number = str_replace('.','',$kab);
        $result = strval(str_pad($number, 10, '0', STR_PAD_RIGHT));
        
        $dataPost = [

            "level_daerah"=> 2, 
            "id_wilayah"=>[$result],
            "limit"=>1000
        ]; 
        $dataPost= json_encode($dataPost);
        // echo "<pre>";
        // print_r ($dataPost);
        // echo "</pre>";
        $curl = curl_init();

        curl_setopt_array($curl, [
        CURLOPT_URL => "https://tataruang.atrbpn.go.id/oss_kkpr2/api-public/kkprData",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => $dataPost,
        CURLOPT_HTTPHEADER => [
            "API-Key: 83187df0e67028234b62a10f5eabd529b3b50354db623bf0d607e861c973e430",
            "Accept: */*",
            "Content-Type: application/json",
        ],
        ]);

        $response = curl_exec($curl);
        $response = json_decode($response);
        
        // echo "<pre>";
        // print_r ($response);
        // echo "</pre>";exit();
        
        $datas = $response->data;

        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
        echo "cURL Error #:" . $err;
        } else {
        // echo $response;
        }


            $pdf = new FPDF('L', 'mm','Letter');
	        $pdf->AddPage();
	        $pdf->SetFont('Arial','B',15);
            
			// $pdf->SetTextColor(255,255,255);
            $pdf->SetFillColor(255,255,255);
	        $pdf->Cell(265,25,'TABEL REKAPITUASI PTP PKKPRUNTUK KEGIATAN BERUSAHA',0,1,'C',true);
	        $pdf->SetFont('Arial','B',6);
	    
			// $pdf->SetTextColor(255,255,255);
			$pdf->SetFillColor(224, 190, 52);
            $currentY = $pdf->GetY();
	        $pdf->Cell(10,25,'No',1,0,'C',true);
	        $pdf->Cell(35,5,'Subjek PTP PKKPR ','LRT',0,'C',true);
	        $pdf->Cell(35,10,'PTP',1,0,'C',true);
	        $pdf->Cell(30,10,'Letak',1,0,'C',true);
	        $pdf->Cell(30,10,'Kegiatan',1,0,'C',true);
	        $pdf->Cell(40,5,'PKKPR ','RTL',0,'C',true);
	        // $pdf->Cell(60,5,'Perolehantanah yang sudah ','RTL',0,'C',true);
	        $pdf->Cell(20,25,'Keterangan',1,1,'C',true);
            $currentY = $currentY+5;
            $pdf->SetY($currentY); 
            $pdf->SetX(20); 
	        $pdf->Cell(35,5,' Berusaha','LRB',0,'C',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,' Berusaha','LRB',0,'C',true);
	        // $pdf->Cell(60,5,'Mendapatkan HAT (Triwulan)','LRB',1,'C',true);

			$pdf->SetFillColor(250, 225, 125);
            $pdf->SetX(20); 
	        $pdf->Cell(35,5,'a. Pemohon',1,0,'L',true);
	        $pdf->Cell(35,5,'(1) No PTP',1,0,'L',true);
	        $pdf->Cell(30,5,'a) Kab/Kota',1,0,'L',true);
			$pdf->SetFillColor(224, 190, 52);
	        $pdf->Cell(30,15,'Rencana Kegiatan',1,0,'C',true);
			$pdf->SetFillColor(250, 225, 125);
	        $pdf->Cell(40,5,'1. No PKKPR',1,0,'L',true);
	        // $pdf->Cell(15,5,'I',1,0,'C',true);
	        // $pdf->Cell(15,5,'II',1,0,'C',true);
	        // $pdf->Cell(15,5,'III',1,0,'C',true);
	        // $pdf->Cell(15,5,'IV',1,1,'C',true);
            
            $pdf->SetX(20); 
	        $pdf->Cell(35,10,'b. Bertindak Atas Nama',1,0,'L',true);
	        $pdf->Cell(35,5,'(2) Tanggal PTP',1,0,'L',true);
	        $pdf->Cell(30,5,'b) Kecamatan',1,0,'L',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,'2. Tanggal PKKPR','LRB',0,'L',true);
	        // $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        // $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        // $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        // $pdf->Cell(15,5,'%','LRB',1,'C',true);
           
            $pdf->SetX(55); 
	        $pdf->Cell(35,5,'(3) Luas (m2)',1,0,'L',true);
	        $pdf->Cell(30,5,'c) Kelurahan/Desa',1,0,'L',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,'3. Luas (m2)',1,0,'L',true);
	        // $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        // $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        // $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        // $pdf->Cell(15,5,'Luas(m2)',1,1,'C',true);
			$pdf->SetFillColor(255, 255, 255);
	        $pdf->SetFont('Arial','',6);
            setlocale(LC_TIME, 'id_ID.utf8'); // Set locale ke bahasa Indonesia


            foreach ($datas as $k => $v) {
                if($tahun_data == '00' || $tahun_data == ''){
                                     
                    
                    if ($pdf->GetY() > 180) { // 270 adalah tinggi halaman maksimum sebelum halaman baru
                        $pdf->AddPage(); // Tambahkan halaman baru
                    }
                    $no = $k+1;
                    $currentY = $pdf->GetY();
                        
                    $pdf->Cell(10,15,$no,1,0,'L',true);
                    $pdf->Cell(35,5,'a. -',1,0,'L',true);
                    $pdf->Cell(35,5,'(1) '.@$v->data_pertek->nomor_ptp,1,0,'L',true);
                    $pdf->Cell(30,5,'a) '.$v->kota,1,0,'L',true);
                    $pdf->Cell(30,15,'-',1,0,'L',true);
                    $pdf->Cell(40,5,'1. -',1,0,'L',true);
                    // $pdf->Cell(15,5,'- ',1,0,'C',true);
                    // $pdf->Cell(15,5,'- ',1,0,'C',true);
                    // $pdf->Cell(15,5,'- ',1,0,'C',true);
                    // $pdf->Cell(15,5,'- ',1,0,'C',true);
                    $pdf->Cell(20,15,str_replace('Produk Final diterima dari OSS','Hasil Final OSS',$v->data_validasi->nama_status) ,1,1,'L',true);
                    
                    $pdf->SetY($currentY+5); 
                    $pdf->SetX(20); 
                    $pdf->Cell(35,10,'b. -',1,0,'L',true);
                    $pdf->Cell(35,5,'(2) '.@$v->data_pertek->tgl_berlaku_izin,1,0,'L',true);
                    $pdf->Cell(30,5,'b) '.$v->kecamatan,1,0,'L',true);
                    $currentX = $pdf->GetX();
                    $pdf->SetX($currentX+30); 
                    $pdf->Cell(40,5,'2. -',1,0,'L',true);
                    // $pdf->Cell(15,5,'-',1,0,'C',true);
                    // $pdf->Cell(15,5,'-',1,0,'C',true);
                    // $pdf->Cell(15,5,'-',1,0,'C',true);
                    // $pdf->Cell(15,5,'-',1,1,'C',true);
                    
                    $pdf->SetX(55); 
                    $pdf->Cell(35,5,'(3) '.$v->luas_tanah.' m2',1,0,'L',true);
                    $pdf->Cell(30,5,'c) '.$v->desa,1,0,'L',true);
                    $currentX = $pdf->GetX();
                    $pdf->SetX($currentX+30); 
                    $pdf->Cell(40,5,'3. -',1,0,'L',true);
                    // $pdf->Cell(15,5,'-',1,0,'C',true);
                    // $pdf->Cell(15,5,'-',1,0,'C',true);
                    // $pdf->Cell(15,5,'-',1,0,'C',true);
                    // $pdf->Cell(15,5,'-',1,1,'C',true);
                    
                }else if($tahun_data != '00' && $tahun_data != ''){
                    $nomor = @$v->data_pertek->nomor_izin;
                    
                    $position = strpos($nomor, $tahun_data);
                    
                    if ($position !== false) {
                        
                        if ($pdf->GetY() > 180) { // 270 adalah tinggi halaman maksimum sebelum halaman baru
                            $pdf->AddPage(); // Tambahkan halaman baru
                        }
                        $no = $k+1;
                        $currentY = $pdf->GetY();
                            
                        $pdf->Cell(10,15,$no,1,0,'L',true);
                        $pdf->Cell(35,5,'a. -',1,0,'L',true);
                        $pdf->Cell(35,5,'(1) '.@$v->data_pertek->nomor_ptp,1,0,'L',true);
                        $pdf->Cell(30,5,'a) '.$v->kota,1,0,'L',true);
                        $pdf->Cell(30,15,'-',1,0,'L',true);
                        $pdf->Cell(40,5,'1. -',1,0,'L',true);
                        // $pdf->Cell(15,5,'- ',1,0,'C',true);
                        // $pdf->Cell(15,5,'- ',1,0,'C',true);
                        // $pdf->Cell(15,5,'- ',1,0,'C',true);
                        // $pdf->Cell(15,5,'- ',1,0,'C',true);
                        $pdf->Cell(20,15,str_replace('Produk Final diterima dari OSS','Hasil Final OSS',$v->data_validasi->nama_status),1,1,'L',true);
                        
                        $pdf->SetY($currentY+5); 
                        $pdf->SetX(20); 
                        $pdf->Cell(35,10,'b. -',1,0,'L',true);
                        $pdf->Cell(35,5,'(2) '.@$v->data_pertek->tgl_berlaku_izin,1,0,'L',true);
                        $pdf->Cell(30,5,'b) '.$v->kecamatan,1,0,'L',true);
                        $currentX = $pdf->GetX();
                        $pdf->SetX($currentX+30); 
                        $pdf->Cell(40,5,'2. -',1,0,'L',true);
                        // $pdf->Cell(15,5,'-',1,0,'C',true);
                        // $pdf->Cell(15,5,'-',1,0,'C',true);
                        // $pdf->Cell(15,5,'-',1,0,'C',true);
                        // $pdf->Cell(15,5,'-',1,1,'C',true);
                        
                        $pdf->SetX(55); 
                        $pdf->Cell(35,5,'(3) '.$v->luas_tanah.' m2',1,0,'L',true);
                        $pdf->Cell(30,5,'c) '.$v->desa,1,0,'L',true);
                        $currentX = $pdf->GetX();
                        $pdf->SetX($currentX+30); 
                        $pdf->Cell(40,5,'3. -',1,0,'L',true);
                        // $pdf->Cell(15,5,'-',1,0,'C',true);
                        // $pdf->Cell(15,5,'-',1,0,'C',true);
                        // $pdf->Cell(15,5,'-',1,0,'C',true);
                        // $pdf->Cell(15,5,'-',1,1,'C',true);
                        
                    } 
                }
                
            }
			// $pdf->SetTextColor(0,0,0);
	       

	        // $pdf->Cell(225,7,'',0,1,'L');
	        
	    
	  
            $pdf->Output();
    }
    function export_pdf($prov,$tahun){

        if ($prov != '00') {
            $this->db->where('kdppum', $prov);
        }
        if ($tahun != '00') {
            $this->db->where('tahun_data', $prov);
        }
            $datas =$this->db->get('monitoring_ptp_non_berusaha')->result();
            


            $pdf = new FPDF('L', 'mm','Letter');
	        $pdf->AddPage();
	        $pdf->SetFont('Arial','B',15);
            
			// $pdf->SetTextColor(255,255,255);
            $pdf->SetFillColor(255,255,255);
	        $pdf->Cell(265,25,'TABEL REKAPITUASI PTP PKKPRUNTUK KEGIATAN BERUSAHA',0,1,'C',true);
	        $pdf->SetFont('Arial','B',6);
	    
			// $pdf->SetTextColor(255,255,255);
			$pdf->SetFillColor(224, 190, 52);
            $currentY = $pdf->GetY();
	        $pdf->Cell(10,25,'No',1,0,'C',true);
	        $pdf->Cell(35,5,'Subjek PTP PKKPR ','LRT',0,'C',true);
	        $pdf->Cell(35,10,'PTP',1,0,'C',true);
	        $pdf->Cell(30,10,'Letak',1,0,'C',true);
	        $pdf->Cell(30,10,'Kegiatan',1,0,'C',true);
	        $pdf->Cell(40,5,'PKKPR ','RTL',0,'C',true);
	        $pdf->Cell(60,5,'Perolehantanah yang sudah ','RTL',0,'C',true);
	        $pdf->Cell(20,25,'Keterangan',1,1,'C',true);
            $currentY = $currentY+5;
            $pdf->SetY($currentY); 
            $pdf->SetX(20); 
	        $pdf->Cell(35,5,' Berusaha','LRB',0,'C',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,' Berusaha','LRB',0,'C',true);
	        $pdf->Cell(60,5,'Mendapatkan HAT (Triwulan)','LRB',1,'C',true);

			$pdf->SetFillColor(250, 225, 125);
            $pdf->SetX(20); 
	        $pdf->Cell(35,5,'a. Pemohon',1,0,'L',true);
	        $pdf->Cell(35,5,'(1) No PTP',1,0,'L',true);
	        $pdf->Cell(30,5,'a) Kab/Kota',1,0,'L',true);
			$pdf->SetFillColor(224, 190, 52);
	        $pdf->Cell(30,15,'Rencana Kegiatan',1,0,'C',true);
			$pdf->SetFillColor(250, 225, 125);
	        $pdf->Cell(40,5,'1. No PKKPR',1,0,'L',true);
	        $pdf->Cell(15,5,'I',1,0,'C',true);
	        $pdf->Cell(15,5,'II',1,0,'C',true);
	        $pdf->Cell(15,5,'III',1,0,'C',true);
	        $pdf->Cell(15,5,'IV',1,1,'C',true);
            
            $pdf->SetX(20); 
	        $pdf->Cell(35,10,'b. Bertindak Atas Nama',1,0,'L',true);
	        $pdf->Cell(35,5,'(2) Tanggal PTP',1,0,'L',true);
	        $pdf->Cell(30,5,'b) Kecamatan',1,0,'L',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,'2. Tanggal PKKPR','LRB',0,'L',true);
	        $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        $pdf->Cell(15,5,'%','LRB',1,'C',true);
           
            $pdf->SetX(55); 
	        $pdf->Cell(35,5,'(3) Luas (m2)',1,0,'L',true);
	        $pdf->Cell(30,5,'c) Kelurahan/Desa',1,0,'L',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,'3. Luas (m2)',1,0,'L',true);
	        $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        $pdf->Cell(15,5,'Luas(m2)',1,1,'C',true);
			$pdf->SetFillColor(255, 255, 255);
	        $pdf->SetFont('Arial','',6);
            setlocale(LC_TIME, 'id_ID.utf8'); // Set locale ke bahasa Indonesia


            foreach ($datas as $k => $v) {
                if ($pdf->GetY() > 180) { // 270 adalah tinggi halaman maksimum sebelum halaman baru
                    $pdf->AddPage(); // Tambahkan halaman baru
                }
                $no = $k+1;
                $currentY = $pdf->GetY();
                    
                $pdf->Cell(10,15,$no,1,0,'L',true);
                $pdf->Cell(35,5,'a. '.$v->pemohon,1,0,'L',true);
                $pdf->Cell(35,5,'(1) '.$v->nomorrisalah,1,0,'L',true);
                $pdf->Cell(30,5,'a) '.$v->wadmkd,1,0,'L',true);
                $pdf->Cell(30,15,$v->rcnkeg,1,0,'L',true);
                $pdf->Cell(40,5,'1. -',1,0,'L',true);
                $pdf->Cell(15,5,'- ',1,0,'C',true);
                $pdf->Cell(15,5,'- ',1,0,'C',true);
                $pdf->Cell(15,5,'- ',1,0,'C',true);
                $pdf->Cell(15,5,'- ',1,0,'C',true);
                $pdf->Cell(20,15,$v->status,1,1,'L',true);
                
                $pdf->SetY($currentY+5); 
                $pdf->SetX(20); 
                $pdf->Cell(35,10,'b. '.$v->rcnkeg,1,0,'L',true);
                $pdf->Cell(35,5,'(2) '.$v->tanggalrisalah,1,0,'L',true);
                $pdf->Cell(30,5,'b) '.$v->wadmkc,1,0,'L',true);
                $currentX = $pdf->GetX();
                $pdf->SetX($currentX+30); 
                $pdf->Cell(40,5,'2. -',1,0,'L',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,1,'C',true);
                
                $pdf->SetX(55); 
                $pdf->Cell(35,5,'(3) '.round($v->luasm2).' m2',1,0,'L',true);
                $pdf->Cell(30,5,'c) '.$v->wadmkd,1,0,'L',true);
                $currentX = $pdf->GetX();
                $pdf->SetX($currentX+30); 
                $pdf->Cell(40,5,'3. -',1,0,'L',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,1,'C',true);
                
            }
			// $pdf->SetTextColor(0,0,0);
	       

	        // $pdf->Cell(225,7,'',0,1,'L');
	        
	    
	  
            $pdf->Output();
    }

    function export_pdfOld($prov,$tahun){

        

            $curl = curl_init();

            curl_setopt_array($curl, [
            CURLOPT_URL => "https://tataruang.atrbpn.go.id/oss_kkpr2/api-public/kkprData",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => "{\n \"id_lokasi\":[\"L-202310030803574735687\", \"L-202309181032393973142\",\"L-202309250645350542743\",\n\"L-202310190952327949927\",\"L-202304061631387924644\" ]\n}",
            CURLOPT_HTTPHEADER => [
                "API-Key: 83187df0e67028234b62a10f5eabd529b3b50354db623bf0d607e861c973e430",
                "Accept: */*",
                "Content-Type: application/json",
                "User-Agent: Thunder Client (https://www.thunderclient.com)"
            ],
            ]);

            $response = curl_exec($curl);
            $datas = json_decode($response);
            $datas = $datas->data;
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
            echo "cURL Error #:" . $err;
            } else {
            // echo $response;
            }

            $list_data = [];
        foreach ($datas as $key => $value) {
        
            $pertek = $value->data_pertek;

            $a= [
                    'pemohon' => @$value->id_proyek ,
                    'uraian_usaha' => @$pertek->uraian_usaha ,
                    'nib' => @$value->data_validasi->nib ,
                    'nomor_ptp' => @$pertek->nomor_ptp ,
                    'tgl_ptp' => '-' ,
                    'luas_ptp' => @$value->luas_tanah ,
                    'kota' => @$pertek->nama_daerah->kota ,
                    'kecamatan' => @$pertek->nama_daerah->kecamatan ,
                    'desa' => @$pertek->nama_daerah->desa ,
                    'uraian_usaha' => @$value->uraian_usaha ,
                    'nilai_investasi' => '-',
                    'kbli' => @$value->kbli,
                    'nomor_kkpr' => @$pertek->nomor_kkpr ,
                    'tgl_kkpr' => '-' ,
                    'luas_kppr' => @$value->data_validasi->nib ,
                    'hat_i_persen' => '-' ,
                    'hat_i_meter' => '-' ,
                    'hat_ii_persen' => '-' ,
                    'hat_ii_meter' => '-' ,
                    'hat_iii_persen' => '-' ,
                    'hat_iii_meter' => '-' ,
                    'hat_iv_persen' => '-' ,
                    'hat_iv_meter' => '-' ,
                    'status_text' => @$value->data_validasi->nama_status ,
                    'status' => @$value->data_validasi->status
                ] ;
            $b =[];
            foreach ($a as $k => $v) {
                $v = $v == '' ? '-' : $v;
                $b[$k] = $v;
            }
            array_push($list_data,$b);
        }
            $datas = $list_data;

            
            

            $pdf = new FPDF('L', 'mm','Letter');
	        $pdf->AddPage();
	        $pdf->SetFont('Arial','B',15);
            
			// $pdf->SetTextColor(255,255,255);
            $pdf->SetFillColor(255,255,255);
	        $pdf->Cell(265,25,'TABEL REKAPITUASI PTP PKKPRUNTUK KEGIATAN BERUSAHA',0,1,'C',true);
	        $pdf->SetFont('Arial','B',6);
	    
			// $pdf->SetTextColor(255,255,255);
			$pdf->SetFillColor(224, 190, 52);
            $currentY = $pdf->GetY();
	        $pdf->Cell(10,25,'No',1,0,'C',true);
	        $pdf->Cell(35,5,'Subjek PTP PKKPR ','LRT',0,'C',true);
	        $pdf->Cell(35,10,'PTP',1,0,'C',true);
	        $pdf->Cell(30,10,'Letak',1,0,'C',true);
	        $pdf->Cell(30,10,'Kegiatan',1,0,'C',true);
	        $pdf->Cell(40,5,'PKKPR ','RTL',0,'C',true);
	        $pdf->Cell(60,5,'Perolehantanah yang sudah ','RTL',0,'C',true);
	        $pdf->Cell(20,25,'Keterangan',1,1,'C',true);
            $currentY = $currentY+5;
            $pdf->SetY($currentY); 
            $pdf->SetX(20); 
	        $pdf->Cell(35,5,' Berusaha','LRB',0,'C',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,' Berusaha','LRB',0,'C',true);
	        $pdf->Cell(60,5,'Mendapatkan HAT (Triwulan)','LRB',1,'C',true);

			$pdf->SetFillColor(250, 225, 125);
            $pdf->SetX(20); 
	        $pdf->Cell(35,5,'a. Pemohon',1,0,'L',true);
	        $pdf->Cell(35,5,'(1) No PTP',1,0,'L',true);
	        $pdf->Cell(30,5,'a) Kab/Kota',1,0,'L',true);
			$pdf->SetFillColor(224, 190, 52);
	        $pdf->Cell(30,15,'Rencana Kegiatan',1,0,'C',true);
			$pdf->SetFillColor(250, 225, 125);
	        $pdf->Cell(40,5,'1. No PKKPR',1,0,'L',true);
	        $pdf->Cell(15,5,'I',1,0,'C',true);
	        $pdf->Cell(15,5,'II',1,0,'C',true);
	        $pdf->Cell(15,5,'III',1,0,'C',true);
	        $pdf->Cell(15,5,'IV',1,1,'C',true);
            
            $pdf->SetX(20); 
	        $pdf->Cell(35,10,'b. Bertindak Atas Nama',1,0,'L',true);
	        $pdf->Cell(35,5,'(2) Tanggal PTP',1,0,'L',true);
	        $pdf->Cell(30,5,'b) Kecamatan',1,0,'L',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,'2. Tanggal PKKPR','LRB',0,'L',true);
	        $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        $pdf->Cell(15,5,'%','LRB',0,'C',true);
	        $pdf->Cell(15,5,'%','LRB',1,'C',true);
           
            $pdf->SetX(55); 
	        $pdf->Cell(35,5,'(3) Luas (m2)',1,0,'L',true);
	        $pdf->Cell(30,5,'c) Kelurahan/Desa',1,0,'L',true);
            $pdf->SetX(150); 
	        $pdf->Cell(40,5,'3. Luas (m2)',1,0,'L',true);
	        $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        $pdf->Cell(15,5,'Luas(m2)',1,0,'C',true);
	        $pdf->Cell(15,5,'Luas(m2)',1,1,'C',true);
			$pdf->SetFillColor(255, 255, 255);
	        $pdf->SetFont('Arial','',6);
            setlocale(LC_TIME, 'id_ID.utf8'); // Set locale ke bahasa Indonesia


            foreach ($datas as $k => $v) {
                if ($pdf->GetY() > 180) { // 270 adalah tinggi halaman maksimum sebelum halaman baru
                    $pdf->AddPage(); // Tambahkan halaman baru
                }
                $no = $k+1;
                $currentY = $pdf->GetY();
                    
                $pdf->Cell(10,15,$no,1,0,'L',true);
                $pdf->Cell(35,5,'a. '.$v['pemohon'],1,0,'L',true);
                $pdf->Cell(35,5,'(1) '.$v['nomor_ptp'],1,0,'L',true);
                $pdf->Cell(30,5,'a) '.$v['kota'],1,0,'L',true);
                $pdf->Cell(30,15,$v['uraian_usaha'],1,0,'L',true);
                $pdf->Cell(40,5,'1. -',1,0,'L',true);
                $pdf->Cell(15,5,'- ',1,0,'C',true);
                $pdf->Cell(15,5,'- ',1,0,'C',true);
                $pdf->Cell(15,5,'- ',1,0,'C',true);
                $pdf->Cell(15,5,'- ',1,0,'C',true);
                $pdf->Cell(20,15,str_replace('diterima dari OSS','',$v['status_text']),1,1,'L',true);
                
                $pdf->SetY($currentY+5); 
                $pdf->SetX(20); 
                $pdf->Cell(35,10,'b. '.$v['uraian_usaha'],1,0,'L',true);
                $pdf->Cell(35,5,'(2) '.$v['tgl_ptp'],1,0,'L',true);
                $pdf->Cell(30,5,'b) '.$v['kecamatan'],1,0,'L',true);
                $currentX = $pdf->GetX();
                $pdf->SetX($currentX+30); 
                $pdf->Cell(40,5,'2. -',1,0,'L',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,1,'C',true);
                
                $pdf->SetX(55); 
                $pdf->Cell(35,5,'(3) '.round($v['luas_ptp']).' m2',1,0,'L',true);
                $pdf->Cell(30,5,'c) '.$v['desa'],1,0,'L',true);
                $currentX = $pdf->GetX();
                $pdf->SetX($currentX+30); 
                $pdf->Cell(40,5,'3. -',1,0,'L',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,0,'C',true);
                $pdf->Cell(15,5,'-',1,1,'C',true);
                
            }
			// $pdf->SetTextColor(0,0,0);
	       

	        // $pdf->Cell(225,7,'',0,1,'L');
	        
	    
	  
            $pdf->Output();
    }
    function export_pdff(){
        $pdf = new FPDF('L', 'mm','Letter');
		foreach ($datas as $key => $value) {
	        $pdf->AddPage();
	        $pdf->SetFont('Arial','B',12);
	        $pdf->Cell(0,7,'REKAPITULASI DATA PEMETAAN SOSIAL INFRASTRUKTUR SUBJEK REFORMA AGRARIA TAHUN 2023',0,1,'C');
	        $pdf->Cell(10,7,'',0,1);
	        $pdf->SetFont('Arial','',10);
	        $pdf->Cell(30,5,'Propinsi',0,0,'L');
	        $pdf->Cell(5,5,':',0,0,'L');
	         $nama_provinsi = ucfirst(strtolower($value[0]->nama_provinsi));
	        $pdf->Cell(225,5,$nama_provinsi,0,1,'L');
	        $pdf->Cell(30,5,'Kab/Kota',0,0,'L');
	        $pdf->Cell(5,5,':',0,0,'L');
	        $nama_kab_kota = ucfirst(strtolower($value[0]->nama_kab_kota));
	        $pdf->Cell(225,5,$nama_kab_kota,0,1,'L');
	        $pdf->Cell(30,5,'Kecamatan',0,0,'L');
	        $pdf->Cell(5,5,':',0,0,'L');
	        $nama_kecamatan = ucfirst(strtolower($value[0]->nama_kecamatan));
	        $pdf->Cell(225,5,$nama_kecamatan,0,1,'L');
	        $pdf->Cell(30,5,'Desa/Kel',0,0,'L');
	        $pdf->Cell(5,5,':',0,0,'L');
	        $nama_desa_kelurahan = ucfirst(strtolower($value[0]->nama_desa_kelurahan));
	        $pdf->Cell(225,5,$nama_desa_kelurahan,0,1,'L');
	        $pdf->Cell(260,10,'',0,1,'L');

			$pdf->SetTextColor(255,255,255);
			$pdf->SetFillColor(67, 88, 186);

	        $pdf->Cell(10,14,'No',1,0,'C',true);
	        $pdf->Cell(55,14,'Jenis Prasarana',1,0,'C',true);
	        $pdf->Cell(65,7,'Keberadaan %',1,0,'C',true);
	        $pdf->Cell(70,7,'Kondisi %',1,0,'C',true);
	        $pdf->Cell(65,7,'Kepemilikan %',1,1,'C',true);

	        $pdf->Cell(10,7,'',0,0,'C');
	        $pdf->Cell(55,7,'',0,0,'C');
			$pdf->SetFillColor(199, 144, 42);
	        $pdf->Cell(20,7,'Ada',1,0,'C',true);
	        $pdf->Cell(22,7,'Tidak Ada',1,0,'C',true);
	        $pdf->Cell(23,7,'Kosong',1,0,'C',true);
	        $pdf->Cell(18,7,'Baik',1,0,'C',true);
	        $pdf->Cell(18,7,'Cukup ',1,0,'C',true);
	        $pdf->Cell(17,7,'Kurang',1,0,'C',true);
	        $pdf->Cell(17,7,'Kosong',1,0,'C',true);
	        $pdf->Cell(22,7,'Individu ',1,0,'C',true);
	        $pdf->Cell(22,7,'Komunal',1,0,'C',true);
	        $pdf->Cell(21,7,'Lainnya',1,1,'C',true);

			$pdf->SetTextColor(0,0,0);
	        // foreach ($value as $k => $v) {
		    //     $pdf->Cell(10,7,$k+1,1,0,'C');
		    //     $pdf->Cell(55,7,$v->jenis_prasarana,1,0,'C');
		    //     $pdf->Cell(20,7,$v->keberadaan_ada,1,0,'C');
		    //     $pdf->Cell(22,7,$v->keberadaan_tidak_ada,1,0,'C');
		    //     $pdf->Cell(23,7,$v->keberadaan_kosong,1,0,'C');
		    //     $pdf->Cell(18,7,$v->kondisi_baik,1,0,'C');
		    //     $pdf->Cell(18,7,$v->kondisi_cukup,1,0,'C');
		    //     $pdf->Cell(17,7,$v->kondisi_kurang,1,0,'C');
		    //     $pdf->Cell(17,7,$v->kondisi_kosong,1,0,'C');
		    //     $pdf->Cell(22,7,$v->kepemilikan_individu,1,0,'C');
		    //     $pdf->Cell(22,7,$v->kepemilikan_komunal,1,0,'C');
		    //     $pdf->Cell(21,7,$v->kepemilikan_lainnya,1,1,'C');
	        // }

	        // $pdf->Cell(225,7,'',0,1,'L');
	        
		}
	    
	  
        $pdf->Output();
    }
    function import_ptp()  {

            $file = 'upload_shp/ptp.json';
            $jsonData = file_get_contents($file);
            // Decode the JSON data into a PHP array
            $arrayData = json_decode($jsonData, true);
            // echo $jsonData;exit();

            // Check if decoding was successful
            if ($arrayData === null && json_last_error() !== JSON_ERROR_NONE) {
                echo "Error decoding JSON: " . json_last_error_msg();
            } else {
                // Display the array content (for demonstration purposes)
                // print_r($arrayData);
                
                // echo "<pre>";
                // print_r ($arrayData['data']);
                // echo "</pre>";
                foreach ($arrayData['data'] as $key => $value) {
                    
                    $this->db->insert('monitoring_ptp', $value);
                    // if ($value['jenis_layanan_ptp'] == 'PKKPR Untuk Kegiatan Non Berusaha') {
                    //     $this->db->insert('monitoring_ptp_non_berusaha', $value);
                        
                    // } else if ($value['jenis_layanan_ptp'] == 'Penegasan Status Dan Rekomendasi Penguasaan Tanah Timbul')  {
                        
                    //     $this->db->insert('monitoring_ptp_tanah_timbul', $value);
                
                    // } else if ($value['jenis_layanan_ptp'] == 'Penyelenggaraan Kebijakan Penggunaan dan Pemanfaatan Tanah')  {
                        
                    //     $this->db->insert('monitoring_ptp_pkp3t', $value);
                    // } else if ($value['jenis_layanan_ptp'] == 'PKKPR atau RKKPR Untuk Kegiatan Yang Bersifat Strategis Nasional')  {
                        
                    //     $this->db->insert('monitoring_ptp_stranas', $value);
                    // }

                    // if ($value['jenis_layanan_ptp'] == 'PTP PKKPR atau RKKPR Untuk Kegiatan Yang Bersifat Strategis Nasional')  {
                        
                    //     $this->db->insert('monitoring_ptp_stranas', $value);
                    // } else if ($value['jenis_layanan_ptp'] == 'PTP Untuk Kegiatan Penegasan Status Dan Rekomendasi Penguasaan Tanah Timbul')  {
                        
                    //     $this->db->insert('monitoring_ptp_tanah_timbul', $value);
                
                    // }
                    
                    
                }
                
                // Now $arrayData contains the JSON data as a PHP array
            }
    }

}