<div class="modal fade" id="modal-tambah" role="dialog" tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Tambah Info Ticker</h4>
                <button type="button" class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close">
                    <span aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" id="frm-tambah">
                <fieldset>
           

                <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.1/spectrum.min.css">
                <!-- Include Spectrum Color Picker JS -->
                <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.1/spectrum.min.js"></script>

                
             
                <style>
                  .sp-replacer.sp-light{
                    width: 100%;
                  }
                  .sp-preview{
                    width: 95%;
                  }
                </style>
                <!-- Text input-->
                
                <div class="form-group">
                  <label class="col-md-12 control-label" for="layer">Layer</label>  
                  <div class="col-md-12">
                  <select id="layer" name="layer" required class="bootstrap-select form-control" data-live-search="true">
                        <!-- <option value="#">Pilih</option> -->
                      </select>  
                  <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-md-12 control-label" for="jenis_geom">Jenis Geometry</label>  
                  <div class="col-md-12">
                  <select id="jenis_geom" name="jenis_geom" required class="bootstrap-select form-control" data-live-search="true">
                        <option selected value="Polygon">Polygon</option>
                        <option value="Point">Point</option>
                        <option value="Polyline">Polyline</option>
                  </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-md-12 control-label" for="jenis_simbologi">Jenis Simbologi</label>  
                  <div class="col-md-12">
                  <input id="jenis_simbologi" name="jenis_simbologi" type="text" placeholder="" class="form-control input-md">
                    
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-md-12 control-label" for="hex_outline">Warna Luar</label>  
                  <div class="col-md-12">
                  <input id="hex_outline" name="hex_outline" type="text" placeholder="" class="form-control input-md">
                    
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-md-12 control-label" for="hex_fill">Warna Dalam</label>  
                  <div class="col-md-12">
                  <input id="hex_fill" name="hex_fill" type="text" placeholder="" class="form-control input-md">
                    
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-md-12 control-label" for="ket">Keterangan</label>  
                  <div class="col-md-12">
                  <input id="ket" name="ket" type="text" placeholder="" class="form-control input-md">
                    
                  </div>
                </div>

       

                <!-- File Button --> 
                <!-- <div class="form-group">
                  <label class="col-md-12 control-label" for="filebutton">Upload Dokumen</label>
                  <div class="col-md-12">
                    <input id="filebutton" name="filebutton" class="input-file" type="file">
                  </div>
                </div> -->

              
                <!-- Button -->
                <!-- <div class="form-group">
                  <label class="col-md-12 control-label" for="singlebutton"></label>
                  <div class="col-md-12">
                    <button id="singlebutton" name="singlebutton" class="btn btn-success">Simpan</button>
                  </div>
                </div> -->

                </fieldset>
                </form>
                </div>
                <div class="modal-footer">
                                <button type="button"
                                    class="btn btn-default waves-effect "
                                    data-bs-dismiss="modal">Close</button>
                                <button type="button" onclick="simpanForm()" 
                                    class="btn btn-primary waves-effect waves-light ">Simpan</button>
                            </div>
                        </div>
                    </div>
                </div>

                
