<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Simbologi extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "Simbologi";
        $js_file = $this->load->view('simbologi/js_file', '', true);
        $kontak = $this->db->get('landing_page')->row_array();
        $modal_tambah = $this->load->view('simbologi/modal_tambah', '', true);
        $modal_edit = $this->load->view('simbologi/modal_edit', '', true);
        
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "jv_script" => $js_file,
            "data" => $kontak
        );
        
        $this->load->view('index', $data);
    }

    function getSimbologi() {
        $this->db->select('layer');
        $this->db->group_by('layer');
        $data = $this->db->get('simbologi')->result();
        echo json_encode($data);
        
    }

    public function save_form() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        
 
        $data_detail = [ 
            "layer" => $this->input->post("formData")["layer"] ,
            "jenis_geom" => $this->input->post("formData")["jenis_geom"] ,
            "jenis_simbologi" => $this->input->post("formData")["jenis_simbologi"], 
            "hex_fill" => $this->input->post("formData")["hex_fill"], 
            "hex_outline" => $this->input->post("formData")["hex_outline"], 
            "rgb_fill" => $this->input->post("formData")["rgb_fill"],
            "rgb_outline" => $this->input->post("formData")["rgb_outline"], 
            "ket" => $this->input->post("formData")["ket"] 
        ];
        $this->db->insert('simbologi', $data_detail);


        echo json_encode(array("status" => TRUE));
    }


    function update_form() {
        // $this->wgisitia->handle_removed($param);
        $param = $this->input->post('formData', true);
        
        // echo "<pre>";
        // print_r ($this->input->post('formData', true));
        // echo "</pre>";
        // exit();
        
        
        $data_detail = [ 
            "layer" => $this->input->post("formData")["layer"] ,
            "jenis_geom" => $this->input->post("formData")["jenis_geom"] ,
            "jenis_simbologi" => $this->input->post("formData")["jenis_simbologi"], 
            "hex_fill" => $this->input->post("formData")["hex_fill"], 
            "hex_outline" => $this->input->post("formData")["hex_outline"], 
            "rgb_fill" => $this->input->post("formData")["rgb_fill"],
            "rgb_outline" => $this->input->post("formData")["rgb_outline"], 
            "ket" => $this->input->post("formData")["ket"] 
        ];

        $this->db->where('id_simbologi', $param["id_simbologi"]);
        $this->db->update('simbologi', $data_detail);

        echo json_encode(array("status" => TRUE,"data"=>$param["id"]));
    }

    
    public function ajax_delete($id) {

        $this->M_model->delete_by_id('simbologi','id', $id);
        echo json_encode(array("status" => TRUE));
    }

    public function ssp_paket() {
      
        $table = 'simbologi';
        $primaryKey = 'id_simbologi'; //test        
         
        $columns = array(
            array('db' => 'id_simbologi', 'dt' => 0),
            array('db' => 'layer' , 'dt' => 1),
            array('db' => 'jenis_geom' , 'dt' => 2),
            array('db' => 'jenis_simbologi' , 'dt' => 3),
            array('db' => 'rgb_fill' , 'dt' => 4),
            array('db' => 'rgb_outline' , 'dt' => 5),
            array('db' => 'hex_fill' , 'dt' => 6),
            array('db' => 'hex_outline' , 'dt' => 7),
            array('db' => 'ket' , 'dt' => 8),
            array('db' => 'image' , 'dt' => 9),
        );
       

        datatable_ssp($table, $primaryKey, $columns);

        
    }


}
