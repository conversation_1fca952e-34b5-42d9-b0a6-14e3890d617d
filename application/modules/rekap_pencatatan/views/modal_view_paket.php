<!-- Slide Right Modal -->
<!-- farwah -->
<style>
    #maxx {
        width: 100%;
        height: 100%;
        padding: 0;
        margin:0;
    }
    #maxxs {
        height: 100%;
        border-radius: 0;
        color:#333;
        overflow:auto;
    }

    #tlist_detail_wrapper {
        width: 800px;
        margin: 0 auto;
    }

    #loading-img {
        background: url(<?= base_url(); ?>assets/img/load.gif) center center no-repeat;
        height: 100%;
        z-index: 1000;
    }

    .overlay {
        background: #e9e9e9;
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        opacity: 0.5;
    }
    .ui-autocomplete {
        z-index: 2150000000;
        position: absolute;
    }
    input{
        color:#222 !important;

    }
    select{
        color:#222 !important;
    }

    input[type='number'] {
        -moz-appearance:textfield;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    label{
        color: gray;
    }
    th{
        font-size: 11px !important;
        /* width: auto !important; */
    }
    table tr td{
        font-size: 9px !important;
    }
    #tusulandpr2{
        width: 1800px !important;
    }
    thead{
        background: #cadada6e ;
    }
    th{
        background: #cadada6e ;
    }
    .DTFC_LeftBodyWrapper{
        top:-12px !important;

    }

    .checkboxlabel {
        padding-top: 5px;
        display: block;
        padding-left: 15px;
        text-indent: -15px;
    }

    .checkboxinput {
        width: 13px;
        height: 13px;
        padding: 0;
        margin:0;
        vertical-align: bottom;
        position: relative;
        top: -1px;
        *overflow: hidden;
    }
    #boxpagu h5{
        text-align: center;
    }
    #boxs td {
        padding: 2px !important;
    }
</style>

<div class="modal fade rotate" id="modal-view-paket"  tabindex="-1" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg" id="maxx">
        <div class="modal-content" id="maxxs">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title tbhItem"></h3>

                </div>
                <div class="block-content">
                    <div class="col-md-12" style="background:#a7a7a7;">
                        <div class="container" style="background:#fff;">
                            <div class="row">
                                <input type="hidden" id="modeform">
                                <form role="form" method="POST" id="frm-edit-paket" >
                                    <!-- <input type="hidden" id="source_rujukan"> -->
                                    <input type="hidden" id="did_paket" name="did_paket">
                                            <div class="col-md-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>UMUM</label>
                                                    </div>
                                                    <div class="panel-body">                                   
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label class="control-label">Tahun</label>
                                                                    <select id="dtahun" name="dtahun" class="form-control" required="required"></select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Ruas</label>
                                                                    <select id="did_ruas" name="did_ruas" class="form-control" required="required" onchange="prov_ruas(this,'update')"></select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Status</label>
                                                                    <select id="did_status" name="did_status" class="form-control" required="required"></select>
                                                                </div>   
                                                                <div class="form-group">
                                                                    <label class="control-label">Nilai PPJT</label>
                                                                    <input id="dnilai_ppjt_awal" name="dnilai_ppjt_awal" type="text" required="required" class="form-control decformat number" />
                                                                </div>
                                                                <!-- <div class="form-group">
                                                                    <label class="control-label">Nilai PPJT Akhir</label>
                                                                    <input id="dnilai_ppjt_akhir" name="dnilai_ppjt_akhir" type="text" required="required" class="form-control decformat number" />
                                                                </div> -->
                                                                <div class="form-group">
                                                                    <label class="control-label">Deskripsi Perjanjian</label>
                                                                    <textarea id="ddeskripsi_perjanjian" name="ddeskripsi_perjanjian" class="form-control"></textarea>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Tahun Perolehan</label>
                                                                    <input type="text" class="form-control" id="dtp" name="dtp"  onkeypress="return isNumber(event)">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <!-- <div class="form-group">
                                                                    <label class="control-label">Nilai BPK</label>
                                                                    <input id="dnilai_bpk"  onkeypress="return isNumber(event)" name="dnilai_bpk" type="text" required="required" class="form-control decformat number" />
                                                                </div> -->
                                                                <div class="form-group">
                                                                    <label class="control-label">Nomor PPJT</label>
                                                                    <input type="text" class="form-control" id="dnp" name="dnp"  onkeypress="return isNumber(event)">
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Tahun Konstruksi</label>
                                                                    <input type="text" class="form-control" id="dtk" name="dtk"  onkeypress="return isNumber(event)">
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Masa Konsesi</label>
                                                                    <input id="dmasa_konsesi" name="dmasa_konsesi" type="text" required="required" class="form-control" />
                                                                </div>                                                            
                                                                <!-- <div class="form-group">
                                                                    <label for="exampleInput8">Nilai Investasi (Rupiah)</label>
                                                                    <input id="djumlah" name="djumlah"  type="text" class="form-control decformat number" required="required"  onkeypress="return isNumber(event)"/>
                                                                </div> -->
                                                                <div class="form-group">
                                                                    <label class="control-label">Kondisi</label>
                                                                    <select id="dkondisi" name="dkondisi" class="form-control" required="required"></select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Kewajiban BUJT</label>
                                                                    <textarea id="dkewajiban_bujt" name="dkewajiban_bujt" class="form-control"></textarea>
                                                                </div>                                                             
                                                            </div>
                                                    </div>
                                                </div>
                                            </div>

                                            
                                            <div class="col-md-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>LOKASI</label>
                                                    </div>
                                                    <div class="panel-body">                                   
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="exampleInput7">Provinsi</label>
                                                                    <select id="dkd_prov" name="dkd_prov" class="form-control" required="required">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="exampleInput7">Kabupaten/Kota</label>
                                                                    <select id="dkd_kabkot" name="dkd_kabkot" class="form-control">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>                                                           
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label for="exampleInput7">Kecamatan</label>
                                                                    <select id="dkd_camat" name="dkd_camat" class="form-control">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>
                                                                <div class="form-group">
                                                                    <label for="exampleInput7">Kelurahan/Desa</label>
                                                                    <select id="dkd_lurah" name="dkd_lurah" class="form-control">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>SPESIFIKASI LOKASI</label>
                                                    </div>
                                                    <div class="panel-body">                                   
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label class="control-label">Titik Awal (x1)</label>
                                                                    <input id="dx1" name="dx1" type="text" required="required" class="dx1 form-control" />
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">Titik Akhir (x2)</label>
                                                                    <input id="dx2" name="dx2" type="text" required="required" class="dx2 form-control" />
                                                                </div>                                                           
                                                            </div>                                                                  
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label class="control-label">(y1)</label>
                                                                    <input id="dy1" name="dy1" type="text" required="required" class="dy1 form-control" />
                                                                </div>
                                                                <div class="form-group">
                                                                    <label class="control-label">(y2)</label>
                                                                    <input id="dy2" name="dy2" type="text" required="required" class="dy2 form-control" />
                                                                </div>                                                           
                                                            </div>    
                                                        
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12" style="display:none;">
                                                    <textarea id="xgeom" name="xgeom" class="form-control"></textarea>
                                                </div>
                                            </div>
                                </form>
                            </div>
                            <div class="overlay">
                                <div id="loading-img"></div>
                            </div>
                            <!-- /.box-body -->

                            <div class="modal-footer">
                                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Tutup</button>
                                <button class="btn btn-sm btn-primary" type="button" onclick="javascript:update_form_pagu();"><i class="fa fa-check"></i>Simpan</button>
                                <!--button type="button" class="js-swal-success btn btn-light push">
                                    <i class="fa fa-check-circle text-success mr-1"></i> Launch Dialog
                                </button-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    /* Fungsi formatRupiah */
    function formatRupiah(angka, prefix) {
        var number_string = angka.replace(/[^.\d]/g, '').toString(),
                split = number_string.split(','),
                sisa = split[0].length % 3,
                rupiah = split[0].substr(0, sisa),
                ribuan = split[0].substr(sisa).match(/\d{3}/gi);

        // tambahkan titik jika yang di input sudah menjadi angka ribuan
        if (ribuan) {
            separator = sisa ? ',' : '';
            rupiah += separator + ribuan.join(',');
        }

        rupiah = rupiah;
        return rupiah;
    }
</script>
<!-- END Slide Right Modal -->
