<!-- Slide Right Modal -->
<style>
    #maxx {
        width: 100%;
        height: 100%;
        padding: 0;
        margin:0;
    }
    #maxxs {
        height: 100%;
        border-radius: 0;
        color:#333;
        overflow:auto;
    }

    #loading-img {
        background: url(<?= base_url(); ?>assets/img/load.gif) center center no-repeat;
        height: 100%;
        z-index: 1000;
    }

    .overlay {
        background: #e9e9e9;
        display: none;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        opacity: 0.5;
    }

    .bootstrap-tagsinput {
        min-height: 100px;
        width: 100%;
    }

    input[type='number'] {
        -moz-appearance:textfield;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }

</style>
<div class="modal fade rotate" id="modal-detail" tabindex="-2" role="dialog" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-detail" id="maxx">
        <div class="modal-content"  id="maxxs">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title tbhItem">Tambah Detail Paket Pagu Indikatif</h3>
                </div>
                <div class="block-content">
                    <!--                    <h2 class="content-heading border-bottom mb-4 pb-2 rujuk">Rujukan</h2>
                                        <hr class="rujuk">
                                        <div class="row rujuk">
                                            <div class="col-sm-12">
                                                <div class="block">
                                                    <ul class="nav nav-tabs nav-tabs-right" data-toggle="tabs">

                                                         <li class="active">
                                                             <a href="#btabswo-static2-xrams">RAMS</a>
                                                         </li>

                                                        <li class="active" onclick="tabirms('3')">
                                                            <a href="#btabswo-static2-xirmsv3">IRMSv3</a>
                                                        </li>
                                                        <li onclick="tabprogram('3')">
                                                            <a href="#btabswo-static2-xeprogram">e-PROGRAM</a>
                                                        </li>
                                                        <li  onclick="tabrenstra('3')">
                                                            <a href="#btabswo-static2-xrenstra">Renstra</a>
                                                        </li>
                                                        <li onclick="tabdpr('3')">
                                                            <a href="#btabswo-static2-xusulandpr">Usulan DPR</a>
                                                        </li>
                                                        <li onclick="tabpemda('3')">
                                                            <a href="#btabswo-static2-xusulanpemda">Usulan PEMDA</a>
                                                        </li>
                                                        <li class="pull-left">
                                                            <ul class="block-options push-10-t push-10-l">

                                                                <li>
                                                                    <button type="button" data-toggle="block-option" data-action="content_toggle"><i class="si si-arrow-up"></i></button>
                                                                </li>

                                                            </ul>
                                                        </li>
                                                    </ul>
                                                    <div class="block-content tab-content">

                                                        <div class="tab-pane active" id="btabswo-static2-xirmsv3">
                                                            <h4 class="font-w300 push-15">WP IRMSv3</h4>
                                                            <p>
                                                            <table class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer" id="tirmsv3_wp3">
                                                                <thead>
                                                                <th>ID</th>
                                                                <th>PMS TREATMENT</th>
                                                                <th>TREATMENT COST</th>
                                                                <th>PMS BUDGET CAT</th>
                                                                <th>SCN YEAR NUM</th>
                                                                <th>ROUTE NAME</th>
                                                                <th>LENGTH</th>
                                                                <th>LANE DIR NAME</th>
                                                                <th>OFFSET FROM</th>
                                                                <th>OFFSET TO</th>
                                                                <th>BM REGION</th>
                                                                <th>BM PROVINCE</th>
                                                                <th>Penanda</th>
                                                                </tr>
                                                                </thead>
                                                            </table>
                                                            </p>
                                                        </div>
                                                        <div class="tab-pane" id="btabswo-static2-xeprogram">
                                                            <h4 class="font-w300 push-15">e-Program</h4>
                                                            <p>
                                                            <table class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer" style="width: 100%;" id="teprogram3">
                                                                <thead>
                                                                    <tr>
                                                                      <th>Penanda</th>
                                                                        <th>Usulan</th>
                                                                        <th>Nama Skenario</th>
                                                                        <th>Prioritas</th>
                                                                        <th>Biaya Thn 1</th>
                                                                        <th>Biaya Thn 2</th>
                                                                        <th>Biaya Thn 3</th>
                                                                        <th>Biaya Thn 4</th>
                                                                        <th>Biaya Thn 5</th>
                                                                        <th>Biaya Thn 6</th>
                                                                        <th>Panjang Thn 1</th>
                                                                        <th>Panjang Thn 2</th>
                                                                        <th>Panjang Thn 3</th>
                                                                        <th>Panjang Thn 4</th>
                                                                        <th>Panjang Thn 5</th>
                                                                        <th>Panjang Thn 6</th>
                                                                        <th>Benefit</th>
                                                                        <th>ID Skenario</th>
                                                                        <th>GID</th>
                                                                        <th>Penanda</th>

                                                                    </tr>
                                                                </thead>

                                                            </table>
                                                            </p>
                                                        </div>
                                                        <div class="tab-pane" id="btabswo-static2-xrenstra" style="overflow:scroll">
                                                            <h4 class="font-w300 push-15">RENSTRA</h4>
                                                            <p>
                                                            <table class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer" style="width: 100%;" id="trenstr3a">
                                                              <thead>
                                                                  <tr>
                                                                        <th>Penanda</th>
                                                                      <th>ID Renstra</th>
                                                                       <th>ID Target</th>
                                                                      <th>Target</th>
                                                                      <th>Nilai</th>
                                                                      <th>Satuan</th>
                                                                      <th>Tahun</th>

                                                                  </tr>
                                                              </thead>
                                                            </table>
                                                            </p>
                                                        </div>
                                                        <div class="tab-pane" id="btabswo-static2-xusulandpr">
                                                            <h4 class="font-w300 push-15">USULAN DPR</h4>
                                                            <p>
                                                            <table class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer" style="width: 100%;" id="tusulandpr3">
                                                              <thead>
                                                                  <tr>
                                                                      <th class="hidden">ID Usulan</th>
                                                                      <th>Penanda</th>
                                                                      <th style="width:500px !important;">Uraian</th>
                                                                      <th>Sumber</th>
                                                                      <th>Tanggal</th>
                                                                      <th>Pengusul</th>
                                                                      <th>Provinsi</th>
                                                                      <th>RKAKL Vol.</th>
                                                                      <th>RKAKL Biaya</th>
                                                                      <th>S.Kewenangan</th>
                                                                      <th>Evaluasi</th>
                                                                      <th>FS</th>
                                                                      <th>DED</th>
                                                                      <th>Lahan</th>
                                                                      <th>Dok. Ling</th>
                                                                      <th>Keterangan</th>
                                                                      <th>Usulan Volume</th>
                                                                      <th>Usulan Biaya</th>
                                                                       <th>Penanda</th>
                                                                  </tr>
                                                              </thead>
                                                            </table>
                                                            </p>
                                                        </div>

                                                        <div class="tab-pane" id="btabswo-static2-xusulanpemda">
                                                            <h4 class="font-w300 push-15">USULAN PEMDA</h4>
                                                            <p>
                                                            <table class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer" style="width: 100%;" id="tusulanpemda3">
                                                              <thead>
                                                                  <tr>
                                                                      <th class="hidden">ID Usulan</th>
                                                                      <th>Penanda</th>
                                                                      <th style="width:500px !important;">Uraian</th>
                                                                      <th>Sumber</th>
                                                                      <th>Tanggal</th>
                                                                      <th>Pengusul</th>
                                                                      <th>Provinsi</th>
                                                                      <th>RKAKL Vol.</th>
                                                                      <th>RKAKL Biaya</th>
                                                                      <th>S.Kewenangan</th>
                                                                      <th>Evaluasi</th>
                                                                      <th>FS</th>
                                                                      <th>DED</th>
                                                                      <th>Lahan</th>
                                                                      <th>Dok. Ling</th>
                                                                      <th>Keterangan</th>
                                                                      <th>Usulan Volume</th>
                                                                      <th>Usulan Biaya</th>
                                                                       <th>Penanda</th>
                                                                  </tr>
                                                              </thead>
                                                            </table>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>-->

                    <div class="col-md-12" style="background:#a7a7a7;">
                        <div class="container" style="background:#fff;">
                            <div class="row">
                                <div class="col-md-12"><h2 class="content-heading border-bottom mb-4 pb-2">Form Paket</h2><hr></div>
                                <input type="hidden" id="modeform">
                                <div class="col-md-12" id="boxpagu">
                                  <div class="col-md-2" id="kosong"></div>
                                  <div class="col-md-4 tab-pagu-detail">
                                    <h5>Usulan</h5>
                                    <table id="boxs"  class="table table-bordered">
                                      <tr>
                                        <td>Propinsi Fisik</td>
                                        <td><div id='yupf'></div><div style="display:none;" id='yupf1'></div></td>
                                      </tr>
                                      <tr>
                                        <td>Propinsi Non Fisik</td>
                                        <td><div id='yupnf'></div><div style="display:none;" id='yupnf1'></div></td>
                                      </tr>
                                      <tr>
                                        <td>Preservasi Jalan</td>
                                        <td><div id='yuppj'></div><div style="display:none;" id='yuppj1'></div></td>
                                      </tr>
                                      <tr>
                                        <td>Pembangunan Jalan</td>
                                        <td><div id='yuppem'></div><div style="display:none;" id='yuppem1'></div></td>
                                      </tr>
                                      <tr>
                                        <td>Jembatan</td>
                                        <td><div id='yupj'></div><div style="display:none;" id='yupj1'></div></td>
                                      </tr>
                                      <tr>
                                        <td>JBH</td>
                                        <td><div id='yupjbh'></div><div style="display:none;" id='yupjbh1'></div></td>
                                      </tr>
                                    </table>
                                  </div>
                                  <div class="col-md-4 tab-pagu-detail">
                                    <h5>Pagu</h5>
                                    <table  id="boxs"  class="table table-bordered">
                                      <tr>
                                        <td>Propinsi Fisik</td>
                                        <td><div id='yppf'></div></td>
                                      </tr>
                                      <tr>
                                        <td>Propinsi Non Fisik</td>
                                          <td><div id='yppnf'></div></td>
                                      </tr>
                                      <tr>
                                        <td>Preservasi Jalan</td>
                                        <td><div id='ypppj'></div></td>
                                      </tr>
                                      <tr>
                                        <td>Pembangunan Jalan</td>
                                          <td><div id='ypppem'></div></td>
                                      </tr>
                                      <tr>
                                        <td>Jembatan</td>
                                        <td><div id='yppj'></div></td>
                                      </tr>
                                      <tr>
                                        <td>JBH</td>
                                        <td><div id='yppjbh'></div></td>
                                      </tr>
                                    </table>
                                  </div>
                                </div>
                                <form role="form" id="frm-detail">
                                    <input type="hidden" id="ysource_rujukan">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <input type="hidden" id="yurutans" value="3">
                                            <label class="control-label">Tahun Anggaran</label>
                                            <select id="ythang_sel" name="ythang_sel" class="form-control" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                            <input type="hidden" name="thang_sel" id="thang_sel"  />
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label class="control-label">Program</label>
                                            <select id="ykd_program_sel" name="ykd_program_sel" class="form-control" required="required" disabled>
                                                <option value="">--Pilih--</option>
                                            </select>
                                            <input type="hidden" name="kd_program_sel" name="kd_program_sel" required="required"/>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">Kegiatan</label>
                                            <select id="ykd_kegiatan_sel" name="ykd_kegiatan_sel" class="form-control" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                            <input type="hidden" name="kd_kegiatan_sel" name="kd_kegiatan_sel" required="required"/>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <?php
                                            if ($this->session->konfig_tahun_ang > 2020) {
                                                echo '<label class="control-label">KRO </label>';
                                            } else {
                                                echo '<label class="control-label">Output </label>';
                                            }
                                            ?>
                                            <select id="ykd_output_sel" name="ykd_output_sel" class="form-control"  onchange="javascript:handleOutput2(this);" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <?php
                                            if ($this->session->konfig_tahun_ang > 2020) {
                                                echo '<label class="control-label">RO </label>';
                                            } else {
                                                echo '<label class="control-label">Sub Output </label>';
                                            }
                                            ?>
                                            <select id="ykd_sub_output_sel" name="ykd_sub_output_sel" class="form-control" onchange="javascript:handleSoutput2(this);" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="control-label">Komponen</label>
                                            <select id="ykd_komponen_sel" name="ykd_komponen_sel" class="form-control" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="control-label">Kode Paket</label>
                                            <input id="ykd_sub_komponen_sel" name="ykd_sub_komponen_sel" type="text" required="required" class="form-control" required="required"/>
                                            <input type="hidden" name="kd_sub_komponen_sel" />
                                        </div>
                                    </div>
                                    <div class="col-md-7">
                                        <div class="form-group">
                                            <label class="control-label">Nama Paket</label>
                                            <input id="ynama_sub_komponen_sel" name="ynama_sub_komponen" type="text" required="required" class="form-control" />
                                            <input type="hidden" name="nama_sub_komponen_sel" />
                                        </div>
                                    </div>

                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Jenis Kontrak</label>
                                            <select id="yjnskontrak" name="yjnskontrak" class="form-control" required="required" disabled>
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div id="yfsk" style="display:none;">
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC DED Status</label>
                                                <select id="yrc_ded_status" name="yrc_ded_status" class="form-control" required="required" disabled>
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC FS Status </label>
                                                <select id="yrc_fs_status" name="yrc_fs_status" class="form-control" required="required" disabled>
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8" onchnge="">RC Lahan Status</label>
                                                <select id="yrc_lahan_status" name="yrc_lahan_status" class="form-control" required="required" disabled>
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8">RC Dockling Status</label>
                                                <select id="yrc_doklin_status" name="yrc_doklin_status" class="form-control" required="required" disabled>
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="ynon" style="display:none;">
                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8">KAK</label>
                                                <select id="ykak" name="ykak" class="form-control" required="required" disabled>
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-2">
                                            <div class="form-group">
                                                <label for="exampleInput8">RAB</label>
                                                <select id="yrab" name="yrab" class="form-control" required="required" disabled>
                                                    <option value="">--Pilih--</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Provinsi</label>
                                            <select id="yprov" name="yprov" class="form-control" required="required" disabled>
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-12"><h2 class="content-heading border-bottom mb-4 pb-2">Form Detail</h2><hr></div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Kab/Kota</label>
                                            <select id="ykabkot" name="ykabkot" class="form-control" required="required" onchange="javascript:handleRuaskabkot(this, 'y');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Jenis Belanja</label>
                                            <select id="ykdgbkpk" name="ykdgbkpk" class="form-control" onchange="javascript:handleJnsBelanja(this, 'y');" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Jenis Akun</label>
                                            <select id="ykdakun" name="ykdakun" class="form-control" onchange="javascript:handleAkun(this, 'y');" required="required">
                                                <option value="">--Pilih--</option>

                                            </select>
                                        </div>
                                    </div>

                                    <!--new code-->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Beban/Jns Bantuan/Cara Penarikan </label>
                                            <select id="ysumber" name="ysumber" required="required"  class="form-control" onchange="javascript:handleSumber(this, 'y');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">KPPN</label>
                                            <select id="ykdkppn" name="ykdkppn"  class="form-control" required="required" onchange="javascript:ChangeSumberList(this, 'y');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Register </label>
                                            <select id="yregister" name="yregister" class="form-control selectpicker with-ajax" data-live-search="true" disabled>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="col-md-6">
                                                <div class="panel panel-default" id="ypanelHitung">
                                                    <div class="panel-heading">
                                                        <table>
                                                            <tr>
                                                                <td><label>Cara Hitung</label></td>
                                                                <td>
                                                                    <div class="form-group" id="yradioHitung">
                                                                        <label class="radio-inline" style="display:none;"><input name="ycaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('y');" value="0"/><span style="padding-left:50px; display:none"/></label>
                                                                        <label class="radio-inline"><input name="ycaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('y');" value="1"/><span style="padding-left:50px"/>Non PPN</label>
                                                                        <label class="radio-inline"><input name="ycaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('y');" value="2"/><span style="padding-left:30px"/>Netto</label>
            <!--                                                            <label class="radio-inline"><input name="caraHitung" type="radio" disabled onchange="javascript:handleRadioHitung(this);"/ value="3">Bruto</label>-->
                                                                        <label class="radio-inline"><input name="ycaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('y');" value="4"/><span style="padding-left:70px"/>Non Sharing</label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="8"><p id="ytext1" style="color:red;"/></td></tr>
                                                            <tr>
                                                                <td style="text-align:right">PHLN </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputPHLN" name="yinputPHLN" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('y');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
<!--                                                                <td><input type="text" id="xinputPHLN2" name="xinputPHLN2" style="width:155px; display:none;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('x');"></td>-->
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="yinputKPPNph" name="yinputKPPNph"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputRMPdp" name="yinputRMPdp" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('y');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
<!--                                                                <td><input type="text" id="xinputRMPdp2" name="xinputRMPdp2" style="width:155px; display:none;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('x');"></td>-->
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="yinputKPPNrm" name="yinputKPPNrm"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RPLN Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputRPLNPdp" name="yinputRPLNPdp" style="width:70px; display:block;" class="form-control" onkeyup="hitungk('y');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
<!--                                                                <td><input type="text" id="xinputRPLNPdp2" name="xinputRPLNPdp2" style="width:155px; display:none;" class="form-control number" placeholder="Rp"></td>-->
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="yinputKPPNrp" name="yinputKPPNrp"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
<!--                                                            <tr id="ytotalkppn" style="display:none">
                                                                <td style="text-align:right">Total </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="5"><input type="text" id="yTotal" name="yTotal" style="width:155px" class="form-control number" placeholder="Rp"></td>
                                                            </tr>-->
                                                            <tr>
                                                                <td style="text-align:right; width:85px">Register Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="6">
                                                                    <select id="yregisterpdp" name="yregisterpdp" class="form-control selectpicker with-ajax" data-live-search="true" disabled>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" style="height:43px">
                                                        <label>Catatan Akun (Optional)</label>
                                                    </div>
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="3">&nbsp</td></tr>
                                                            <tr>
                                                                <td style="text-align:right">Halaman 4</td>
                                                                <td style="width:20px">&nbsp;</td>
                                                                <td>
                                                                    <textarea id="yinputHal4" name="yinputHal4" style="width:400px;" class="form-control"></textarea>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">Dipa</td>
                                                                <td style="width:20px">&nbsp;</td>
                                                                <td>
                                                                    <textarea id="yinputDipa" name="yinputDipa" style="width:400px;" class="form-control"></textarea>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">PPK (Header 1)</label>
<!--                                            <select id="yid_ppk" name="yid_ppk" class="form-control" required="required" onchange="javascript:handleCekPPK(this, 'y');">-->
                                            <select id="yid_ppk" name="yid_ppk" class="form-control" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="" onchnge="">Header 2 (Optional)</label>
                                            <input id="yheader2" name="yheader2" type="text" class="form-control" maxlength="87"/>
                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <table>
                                                        <tr>
                                                            <td><label></label></td>
                                                            <td>
                                                                <div id="yradioVolumeKegiatan">
                                                                    <label class="radio-inline id_radioRuas"><input name="yradioVolume" type="radio" onchange="javascript:handleRadioVolume('y');" value="1"/><span style="padding-left:30px"/>Ruas</label>
                                                                    <label class="radio-inline id_radioJembatan"><input name="yradioVolume" type="radio" onchange="javascript:handleRadioVolume('y');" value="2"/><span style="padding-left:50px"/>Jembatan</label>
                                                                    <label class="radio-inline id_radioHonor"><input name="yradioVolume" type="radio" onchange="javascript:handleRadioVolume('y');" value="3"/><span style="padding-left:33px"/>Uraian</label>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                </div>
                                                <div class="panel-body">
                                                    <div id="yradioHonor" style="display:none">
                                                        <div class="row">
                                                            <div class="col-md-8">
                                                                <table>
                                                                    <tr>
                                                                        <td><input type="number" id="yinputVol1" name="yinputVol1" style="width:70px; display:block;" class="form-control" value="0" onkeyup="hitungvolume('y');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="yinputSat1" name="yinputSat1" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('y');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="yinputVol2" name="yinputVol2" style="width:70px; display:block;" class="form-control" value="0" onkeyup="hitungvolume('y');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="yinputSat2" name="yinputSat2" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('y');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="yinputVol3" name="yinputVol3" style="width:70px; display:block;" class="form-control" value="0" onkeyup="hitungvolume('y');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="yinputSat3" name="yinputSat3" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('y');"></td>
                                                                        <td style="width:30px">&nbsp;</td>
                                                                        <td><input type="number" id="yinputVol4" name="yinputVol4" style="width:70px; display:block;" class="form-control" value="0" onkeyup="hitungvolume('y');"></td>
                                                                        <td>X</td>
                                                                        <td><input type="text" id="yinputSat4" name="yinputSat4" style="width:70px; display:block;" class="form-control" onkeyup="hitungvolume('y');"></td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <button class="btn btn-sm btn-success" type="button" onclick="javascript:modalSBM('y');">SBM</button>
                                                            </div>
                                                        </div>

                                                        <div class="row" style="display: none;">
                                                            <div class="col-md-12">
                                                                <input type="text" id="ykdsbu" name="ykdsbu" class="form-control">
                                                            </div>
                                                        </div>

                                                        <div class="row">
                                                            <div class="col-md-12 uraians">
                                                                <div class="form-group">
                                                                    <label for="exampleInput1" onchnge="">Uraian</label>
                                                                    <input id="ydetail" name="ydetail" type="text" required="required" class="form-control" />
                                                                    <!--                                            input id="id_usulan" name="id_usulan" type="text" required="required" class="form-control" /-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div id="yradioRuas" style="display:none">
                                                        <div class="row">
                                                            <div class="col-md-6 ruasjalan" style="display: none;">
                                                                <div class="form-group">
                                                                    <label class="col-md-6">Ruas Jalan</label><div class="col-md-6" style="text-align:right;padding:0px;"><label id="ymaxRuas" style="color:blue;"/></div>
                                                                    <select id="id_ruas" name="id_ruas" class="form-control" onchange="javascript:handleRuas(this, 'y');" required="required">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
<!--                                                                    <input type="hidden" name="nm_ruas" id="nm_ruas">-->
                                                                </div>
                                                            </div>
                                                            <!--                                                            <div class="col-md-3 staw" style="display: none;">
                                                                                                                            <div class="form-group">
                                                                                                                                <label for="exampleInput2" onchnge="">STA Awal</label>
                                                                                                                                <select id="sta_awal_sel" name="sta_awal_sel" required="required" class="form-control" onchange="javascript:handleSTAdetail('awal', this);" required="required">
                                                                                                                                    <option value="">--Pilih--</option>
                                                                                                                                </select>
                                                                                                                                <input type="hidden" name="sta_awal">
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                        <div class="col-md-3 staw" style="display: none;">
                                                                                                                            <div class="form-group">
                                                                                                                                <label for="exampleInput3" onchnge="">STA Akhir</label>
                                                                                                                                <select id="sta_akhir_sel" name="sta_akhir_sel" required="required" class="form-control" onchange="javascript:handleSTAdetail('akhir', this);">
                                                                                                                                    <option value="">--Pilih--</option>
                                                                                                                                </select>
                                                                                                                                <input type="hidden" name="sta_akhir">
                                                                                                                            </div>
                                                                                                                        </div>-->
                                                            <div class="col-md-3 staw" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput2" onchnge="">STA Awal (Meter)</label>
                                                                    <input type="number" id="sta_awal_sel" name="sta_awal_sel" class="form-control"  onkeyup="javascript:handleSTAdetail('awal', this);" required="required" placeholder="e.g. 100"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 staw" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput3" onchnge="">STA Akhir (Meter)</label>
                                                                    <input type="number" id="sta_akhir_sel" name="sta_akhir_sel" required="required" class="form-control" onkeyup="javascript:handleSTAdetail('akhir', this);" placeholder="e.g. 2100"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6 jembatans" style="display: none;">
                                                                <div class="form-group">
                                                                    <label>Jembatan</label>
                                                                    <select id="yid_jembatan" name="yid_jembatan"  class="form-control" onchange="javascript:handleJembatan(this, 'y');" required="required">
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
<!--                                                                    <input type="hidden" name="nm_jembatan" id="nm_jembatan">-->
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longs" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput5" onchange=""  id="l5"></label>
                                                                    <input id="ylongitude" name="ylongitude" type="text" required="required" class="form-control" onkeyup="staAwal('y');"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longs" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput6" onchange=""  id="l6"></label>
                                                                    <input id="ylatitude" name="ylatitude" type="text" required="required" class="form-control" onkeyup="staAwal('y');"/>
                                                                </div>
                                                            </div>

                                                            <div class="col-md-3 longsa" style="display: none;">
                                                                <div class="form-group">
                                                                    <label for="exampleInput5" onchnge="" id="l7"> </label>
                                                                    <input id="ylongitude2" name="ylongitude2" type="text" required="required" class="form-control" onkeyup="staAkhir('y');"/>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-3 longsa" style="display: none;" >
                                                                <div class="form-group">
                                                                    <label for="exampleInput6" id="l8"> </label>
                                                                    <input id="ylatitude2" name="ylatitude2" type="text" required="required" class="form-control" onkeyup="staAkhir('y');"/>
                                                                </div>
                                                            </div>
                                                            <!--                                                            <div class="col-md-12">
                                                                                                                            <p id="ytextMaxx" style="color:red;"/>
                                                                                                                        </div>-->
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12" style="display:none;">
                                            <textarea id="ygeom" name="ygeom" class="form-control"></textarea>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">Volume</label>
                                            <!--<input id="yvolume" name="yvolume" type="text" required="required" class="form-control number" />-->
                                            <input id="volume" name="volume" type="text" required="required" class="form-control vol" />
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="exampleInput8" onchnge="">Satuan</label>
<!--                                            <input id="ysatuan" name="ysatuan" type="text" required="required" class="form-control" />-->
                                            <select id="ysatuan" name="ysatuan" class="form-control" required="required">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="form-inline"style="display:inline-block; width:50px;">
                                            <br>
                                            <label class="form-control" style="border:none">X</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="exampleInput8">Harga Satuan (Rupiah)</label>
                                            <input type="text" id="yhargasat" name="yhargasat" required="required" class="form-control decformat2 number" />
                                            <input type="hidden" id="hargasat" name="hargasat"/>
                                        </div>
                                    </div>
                                    <div class="col-md-1" style="display:inline-block; width:60px;">
                                        <div class="form-inline">
                                            <br>
                                            <label class="form-control" style="border:none;">=</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="exampleInput8" >Jumlah (Rupiah)</label>
                                            <input id="yjumlah" name="yjumlah" type="text" class="form-control number" disabled/>
                                            <input id="totalpagu" name="totalpagu" type="hidden" />
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="col-md-6" id="ypanelNonSharing" style="display:none;">
                                                <div class="panel panel-default">
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="8"><p id="ytext2" style="color:red;"/></td></tr>
                                                            <tr>
                                                                <td style="text-align:right">PHLN </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputNSPHLN" name="yinputNSPHLN" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="yinputNSPHLN2" name="yinputNSPHLN2" style="width:155px;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('y');"></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputNSRMPdp" name="yinputNSRMPdp" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="yinputNSRMPdp2" name="yinputNSRMPdp2" style="width:155px;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('y');"></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RPLN Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputNSRPLNPdp" name="yinputNSRPLNPdp" style="width:70px;" value="0" class="form-control" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="yinputNSRPLNPdp2" name="yinputNSRPLNPdp2" style="width:155px;" class="form-control number" placeholder="Rp" disabled></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">Total </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="3"><input type="text" id="yTotal" name="yTotal" style="width:200px" class="form-control number" placeholder="Rp" disabled></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6" id="ypanelPaguBlokir" style="display:none;">
                                                <div class="panel panel-default">
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="5"><p id="ytextBlokir" style="color:red;"/></td></tr>
                                                            <tr id="yblokirs1" style="display:none;">
                                                                <td style="text-align:right;">Blokir PHLN </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="yinputBlokirPHLN" name="yinputBlokirPHLN" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('y');" disabled></td>
                                                            </tr>
                                                            <tr id="yblokirs2" style="display:none;">
                                                                <td style="text-align:right;">Blokir RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="text" id="yinputBlokirRMPdp" name="yinputBlokirRMPdp" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('y');" disabled></td>
                                                            </tr>
                                                            <tr id="yblokirs3" style="display:none;">
                                                                <td style="text-align:right;">Blokir RPLN Pdp </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="yinputBlokirRPLNPdp" name="yinputBlokirRPLNPdp" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('y');" disabled></td>
                                                            </tr>
                                                            <tr id="yrphblok" style="display:none;">
                                                                <td style="text-align:right;">Blokir </td>
                                                                <td style="width:10px;">&nbsp;</td>
                                                                <td><input type="text" id="yinputRphBlokir" name="yinputRphBlokir" style="width:200px;" class="form-control number" placeholder="Rp" onkeyup="hitungBlokir('y');" disabled></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="panel panel-default">
                                            <div class="panel-heading">
                                                <label>Blokir</label>
                                            </div>
                                            <div class="panel-body" style="padding-bottom:0px;">
                                                <table class="table table-borderless">
                                                    <tr>
                                                        <td style="text-align:right; width:110px; text-align:center; vertical-align:middle;">Kode Blokir</td>
                                                        <td style="text-align:right; width:auto;">
                                                            <select id="ykd_blokir" name="ykd_blokir" class="form-control" onchange="handleBlok(this, 'y');">
                                                                <option value="">--Pilih--</option>
                                                            </select>
                                                        </td>
                                                        <td style="text-align:right; text-align:center; vertical-align:middle; width:110px;">Blokir Detail<span style="padding-left:10px;"/></td>
                                                        <td style="text-align:left; text-align:center; vertical-align:middle; width:10px;"><label class="radio"><input name="yitemblokir" type="radio" value="1" disabled onchange="handleBlokir(this, 'y');"/>Ya</label></td>
                                                        <td style="text-align:left; text-align:center; vertical-align:middle; width:30px;"><label class="radio"><input name="yitemblokir" type="radio" value="0" disabled onchange="handleBlokir(this, 'y');"/><span style="padding-left:10px;"/>Tidak</label></td>
                                                    </tr>
                                                    <tr>
                                                        <td style="text-align:right; text-align:center; vertical-align:middle;">Uraian Blokir</td>
                                                        <td colspan="4">
                                                            <input type="text" id="yurblokir" name="yurblokir" class="form-control"/>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <!--                            <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label for="exampleInput4" class="col-md-6">Sumber Dana </label>
                                                                            <select id="ysumber" name="ysumber"  class="form-control">
                                                                                <option value="">--Pilih--</option>
                                                                            </select>

                                                                        </div>
                                                                </div>-->

                                    <!--
                                                                <div class="col-md-12 divNonFisik">
                                                                    <div class="form-group">
                                                                        <label for="exampleInput2" onchnge="">Uraian</label>
                                                                        <input id="detail" name="detail" type="text" required="required" class="form-control" />
                                                                        input id="id_usulan" name="id_usulan" type="text" required="required" class="form-control" /
                                                                    </div>
                                                                </div>
                                    -->
                                    <!--                                    <div class="col-md-12">
                                                                            <div style="display:none;" class="alert alert-success" role="alert" id="ruas_loading"><img src="<?php echo base_url() ?>assets/img/loading.gif" width="48" height="48">&nbsp;
                                                                                memuat data Ruas... &nbsp;
                                                                            </div>
                                                                        </div>-->
                                    <!--                                    <div class="col-md-6 ruasjalan" style="display: none;">
                                                                            <div class="form-group">
                                                                                <label for="exampleInput1" onchnge="" class="col-md-6">Ruas Jalan</label><div class="col-md-6" style="text-align:right;padding:0px;"> </div>
                                                                                <select id="id_ruas" name="id_ruas" class="form-control" onchange="javascript:handleRuas(this);" required="required">
                                                                                    <option value="">--Pilih--</option>
                                                                                </select>
                                                                                <input type="hidden" name="nm_ruas" id="nm_ruas">
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3 staw" style="display: none;">
                                                                            <div class="form-group">
                                                                                <label for="exampleInput2" onchnge="">STA Awal</label>
                                                                                <select id="sta_awal_sel" name="sta_awal_sel" required="required" class="form-control" onchange="javascript:handleSTAdetail('awal', this);" required="required">
                                                                                    <option value="">--Pilih--</option>
                                                                                </select>
                                                                                <input type="hidden" name="sta_awal">
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3 staw" style="display: none;">
                                                                            <div class="form-group">
                                                                                <label for="exampleInput3" onchnge="">STA Akhir</label>
                                                                                <select id="sta_akhir_sel" name="sta_akhir_sel" required="required" class="form-control" onchange="javascript:handleSTAdetail('akhir', this);">
                                                                                    <option value="">--Pilih--</option>
                                                                                </select>
                                                                                <input type="hidden" name="sta_akhir">
                                                                            </div>
                                                                        </div>-->

                                    <!--                                    <div class="col-md-6 jembatans" style="display: none;">
                                                                            <div class="form-group">
                                                                                <label for="exampleInput4" class="col-md-6">Jembatan</label><div class="col-md-6" style="text-align:right;padding:0px;"></div>
                                                                                <select id="yid_jembatan" name="yid_jembatan"  class="form-control" onchange="javascript:handleJembatan(this);" required="required">
                                                                                    <option value="">--Pilih--</option>
                                                                                </select>
                                                                                <input type="hidden" name="nm_jembatan" id="nm_jembatan">
                                                                            </div>
                                                                        </div>-->
                                    <!--                                    <div class="col-md-3 longs" style="display: none;">
                                                                            <div class="form-group">
                                                                                <label for="exampleInput5" onchange=""  id="l5"></label>
                                                                                <input id="ylongitude" name="ylongitude" type="text" required="required" class="form-control" />
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3 longs" style="display: none;">
                                                                            <div class="form-group">
                                                                                <label for="exampleInput6" onchange=""  id="l6"></label>
                                                                                <input id="ylatitude" name="ylatitude" type="text" required="required" class="form-control" />
                                                                            </div>
                                                                        </div>

                                                                        <div class="col-md-3 longsa" style="display: none;">
                                                                            <div class="form-group">
                                                                                <label for="exampleInput5" onchnge="" id="l7"> </label>
                                                                                <input id="ylongitude2" name="ylongitude2" type="text" required="required" class="form-control" />
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3 longsa" style="display: none;" >
                                                                            <div class="form-group">
                                                                                <label for="exampleInput6" id="l8"> </label>
                                                                                <input id="ylatitude2" name="ylatitude2" type="text" required="required" class="form-control" />
                                                                            </div>
                                                                        </div>-->

                                    <!--new code-->
                                    <!--
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Beban/Jns Bantuan/Cara Penarikan </label>
                                            <select id="ysumber" name="ysumber" required="required"  class="form-control" onchange="javascript:handleSumber(this, 'y');">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Register </label>
                                            <select id="yregister" name="yregister" class="form-control">
                                                <option value="">--Pilih--</option>
                                            </select>
                                        </div>
                                    </div>

                                   <div class="row">
                                        <div class="col-md-12">
                                            <div class="col-md-6">
                                                <div class="panel panel-default" id="ypanelHitung">
                                                    <div class="panel-heading">
                                                        <table>
                                                            <tr>
                                                                <td><label>Cara Hitung</label></td>
                                                                <td>
                                                                    <div class="form-group" id="yradioHitung">
                                                                        <label class="radio-inline" style="display:none;"><input name="ycaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('y');" value="0"/><span style="padding-left:50px; display:none"/></label>
                                                                        <label class="radio-inline"><input name="ycaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('y');" value="1"/><span style="padding-left:50px"/>Non PPN</label>
                                                                        <label class="radio-inline"><input name="ycaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('y');" value="2"/><span style="padding-left:30px"/>Netto</label>
                                                                        <label class="radio-inline"><input name="caraHitung" type="radio" disabled onchange="javascript:handleRadioHitung(this);"/ value="3">Bruto</label>
                                                                        <label class="radio-inline"><input name="ycaraHitung" type="radio" disabled onchange="javascript:handleRadioHitung('y');" value="4"/><span style="padding-left:70px"/>Non Sharing</label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                    <div class="panel-body">
                                                        <table>
                                                            <tr><td colspan="8"><p id="ytext1" style="color:red;"/></td></tr>
                                                            <tr>
                                                                <td style="text-align:right">PHLN </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputPHLN" name="yinputPHLN" style="width:70px; display:block;" class="form-control" onchange="hitung('y');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="yinputPHLN2" name="yinputPHLN2" style="width:200px; display:none;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('y');"></td>
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:5px">&nbsp;</td>
                                                                <td>
                                                                    <select id="yinputKPPNph" name="yinputKPPNph"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RM Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputRMPdp" name="yinputRMPdp" style="width:70px; display:block;" class="form-control" onchange="hitung('y');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="yinputRMPdp2" name="yinputRMPdp2" style="width:200px; display:none;" class="form-control number" placeholder="Rp" onkeyup="hitungkppn('y');"></td>
                                                                <td name="kppn" style="padding-left:10px">KPPN </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td>
                                                                    <select id="yinputKPPNrm" name="yinputKPPNrm"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right">RPLN Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td><input type="number" id="yinputRPLNPdp" name="yinputRPLNPdp" style="width:70px; display:block;" class="form-control" onchange="hitung('y');" disabled></td>
                                                                <td name="percent" style="padding-left:5px">%</td>
                                                                <td><input type="text" id="yinputRPLNPdp2" name="yinputRPLNPdp2" style="width:200px; display:none;" class="form-control number" placeholder="Rp"></td>
                                                                <td name="kppn" style="padding-left:10px">KPPN</td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td>
                                                                    <select id="yinputKPPNrp" name="yinputKPPNrp"  class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                            <tr id="ytotalkppn" style="display:none">
                                                                <td style="text-align:right">Total </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="5"><input type="text" id="yTotal" name="yTotal" style="width:200px" class="form-control number" placeholder="Rp"></td>
                                                            </tr>
                                                            <tr>
                                                                <td style="text-align:right; width:85px">Register Pdp </td>
                                                                <td style="width:10px">&nbsp;</td>
                                                                <td colspan="6">
                                                                    <select id="yregisterpdp" name="yregisterpdp" class="form-control" disabled>
                                                                        <option value="">--Pilih--</option>
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="panel panel-default">
                                                        <div class="panel-heading" style="height:43px">
                                                            <label>Catatan Akun (Optional)</label>
                                                        </div>
                                                        <div class="panel-body" style="height:170px">
                                                            <div class="form-group">
                                                                <table>
                                                                    <tr>
                                                                        <td style="text-align:right">Halaman 4</td>
                                                                        <td style="width:20px">&nbsp;</td>
                                                                        <td>
                                                                            <textarea id="yinputHal4" name="yinputHal4" style="width:400px;" class="form-control"></textarea>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td style="text-align:right">Dipa</td>
                                                                        <td style="width:20px">&nbsp;</td>
                                                                        <td>
                                                                            <textarea id="yinputDipa" name="yinputDipa" style="width:400px;" class="form-control"></textarea>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>-->

                                    <!--
                                                                <div class="col-md-12 divNonFisik">
                                                                    <div class="form-group">
                                                                        <label for="exampleInput2" onchnge="">Uraian</label>
                                                                        <input id="ydetail" name="ydetail" type="text" required="required" class="form-control" />

                                                                    </div>
                                                                </div>
                                    -->

                                    <div class="col-md-12" style="display:none;">
                                        <div class="form-group">
                                            <label for="exampleInput7" onchnge="">ID Paket</label>
                                            <input type ="text" style="display:none;" class="form-control" id="yid_paket" name="yid_paket"/>
                                        </div>
                                    </div>


                                    <div class="col-md-12" style="display: block;">
                                        <div class="form-group mdl-xtbh-detail">
                                            <label for="exampleInput7" onchnge="">Rujukan</label>
                                            <div style="float:right;">
                                                <button style='background:#5ab4ac' class="btn btn-sm" type="button" onclick="javascript:$('#modalSipro3').modal('show');tabsipro('3');">Sipro</button>
                                                <button style='background:#EDBB99' class="btn btn-sm btnDprd" type="button" onclick="javascript:$('#modalDprd3').modal('show');tabdprd('3');">DPRD</button>
                                                <button style='background:#ffeda0' class="btn btn-sm btnDiskresi" type="button" onclick="javascript:$('#modalDiskresi3').modal('show');tabdiskresi('3');">Diskresi</button>
                                                <button style='background:#FFA07A' class="btn btn-sm btnKL" type="button" onclick="javascript:$('#modalKL3').modal('show');tabkl('3');">K/L</button>
                                                <button style='background:#52BE80' class="btn btn-sm btnAkademisi" type="button" onclick="javascript:$('#modalAkademisi3').modal('show');tabakademisi('3');">Akademisi</button>
                                                <button style='background:#a6cee3' class="btn btn-sm btnPemda" type="button" onclick="javascript:$('#modalPemda3').modal('show');tabpemda('3');">Pemda</button>
                                                <button style='background:#b2df8a' class="btn btn-sm btnDpr" type="button" onclick="javascript:$('#modalDpr3').modal('show');tabdpr('3');">DPR</button>
                                                <button style='background:#cab2d6' class="btn btn-sm btnRenstra" type="button" onclick="javascript:$('#modalRenstra3').modal('show');tabrenstra('3');">Renstra</button>
                                                <button style='background:#fb9a99' class="btn btn-sm btnEprog" type="button" onclick="javascript:$('#modalEprogram3').modal('show');tabprogram('3');">e-PROGRAM</button>
                                                <!--                                                <button style='background:#fdbf6f' class="btn btn-sm btnIrms" type="button" onclick="javascript:$('#modalIrms3').modal('show');tabirms('3');">IRMSv3</button>-->
                                                <button style='background:#fdbf6f' class="btn btn-sm btnIrms" type="button" onclick="javascript:$('#modalIrms3').modal('show');tabirms('3');">IRMSv3 (jalan)</button>
                                                <button style='background:#fcc956' class="btn btn-sm btnIrmsJembatan" type="button" onclick="javascript:$('#modalIrmsJembatan3').modal('show');tabirmsjembatan('3');">IRMSv3 (jembatan)</button>
                                            </div>
                                            <br/>
                                            <div class="div-tags2">

                                            </div>
                                            <input id="tags2" name="tags2" type="hidden"  class="form-control">
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="overlay">
                                <div id="loading-img"></div>
                            </div>
                            <!-- /.box-body -->

                            <div class="modal-footer">
                                <button class="btn btn-sm btn-default btn-tutup-tb-detail" type="button" data-bs-dismiss="modal">Tutup</button>
                                <button class="btn btn-sm btn-primary" type="button" onclick="javascript:simpanForm();"><i class="fa fa-check"></i>Simpan</button>
                                <!--button type="button" class="js-swal-success btn btn-light push">
                                    <i class="fa fa-check-circle text-success mr-2"></i> Launch Dialog
                                </button-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- END Slide Right Modal -->
<!--modal rujuk-->
<!--modal DPRD-->
<div class="modal" id="modalDprd3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan DPRD
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="dprd3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Diskresi-->
<div class="modal" id="modalDiskresi3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Diskresi
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="diskresi3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal KL-->
<div class="modal" id="modalKL3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan K/L
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="kl3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal akademik-->
<div class="modal" id="modalAkademisi3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Akademisi
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="akademisi3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal pemda-->
<div class="modal" id="modalPemda3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan Pemda
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table  id="tusulanpemda3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal DPR-->
<div class="modal" id="modalDpr3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Usulan DPR
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 100%;" id="tusulandpr3"  class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="hidden">ID Usulan</th>
                                <th>Penanda</th>
                                <th style="width:500px !important;">Uraian</th>
                                <th>Sumber</th>
                                <th>Tanggal</th>
                                <th>Pengusul</th>
                                <th>Provinsi</th>
                                <th>RKAKL Vol.</th>
                                <th>RKAKL Biaya</th>
                                <th>S.Kewenangan</th>
                                <th>Evaluasi</th>
                                <th>FS</th>
                                <th>DED</th>
                                <th>Lahan</th>
                                <th>Dok. Ling</th>
                                <th>Keterangan</th>
                                <th>Usulan Volume</th>
                                <th>Usulan Biaya</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Renstra-->
<div class="modal" id="modalRenstra3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Renstra
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width: 100%;" id="trenstra3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>ID Renstra</th>
                                <th>Target</th>
                                <th>Nilai</th>
                                <th>Satuan</th>
                                <th>Tahun</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Eprogram-->
<div class="modal" id="modalEprogram3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                e-PROGRAM
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table style="width:2200px !important;" id="teprogram3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>Usulan</th>
                                <th>Nama Skenario</th>
                                <th>Prioritas</th>
                                <th>Biaya Thn 1</th>
                                <th>Biaya Thn 2</th>
                                <th>Biaya Thn 3</th>
                                <th>Biaya Thn 4</th>
                                <th>Biaya Thn 5</th>
                                <th>Biaya Thn 6</th>
                                <th>Panjang Thn 1</th>
                                <th>Panjang Thn 2</th>
                                <th>Panjang Thn 3</th>
                                <th>Panjang Thn 4</th>
                                <th>Panjang Thn 5</th>
                                <th>Panjang Thn 6</th>
                                <th>Benefit</th>
                                <th>ID Skenario</th>
                                <th>GID</th>
                            </tr>
                        </thead>

                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Irms-->
<!--<div class="modal" id="modalIrms3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
             Modal Header
            <div class="modal-header">
                IRMSv3
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
             Modal body
            <div class="modal-body">
                <div>
                    <table id="tirmsv3_wp3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>PMS TREATMENT</th>
                                <th>TREATMENT COST</th>
                                <th>PMS BUDGET CAT</th>
                                <th>SCN YEAR NUM</th>
                                <th>ROUTE NAME</th>
                                <th>LENGTH</th>
                                <th>LANE DIR NAME</th>
                                <th>OFFSET FROM</th>
                                <th>OFFSET TO</th>
                                <th>BM REGION</th>
                                <th>BM PROVINCE</th>
                                <th>Penanda</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
             Modal footer
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>-->

<!--modal Irms-->
<div class="modal" id="modalIrms3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                IRMSv3(jalan)
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tirmsv3_wp3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>OPTION</th>
                                <th>LINKNAME</th>
<!--                                <th>lane</th>-->
<!--                                <th>PMS section</th>-->
                                <th>START KM</th>
                                <th>END KM</th>
<!--                                <th>LENGTH</th>
                                <th>SCENARIO YEAR</th>
                                <th>IRI</th>
                                <th>KPI</th>
                                <th>PCI</th>-->
                                <th>TREATMENT</th>
                                <th>Treatment CostE</th>
                                <th>Kegiatan</th>
                                <th>output</th>
                                <th>Sub Output</th>
                                <th>Komponen</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<div class="modal" id="modalIrmsJembatan3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                IRMSv3(jembatan)
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tirmsv3jembatan_wp3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>OPTION</th>
                                <th>NAMA RUAS</th>
                                <th>NAMA JEMBATAN</th>
                                <th>PANJANG</th>
                                <th>PENANGANAN</th>
                                <th>ESTIMASI BIAYA</th>
                                <th>LATITUDE</th>
                                <th>LONGITUDE</th>
                                <th>KEGIATAN</th>
                                <th>OUTPUT</th>
                                <th>SUB OUTPUT</th>
                                <th>KOMPONEN</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>

<!--modal Sipro-->
<div class="modal" id="modalSipro3">
    <div class="modal-dialog modal-lg" style="width:80%">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                Sipro
                <button type="button" class="close" data-bs-dismiss="modal">&times;</button>
            </div>
            <!-- Modal body -->
            <div class="modal-body">
                <div>
                    <table id="tsipro_wp3" class="table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer">
                        <thead>
                            <tr>
                                <th>Penanda</th>
                                <th>Sa1thn_id</th>
                                <th>Jenis Arahan</th>
                                <th>Kegiatan</th>
                                <th>Output</th>
                                <th>Sub output</th>
                                <th>Komponen</th>
                                <th>Nama Sub Komponen</th>
                                <th>Rc FS</th>
                                <th>Rc DED</th>
                                <th>Rc Lahan</th>
                                <th>Rc Docklin</th>
                                <th>Isu Strategis</th>
                                <th >Sub Kawasan</th>
                                <th>Volume</th>
                                <th >Satuan</th>
                                <th>RPM</th>
                                <th >PHLN</th>
                                <th>SBSN</th>
                                <th >RMP</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>
