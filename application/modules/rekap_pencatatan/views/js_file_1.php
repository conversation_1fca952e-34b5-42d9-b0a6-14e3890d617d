<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<script>
    var xhrdatairms = null;
    var xhrdatasipro = null;
    var xhrdatarams = null;
    var xhrdataeprogram = null;
    var xhrdatarenstra = null;
    var xhrdatausulandpr = null;
    var xhrdatausulanpemda = null;

    //modal detail
    var xhrdataxirms = null;
    var xhrdatasxipro = null;
    var xhrdataxrams = null;
    var xhrdataxeprogram = null;
    var xhrdataxrenstra = null;
    var xhrdataxusulandpr = null;
    var xhrdatausxulanpemda = null;

    var tlist_paket = null;




    var user_satker = "<?php echo $this->session->users['kode_satker']; ?>";
    var id_user_get = "<?php echo $this->session->users['id_user']; ?>";
    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";
    var id_user_group = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var yearnow = "<?php echo $this->session->konfig_tahun_ang; ?>"
    var kd_satker = "<?php echo $this->session->users['kode_satker']; ?>";


//     $columns = array(
//            array('db' => 'sa1thn_id', 'dt' => 0),//ditampilkan//tarik 
//            array('db' => 'tahun_anggaran', 'dt' => 1),//tarik
//            array('db' => 'kawasan_nama', 'dt' => 2),//tarik
//            array('db' => 'subkawasan_nama', 'dt' => 3),//tarik
//            array('db' => 'kegiatan_nama', 'dt' => 4),
//            array('db' => 'suboutput_nama', 'dt' => 5),//tarik
//            array('db' => 'output_nama', 'dt' => 6),//tarik
//            array('db' => 'sub_aktivitas', 'dt' => 7),           
//            array('db' => 'satuan_output', 'dt' => 8),
//            array('db' => 'volume', 'dt' => 9),//tarik                        
//            array('db' => 'rpm', 'dt' => 10), //tarik           
//            array('db' => 'phln', 'dt' => 11),//tarik                            
//            array('db' => 'sbsn', 'dt' => 12),//tarik           
//            array('db' => 'rmp', 'dt' => 13),//tarik         
//            array('db' => 'unit_id', 'dt' => 14),//tidak ditampilkan//tarik
//            array('db' => 'program_id', 'dt' =>15),//tarik
//            array('db' => 'kegiatan_id', 'dt' =>16),//tarik
//            array('db' => 'output_id', 'dt' => 17),//tarik           
//            array('db' => 'suboutput_id', 'dt' =>18),//tarik         
//            array('db' => 'provinsi_id', 'dt' =>19),//tarik 
//            array('db' => 'kabkot', 'dt' =>20), //tarik                                   
//            array('db' => 'jenis_kontrakID', 'dt' => 21), //tarik            
//            array('db' => 'rc_FS', 'dt' => 22), //tarik                            
//            array('db' => 'rc_DED', 'dt' => 23), //tarik            
//            array('db' => 'rc_Dokling', 'dt' => 24),//tarik                             
//            array('db' => 'rc_lahan', 'dt' => 25),//tarik 
//            array('db' => 'wps_kode', 'dt' => 26),//tarik 
//            array('db' => 'kws_kode', 'dt' => 27),//tarik           
//            array('db' => 'status_konreg', 'dt' => 28),//tarik 
//            array('db' => 'status_verifikasi', 'dt' =>29),//tarik 
//            array('db' => 'status_rakor', 'dt' => 30),//tarik 
//            array('db' => 'catatan', 'dt' => 31),//tarik 
//            array('db' => 'isu_strategis_id', 'dt' => 32),//tarik 
//            array('db' => 'isu_strategis_nama', 'dt' => 33),//tarik 
//            array('db' => 'wps_nama', 'dt' => 34)
//        );





    var data_cart = [];
    var obj_data_cart = {};
    function add_to_chart(sa1thn_id, iDataIndex) {
        
        /***start tagging sipro***/
        
        var statement_tag='SIPRO::'+sa1thn_id;
        dtTagging(statement_tag);
        /**end tagging sipro***/
        
        
        
        
        
        //alert(sa1thn_id);
        var data_selected = xhrdatasipro.data.filter(x => x[0] == sa1thn_id)[0];
        //console.log('---data selected---');
        //console.log(data_selected);
        //if ($().is(':checked')) {
        var obj_arahan = {
            "sa1thn_id": sa1thn_id,
            "tahun_anggaran": data_selected[1],
            "kawasan_nama": data_selected[2],
            "subkawasan_nama": data_selected[3],
            "kegiatan_nama": data_selected[4],
            "suboutput_nama": data_selected[5],
            "output_nama": data_selected[6],
            "sub_aktivitas": data_selected[7],
            "satuan_output": data_selected[8],
            "volume": data_selected[9],
            "rpm": data_selected[10],
            "phln": data_selected[11],
            "sbsn": data_selected[12],
            "rmp": data_selected[13],
            "unit_id": data_selected[14],
            "program_id": data_selected[15],
            "kd_kegiatan": data_selected[16],
            "kd_output": data_selected[17],
            "kd_suboutput": data_selected[18],
            "provinsi_id": data_selected[19],
            "kabkot": data_selected[20],
            "jenis_kontrakID": data_selected[21],
            "rc_FS": data_selected[22],
            "rc_DED": data_selected[23],
            "rc_Dokling": data_selected[24],
            "rc_lahan": data_selected[25],
            "wps_kode": data_selected[26],
            "kws_kode": data_selected[27],
            "status_konreg": data_selected[28],
            "status_verifikasi": data_selected[29],
            "status_rakor": data_selected[30],
            "catatan": data_selected[31],
            "kd_isu": data_selected[32],
            "kd_komponen": $("#kd_komponen").val(),
            "kd_sub_komponen": $("#kd_sub_komponen").val(),
            "nama_sub_komponen": $("#nama_sub_komponen").val(),
            "kd_jns_belanja": $("#kdgbkpk").val(),
            "kdakun": $("#kdakun").val(),
            "kdkppn": $("#kdkppn").val(),
            "id_ppk": $("#id_ppk").val(),
            "rc_ded_status": $("#rc_ded_status").val(),
            "rc_fs_status": $("#rc_fs_status").val(),
            "rc_lahan_status": $("#rc_lahan_status").val(),
            "rc_doklin_status": $("#rc_doklin_status").val(),
            "jnskontrak": $("#jnskontrak").val(),
            "id_ruas": $("#id_ruas").val(),
            "sta_awal": $("#sta_awal").val(),
            "sta_akhir": $("#sta_akhir").val(),
            "id_jembatan": $("#xid_jembatan").val(),
            "longitude": $("#longitude").val(),
            "latitude": $("#latitude").val(),
            //"id_paket":$("#").val(),
            "volume": data_selected[9],
            "satuan": data_selected[8],
            "hargasat": $("#hargasat").val(),
            "jumlah": data_selected[10],
        };
        //cek jika kode prov dari rujukan != null atau kosong
        if(data_selected[19]!= "" || typeof data_selected[19]!= "object"){
            var pkdprov=data_selected[19];
            bind_prov_from_vprovsatker(kd_satker,pkdprov);
        }
        //alert('kode provinsi :'+data_selected[19]);

       
        $("#thang").val(data_selected[1]);
        $("#kd_isu").val(data_selected[32]);

        //alert(check_rc((obj_arahan.rc_DED)));
        $("#rc_ded_status").val(check_rc((obj_arahan.rc_DED)));
        $("#rc_fs_status").val(check_rc((obj_arahan.rc_FS)));
        $("#rc_lahan_status").val(check_rc((obj_arahan.rc_lahan)));
        $("#rc_doklin_status").val(check_rc((obj_arahan.rc_Dokling)));
        $("#xvolume").val(data_selected[9]);
        //$("#wps_kode").append("<option selected value="+data_selected[26]+" >"+data_selected[34]+"</option>");
        //alert(isNull(obj_arahan.wps_kode));
        if (isNull(obj_arahan.wps_kode) == "#") {
            //initCombobox('wps_kode',59);    
        } else {
            //refreshCombobox('wps_kode', 59, 'wps_kode', obj_arahan.wps_kode);
        }
        var rIdprov = $("#id_rprov").val();
        //refreshCombobox('prov', 60, 'kd_prov',rIdprov);
        //refreshComboboxOutput('prov', 60, 'kd_prov',rIdprov,rIdprov);
        //refreshComboboxOutput('prov', 60, 'kd_prov',id_rprov,id_rprov);
        //var xvalSelect = obj_arahan.provinsi_id + "::" + obj_arahan.kabkot;
        refreshComboboxOutput('kabkot', 61, 'id_kabkot', obj_arahan.kabkot, obj_arahan.kabkot);
        //refreshCombobox4('kabkot', 61, 'kd_prov::id_kabkot', xvalSelect, obj_arahan.kabkot);


        //refreshCombobox('kabkot', 61, 'kd_prov',rIdprov);
        //$("#wps_kode").val(data_selected[26]);
        //$("#kws_kode").append("<option selected value="+data_selected[27]+" >"+data_selected[2]+"</option>");

        //$("#subkw").append("<option selected value="+""+" >"+data_selected[36]+"</option>");
        $("#subkw").val(data_selected[36]);
        //$("#kd_kegiatan").append("<option selected value="+data_selected[16]+" >"+data_selected[4]+"</option>");
        $("#kd_kegiatan").val(data_selected[16]);

        $(".decformat").val(obj_arahan.rpm);
        $("#totalpagu").val(obj_arahan.rpm);
        $("#rm").val(obj_arahan.rpm);
        $("#volume").val(obj_arahan.volume);
        $("#satuan").val(obj_arahan.satuan);

        $(".decformat2").val($("#totalpagu").val() / $("#volume").val());
        var j = function () {
            var defer = $.Deferred();




            refreshComboboxOutput('kd_output', 30, 'kdgiat', obj_arahan.kd_kegiatan, obj_arahan.kd_output.split('-')[1]);

            //console.log('a() called');
//                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
            //initCombobox('kd_kegiatan-sel', 5);

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };
        var a = function () {
            var defer = $.Deferred();

            var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1];
            refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, obj_arahan.kd_suboutput.split('-')[2]);
            //console.log('a() called');
//                      updateCombobox('kd_output-sel', 30, data.kd_output);
//                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
//                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        var b = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1] + "::" + obj_arahan.kd_suboutput.split('-')[2];
            refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, "-1");



            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };




        var c = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.wps_kode;
            //alert(obj_arahan.kws_kode);


            //value="-1"
            //alert(obj_arahan.kws_kode);
            var selected = "";
            if (obj_arahan.kws_kode == null) {
                selected = "-1"
            } else {

                selected = obj_arahan.kws_kode;
            }
            refreshComboboxOutput('kws_kode', 57, 'wps_kode', valSelect, selected);

            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        //wps_kode,kws_kode
        var d = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.wps_kode + "::" + obj_arahan.kws_kode;
            refreshCombobox4('subkw', 58, 'wps_kode::kws_kode', valSelect, "-1");



            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        c().then(d).then(j).then(a).then(b);

//        data_cart.push(obj_arahan);
//        console.log("data cart")
//        console.log(JSON.stringify(data_cart));
            var xhargasat=numberWithCommas($("#xhargasat").val());
            var newhargasat=xhargasat.split(".")[0];
            $("#xhargasat").val(newhargasat);
            var xjumlah=numberWithCommas($("#xjumlah").val());
            $("#xjumlah").val(numberWithCommas(xjumlah));
            
            $("#xrm").val(xjumlah);
            var xvolume=$("#xvolume").val();
            $("#xvolume").val(numberWithCommas(xvolume));
     
    }
    
    function bind_prov_from_vprovsatker(kode_satker,kdprov){
        var obj_vprovsatker=get_vprov_satker(kode_satker);
          $("#prov").empty();
          $("#prov").append("<option value='#'>Pilih</option>");
            for (var i = 0; i <= obj_vprovsatker.length - 1; i++) {
                var option_value = obj_vprovsatker[i].kdlokasi;
                if(kdprov==kdprov){
                    var html_option = [
                        "<option selected value=" + option_value + " >",
                        obj_vprovsatker[i].nama_prov,
                        "</option>",
                    ].join("\n");
                }else{
                   var html_option = [
                        "<option value=" + option_value + " >",
                        obj_vprovsatker[i].nama_prov,
                        "</option>",
                    ].join("\n"); 
                }
                
                $("#prov").append(html_option);
            }
    }
    
    function get_vprov_satker(kode_satker){
         var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_vprov_satker') ?>" + "/" + kode_satker;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
                console.log("javascript response")
                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    

    var data_cart_edit = [];
    var obj_data_cart_edit = {};
    function add_to_chart_edit(sa1thn_id, iDataIndex) {
        //alert(sa1thn_id);
        var data_selected = xhrdatasipro.data.filter(x => x[0] == sa1thn_id)[0];
        //console.log('---data selected---');
        //console.log(data_selected);
        //if ($().is(':checked')) {
        var obj_arahan = {
            "sa1thn_id": sa1thn_id,
            "tahun_anggaran": data_selected[1],
            "kawasan_nama": data_selected[2],
            "subkawasan_nama": data_selected[3],
            "kegiatan_nama": data_selected[4],
            "suboutput_nama": data_selected[5],
            "output_nama": data_selected[6],
            "sub_aktivitas": data_selected[7],
            "satuan_output": data_selected[8],
            "volume": data_selected[9],
            "rpm": data_selected[10],
            "phln": data_selected[11],
            "sbsn": data_selected[12],
            "rmp": data_selected[13],
            "unit_id": data_selected[14],
            "program_id": data_selected[15],
            "kd_kegiatan": data_selected[16],
            "kd_output": data_selected[17],
            "kd_suboutput": data_selected[18],
            "provinsi_id": data_selected[19],
            "kabkot": data_selected[20],
            "jenis_kontrakID": data_selected[21],
            "rc_FS": data_selected[22],
            "rc_DED": data_selected[23],
            "rc_Dokling": data_selected[24],
            "rc_lahan": data_selected[25],
            "wps_kode": data_selected[26],
            "kws_kode": data_selected[27],
            "status_konreg": data_selected[28],
            "status_verifikasi": data_selected[29],
            "status_rakor": data_selected[30],
            "catatan": data_selected[31],
            "kd_isu": data_selected[32],
            "kd_komponen": $("#zkd_komponen").val(),
            "kd_sub_komponen": $("#zkd_sub_komponen").val(),
            "nama_sub_komponen": $("#znama_sub_komponen").val(),
            "kd_jns_belanja": $("#zkdgbkpk").val(),
            "kdakun": $("#zkdakun").val(),
            "kdkppn": $("#zkdkppn").val(),
            "id_ppk": $("#zid_ppk").val(),
            "rc_ded_status": $("#zrc_ded_status").val(),
            "rc_fs_status": $("#zrc_fs_status").val(),
            "rc_lahan_status": $("#zrc_lahan_status").val(),
            "rc_doklin_status": $("#zrc_doklin_status").val(),
            "jnskontrak": $("#zjnskontrak").val(),
            "id_ruas": $("#zid_ruas").val(),
            "sta_awal": $("#zsta_awal").val(),
            "sta_akhir": $("#zsta_akhir").val(),
            "id_jembatan": $("#zxid_jembatan").val(),
            "longitude": $("#zlongitude").val(),
            "latitude": $("#zlatitude").val(),
            //"id_paket":$("#").val(),
            "volume": data_selected[9],
            "satuan": data_selected[8],
            "hargasat": $("#zhargasat").val(),
            "jumlah": data_selected[10],
        };

        //obj_data_cart=obj_arahan;
        //
        console.log("object arahan");
        console.log(obj_arahan);

        $("#zthang").val(data_selected[1]);
        $("#zkd_isu").val(data_selected[32]);

        //alert(check_rc((obj_arahan.rc_DED)));
        $("#zrc_ded_status").val(check_rc((obj_arahan.rc_DED)));
        $("#zrc_fs_status").val(check_rc((obj_arahan.rc_FS)));
        $("#zrc_lahan_status").val(check_rc((obj_arahan.rc_lahan)));
        $("#zrc_doklin_status").val(check_rc((obj_arahan.rc_Dokling)));
        $("#zvolume").val(data_selected[9]);
        //$("#wps_kode").append("<option selected value="+data_selected[26]+" >"+data_selected[34]+"</option>");
        //alert(isNull(obj_arahan.wps_kode));
        if (isNull(obj_arahan.wps_kode) == "#") {
            //initCombobox('wps_kode',59);    
        } else {
            //refreshCombobox('wps_kode', 59, 'wps_kode', obj_arahan.wps_kode);
        }
        var rIdprov = $("#id_rprov").val();
        //refreshCombobox('prov', 60, 'kd_prov',rIdprov);
        //refreshComboboxOutput('prov', 60, 'kd_prov',rIdprov,rIdprov);
        //refreshComboboxOutput('prov', 60, 'kd_prov',id_rprov,id_rprov);
        //var xvalSelect = obj_arahan.provinsi_id + "::" + obj_arahan.kabkot;
        refreshComboboxOutput('kabkot', 61, 'id_kabkot', obj_arahan.kabkot, obj_arahan.kabkot);
        //refreshCombobox4('kabkot', 61, 'kd_prov::id_kabkot', xvalSelect, obj_arahan.kabkot);


        //refreshCombobox('kabkot', 61, 'kd_prov',rIdprov);
        //$("#wps_kode").val(data_selected[26]);
        //$("#kws_kode").append("<option selected value="+data_selected[27]+" >"+data_selected[2]+"</option>");

        //$("#subkw").append("<option selected value="+""+" >"+data_selected[36]+"</option>");
        $("#zsubkw").val(data_selected[36]);
        //$("#kd_kegiatan").append("<option selected value="+data_selected[16]+" >"+data_selected[4]+"</option>");
        $("#zkd_kegiatan").val(data_selected[16]);

        $(".decformat").val(obj_arahan.rpm);
        $("#ztotalpagu").val(obj_arahan.rpm);
        $("#zrm").val(obj_arahan.rpm);
        $("#zvolume").val(obj_arahan.volume);
        $("#zsatuan").val(obj_arahan.satuan);

        $(".decformat2").val($("#totalpagu").val() / $("#volume").val());
        var j = function () {
            var defer = $.Deferred();




            refreshComboboxOutput('kd_output', 30, 'kdgiat', obj_arahan.kd_kegiatan, obj_arahan.kd_output.split('-')[1]);

            //console.log('a() called');
//                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
            //initCombobox('kd_kegiatan-sel', 5);

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };
        var a = function () {
            var defer = $.Deferred();

            var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1];
            refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, obj_arahan.kd_suboutput.split('-')[2]);
            //console.log('a() called');
//                      updateCombobox('kd_output-sel', 30, data.kd_output);
//                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
//                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        var b = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.kd_kegiatan + "::" + obj_arahan.kd_output.split('-')[1] + "::" + obj_arahan.kd_suboutput.split('-')[2];
            refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, "-1");



            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };




        var c = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.wps_kode;
            //alert(obj_arahan.kws_kode);


            //value="-1"
            //alert(obj_arahan.kws_kode);
            var selected = "";
            if (obj_arahan.kws_kode == null) {
                selected = "-1"
            } else {

                selected = obj_arahan.kws_kode;
            }
            refreshComboboxOutput('kws_kode', 57, 'wps_kode', valSelect, selected);

            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        //wps_kode,kws_kode
        var d = function () {
            var defer = $.Deferred();
            var valSelect = obj_arahan.wps_kode + "::" + obj_arahan.kws_kode;
            refreshCombobox4('subkw', 58, 'wps_kode::kws_kode', valSelect, "-1");



            //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 500);
            return defer;
        };

        c().then(d).then(j).then(a).then(b);

        data_cart_edit.push(obj_arahan);
        console.log("data cart")
        console.log(JSON.stringify(data_cart_edit));
    }


    //memanaggil API
    function get_data_wps() {
        //alert(typeof JSON.parse(usulans));
        var id_province = $("#id_rprov").val();
        var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_wps2') ?>" + "/" + id_province;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
                console.log("javascript response")
                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }


    function get_data_kawasan(kawasan_kode) {
        var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_kws2') ?>" + "/" + kawasan_kode;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
                console.log("javascript response")
                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    function get_data_sub_kawasan(kawasan_kode) {
        var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_kws2') ?>" + "/" + kawasan_kode;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
                console.log("javascript response")
                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }


    function get_ruas_by_province() {
        var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_ruas_by_province') ?>";
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
//                console.log("javascript response")
//                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    /**
    function bind_ruas_by_province(no_ruas){
        var objruas=get_ruas_by_province();
           
        for(var i=0; i<= objruas.length-1; i++){
            //alert(objruas[i].linkname);
            if(no_ruas==objruas[i].no_ruas){
                $("#wid_ruas").append("<option selected value="+objruas[i].no_ruas+">"+objruas[i].linkname+"</option>");
            }else{
                $("#wid_ruas").append("<option value="+objruas[i].no_ruas+">"+objruas[i].linkname+"</option>");
            }
        }
    }
**/

    /**
    function handleRuas(el)
    {
        //refreshCombobox2('id_jembatan', 32, 'linkid', el.value);
        refreshCombobox3('sta_awal-sel', 35, 'kode_ruas', el.value);
        refreshCombobox3('sta_akhir-sel', 35, 'kode_ruas', el.value);
        
        refreshCombobox3('sta_awal', 35, 'kode_ruas', el.value);
        refreshCombobox3('sta_akhir', 35, 'kode_ruas', el.value);
        
        //refreshCombobox3('wsta_awal', 35, 'kode_ruas', el.value);
        //refreshCombobox3('wsta_akhir', 35, 'kode_ruas', el.value);
//
//
//        var tirmsv3_wp = $('#tirmsv3_wp').DataTable();
//        var trams_wp = $('#trams_wp').DataTable();
//        tirmsv3_wp.ajax.reload();
//        trams_wp.ajax.reload();

    }
**/
    function isNull(data) {
        //alert(data);
        var result = "";
        if (data == "-") {
            result = "#"; //--pilih--
        } else if (data == "") {
            result = "#";
        } else if (data == null) {
            result = "#";
        }

        return result;
    }


    function check_rc(data) {
        //alert(data);
        var result = "";
        if (isNaN(data) == false) {

            if (data < yearnow) {
                result = "siap";
            } else {
                result = "tidak_siap";
            }

        } else {

            if (data == "-") {
                result = "#"; //--pilih--
            } else if (data == "") {
                result = "#";
            } else if (data == null) {
                result = "#";
            }

        }
        return result;
    }




    function fnFormatDetails(table_id, html) {
        //var sOut = "<table id=\"tlist_detail_" + table_id + "\">";
        return "<table style='overflow=scroll' id=\"tlist_detail_" + table_id + "\">" +
                "<thead>" +
                "<tr>" +
                "<th>Rujukan</th>" +
                "<th>ID</th>" +
                "<th>Tahun</th>" +
                "<th>Paket</th>" +
                "<th>Uraian</th>" +
                "<th>Ruas</th>" +
                "<th>STA Awal</th>" +
                "<th>STA Akhir</th>" +
                "<th>Jembatan</th>" +
                "<th>Koord. X</th>" +
                "<th>Koord. Y</th>" +
                "<th>Penanganan</th>" +
                "<th>Volume</th>" +
                "<th>Satuan</th>" +
                "<th>Jenis Belanja</th>" +
                "<th>Akun</th>" +
                "<th>Jumlah</th>" +
                "<th>#</th>" +
                "</tr>" +
                "</thead>" +
                "</table>";
        //sOut += html;
        //sOut += "</table>";
//        return sOut;
    }



//function format ( d ) {
//    // `d` is the original data object for the row
//    return '<table cellpadding="5" cellspacing="0" border="0" style="padding-left:50px;">'+
//        '<tr>'+
//            '<td>Full name:</td>'+
//            '<td>'+'sefdsf'+'</td>'+
//        '</tr>'+
//        '<tr>'+
//            '<td>Extension number:</td>'+
//            '<td>'+'aaaaa'+'</td>'+
//        '</tr>'+
//        '<tr>'+
//            '<td>Extra info:</td>'+
//            '<td>And any further details here (images etc)...</td>'+
//        '</tr>'+
//    '</table>';
//}

    function bind_wps_by_province() {
        var obj_wps = get_data_wps();
        $("#wps_kode").empty();
        $("#wps_kode").append("<option value='#'>Pilih</option>");
        for (var i = 0; i <= obj_wps.length - 1; i++) {
            var option_value = obj_wps[i].wps_kode + "::" + obj_wps[i].kws_kode
            var html_option = [
                "<option value=" + option_value + " >",
                obj_wps[i].wps_nama,
                "</option>",
            ].join("\n");
            $("#wps_kode").append(html_option);
        }
    }

    function bind_kawasan_by_kws_kode(element) {

        var kawasan_kode = element.value.split('::')[1];

        var obj_kawasan = get_data_kawasan(kawasan_kode);
        //alert(obj_kawasan);
        $("#kws_kode").empty();
        for (var i = 0; i <= obj_kawasan.length - 1; i++) {
            var option_value = obj_kawasan[i].kws_kode + "::" + obj_kawasan[i].subkawasan_nama;
            var html_option = [
                "<option value=" + option_value + " >",
                obj_kawasan[i].kws_nama,
                "</option>",
            ].join("\n");
            $("#kws_kode").append(html_option);
        }

        bind_subkawasan(kawasan_kode);
    }
    
    function get_data_subkawasan(kawasan_kode){
        var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_subkws2') ?>" + "/" + kawasan_kode;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
               
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    function bind_subkawasan(kawasan_kode) {
        //var kawasan_kode=element.value.split('::')[0];
        //alert(kawasan_kode);
        var obj_subkawasan = get_data_subkawasan(kawasan_kode);
        for (var i = 0; i <= obj_subkawasan.length; i++) {
            var option_value = obj_subkawasan[i].subkawasan_nama;
            var html_option = [
                "<option value='" + option_value + "' >",
                obj_subkawasan[i].subkawasan_nama,
                "</option>",
            ].join("\n");
            $("#subkw").append(html_option);
        }
    }

    var iTableCounter = 1;
    var oTable;
    var oInnerTable;
    var TableHtml;

    function count_jumlah(element) {
        var hargasatuan = element.value.replace(/\D/g, '');        
        //modal lain
        var volume = $("#volume").val();
        var jumlah = parseFloat(hargasatuan) * volume;
        $("#yjumlah").val(jumlah);
        
        //modal tambah 
        var xvolume = $("#xvolume").val();
       
        var xjumlah = parseFloat(hargasatuan) * parseFloat(xvolume);
        $("#xjumlah").val(xjumlah);
       
        var wvolume = $("#wvolume").val();
        var wjumlah = parseFloat(hargasatuan) * parseFloat(wvolume);
        $("#wjumlah").val(wjumlah);
        
        //formating number
        $("#xhargasat").val(numberWithCommas(hargasatuan));
        $("#xjumlah").val(numberWithCommas($("#xjumlah").val()));
        $("#xrm").val(numberWithCommas($("#xjumlah").val()));
        
        $("#yhargasat").val(numberWithCommas(hargasatuan));
        $("#yjumlah").val(numberWithCommas($("#yjumlah").val()));
        $("#yrm").val(numberWithCommas($("#yjumlah").val()));
        
       
       
        
        $("#whargasat").val(numberWithCommas(hargasatuan));
        $("#wjumlah").val(numberWithCommas($("#wjumlah").val()));
        
        //modal tambah item
        $("#rm").val(numberWithCommas($("#yjumlah").val()));
       
    }

    function get_sum_batas(p) {
//        $("#conte").empty();
        $("#conte").show();
        var x = p.value;
        var z = 'kosong';
        alert(x)
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/sum_batas') ?>/" + x + "/" + z,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                alert(data)
                $("#box-bts-prov").empty();
                $("#box-bts-prov").append(data)
                $("#btspagu").empty();
                $("#btspagu").append(data)
                $("#textbtspagu").text("Total Batas Pagu Provinsi");
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });

    }
    function get_sum_batas_output(p) {
//        $("#conte").empty();
        $("#conte").show();
        var x = p.value;
        var z = $("#kd_prov").val();
        alert(x)
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/sum_batas') ?>/" + z + "/" + x,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                alert(data)
                $("#box-bts-output").empty();
                $("#box-bts-output").append(data)
                $("#btspagu").empty();
                $("#btspagu").append(data)
                $("#textbtspagu").text("Total Batas Pagu Output");
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });

    }
    function filters() {
        var x = $("#kd_prov").val();
        var z = $("#outs").val();
        
        if (z == '' || z == 'undefined' || z=='#')
        {
            var l = 'abc'
        } else
        {
            var l = $("#outs").val();
        }
        //alert(z);
        //alert(l);
        var yuhu = '';
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/sum_usulan') ?>/" + x + "/" + l,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                alert(data);

                yuhu = data

            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        alert(yuhu);
        if (parseInt($("#btspagu").text()) < yuhu)
        {
            var ale = "<div class='alert alert-danger'> <strong>Danger!</strong> Indicates a dangerous or potentially negative action.</div>"

            $("#colo").css({"color": "red"});
        }else if (parseInt($("#btspagu").text()) == 0 && yuhu==0)
        {
            $("#conte").css({"display": "none"});
            $("#alertss").css({"display": "none"});
        }
        else
        {
            var ale = "<div class='alert alert-success'> <strong>Success!</strong> Indicates a successful or positive action.</div>"
            $("#colo").css({"color": "green"});
        }
        $("#alertss").empty();
        setTimeout(function () {
            $("#alertss").append(ale);
        }, 2000);


        // setTimeout(ale, 2000);
        var tlist_paket = $('#tlist_paket').DataTable();
        tlist_paket.ajax.reload();
    }
    function dataTablepaket() {

        tlist_paket = $("#tlist_paket").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            "deferRender": true,
            "order": [[1, "desc"]],
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_paket",
                 type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		 }
               
            },
            "aoColumnDefs": [
//                {"aTargets": [8], "visible": false},
//                {
//                    "aTargets": [9],
//                    "mRender": function (data, type, row) {
//
//                        var id = row[1];
//                        var kdgiat = row[8];
//
//                        var html_button = [
//                            "<button onclick=upload_attachment('" + row[1] + "','" + row[9] + "','" + row[2] + "'); class='btn btn-success btn-xs' data-toggle='tooltip' title='Upload File'>",
//                            "<i class='fa fa-upload'>",
//                            "</i>",
//                            "</button></a>",
//                            "<button onclick= dtTambahRowDetail('" + id + "','" + kdgiat + "') class='btn btn-success btn-xs' data-toggle='tooltip' title='Tambah Detail Paket'>",
//                            "<i class='fa fa-plus'>",
//                            "</i>",
//                            "</button>",
//                            "<button onclick= dtEditRowPaket('" + id + "') class='btn btn-primary btn-xs' data-toggle='tooltip' title='Edit Paket'>",
//                            "<i class='fa fa-pencil'>",
//                            "</i>",
//                            "</button>",
//                            "<button onclick= dtDeleteRow('paket','" + id + "') class='btn btn-danger btn-xs' data-toggle='tooltip' title='Hapus Paket'>",
//                            "<i class='fa fa-trash'>",
//                            "</i>",
//                            "</button>",
//                            "<button onclick= dtHistory('" + id + "') class='btn btn-warning btn-xs'>History",
//                            "</button></a>"
//                        ].join("\n");
//                        return html_button;
//                    }
//
//
//                }
//                {
//                    "aTargets": [23], "visible": false
//                },
                {
                    "aTargets": [9],
                    "mRender": function (data, type, full) {
                        var html;
////                            console.log('--data from button--');
                        //console.log(data);
//                            var html_button = [
//                                "<button onclick= dtEditRowSipro('" + data + "') class='btn btn-primary btn-xs'>",
//                                "<i class='fa fa-eye'>",
//                                "</i>",
//                                "</button>"
////                                "<button onclick= dtHistory(\'" + data + "\',\'sipro\') class='btn btn-warning btn-xs'>History",
////                                "</button>",
//                            ].join("\n");
//                            return html_button;
                        var xdata = data.split('~')[0];
                        var id = data.split('~')[1];

//                           console.log(xdata + ' ----- ' + id);

                        var d = new Array();
                        var aData = xdata.split('|');
                        // console.log(aData[0]);

//                              console.log(aData[1]);
                        var html = '';
                        aData.forEach(function (e) {
                            eData = e.split('_');
//                              console.log(eData);
                            d.push(eData);
                        });
                        //console.log(d);
//
//                            var aktor = '';


                        for (var i = 0; i < d.length; i++) {
                            switch (i) {
                                case 0:
                                    aktor = full[15] + ' ' + full[17];
                                    break;
                                case 1:
                                    aktor = full[16];
                                    break;
                            }
//                                            alert(aktor);
                            var formtype;

//                                alert(d[i][2] );

                            if (d[i][2] != '0') {
                                formtype = 'add';
                            } else {
                                formtype = 'edit';
                            }

                            //console.log('------->'+d[i][2]);

                            switch (d[i][2]) {
                                case '0': //belum diisi
                                    if (d[i][0] != '0') {
                                        html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-default btn-xs">Belum verifikasi ' + aktor + '</button>\n';
                                    } else {

                                    }
                                    break;
                                case '1': //diterima
                                    html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-success btn-xs">Diterima ' + aktor + '</button>\n';
                                    break;
                                case '2': //reject
                                    html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-danger btn-xs">Belum bisa dilaksanakan</button>\n';
                                    break;
                                case '3': //hold
                                    html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-primary btn-xs">Dihold ' + aktor + '</button>\n';
                                    break;
                                case '4': //stock
                                    html += '<button onclick="btnProsesVerifikasiIndi(' + "'" + data + "~" + i + "'" + ')" class="btn btn-info btn-xs">Distock ' + aktor + '</button>\n';
                                    break;
                            }
                        }

                        return html;
                    }
                },
                {
                    "aTargets": [10],
                    "mRender": function (data, type, row) {

                        var id = row[1];
                        var kdgiat = row[10];

                        var html_button = [
                            "<button onclick=upload_attachment('" + row[1] + "'); class='btn btn-success btn-xs' data-toggle='tooltip' title='Upload File'>",
                            "<i class='fa fa-upload'>",
                            "</i>",
                            "</button>",
                            "</button>",
                            "<button onclick=download_attachment('" + row[1] + "'); class='btn btn-success btn-xs' data-toggle='tooltip' title='List Attachment'> ",
                            "<i class='fa fa-file'>",
                            "</i>",
                            "</button>"
                                    ,
                            "<button onclick=dtTambahRowDetail('" + id + "','" + kdgiat + "') class='btn btn-success btn-xs' data-toggle='tooltip' title='Tambah Detail Paket'>",
                            "<i class='fa fa-plus'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtEditRowPaket('" + id + "') class='btn btn-primary btn-xs' data-toggle='tooltip' title='Edit Paket'>",
                            "<i class='fa fa-pencil'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtDeleteRow('paket','" + row[1] + "') class='btn btn-danger btn-xs' data-toggle='tooltip' title='Hapus Paket'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>",
                            "<button onclick=dtHistory('paket','" + id + "') class='btn btn-warning btn-xs'>History",
                            "</button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"className": 'details-control', "width": "2px"},
                {"width": "5px"},
                {"width": "5px"},
                {"width": "125px"},
                {"width": "120px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "125px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        $('#tlist_paket tbody').on('click', 'td.details-control img', function () {
            TableHtml = $("#tlist_detail").html();

            var tr = $(this).closest('tr');
            var row = tlist_paket.row(tr);

            var drow = row.data();

            if (row.child.isShown()) {
                // This row is already open - close it
                this.src = "https://datatables.net/examples/resources/details_open.png";
                row.child.hide();
                tr.removeClass('shown');
            } else {
                // Open this row
                //console.log(drow);


                this.src = "https://datatables.net/examples/resources/details_close.png";
                row.child(fnFormatDetails(iTableCounter, TableHtml)).show();
                oInnerTable = $("#tlist_detail_" + iTableCounter).DataTable({
                    "draw": 0,
                    "responsive": true,
                    "processing": true,
                    "serverSide": true,
                    "scrollCollapse": true,
                    "ajax": {
                        url: "<?php echo base_url(); ?>pagu_indikatif/ssp_detail",
                        type: "POST",
                        data: function (d) {
                            // d.id_paket = drow[1];
                            //var kd_sub = drow[3];
                            //var kd = kd_sub.substr(0, 2);
                            d.kdsatker = kd_satker;
                            d.thang = yearnow;
                            d.kd_sub_komponen = drow[3].split(" - ")[0];
                            d.kd_komponen = drow[7].split(" - ")[0];
                            d.kd_giat = drow[4].split(" - ")[0];
                            d.kd_output = drow[5].split(" - ")[0];
                            d.kd_sub_output = drow[6].split(" - ")[0];
                            d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
                            //d.stae = $('#sta_akhir').val();
                        }
                    },
                    "pagingType": "full_numbers",
                    "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
                    "aoColumnDefs": [
                        {
                            "aTargets": [0],
                            "width": "150px",
                            "mRender": function (data, type, row) {
                                var html = '';
                                var tags;
                                if (row[0] == '')
                                {
                                    tags = [];
                                } else
                                {
                                    tags = JSON.parse(row[0]);
                                }



                                $.each(tags, function (index, value) {
                                    switch (value.rujukan) {
                                        case 'PEMDA':
                                            html += '<span class="tag label label-default">' + value.text + '</span><br/>';
                                            break;
                                        case 'DPR':
                                            html += '<span class="tag label label-info">' + value.text + '</span><br/>';
                                            break;
                                        case 'SIPRO':
                                            html += '<span class="tag label label-primary">' + value.text + '</span><br/>';
                                            break;
                                        case 'IRMS':
                                            html += '<span class="tag label label-success">' + value.text + '</span><br/>';
                                            break;
                                        case 'RAMS':
                                            html += '<span class="tag label label-info">' + value.text + '</span><br/>';
                                            break;
                                        case 'EPROG':
                                            html += '<span class="tag label label-warning">' + value.text + '</span><br/>';
                                            break;
                                        case 'RENSTRA':
                                            html += '<span class="tag label label-danger">' + value.text + '</span>';
                                            break;
                                    }
                                });

                                return html;
                            }
                        },
                        {
                            "aTargets": [1],
                            "width": "20px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [2],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [3],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [4],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [5],
                            "width": "100px",
                            "visible": false
//                        "searchable": true
                        },
                        {
                            "aTargets": [6],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [7],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [8],
                            "width": "100px",
                            "visible": false
//                        "searchable": true
                        },
                        {
                            "aTargets": [9],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [10],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [11],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [12],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [13],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [14],
                            "width": "100px",
                            "visible": false
//                        "searchable": true
                        },
                        {
                            "aTargets": [15],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },
                        {
                            "aTargets": [16],
                            "width": "100px",
                            "visible": true
//                        "searchable": true
                        },

                        {
                            "aTargets": [17],
                            "mRender": function (data, type, row) {

                                var id = row[1];
                                var html_button = [
//                                    "<button onclick=dtEditRowDetail('" + id + "') class='btn btn-primary btn-xs'>",
//                                    "<i class='fa fa-pencil'>",
//                                    "</i>",
                                    "</button>",
                                    "<button onclick= dtDeleteRow('detail','" + id + "') class='btn btn-danger btn-xs'>",
                                    "<i class='fa fa-trash'>",
                                    "</i>",
                                    "</button>",
                                    "<button onclick=dtHistory('detail','" + id + "') class='btn btn-warning btn-xs'>History",
                                    "</button>",
                                    "<button onclick=editDetail('" + id + "') class='btn btn-dark btn-xs'><i class='fa fa-bars'></i>",
                                    "</button>",
                                ].join("\n");
                                return html_button;
                            }
                        }
                    ],
                    "pageLength": 5,
                    "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
                    "language": {
                        "decimal": "",
                        "emptyTable": "Data tidak ditemukan",
                        "info": "Data _START_ s/d _END_ dari _TOTAL_",
                        "infoEmpty": "Tidak ada data",
                        "infoFiltered": "(tersaring dari _MAX_)",
                        "infoPostFix": "",
                        "thousands": ",",
                        "lengthMenu": "_MENU_  data per halaman",
                        "loadingRecords": "Memuat...",
                        "processing": "Memroses...",
                        "search": "Cari:",
                        "zeroRecords": "Tidak ada data ditemukan",
                        "paginate": {
                            "first": "<i class='fa fa-angle-double-left'></i>",
                            "last": "<i class='fa fa-angle-double-right'></i>",
                            "next": "<i class='fa fa-angle-right'></i>",
                            "previous": "<i class='fa fa-angle-left'></i>"
                        },
                        "aria": {
                            "sortAscending": ": aktifkan untuk mengurutkan naik",
                            "sortDescending": ": aktifkan untuk mengurutkan turun"
                        }
                    }
                });

                $("#tlist_detail_" + iTableCounter).on('xhr', function () {
                    xhrdata = $("#tlist_detail_" + iTableCounter).ajax.json();
                    //            console.log(xhrdata);
                });

                $('#tlist_detail_' + iTableCounter + '_length').hide();
                $("#tlist_detail_" + iTableCounter).addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");

                iTableCounter = iTableCounter + 1;

                tr.addClass('shown');


            }


        });



        tlist_paket.on('xhr', function () {
            xhrdata = tlist_paket.ajax.json();
//            console.log(xhrdata);
        });
    }
    
     var addFormGroup = function (event) {
            event.preventDefault();
            var $formGroup = $(this).closest('.form-group');
            var $multipleFormGroup = $formGroup.closest('.multiple-form-group');
            var $formGroupClone = $formGroup.clone();
            $(this).toggleClass('btn-success btn-add btn-danger btn-remove').html('–');
            $formGroupClone.find('input').val('');
            $formGroupClone.find('.concept').text('RM');
            $formGroupClone.insertAfter($formGroup);
            var $lastFormGroupLast = $multipleFormGroup.find('.form-group:last');
            if ($multipleFormGroup.data('max') <= countFormGroup($multipleFormGroup)) {
                $lastFormGroupLast.find('.btn-add').attr('disabled', true);
            }
        };
        var removeFormGroup = function (event) {
            event.preventDefault();
            var $formGroup = $(this).closest('.form-group');
            var $multipleFormGroup = $formGroup.closest('.multiple-form-group');
            var $lastFormGroupLast = $multipleFormGroup.find('.form-group:last');
            if ($multipleFormGroup.data('max') >= countFormGroup($multipleFormGroup)) {
                $lastFormGroupLast.find('.btn-add').attr('disabled', false);
            }

            $formGroup.remove();
        };
        var selectFormGroup = function (event) {
            event.preventDefault();
            var $selectGroup = $(this).closest('.input-group-select');
            var param = $(this).attr("href").replace("#", "");
            var concept = $(this).text();
            //alert((this).text());
            //alert(param);

            $selectGroup.find('.concept').text(concept);
            $selectGroup.find('.input-group-select-val').val(param);
            var lowParam = param.toLowerCase();
            var $selectEntryDana = $(this).closest('.input-group');
            $selectEntryDana.find('.valuesdana').attr("id", lowParam);
            $selectEntryDana.find('.valuesdana').attr("name", lowParam);
        }

        var countFormGroup = function ($form) {
            return $form.find('.form-group').length;
        };
    
    
    $(document).ready(function () {
        //bind_combo_wps();
        refreshCombobox('kd_prov', 63, 'kdinduk', kd_satker);
        initCombobox('outs', 62);
        if (typeof (kd_satker) == 'undefined' || kd_satker == '' || kd_satker == null)
        {
            //do nothing

        } else
        {
            initComboboxRuasSatker('id_ruas');
            initComboboxRuasSatker('xid_ruas');
            initComboboxRuasSatker('wid_ruas');
            //refreshCombobox('id_ruas', 33, 'kode_satker', kd_satker);//modal-detail
            //refreshCombobox('xid_ruas', 33, 'kode_satker', kd_satker);//modal-tambah
            //refreshCombobox('wid_ruas', 33, 'kode_satker', kd_satker);//modal-tambah
            refreshComboboxKPPN('kdkppn', 46, 'kdsatker', kd_satker);
            
             //refreshComboboxKPPN('zkdkppn', 46, 'kdsatker', kd_satker);
            refreshCombobox('kdkppn-sel', 46, 'kdsatker', kd_satker);
        }

        //initCombobox('id_ruas', 33);
        //initCombobox('kdgbkpk', 36);
        //initCombobox('id_satuan', 26);
        //multipleSelect('kdsdana', 38);
        var xkdprov = $("#prov").val();
        var id_rprov = $("#id_rprov").val();
        initCombobox('jnskontrak', 23);
        //initCombobox('wps_kode', 59);       
        //initCombobox('prov', 55);
        //refreshComboboxOutput('prov', 60, 'kd_prov', id_rprov, id_rprov);
        //alert(kd_satker);
        refreshComboboxOutput('prov', 63,'kdsatker', kd_satker);
        refreshCombobox('kabkot', 61, 'kd_prov', id_rprov);
        bind_wps_by_province();
        //initCombobox('kabkot',61);  
        //refreshCombobox('kabkot', 49, 'kd_prov',id_rprov);


//       $('input[name^=sum]').on("keyup", function(){
//		var $this = $(this);
//		var $parent = $this.parents('tr');
//		var $chance = $parent.find('.chance');
//		var $number = $parent.find('.number');    
//		$chance.text($number.text() * $this.val());
//	});




//        $('.decformat').on("keyup", function (event) {
//            // skip for arrow keys
//            if (event.which >= 37 && event.which <= 40)
//                return;
//
//            // format number
//            $(this).val(function (index, value) {
//                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
//            });
//        });


//
////        alert(kd_satker);
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function () {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });

        $(document).on('hidden.bs.modal', '.modal', function () {
            $('.modal:visible').length && $(document.body).addClass('modal-open');
        });

        $('.rujuk').hide();


//        $('#modal-detail').on('hidden.bs.modal', function () {
//            // do something…
//            $('.div-tags').empty();
//
//        })


       
        $(document).on('click', '.btn-add', addFormGroup);
        $(document).on('click', '.btn-remove', removeFormGroup);
        $(document).on('click', '.dropdown-menu a', selectFormGroup);
        initCombobox('kd_isu', 3);
//        initCombobox('kd_sasaran_pembangunan', 14);
//        initCombobox('kd_sasaran_strategis', 15);
        //initCombobox('kdunit', 22);

        initCombobox('thang', 28);
        // initCombobox('id_ppk', 47);

        initCombobox('thang-sel', 28);
        //initCombobox('id_ppk-sel', 47);
        initCombobox('kd_kegiatan', 5);


        if (typeof (kd_satker) == 'undefined' || kd_satker == '' || kd_satker == null)
        {
            //do nothing

        } else
        {
            
            //initComboboxRuasSatker('id_ruas');
            initComboboxRuasSatker('xid_ruas');
            
            /**
            refreshCombobox('xid_ruas', 33, 'kode_satker', kd_satker);
            **/
            refreshCombobox('id_ppk', 47, 'kdsatker', kd_satker);
            refreshCombobox('id_ppk-sel', 47, 'kdsatker', kd_satker);
            refreshComboboxKPPN('kdkppn', 46, 'kdsatker', kd_satker);
            refreshCombobox('kdkppn-sel', 46, 'kdsatker', kd_satker);
        
            
        }




        $("#thang").change(function () {
            var thang = $('#thang').val();

            //refreshCombobox('kd_kegiatan', 5, 'thang', thang);
            $('#kd_output').empty();
            $('#kd_output').append(new Option("--Pilih--", -1));
            $('#kd_sub_output').empty();
            $('#kd_sub_output').append(new Option("--Pilih--", -1));
            $('#kd_komponen').empty();
            $('#kd_komponen').append(new Option("--Pilih--", -1));
            initCombobox('kd_kegiatan', 5);

        });

if(id_user_group != 60)
{

        var tlist_usulanpemda = $("#tusulanpemda").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                            ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_usulanpemda"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        //var statement_tag = 'PEMDA::' + id + '::' + judul;
                        var statement_tag = 'PEMDA::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-default btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulanpemda.on('xhr', function () {
            xhrdatausulanpemda = tlist_usulanpemda.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });


        var ztlist_usulanpemda = $("#ztusulanpemda").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                            ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_usulanpemda"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        //var statement_tag = 'PEMDA::' + id + '::' + judul;
                        var statement_tag = 'PEMDA::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-default btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulanpemda.on('xhr', function () {
            xhrdatausulanpemda = tlist_usulanpemda.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });


        var wtlist_usulanpemda = $("#wtusulanpemda").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                            ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_usulanpemda"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        //var statement_tag = 'PEMDA::' + id + '::' + judul;
                        var statement_tag = 'PEMDA::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-default btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulanpemda.on('xhr', function () {
            xhrdatausulanpemda = tlist_usulanpemda.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });


        var xtlist_usulanpemda = $("#xtusulanpemda").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                            ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_usulanpemda"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        //var statement_tag = 'PEMDA::' + id + '::' + judul;
                        var statement_tag = 'PEMDA::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-default btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulanpemda.on('xhr', function () {
            xhrdataxusulanpemda = xtlist_usulanpemda.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });

        var tlist_usulandpr = $("#tusulandpr").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_usulandpr",
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
//                {"width": "100px", render: $.fn.dataTable.render.number( ',', '.', 0, '' )},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
//                        var statement_tag = 'DPR::' + id + '::' + judul;
                        var statement_tag = 'DPR::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-default btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulandpr.on('xhr', function () {
            xhrdatausulandpr = tlist_usulandpr.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });


        var ztlist_usulandpr = $("#ztusulandpr").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_usulandpr",
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
//                {"width": "100px", render: $.fn.dataTable.render.number( ',', '.', 0, '' )},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
//                        var statement_tag = 'DPR::' + id + '::' + judul;
                        var statement_tag = 'DPR::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-default btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulandpr.on('xhr', function () {
            xhrdatausulandpr = tlist_usulandpr.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });


        var wtlist_usulandpr = $("#wtusulandpr").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_usulandpr",
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
//                {"width": "100px", render: $.fn.dataTable.render.number( ',', '.', 0, '' )},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
//                        var statement_tag = 'DPR::' + id + '::' + judul;
                        var statement_tag = 'DPR::' + id;

                        var html_button = '<button id="btn-edit#' + statement_tag +'" class="btn btn-default btn-xs edit-tag">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_usulandpr.on('xhr', function () {
            xhrdatausulandpr = tlist_usulandpr.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });


        var xtlist_usulandpr = $("#xtusulandpr").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_usulandpr",
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
//                {"width": "100px", render: $.fn.dataTable.render.number( ',', '.', 0, '' )},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "50px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [0],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
//                        var statement_tag = 'DPR::' + id + '::' + judul;
                        var statement_tag = 'DPR::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-default btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        xtlist_usulandpr.on('xhr', function () {
            xhrdataxusulandpr = xtlist_usulandpr.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });


        var tlist_renstra = $("#trenstra").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_renstra"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [1],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [6],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        var nilai = row[3];
                        var satuan = row[4];
                        var tahun = row[5];
                        //var statement_tag = 'RENSTRA::' + id + '::' + judul + '-' + tahun + '-' + nilai + ' ' + satuan;
                        var statement_tag = 'RENSTRA::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-danger btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_renstra.on('xhr', function () {
            xhrdatarenstra = tlist_renstra.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });

        var xtlist_renstra = $("#xtrenstra").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_renstra"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [1],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [6],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        var nilai = row[3];
                        var satuan = row[4];
                        var tahun = row[5];
                        //var statement_tag = 'RENSTRA::' + id + '::' + judul + '-' + tahun + '-' + nilai + ' ' + satuan;
                        var statement_tag = 'RENSTRA::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-danger btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        xtlist_renstra.on('xhr', function () {
            xhrxdataxrenstra = xtlist_renstra.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });

        var ztlist_renstra = $("#ztrenstra").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_renstra"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [1],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [6],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        var nilai = row[3];
                        var satuan = row[4];
                        var tahun = row[5];
                        //var statement_tag = 'RENSTRA::' + id + '::' + judul + '-' + tahun + '-' + nilai + ' ' + satuan;
                        var statement_tag = 'RENSTRA::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-danger btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_renstra.on('xhr', function () {
            xhrdatarenstra = tlist_renstra.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });



        var wtlist_renstra = $("#wtrenstra").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
//            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_renstra"
            },
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "50px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
            "aoColumnDefs": [
                {
                    "aTargets": [1],
                    "width": "50px",
                    "visible": false
                },
                {
                    "aTargets": [6],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var judul = row[2];
                        var nilai = row[3];
                        var satuan = row[4];
                        var tahun = row[5];
                        //var statement_tag = 'RENSTRA::' + id + '::' + judul + '-' + tahun + '-' + nilai + ' ' + satuan;
                        var statement_tag = 'RENSTRA::' + id;

                        var html_button = '<button id="btn-edit#' + statement_tag +'" class="btn btn-danger btn-xs edit-tag">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            //                "scrollY":        "300px",
            //                "scrollX":        true,
            //                "scrollCollapse": true,
//                "fixedColumns":   {
            //                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tlist_renstra.on('xhr', function () {
            xhrdatarenstra = tlist_renstra.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });




        var tsipro_wp = $("#tsipro_wp").DataTable({
            'fnCreatedRow': function (nRow, aData, iDataIndex) {
                //alert("Row=> "+iDataIndex);
                $(nRow).attr('id', 'row-' + iDataIndex); // or whatever you choose to set as the id
                $('td', nRow).eq(-1).empty();
                $('td', nRow).eq(-1).append('<button class="btn btn-primary btn-xs btn-primary" id=cbx-' + iDataIndex + ' onclick="add_to_chart(' + "'" + aData[0] + "'" + "," + "'" + iDataIndex + "'" + ');">Paketkan</button>');


            },
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "searching": true,
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/siprolist_pagu"
            },
//            "aoColumnDefs": [
//                {
//                    "aTargets": [-1],
//                    "visible":true,
//                    "mRender": function (data, type, row) {
//
////                        var id = row[12];
////                        //alert(id);
////                        var judul = row[5];
////                        //var statement_tag = 'SIPRO::' + id + '::' + judul;
////                        var statement_tag = 'SIPRO::' + id;
////
////                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-primary btn-xs">Tandai</button>';
//        var html_button="";
//        return html_button;
//                    }
//                }
//            ],
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "300px"},
                {"width": "200px"},
                {"width": "300px"},
                {"width": "200px"},
                {"width": "300px"},
                {"width": "20px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px", "visible": false}, //11
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "50px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "200px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "200px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "20px"},
                {"width": "100px", "visible": false},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tsipro_wp.on('xhr', function () {
            xhrdatasipro = tsipro_wp.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });
        //alert($('#sta_awal').val());


        var ztsipro_wp = $("#ztsipro_wp").DataTable({
            'fnCreatedRow': function (nRow, aData, iDataIndex) {
                //alert("Row=> "+iDataIndex);
                $(nRow).attr('id', 'row-' + iDataIndex); // or whatever you choose to set as the id
                $('td', nRow).eq(-1).empty();
                $('td', nRow).eq(-1).append('<button class="btn btn-primary btn-xs btn-primary" id=cbx-' + iDataIndex + ' onclick="add_to_chart_edit(' + "'" + aData[0] + "'" + "," + "'" + iDataIndex + "'" + ');">Paketkan</button>');


            },
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "searching": true,
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/siprolist_pagu"
            },
//            "aoColumnDefs": [
//                {
//                    "aTargets": [-1],
//                    "visible":true,
//                    "mRender": function (data, type, row) {
//
////                        var id = row[12];
////                        //alert(id);
////                        var judul = row[5];
////                        //var statement_tag = 'SIPRO::' + id + '::' + judul;
////                        var statement_tag = 'SIPRO::' + id;
////
////                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-primary btn-xs">Tandai</button>';
//        var html_button="";
//        return html_button;
//                    }
//                }
//            ],
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "300px"},
                {"width": "200px"},
                {"width": "300px"},
                {"width": "200px"},
                {"width": "300px"},
                {"width": "20px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px", "visible": false}, //11
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "50px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "200px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "200px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "20px"},
                {"width": "100px", "visible": false},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tsipro_wp.on('xhr', function () {
            xhrdatasipro = tsipro_wp.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });




        //arahan edit detail
        var wtsipro_wp = $("#wtsipro_wp").DataTable({
            'fnCreatedRow': function (nRow, aData, iDataIndex) {
                //alert("Row=> "+iDataIndex);
                $(nRow).attr('id', 'row-' + iDataIndex); // or whatever you choose to set as the id
                $('td', nRow).eq(-1).empty();
                $('td', nRow).eq(-1).append('<button class="btn btn-primary btn-xs btn-primary" id=cbx-' + iDataIndex + ' onclick="add_to_chart_edit(' + "'" + aData[0] + "'" + "," + "'" + iDataIndex + "'" + ');">Paketkan</button>');


            },
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "searching": true,
            "ajax": {
                type: "POST",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                url: "<?php echo base_url(); ?>pagu_indikatif/siprolist_pagu"
            },
//            "aoColumnDefs": [
//                {
//                    "aTargets": [-1],
//                    "visible":true,
//                    "mRender": function (data, type, row) {
//
////                        var id = row[12];
////                        //alert(id);
////                        var judul = row[5];
////                        //var statement_tag = 'SIPRO::' + id + '::' + judul;
////                        var statement_tag = 'SIPRO::' + id;
////
////                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-primary btn-xs">Tandai</button>';
//        var html_button="";
//        return html_button;
//                    }
//                }
//            ],
            "autoWidth": false,
            "columns": [
                {"width": "50px"},
                {"width": "300px"},
                {"width": "200px"},
                {"width": "300px"},
                {"width": "200px"},
                {"width": "300px"},
                {"width": "20px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px", "visible": false}, //11
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "50px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "200px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "200px", "visible": false},
                {"width": "300px", "visible": false},
                {"width": "20px"},
                {"width": "100px", "visible": false},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px", "visible": false},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tsipro_wp.on('xhr', function () {
            xhrdatasipro = tsipro_wp.ajax.json();
            //        console.log('xhr data: ' );
            //console.log(xhrdata);
        });



        var tirmsv3_wp = $("#tirmsv3_wp").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "order": [[5, "asc"], [9, "asc"], [10, "asc"]],
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/pavlist",
                type: "POST",
                data: function (d) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
//                    if ($('#id_ruas').val() == '#')
//                    {
//                        d.noruas = '#::#::#';
//                    } else
//                    {
//                        d.noruas = $('#xid_ruas').val();
//                    }
//
//                    d.staa = $('#sta_awal').val() || $('#sta_awal-sel').val();
//                    d.stae = $('#sta_akhir').val() || $('#sta_akhir-sel').val();
                    
                    d.noruas = $('#xid_ruas').val();
                    d.staa = $('#sta_awal').val() || $('#sta_awal').val();
                    d.stae = $('#sta_akhir').val() || $('#sta_akhir').val();

                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [12],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var thn = row[1];
                        var kat = row[2];
                        var road = row[5];
                        //var statement_tag = 'IRMS::' + id + '::' + thn + '-' + road + '-' + kat;
                        var statement_tag = 'IRMS::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"width": "5px"},
                {"width": "20px"},
                {"width": "200px"},
                {"width": "200px"},
                {"width": "150px"},
                {"width": "50px"},
                {"width": "200px"},
                {"width": "20px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "200px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [5, 9, 10]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tirmsv3_wp.on('xhr', function () {
            xhrdatairms = tirmsv3_wp.ajax.json();
            //console.log('xhr data: ' );
            // console.log(xhrdatairms);
        });

        var xtirmsv3_wp = $("#xtirmsv3_wp").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "order": [[5, "asc"], [9, "asc"], [10, "asc"]],
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/pavlist",
                type: "POST",
                data: function (d) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
	
//                    if ($('#id_ruas').val() == '#')
//                    {
//                        d.noruas = '#::#::#';
//                    } else
//                    {
//                        d.noruas = $('#xid_ruas').val();
//                    }
                    d.noruas = $('#id_ruas').val();
                    d.staa = $('#sta_awal').val() || $('#sta_awal-sel').val();
                    d.stae = $('#sta_akhir').val() || $('#sta_akhir-sel').val();

                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [12],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var thn = row[1];
                        var kat = row[2];
                        var road = row[5];
                        //var statement_tag = 'IRMS::' + id + '::' + thn + '-' + road + '-' + kat;
                        var statement_tag = 'IRMS::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"width": "5px"},
                {"width": "20px"},
                {"width": "200px"},
                {"width": "200px"},
                {"width": "150px"},
                {"width": "50px"},
                {"width": "200px"},
                {"width": "20px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "200px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [5, 9, 10]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        xtirmsv3_wp.on('xhr', function () {
            xhrdataxirms = xtirmsv3_wp.ajax.json();
            //console.log('xhr data: ' );
            // console.log(xhrdatairms);
        });


        var ztirmsv3_wp = $("#ztirmsv3_wp").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "order": [[5, "asc"], [9, "asc"], [10, "asc"]],
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/pavlist",
                type: "POST",
                data: function (d) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
	
//                    if ($('#id_ruas').val() == '#')
//                    {
//                        d.noruas = '#::#::#';
//                    } else
//                    {
//                        d.noruas = $('#xid_ruas').val();
//                    }
                    
                    d.noruas = $('#xid_ruas').val();                   
                    d.staa = $('#sta_awal').val() || $('#sta_awal-sel').val();
                    d.stae = $('#sta_akhir').val() || $('#sta_akhir-sel').val();

                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [12],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var thn = row[1];
                        var kat = row[2];
                        var road = row[5];
                        //var statement_tag = 'IRMS::' + id + '::' + thn + '-' + road + '-' + kat;
                        var statement_tag = 'IRMS::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-success btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"width": "5px"},
                {"width": "20px"},
                {"width": "200px"},
                {"width": "200px"},
                {"width": "150px"},
                {"width": "50px"},
                {"width": "200px"},
                {"width": "20px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "200px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [5, 9, 10]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tirmsv3_wp.on('xhr', function () {
            xhrdatairms = tirmsv3_wp.ajax.json();
            //console.log('xhr data: ' );
            // console.log(xhrdatairms);
        });



        var wtirmsv3_wp = $("#wtirmsv3_wp").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "order": [[5, "asc"], [9, "asc"], [10, "asc"]],
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/pavlist",
                type: "POST",
                data: function (d) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
	
//                    if ($('#id_ruas').val() == '#')
//                    {
//                        d.noruas = '#::#::#';
//                    } else
//                    {
//                        d.noruas = $('#xid_ruas').val();
//                    }
                    d.noruas = $('#wid_ruas').val();
                    d.staa = $('#wsta_awal').val();
                    d.stae = $('#wsta_akhir').val();

                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [12],
                    "mRender": function (data, type, row) {

                        var id = row[0];
                        var thn = row[1];
                        var kat = row[2];
                        var road = row[5];
                        //var statement_tag = 'IRMS::' + id + '::' + thn + '-' + road + '-' + kat;
                        var statement_tag = 'IRMS::' + id;

                        var html_button = '<button id="btn-edit#' + statement_tag +'" class="btn btn-success btn-xs edit-tag">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"width": "5px"},
                {"width": "20px"},
                {"width": "200px"},
                {"width": "200px"},
                {"width": "150px"},
                {"width": "50px"},
                {"width": "200px"},
                {"width": "20px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "200px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [5, 9, 10]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        tirmsv3_wp.on('xhr', function () {
            xhrdatairms = tirmsv3_wp.ajax.json();
            //console.log('xhr data: ' );
            // console.log(xhrdatairms);
        });


        var teprogram = $("#teprogram").DataTable({
            "draw": 0,
            "responsive": true,
//            "processing": true,
//            "serverSide": true,
//            "deferRender": true,
//            "order": [[0, "asc"]],
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/get_eprogram",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                type: "POST"
            },
            "autoWidth": false,
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,17]}],
            "columns": [
                {data: "title", "width": "20px"},
                {data: "nama_skenario", "width": "100px"},
                {data: "prioritas", "width": "100px"},
                {data: "rp_1", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_2", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_3", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_4", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_5", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_6", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "length_1", "width": "100px"},
                {data: "length_2", "width": "100px"},
                {data: "length_3", "width": "100px"},
                {data: "length_4", "width": "100px"},
                {data: "length_5", "width": "100px"},
                {data: "length_6", "width": "100px"},
                {data: "benefit", "width": "100px"},
                {data: "id_skenario", "width": "100px"},
                {data: "gid", "width": "100px"},
                {"width": "100px"}
//                {data: "remarks"},
//                {data: "length"},
//                {data: "bol"},
//                {data: "code"},
//                {data: "sid"},
//                {data: "noprop"},
//                {data: "data_dasar"},
//                {data: "tahun_dasar"},
//                {data: "inflasi"},
//                {data: "status"},
//                {data: "cakupan_user"}
            ],
            "aoColumnDefs": [
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row.gid;
                        var scene = row.nama_skenario;
                        var judul = row.title;
                        //var statement_tag = 'EPROGRAM::' + id + '::' + scene + '-' + judul;
                        var statement_tag = 'EPROG::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-warning btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
//                "thousands": ".",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        teprogram.on('xhr', function () {
            xhrdataeprogram = teprogram.ajax.json();
            //console.log('xhr data: ' );
            // console.log(xhrdatairms);
        });

        var zteprogram = $("#zteprogram").DataTable({
            "draw": 0,
            "responsive": true,
//            "processing": true,
//            "serverSide": true,
//            "deferRender": true,
//            "order": [[0, "asc"]],
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/get_eprogram",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                type: "POST"
            },
            "autoWidth": false,
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]}],
            "columns": [
                {data: "title", "width": "20px"},
                {data: "nama_skenario", "width": "100px"},
                {data: "prioritas", "width": "100px"},
                {data: "rp_1", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_2", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_3", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_4", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_5", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_6", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "length_1", "width": "100px"},
                {data: "length_2", "width": "100px"},
                {data: "length_3", "width": "100px"},
                {data: "length_4", "width": "100px"},
                {data: "length_5", "width": "100px"},
                {data: "length_6", "width": "100px"},
                {data: "benefit", "width": "100px"},
                {data: "id_skenario", "width": "100px"},
                {"width": "100px"}
//                {data: "remarks"},
//                {data: "length"},
//                {data: "bol"},
//                {data: "code"},
//                {data: "sid"},
//                {data: "noprop"},
//                {data: "data_dasar"},
//                {data: "tahun_dasar"},
//                {data: "inflasi"},
//                {data: "status"},
//                {data: "cakupan_user"}
            ],
            "aoColumnDefs": [
                {
                    "aTargets": [17],
                    "mRender": function (data, type, row) {

                        var id = row.id_skenario;
                        var scene = row.nama_skenario;
                        var judul = row.title;
                        //var statement_tag = 'EPROGRAM::' + id + '::' + scene + '-' + judul;
                        var statement_tag = 'EPROG::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-warning btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
//                "thousands": ".",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        teprogram.on('xhr', function () {
            xhrdataeprogram = teprogram.ajax.json();
            //console.log('xhr data: ' );
            // console.log(xhrdatairms);
        });



        var wteprogram = $("#wteprogram").DataTable({
            "draw": 0,
            "responsive": true,
//            "processing": true,
//            "serverSide": true,
//            "deferRender": true,
//            "order": [[0, "asc"]],
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/get_eprogram",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                type: "POST"
            },
            "autoWidth": false,
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,17]}],
            "columns": [
                {data: "title", "width": "20px"},
                {data: "nama_skenario", "width": "100px"},
                {data: "prioritas", "width": "100px"},
                {data: "rp_1", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_2", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_3", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_4", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_5", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_6", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "length_1", "width": "100px"},
                {data: "length_2", "width": "100px"},
                {data: "length_3", "width": "100px"},
                {data: "length_4", "width": "100px"},
                {data: "length_5", "width": "100px"},
                {data: "length_6", "width": "100px"},
                {data: "benefit", "width": "100px"},
                {data: "id_skenario", "width": "100px"},
                {data: "gid", "width": "100px"},
                {"width": "100px"}
//                {data: "remarks"},
//                {data: "length"},
//                {data: "bol"},
//                {data: "code"},
//                {data: "sid"},
//                {data: "noprop"},
//                {data: "data_dasar"},
//                {data: "tahun_dasar"},
//                {data: "inflasi"},
//                {data: "status"},
//                {data: "cakupan_user"}
            ],
            "aoColumnDefs": [
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row.gid;
                        var scene = row.nama_skenario;
                        var judul = row.title;
                        //var statement_tag = 'EPROGRAM::' + id + '::' + scene + '-' + judul;
                        var statement_tag = 'EPROG::' + id;

                        var html_button = '<button id="btn-edit#' + statement_tag +'" class="btn btn-warning btn-xs edit-tag">Tandai</button>';
                        return html_button;
                    }
                }
            ],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
//                "thousands": ".",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        wteprogram.on('xhr', function () {
            xhrdataeprogram = wteprogram.ajax.json();
            //console.log('xhr data: ' );
            // console.log(xhrdatairms);
        });




        var xteprogram = $("#xteprogram").DataTable({
            "draw": 0,
            "responsive": true,
//            "processing": true,
//            "serverSide": true,
//            "deferRender": true,
//            "order": [[0, "asc"]],
            "searching": false,
            "ajax": {
                url: "<?php echo base_url(); ?>pagu_indikatif/get_eprogram",
		"data": function ( d ) {
			d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                type: "POST"
            },
            "autoWidth": false,
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,17]}],
            "columns": [
                {data: "title", "width": "20px"},
                {data: "nama_skenario", "width": "100px"},
                {data: "prioritas", "width": "100px"},
                {data: "rp_1", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_2", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_3", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_4", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_5", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "rp_6", "width": "100px", render: $.fn.dataTable.render.number('.', '.', 0, '')},
                {data: "length_1", "width": "100px"},
                {data: "length_2", "width": "100px"},
                {data: "length_3", "width": "100px"},
                {data: "length_4", "width": "100px"},
                {data: "length_5", "width": "100px"},
                {data: "length_6", "width": "100px"},
                {data: "benefit", "width": "100px"},
                {data: "id_skenario", "width": "100px"},
                {data: "gid", "width": "100px"},
                {"width": "100px"}
//                {data: "remarks"},
//                {data: "length"},
//                {data: "bol"},
//                {data: "code"},
//                {data: "sid"},
//                {data: "noprop"},
//                {data: "data_dasar"},
//                {data: "tahun_dasar"},
//                {data: "inflasi"},
//                {data: "status"},
//                {data: "cakupan_user"}
            ],
            "aoColumnDefs": [
                {
                    "aTargets": [18],
                    "mRender": function (data, type, row) {

                        var id = row.gid;
                        var scene = row.nama_skenario;
                        var judul = row.title;
                        //var statement_tag = 'EPROGRAM::' + id + '::' + scene + '-' + judul;
                        var statement_tag = 'EPROG::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-warning btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
//                "thousands": ".",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        xteprogram.on('xhr', function () {
            xhrdataxeprogram = xteprogram.ajax.json();
            //console.log('xhr data: ' );
            // console.log(xhrdatairms);
        });


//        var sta_awal = tirmsv3_wp.column(8).data().filter( function ( value, index ) {
//                   return value <= staa;
//            });
//            
//            console.log(sta_awal);
//            
//            
//            var sta_akhir = tirmsv3_wp.column(9).data().filter( function ( value, index ) {
//                   return value;
//            });
//            
//            console.log(sta_akhir);


//                tirmsv3_wp.columns().every( function () {
//                    var that = this;
//                    var staa = parseInt($('#sta_awal').val());
//                    var stae = parseInt($('#sta_akhir').val());
//
//                    $('#sta_akhir').on( 'change', function () {
//                        if ( that.search() !== this.value ) {
//                            that.search( this.value ).draw();
//                        }
//                    } );
//                } );



// $('#sta_awal').on( 'change', function () {
//    
//      var staa = $('#sta_awal').val();
//                   
//      tirmsv3_wp.column(8).search(staa).draw();             
//    biaya
// });  

//        $("#biaya").on('keyup', function () {
//            var n = parseInt($(this).val().replace(/\D/g, ''), 10);
//            $(this).val(n.toLocaleString());
//        });


        $('#sta_awal-sel, #sta_akhir-sel').on('change', function () {

            tirmsv3_wp.ajax.reload();
            trams_wp.ajax.reload();
            var staa = $('#sta_awal-sel').val();
            var stae = $('#sta_akhir-sel').val();
            var volume = parseFloat((stae - staa) / 1000);
            $('#volume').val(volume);

        });

        //modal tambah paket
        $('#sta_awal, #sta_akhir').on('change', function () {

            tirmsv3_wp.ajax.reload();
            trams_wp.ajax.reload();
            var staa = $('#sta_awal').val();
            var stae = $('#sta_akhir').val();
            var volume = parseFloat((stae - staa) / 1000);
            var harga_satuan=$("#hargasat").val();
            $('#xvolume').val(volume);
           
        });

        //modal edit detail
        $('#wsta_awal, #wsta_akhir').on('change', function () {

            tirmsv3_wp.ajax.reload();
            trams_wp.ajax.reload();
            var staa = $('#wsta_awal').val();
            var stae = $('#wsta_akhir').val();
            var volume = parseFloat((stae - staa) / 1000);
            $('#wvolume').val(volume);

        });



       /*
        var trams_wp = $("#trams_wp").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "order": [[3, "asc"], [4, "asc"]],
            "searching": false,
            "ajax": {
                url: "< ?php echo base_url(); ?>pagu_indikatif/ramslist",
                type: "POST",
                data: function (d) {
			d.< ?php echo $this->security->get_csrf_token_name();?> = "< ?php echo $this->security->get_csrf_hash();?>";
		
                    if ($('#id_ruas').val() == '#')
                    {
                        d.noruas = '#::#::#';
                    } else
                    {
                        d.noruas = $('#xid_ruas').val();
                    }

                    d.staa = $('#sta_awal').val() || $('#sta_awal-sel').val();
                    d.stae = $('#sta_akhir').val() || $('#sta_akhir-sel').val();
                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [10],
                    "mRender": function (data, type, row) {



                        var id = row[0];
//                        var thn = row[1];
                        var kat = row[2];
                        var road = row[9];
//                        var statement_tag = 'RAMS::' + id + '::' + road + '-' + kat;
                        var statement_tag = 'RAMS::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-info btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"width": "5px"},
                {"width": "100px"},
                {"width": "350px"},
                {"width": "70px"},
                {"width": "50px"},
                {"width": "50px"},
                {"width": "200px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        trams_wp.on('xhr', function () {
            xhrdatarams = trams_wp.ajax.json();
            //console.log('xhr data: ' );
            //console.log(xhrdata);
        });
        //*/

        //Rujukan untuk detail
        /*
        var xtrams_wp = $("#xtrams_wp").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
//            "deferRender": true,
            "order": [[3, "asc"], [4, "asc"]],
            "searching": false,
            "ajax": {
                url: "< ?php echo base_url(); ?>pagu_indikatif/ramslist",
                type: "POST",
                data: function (d) {
			d.< ?php echo $this->security->get_csrf_token_name();?> = "< ?php echo $this->security->get_csrf_hash();?>";
		
                    if ($('#id_ruas').val() == '#')
                    {
                        d.noruas = '#::#::#';
                    } else
                    {
                        d.noruas = $('#xid_ruas').val();
                    }

                    d.staa = $('#sta_awal').val() || $('#sta_awal-sel').val();
                    d.stae = $('#sta_akhir').val() || $('#sta_akhir-sel').val();
                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [10],
                    "mRender": function (data, type, row) {



                        var id = row[0];
//                        var thn = row[1];
                        var kat = row[2];
                        var road = row[9];
//                        var statement_tag = 'RAMS::' + id + '::' + road + '-' + kat;
                        var statement_tag = 'RAMS::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-info btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"width": "5px"},
                {"width": "100px"},
                {"width": "350px"},
                {"width": "70px"},
                {"width": "50px"},
                {"width": "50px"},
                {"width": "200px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        trams_wp.on('xhr', function () {
            xhrdataxrams = trams_wp.ajax.json();
            //console.log('xhr data: ' );
            //console.log(xhrdata);
        });
        //*/

        //Rujukan untuk detail
        /*
        var ztrams_wp = $("#ztrams_wp").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            //"deferRender": true,
            "order": [[3, "asc"], [4, "asc"]],
            "searching": false,
            "ajax": {
                url: "< ?php echo base_url(); ?>pagu_indikatif/ramslist",
                type: "POST",
                data: function (d) {
			d.< ?php echo $this->security->get_csrf_token_name();?> = "< ?php echo $this->security->get_csrf_hash();?>";
		
                    if ($('#id_ruas').val() == '#')
                    {
                        d.noruas = '#::#::#';
                    } else
                    {
                        d.noruas = $('#xid_ruas').val();
                    }

                    d.staa = $('#sta_awal').val() || $('#sta_awal-sel').val();
                    d.stae = $('#sta_akhir').val() || $('#sta_akhir-sel').val();
                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [10],
                    "mRender": function (data, type, row) {



                        var id = row[0];
//                        var thn = row[1];
                        var kat = row[2];
                        var road = row[9];
//                        var statement_tag = 'RAMS::' + id + '::' + road + '-' + kat;
                        var statement_tag = 'RAMS::' + id;

                        var html_button = '<button onclick="dtTagging(' + "'" + statement_tag + "'" + ')" class="btn btn-info btn-xs">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"width": "5px"},
                {"width": "100px"},
                {"width": "350px"},
                {"width": "70px"},
                {"width": "50px"},
                {"width": "50px"},
                {"width": "200px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        trams_wp.on('xhr', function () {
            xhrdataxrams = trams_wp.ajax.json();
            //console.log('xhr data: ' );
            //console.log(xhrdata);
        });

         //*/
    
        //Rujukan untuk detail
        /*
        var wtrams_wp = $("#wtrams_wp").DataTable({
            "draw": 0,
            "responsive": true,
            "processing": true,
            "serverSide": true,
            //"deferRender": true,
            "order": [[3, "asc"], [4, "asc"]],
            "searching": false,
            "ajax": {
                url: "< ?php echo base_url(); ?>pagu_indikatif/ramslist",
                type: "POST",
                data: function (d) {
			d.< ?php echo $this->security->get_csrf_token_name();?> = "< ?php echo $this->security->get_csrf_hash();?>";
		
                    if ($('#id_ruas').val() == '#')
                    {
                        d.noruas = '#::#::#';
                    } else
                    {
                        d.noruas = $('#xid_ruas').val();
                    }

                    d.staa = $('#sta_awal').val() || $('#sta_awal-sel').val();
                    d.stae = $('#sta_akhir').val() || $('#sta_akhir-sel').val();
                }
            },
            "aoColumnDefs": [
                {
                    "aTargets": [10],
                    "mRender": function (data, type, row) {



                        var id = row[0];
//                        var thn = row[1];
                        var kat = row[2];
                        var road = row[9];
//                        var statement_tag = 'RAMS::' + id + '::' + road + '-' + kat;
                        var statement_tag = 'RAMS::' + id;

                        var html_button = '<button type="button" id="btn-edit' + statement_tag +'" class="btn btn-info btn-xs edit-tag">Tandai</button>';
                        return html_button;
                    }
                }
            ],
            "autoWidth": false,
            "columns": [
                {"width": "5px"},
                {"width": "100px"},
                {"width": "350px"},
                {"width": "70px"},
                {"width": "50px"},
                {"width": "50px"},
                {"width": "200px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"},
                {"width": "100px"}
            ],
            "pagingType": "full_numbers",
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}],
//            "columnDefs": [{orderable: false, targets: [2]}],
            "pageLength": 5,
            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//                "scrollY":        "300px",
//                "scrollX":        true,
//                "scrollCollapse": true,
//                "fixedColumns":   {
//                    leftColumns: 2
//                }
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
//                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fa fa-angle-double-left'></i>",
                    "last": "<i class='fa fa-angle-double-right'></i>",
                    "next": "<i class='fa fa-angle-right'></i>",
                    "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        trams_wp.on('xhr', function () {
            xhrdataxrams = trams_wp.ajax.json();
            //console.log('xhr data: ' );
            //console.log(xhrdata);
        });
         //*/                                            

//        var tlist_detail = $("#tlist_detail").DataTable({
//            "draw": 0,
//            "responsive": true,
//            "processing": true,
//            "serverSide": true,
////            "order": [[ 25, "asc" ]],
////            "destroy":true,
////            "deferRender": true,
////            "scrollY": "600px",
////            "scrollX": true,
////            "scrollCollapse": true,
//////            "columnDefs": [
//////                {width: '20%', targets: -1}
//////            ],
////            "fixedColumns": true,
////            "scrollY": "300px",
////            "scrollX": true,
//            "scrollCollapse": true,
////            "fixedColumns": {
//////                "lefColumns": 1,
////                "rightColumns": 1,
////            },
////            "ajax": "<?php //echo base_url('/pagu_indikatif/usulanlist');                                                                                                                                                             ?>",
//            "ajax": {
//                url: "<?php echo base_url(); ?>pagu_indikatif/ssp_detail",
//                type: "GET",
//                data: function (d) {
//                    d.id_paket = $('#id_paket').val();
//                    //d.stae = $('#sta_akhir').val();
//                }
//            },
////            "autoWidth": false,
////            "columns": [
////                {"width": "200px"},
////                {"width": "5px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"},
////                {"width": "100px"}
////            ],
//            "pagingType": "full_numbers",
//            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}],
////            "columnDefs": [{orderable: false, targets: [2]}],
//            "aoColumnDefs": [
//                {
//                    "aTargets": [0],
//                    "width": "150px",
//                    "mRender": function (data, type, row) {
//
//
//
//                        var html = '';
//                        var tags = JSON.parse(row[0]);
////                        var tags = row[0];
//
////                        console.log(tags);
//
//
//
//
//
//                        //console.log(dttags);
//
//                        $.each(tags, function (index, value) {
////                            console.log(value);
//
//                            switch (value.rujukan) {
//                                case 'PEMDA':
//                                    html += '<span class="tag label label-default">' + value.value + ' - ' + value.text + '</span><br/>';
//                                    break;
//                                case 'DPR':
//                                    html += '<span class="tag label label-default">' + value.value + ' - ' + value.text + '</span><br/>';
//                                    break;
//                                case 'SIPRO':
//                                    html += '<span class="tag label label-primary">' + value.value + ' - ' + value.text + '</span><br/>';
//                                    break;
//                                case 'IRMS':
//                                    html += '<span class="tag label label-success">' + value.value + ' - ' + value.text + '</span><br/>';
//                                    break;
//                                case 'RAMS':
//                                    html += '<span class="tag label label-info">' + value.value + ' - ' + value.text + '</span><br/>';
//                                    break;
//                                case 'EPROGRAM':
//                                    html += '<span class="tag label label-warning">' + value.value + ' - ' + value.text + '</span><br/>';
//                                    break;
//                                case 'RENSTRA':
//                                    html += '<span class="tag label label-danger">' + value.value + ' - ' + value.text + '</span>';
//                                    break;
//                            }
//                            //console.log(value.rujukan);
//
////                            elt.tagsinput('add', value);
//
//                        });
//
//                        return html;
//
//
//
//
//
//                    }
//                },
//                {
//                    "aTargets": [1],
//                    "width": "20px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [2],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [3],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [4],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [5],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [6],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [7],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [8],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [9],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [10],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [11],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [12],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [13],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [14],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [15],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                {
//                    "aTargets": [16],
//                    "width": "100px",
//                    "visible": true
////                        "searchable": true
//                },
//                /*
//                 {
//                 "aTargets": [17],
//                 "width": "100px",
//                 "visible": true
//                 //                        "searchable": true
//                 },
//                 {
//                 "aTargets": [18],
//                 "width": "100px",
//                 "visible": true
//                 //                        "searchable": true
//                 },
//                 {
//                 "aTargets": [19],
//                 "width": "100px",
//                 "visible": true
//                 //                        "searchable": true
//                 },
//                 {
//                 "aTargets": [20],
//                 "width": "100px",
//                 "visible": true
//                 //                        "searchable": true
//                 },
//                 {
//                 "aTargets": [21],
//                 "width": "100px",
//                 "visible": true
//                 //                        "searchable": true
//                 },
//                 {
//                 "aTargets": [22],
//                 "width": "100px",
//                 "visible": true
//                 //                        "searchable": true
//                 },
//                 {
//                 "aTargets": [23],
//                 "width": "100px",
//                 "visible": true
//                 //                        "searchable": true
//                 },
//                 {
//                 "aTargets": [24],
//                 "width": "100px",
//                 "visible": true
//                 //                        "searchable": true
//                 },
//                 
//                 //                 {
//                 //                    "aTargets": [25],
//                 //                    "width": "100px",
//                 //                    "visible": true
//                 ////                        "searchable": true
//                 //                },
//                 //                 {
//                 //                    "aTargets": [26],
//                 //                    "width": "100px",
//                 //                    "visible": true
//                 ////                        "searchable": true
//                 //                },
//                 //*/
//                {
//                    "aTargets": [17],
//                    "mRender": function (data, type, row) {
//
//                        var id = row[1];
//                        var html_button = [
//                            "<button onclick= dtEditRowDetail('" + id + "') class='btn btn-primary btn-xs'>",
//                            "<i class='fa fa-pencil'>",
//                            "</i>",
//                            "</button>",
//                            "<button onclick= dtDeleteRow('detail','" + id + "') class='btn btn-danger btn-xs'>",
//                            "<i class='fa fa-trash'>",
//                            "</i>",
//                            "</button>"
//                        ].join("\n");
//                        return html_button;
//                    }
//                }
//            ],
//            "pageLength": 5,
//            "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
//            //                "scrollY":        "300px",
//            //                "scrollX":        true,
//            //                "scrollCollapse": true,
////                "fixedColumns":   {
//            //                    leftColumns: 2
////                }
//            "language": {
//                "decimal": "",
//                "emptyTable": "Data tidak ditemukan",
//                "info": "Data _START_ s/d _END_ dari _TOTAL_",
//                "infoEmpty": "Tidak ada data",
//                "infoFiltered": "(tersaring dari _MAX_)",
//                "infoPostFix": "",
//                "thousands": ",",
//                "lengthMenu": "_MENU_  data per halaman",
//                "loadingRecords": "Memuat...",
//                "processing": "Memroses...",
//                "search": "Cari:",
//                "zeroRecords": "Tidak ada data ditemukan",
//                "paginate": {
//                    "first": "<i class='fa fa-angle-double-left'></i>",
//                    "last": "<i class='fa fa-angle-double-right'></i>",
//                    "next": "<i class='fa fa-angle-right'></i>",
//                    "previous": "<i class='fa fa-angle-left'></i>"
//                },
//                "aria": {
//                    "sortAscending": ": aktifkan untuk mengurutkan naik",
//                    "sortDescending": ": aktifkan untuk mengurutkan turun"
//                }
//            }
//        });
//        tlist_detail.columns( [0,23] ).visible( false );
        //function start_list_paket(){
           dataTablepaket();
    }
    else
    {
           dataTablepaket();
    }
     

        //Insert a 'details' column to the table
//        var nCloneTh = document.createElement('th');
//        var nCloneTd = document.createElement('td');
//        nCloneTd.innerHTML = '<img src="http://i.imgur.com/SD7Dz.png">';
//        nCloneTd.className = "center";
//
//        $('#tlist_paket thead tr').each(function () {
//            this.insertBefore(nCloneTh, this.childNodes[0]);
//        });
//
//        $('#tlist_paket tbody tr').each(function () {
//            this.insertBefore(nCloneTd.cloneNode(true), this.childNodes[0]);
//        });




//        tlist_paket.on('order.dt search.dt', function () {
//            tlist_paket.column(0, {
//                search: 'applied',
//                order: 'applied'
//            }).nodes().each(function (cell, i) {
//                cell.innerHTML =  '<img src="http://i.imgur.com/d4ICC.png">';
//            });
//        }).draw();

        $("#tlist_paket").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#tsipro_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#trams_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#tirmsv3_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#teprogram").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#trenstra").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#tusulandpr").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#tusulanpemda").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");

        //arahan modal detail
        $("#xtlist_paket").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#xtsipro_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#xtrams_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#xtirmsv3_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#xteprogram").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#xtrenstra").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#xtusulandpr").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#xtusulanpemda").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");

        //arahan modal detail
        //$("#ztlist_paket").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#ztsipro_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#ztrams_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#ztirmsv3_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#zteprogram").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#ztrenstra").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#ztusulandpr").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#ztusulanpemda").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");


        //arahan modal edit detail
        //$("#wtlist_paket").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#wtsipro_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#wtrams_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#wtirmsv3_wp").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#wteprogram").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#wtrenstra").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#wtusulandpr").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
        $("#wtusulanpemda").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");

        $('#btnSetuju').click(function () {
            console.log('stuju');
            var wdata = way.get('formData');
            wdata.status = 1;
//            wdata.id_user = id_user_get;
            console.log(wdata);

            var mode = wdata.modeform;

            var url;
//            if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/pagu_indikatif/addform";
//            } else if (mode == 'edit') {
//                url = "<?php echo base_url(); ?>/pagu_indikatif/editform";
//            }

            var params = {"formData": wdata, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
            //console.log("--form data--");
            //console.log(data);

            $.post(url, params)
                    .done(function (data) {
                        tlist_paket.ajax.reload();
                        $("#alert-content").append(" <p>Insert data suksess");
                        $("#alert_information").css({display: "block"});
//                        setTimeout(close_alert, 2000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-edit").modal("hide");
        });

        $('#btnTolak').click(function () {

            //$("").insertAfter("#block-ct");


            console.log('tolak');
            var wdata = way.get('formData');
            wdata.status = 2;
            // wdata.id_user = id_user_get;
            //console.log(wdata);

            var mode = wdata.modeform;

            var url;
//            if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/pagu_indikatif/addform";
//            } else if (mode == 'edit') {
//                url = "<?php echo base_url(); ?>/pagu_indikatif/editform";
//            }

            var params = {"formData": wdata, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
              //console.log("--form data--");
            //console.log(data);

            $.post(url, params)
                    .done(function (data) {

                        tlist_paket.ajax.reload();
                        $("#alert-content").append(" <p>Insert data suksess");
                        $("#alert_information").css({display: "block"});
                        //setTimeout(close_alert, 2000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-edit").modal("hide");

        });


        $('#btnTutupView').click(function () {
            $('#modal-view').modal('hide');
        });


        $('#btnHold').click(function () {
            console.log('hold');
            var wdata = way.get('formData');
            wdata.status = 3;
//            wdata.id_user = id_user_get;
            console.log(wdata);

            var mode = wdata.modeform;

            var url;
//            if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/pagu_indikatif/addform";
//            } else if (mode == 'edit') {
//                url = "<?php echo base_url(); ?>/pagu_indikatif/editform";
//            }

           var params = {"formData": wdata, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
             //console.log("--form data--");
            //console.log(data);

            $.post(url, params)
                    .done(function (data) {
                        tlist_paket.ajax.reload();
                        $("#alert-content").append(" <p>Insert data suksess");
                        $("#alert_information").css({display: "block"});
//                        setTimeout(close_alert, 2000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-edit").modal("hide");

        });

        $('#btnStock').click(function () {
            console.log('stock');
            var wdata = way.get('formData');
            wdata.status = 4;
//            wdata.id_user = id_user_get;
            console.log(wdata);

            var mode = wdata.modeform;

            var url;
//            if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>/pagu_indikatif/addform";
//            } else if (mode == 'edit') {
//                url = "<?php echo base_url(); ?>/pagu_indikatif/editform";
//            }
var params = {"formData": wdata, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
            
            //console.log("--form data--");
            //console.log(data);

            $.post(url, params)
                    .done(function (data) {
                        tlist_paket.ajax.reload();
                        $("#alert-content").append(" <p>Insert data suksess");
                        $("#alert_information").css({display: "block"});
//                        setTimeout(close_alert, 2000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-edit").modal("hide");

        });

        // you would probably be using templates here
        //detailsTableHtml = $("#detailsTable").DataTables();

        //Insert a 'details' column to the table
//        var nCloneTh = document.createElement('th');
//        var nCloneTd = document.createElement('td');
//        nCloneTd.innerHTML = '<img src="https://datatables.net/examples/resources/details_open.png">';
//        nCloneTd.className = "center";
//
//        $('#exampleTable thead tr').each(function () {
//            this.insertBefore(nCloneTh, this.childNodes[0]);
//        });
//
//        $('#exampleTable tbody tr').each(function () {
//            this.insertBefore(nCloneTd.cloneNode(true), this.childNodes[0]);
//        });


        //Initialse DataTables, with no sorting on the 'details' column
//        var oTable = $('#exampleTable').dataTable({
//            "bJQueryUI": true,
//            "aaData": newRowData,
//            "bPaginate": false,
//            "aoColumns": [
//                {
//                    "mDataProp": null,
//                    "sClass": "control center",
//                    "sDefaultContent": '<img src="https://datatables.net/examples/resources/details_open.png">'
//                },
//                {"mDataProp": "race"},
//                {"mDataProp": "year"},
//                {"mDataProp": "total"}
//            ],
//            "oLanguage": {
//                "sInfo": "_TOTAL_ entries"
//            },
//            "aaSorting": [[1, 'asc']]
//        });

        /* Add event listener for opening and closing details
         * Note that the indicator for showing which row is open is not controlled by DataTables,
         * rather it is done here
         */
//        $('#exampleTable tbody td img').live('click', function () {
//            var nTr = $(this).parents('tr')[0];
//            var nTds = this;
//
//            if (oTable.fnIsOpen(nTr)) {
//                /* This row is already open - close it */
//                this.src = "https://datatables.net/examples/resources/details_open.png";
//                oTable.fnClose(nTr);
//            } else {
//                /* Open this row */
//                var rowIndex = oTable.fnGetPosition($(nTds).closest('tr')[0]);
//                var detailsRowData = newRowData[rowIndex].details;
//
//                this.src = "https://datatables.net/examples/resources/details_close.png";
//                oTable.fnOpen(nTr, fnFormatDetails(iTableCounter, detailsTableHtml), 'details');
//                oInnerTable = $("#exampleTable_" + iTableCounter).dataTable({
//                    "bJQueryUI": true,
//                    "bFilter": false,
//                    "aaData": detailsRowData,
//                    "bSort": true, // disables sorting
//                    "aoColumns": [
//                        {"mDataProp": "pic"},
//                        {"mDataProp": "name"},
//                        {"mDataProp": "team"},
//                        {"mDataProp": "server"}
//                    ],
//                    "bPaginate": false,
//                    "oLanguage": {
//                        "sInfo": "_TOTAL_ entries"
//                    },
//                    "fnRowCallback": function (nRow, aData, iDisplayIndex, iDisplayIndexFull) {
//                        var imgLink = aData['pic'];
//                        var imgTag = '<img width="100px" src="' + imgLink + '"/>';
//                        $('td:eq(0)', nRow).html(imgTag);
//                        return nRow;
//                    }
//                });
//                iTableCounter = iTableCounter + 1;
//            }
//        });


    });
    function get_wps() {
        var x = null;
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_wps') ?>",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)
                x = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }



    function get_detail_usulan(id_detail) {
        var x = null;
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_detail_usulan') ?>" + "/" + id_detail,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {          
                x = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    var htmldropdownsumberdana="";
    function editDetail(id_detail){
       //mengosongkan semua nilai variabel global
       rujukan_all=[];
       obj_wsumberdana={};
       objupdateedittags={};
        $("#xrm").val("");
        var roledesc="<?php echo $this->session->users['roledesc']; ?>"
        
        var obj_usulan = get_detail_usulan(id_detail);
        
        if(roledesc=="Satker Fisik"){
            $(".divNonFisik").css("display","none");
        }else{
            $(".divRuas").css("display","none");
        }
        //menyembunyikan field field khusus bagi satker nonfisik 
        
        refreshComboboxOutput('wthang', 21, 'thang', obj_usulan.thang, obj_usulan.thang);
        //alert(obj_usulan.kd_isu);
        updateComboboxAndSelected('wkd_isu', 3, obj_usulan.kd_isu);
      
        // updateComboboxAndSelected('zwps_kode', 3,data_selected[27]);

        refreshComboboxOutput('wkd_kegiatan', 5, 'kdgiat', obj_usulan.kd_kegiatan, obj_usulan.kd_kegiatan);

        setTimeout(set_kegiatan, 8000);//set kegiatan berdasarkan kode

        $("#wkd_sub_komponen").val(obj_usulan.kd_sub_komponen);

        $("#wnama_sub_komponen").val(obj_usulan.nama_sub_komponen);
        $("#wid_paket").val(obj_usulan.id_paket);
        $("#wid_usulan").val(obj_usulan.id_usulan);


        var id_rprov = $("#id_rprov").val();
        refreshComboboxOutput('wprov', 60, 'kd_prov', id_rprov, id_rprov);

        refreshComboboxOutput('wkd_output', 30, 'kdgiat', obj_usulan.kd_kegiatan, obj_usulan.kd_output);

        //alert(obj_usulan.kd_output+"--#####--"+obj_usulan.kd_sub_output);

        refreshComboboxOutput('wkd_sub_output', 44, 'kdoutput', obj_usulan.kd_output, obj_usulan.kd_sub_output);

        refreshComboboxOutput('wkd_komponen', 18, 'kdsoutput', obj_usulan.kd_sub_output, obj_usulan.kd_komponen);

        updateComboboxAndSelected('wjnskontrak', 23, obj_usulan.jnskontrak);

        updateComboboxAndSelected('wkdkppn', 9, obj_usulan.kdkppn);

        updateComboboxAndSelected('wid_ppk', 11, obj_usulan.id_ppk);

        updateComboboxAndSelected('wwps_kode', 57, obj_usulan.wps_kode);

        updateComboboxAndSelected('wkdgbkpk', 36, obj_usulan.kdgbkpk);
         
        //alert(obj_usulan.id_ruas);
        //alert(obj_usulan.no_ruas);
       // updateComboboxAndSelected('wid_ruas', 33, obj_usulan.no_ruas);
        //bind_ruas_by_province(obj_usulan.no_ruas);
        refreshComboboxRuasSatker("wid_ruas",obj_usulan.id_ruas);
//        refreshComboboxSTASatker("wsta_awal",obj_usulan.id_ruas,obj_usulan.sta_awal);
//        refreshComboboxSTASatker("wsta_akhir",obj_usulan.id_ruas,obj_usulan.sta_akhir);
        //$("#wsta_awal").append("<option selected value=" + obj_usulan.sta_awal + ">" + obj_usulan.sta_awal + "</option>");
        //$("#wsta_akhir").append("<option selected value=" + obj_usulan.sta_akhir + ">" + obj_usulan.sta_akhir + "</option>")

        $("#wvolume").val(obj_usulan.volume);
        //alert((typeof obj_usulan.id_jembatan));

        if (typeof obj_usulan.id_jembatan != "object") {

            //$("#wid_jembatan").append("<option selected value="+obj_usulan.id_jembatan+">"+"Jembatan 5555"+"</option>");
            updateComboboxAndSelected('wid_jembatan', 56, obj_usulan.id_jembatan);
            $("#wlongitude").val(obj_usulan.longitude);
            $("#wlatitude").val(obj_usulan.latitude);
        }
        $("#wsatuan").val(obj_usulan.satuan);
        
        var wjumlah=obj_usulan.jumlah;
        var whargasat=obj_usulan.hargasat;
        $("#wjumlah").val(numberWithCommas(wjumlah));
        $("#whargasat").val(numberWithCommas(whargasat));
        //updateComboboxAndSelected('zid_ppk',11,data_selected[30]);

        //refreshComboboxOutput('ykdgbkpk', 36, 'kdgbkpk',data_selected[20],data_selected[20]);
        //initCombobox("zkdgbkpk",36);

        initCombobox("wkdakun", 37);

        $("#wrc_ded_status").append("<option selected value=" + obj_usulan.rc_ded_status + ">" + obj_usulan.rc_ded_status + "</option>");
        $("#wrc_fs_status").append("<option selected " + obj_usulan.rc_fs_status + ">" + obj_usulan.rc_fs_status + "</option>");
        $("#wrc_lahan_status").append("<option selected " + obj_usulan.rc_lahan_status + ">" + obj_usulan.rc_lahan_status + "</option>");
        $("#wrc_doklin_status").append("<option selected " + obj_usulan.rc_doklin_status + ">" + obj_usulan.rc_doklin_status + "</option>");

        //updateComboboxAndSelected('wid_ruas',33,obj_usulan.id_ruas);
        //updateComboboxAndSelected('zkws_kode',57,obj_usulan.kws_kode);
        // alert( $('#yjns_giat option:eq(1)').val());
        //alert($("#ykd_kegiatan-sel").val());

        setTimeout(set_kegiatan, 8000);//
        $("#rj_rams").val(obj_usulan.rj_rams);
        $("#rj_irms").val(obj_usulan.rj_irms);
        $("#rj_renstra").val(obj_usulan.rj_renstra);
        $("#rj_eprog").val(obj_usulan.rj_eprog);
        $("#rj_dpr").val(obj_usulan.rj_dpr);
        $("#rj_pemda").val(obj_usulan.rj_pemda);
        $("#rj_sipro").val(obj_usulan.rj_sipro);
        
        var tagram=set_rams();
        var tagirms=set_irms();
        var tagrenstra=set_renstra();
        var tageprog=set_eprog();
        var tagdpr=set_dpr();
        var tagsipro=set_sipro();
        var tagpemda=set_pemda();
        var objtag=[
      	{"rams":tagram},
        {"irms":tagirms},
      	{"renstra":tagrenstra},
      	{"eprog":tageprog},
       	{"dpr":tagdpr},
        {"pemda":tagpemda},
        {"sipro":tagsipro},
               ];
        var string_tag="";
        var append_tag="";
        for(var i=0; i<= objtag.length-1; i++){
         
          var strtag=JSON.stringify(Object.values(objtag[i]));
          var arr_tag=strtag.split(",");
           for (var y=0; y<= arr_tag.length-2; y++){
           
            var string_key=JSON.stringify(Object.keys(objtag[i])).replace('[', '');
           
            string_key=string_key.replace(']','');
            string_key=string_key.replace('"','');
            string_key=string_key.replace('"','');
            
            var tag_value=arr_tag[y];
            tag_value=tag_value.replace('[','');
            tag_value=tag_value.replace(']','');
            tag_value=tag_value.replace('"','');
            tag_value=tag_value.replace('"','');
            var xkey= string_key;
            
            var append_tag=append_tag+xkey.toUpperCase()+"|"+tag_value+",";
            
          }                  
        }
        
        bindTagging2(append_tag);
        
         
        $(".edit-tag").off('click');//mencegah kode dieksekusi 2 kali 
        $(".edit-tag").click(function(){
            //console.log("id rujukan");
            //console.log(this.id);
            //var button_id=this.id.replace("btn-edit","");
            //console.log("button ID");
            var rujukan_id= this.id.split("#");
            var taggkey=rujukan_id[1].split('::');
            
            console.log("taggkey");
            console.log(taggkey);
            //alert(button_id);
            var taggclass="";
                   if(taggkey[0]=='RAMS'){
                     taggclass='-info';
                   }else if(taggkey[0]=='IRMS'){
                     console.log(" value irms"+taggkey[0]);
                     taggclass='-success';
                   }else if(taggkey[0]=='RENSTRA'){
                     taggclass='-danger';
                   }
                   else if(taggkey[0]=='EPROG'){
                     taggclass='-warning';
                   }
                   else if(taggkey[0]=='DPR'){
                     taggclass='-info';
                   }
                   else if(taggkey[0]=='SIPRO'){
                     taggclass='-primary';
                   }else if(taggkey[0]=='PEMDA'){
                     taggclass='-default';
                   }
                                   //alert(taggkey[1]);
                                   var html_tag_childrens=["<span class='tag label label"+taggclass+"'>",
                                             taggkey[0]+"|"+taggkey[1],
                                             "<span style='cursor: pointer;' id='"+taggkey[1]+"' class='bc'>",
                                                  "X",
                                             "</span>"].join("\n");
                   $("#md-edit-penandaan").children(".div-tags").children(".bootstrap-tagsinput").append(html_tag_childrens);

                   $(".bc").bind( "click", function() {
                      var button_id = "#"+this.id;
                      $(button_id).parents('.tag').remove();
                   });
        });
        
         
         var objsumberdana={};
         if(obj_usulan.phln !="" && typeof obj_usulan.phln != 'object' && obj_usulan.phln != '.000'){
            objsumberdana["phln"]=obj_usulan.phln;
         }
         
         if(obj_usulan.rmp != "" && typeof obj_usulan.rmp != 'object' && obj_usulan.rmp != '.000'){
             objsumberdana["rmp"]= obj_usulan.rmp;
         }
         
         if(obj_usulan.pnbp != "" && typeof obj_usulan.pnbp != 'object' && obj_usulan.pnbp != '.000'){
             objsumberdana["pnbp"]=obj_usulan.pnbp;
         }
         
         if(obj_usulan.blu != "" && typeof obj_usulan.blu != 'object' && obj_usulan.blu != '.000'){
             objsumberdana["blu"]=obj_usulan.blu;
         }
         
         if(obj_usulan.sbsn != "" && typeof obj_usulan.sbsn != 'object' && obj_usulan.sbsn != '.000'){
             objsumberdana["sbsn"]=obj_usulan.sbsn;
         }
         
         if(obj_usulan.opr != "" && typeof obj_usulan.opr != 'object' && obj_usulan.opr != '.000'){
             objsumberdana["opr"]=obj_usulan.opr;
         }
         
         if(obj_usulan.pdp != "" && typeof obj_usulan.pdp != 'object' && obj_usulan.pdp != '.000'){
             objsumberdana["pdp"]=obj_usulan.pdp;
         }
         
         if(obj_usulan.pdn != "" && typeof obj_usulan.pdn != 'object' && obj_usulan.pdn != '.000'){
             objsumberdana["pdn"]=obj_usulan.pdn;
         }
         if(obj_usulan.rpm != "" && typeof obj_usulan.rpm != 'object' && obj_usulan.rpm != '.000'){
             objsumberdana["rpm"]=obj_usulan.rpm;
         }
         
         console.log('---objsumberdana---')
         console.log(objsumberdana);
         //alert(typeof obj_usulan.blu);
         //var zd=typeof obj_usulan.blu;
         //alert(zd);
         //alert(typeof zd);
         //alert($("#wkdsdana").html());
         multipleSelect('wkdsdana', 38);
         $("#htmldropdownsdana").val($("#wkdsdana").html());
         //setTimeout(setDropdown, 3000)
         //$("#wkdsdana").append($("#htmldropdownsdana").html());
         
         $("#objsumberdana").val("");
         $("#objsumberdana").val(JSON.stringify(objsumberdana));
         //setSumberDana();
         setTimeout(setSumberDana, 3000)
         
         console.log("---tipe hargasat---")
         console.log(typeof obj_usulan.hargasat);
         
         console.log("--tipe jumlah---")
         console.log(typeof obj_usulan.jumlah);
         
         var whargasat=obj_usulan.hargasat;
         $("#whargasat").val(numberWithCommas(parseInt(whargasat)));
         console.log(numberWithCommas(whargasat));
         var wjumlah=obj_usulan.jumlah;
         $("#wjumlah").val(numberWithCommas(parseInt(wjumlah)));
         
         console.log("tipe textjumlah"+$("#wjumlah").val());
         
         $("#modal-edit-detail").modal("show");

    }
    
    function setDropdown(){
         $("#htmldropdownsdana").val($("#wkdsdana").val());
    }
   
 
    $(".input-group-select:input").change(function(){
        alert(11111);
    });
    
   
    function update_form_pagu_detail() {
        var objupdateedittags=extract_tags();
        var obj_wsumberdana={};
        
        console.log("jumlah input :"+$(".mdl-edit-detail input").length);
        if($(".mdl-edit-detail input").length !=1){
           obj_wsumberdana=Object.assign({},arr_wsumberdana); 
           console.log("lebih dari 1")
        }else{
            console.log("1 input")
           var key=$(".mdl-edit-detail .dropdown-toggle .concept").text();
           var single_sdana= $(".mdl-edit-detail input").val().split(".")[0];
           single_sdana=single_sdana.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1");
           arr_wsumberdana[key.toLowerCase()]=single_sdana;
           obj_wsumberdana=[{key.toLowerCase():single_sdana}]; 
        }
        
        //console.log("key"+key)
        var tags_val=JSON.stringify(rujukan_all).replace(/\\/g, '');
        console.log("---key ---");
        console.log(JSON.stringify(JSON.stringify(single_sdana)));
        
        console.log("obj wsumberdana");
        console.log(obj_wsumberdana);
        //console.log(tags_val);
        //console.log(tags_val);
        tags_val= tags_val.replace(/\"{/g,'{');
        tags_val= tags_val.replace(/\}"/g,'}');
        //console.log(JSON.stringify(obj_wsumberdana));
        var hargasat= $("#whargasat").val().split(".")[0];
        var jumlah =$("#wjumlah").val().split(".")[0];
        
        //menentukan data uraian
        if ($("#wid_ruas").val() != "-1" || $("#wid_ruas").val() != "#") {

            if ($("#wid_jembatan").val() == "#" || $("#wid_jembatan").val() == "-1") {

                $("#wdetail").val($("#wid_ruas option:selected").text());
            } else {


                $("#wdetail").val($("#wid_jembatan option:selected").text());


            }
        }


        var uraian = $("#wdetail").val();
        
        
        
        var obj_usulan = {
            "id_ruas": $("#wid_ruas").val(),
            "sta_awal": $("#wsta_awal").val(),
            "sta_akhir": $("#wsta_akhir").val(),
            "id_jembatan": $("#wid_jembatan").val(), //not exists in form exists in table
            "longitude": $("#wlongitude").val(), //not exists in form exists in table
            "latitude": $("#wlatitude").val(), //not exists in form exists in table
            "volume": $("#wvolume").val(), //not exists in form exists in table
            "satuan": $("#wsatuan").val(), //not exists in form exists in table
            "hargasat": parseInt(hargasat.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1")),
            "jumlah": parseInt(jumlah.replace(/(\d+).(?=\d{3}(\D|$))/g, "$1")),
            "id_paket": $("#wid_paket").val(),
            "id_usulan": $("#wid_usulan").val(),
            "id_usulan": $("#wid_usulan").val(),
            "obj_sumberdana":JSON.stringify(obj_wsumberdana),
            "data_tag" : JSON.stringify(objupdateedittags),
            "detail":uraian,
            "tags_val":tags_val,
        };
        console.log("object usulan")
      
        var url = "<?php echo base_url("pagu_indikatif/update_detail_usulan"); ?>"
        var params = {"formData": obj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
          
        $.post(url, params)
                    .done(function (data) {
                         var tlist_paket = $('#tlist_paket').DataTable();
                         tlist_paket.ajax.reload();
                        $('#modal-edit-detail').modal('hide');
                    })
        .fail(function () {
            alert("error");
        })
       
    }



    function bind_combo_wps() {
        $("#wps_kode").empty();
        var data = get_wps();
        //alert(data);
        for (var i = 0; i <= data.length - 1; i++) {
            $("#wps_kode").append("<option value=" + data[i].wps_kode + ">" + data[i].wps_nama + "</option>")
            console.log(data[i].wps_kode);

        }
    }

    function wps()
    {
        var x = $("#wps_kode").val();
        // alert(x);
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_kws/') ?>" + x,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)

                x = data;
                lookkws(x)

            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });


    }

    function lookkws(x)
    {
        $("#kws_kode").empty();
        var dataa = x;
        var html_looku = "<option value=''>--Pilih KWS----</option>";
        $("#kws_kode").append(html_looku);
        for (var i = 0; i <= dataa.length - 1; i++) {

            var html_lookup = ["<option value=" + dataa[i].kws_kode + " >",
                dataa[i].kws_nama,
                "</option>",
            ].join("\n");

            $("#kws_kode").append(html_lookup);

        }
    }
    function kws()
    {
        var x = $("#kws_kode").val();
        // alert(x);
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_sub/') ?>" + x,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)

                x = data;
                looksub(x)

            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });


    }

    function looksub(x)
    {
        $("#subkw").empty();
        var dataa = x;
        var html_looku = "<option value=''>--Pilih Sub Kawasan----</option>";
        $("#subkw").append(html_looku);
        for (var i = 0; i <= dataa.length - 1; i++) {
            console.log(dataa[i].subkawasan_nama);
            var nm = dataa[i].subkawasan_nama;
            var html_lookup = ['<option value="' + nm + '" >',
                dataa[i].subkawasan_nama,
                '</option>',
            ].join("\n");

            $("#subkw").append(html_lookup);

        }
    }
//    var set_val_thang = function (yearnow) {
//        setTimeout(function () {
//            tlist_paket.columns(2)
//                    .search(yearnow)
//                    .draw();
//        }, 1000);
//    };

    function btnProsesVerifikasiIndi(data) {
        //console.log(data);

        var adata = data.split('~');

        var id_paket = adata[1];
        var idx = parseInt(adata[2], 10);

        console.log('idx ' + idx);
        var sstat = adata[0].split('|')[idx];
        var astat = sstat.split('_');


        var roleberhak = astat[0];

        // console.log(roleberhak);

//        console.log(id_paket);
//        console.log(idx);
//        console.log(sstat);
//        console.log(astat);

        var thedata = xhrdata.data.filter(x => x[1] == id_paket)[0];  //kolom 18 (id paket) sama dengan id
        console.log(thedata);


        var waydata = {
            status_idx: idx,
            id_paket: id_paket,
////            id_flow: (thedata[3 + idx] ? thedata[3 + idx] : ''),
            nama_sub_komponen: (thedata[3] ? thedata[3] : ''),
            nmgiat: (thedata[4] ? thedata[4] : ''),
            nmoutput: (thedata[5] ? thedata[5] : ''),
            nmsoutput: (thedata[6] ? thedata[6] : ''),
            nmkmpnen: (thedata[7] ? thedata[7] : ''),
            kode_satker: (thedata[23] ? thedata[23] : ''),
            modeform: 'tambah',
            //nama_satker: (thedata[24] ? thedata[24] : ''),
//            ket_lokasi: (thedata[2] ? thedata[2] : ''),
            // kd_kegiatan: (thedata[18] ? thedata[18] : ''),
            //   kd_output: (thedata[19] ? thedata[19] : ''),
            //    kd_sub_output: (thedata[20] ? thedata[20] : ''),
//            aktivitas_nama: (thedata[6] ? thedata[6] : ''),
//            sub_aktivitas: (thedata[7] ? thedata[7] : ''),
            tahun_anggaran: (thedata[8] ? thedata[8] : ''),
//            jenis_kontrakNama: (thedata[9] ? thedata[9] : ''),
//            kewenangan: (thedata[10] ? thedata[10] : ''),
//            sumber_dana: (thedata[11] ? thedata[11] : ''),
            volume: (thedata[7] ? thedata[7] : ''),
            satuan: (thedata[6] ? thedata[6] : ''),
            jumlah: (thedata[8] ? thedata[8] : ''),
//            rpm: (thedata[14] ? thedata[14] : ''),
//            phln: (thedata[15] ? thedata[15] : ''),
//            sbsn: (thedata[16] ? thedata[16] : ''),
//            rmp: (thedata[17] ? thedata[17] : ''),
            eval1: (thedata[11] ? thedata[11] : ''),
            eval2: (thedata[13] ? thedata[13] : ''),
//            jenis_arahan: (thedata[26] ? thedata[26] : '')

//            status_idx: idx,
//            id_paket: id_paket,
//            id_flow: (thedata[3 + idx] ? thedata[3 + idx] : ''),
//            nama_paket: (thedata[1] ? thedata[1] : ''),
//            kode_satker: (thedata[7] ? thedata[7] : ''),
//            status_kewenangan: (thedata[12] ? thedata[12] : ''),
//            modeform: 'tambah',
            nmsatker_pengusul: (thedata[24] ? thedata[24] : ''),
//            nama_satker: (thedata[8] ? thedata[8] : ''),
//            keterangan: (thedata[17 + 2 * idx] ? thedata[17 + 2 * idx] : ''),
//            evaluasi: (thedata[20] ? thedata[20] : ''),
//            rc_ded: (thedata[14] ? thedata[14] : ''),
//            rc_fs: (thedata[13] ? thedata[13] : ''),
//            rc_lahan: (thedata[15] ? thedata[15] : ''),
//            rc_dokling: (thedata[16] ? thedata[16] : ''),
//            rkakl_volume: (thedata[21] ? thedata[21] : ''),
//            rkakl_biaya: (thedata[22] ? thedata[22] : ''),
//            kddisposisi: (thedata[23] ? thedata[23] : '0')


        }

//        console.log(waydata);

        way.set('formData', waydata);


        $('#modalTitle').text('Verifikasi');

        console.log("======ready to show form");
        console.log("role yang aktif saat ini: " + role);
        console.log("role alias yang aktif saat ini: " + roledesc);
        console.log("role yang harusnya melakukan approval: " + roleberhak);
        console.log('kode satker pengusul: ' + waydata.kode_satker);
        console.log('kode satker user saat ini: ' + user_satker);

        //verifikasi hanya bisa dilakukan oleh orang yang:
        //- rolenya sesuai dengan yang diminta
        //- role pusat, atau role yang satkernya sama dengan satker tempat usulan ini dibuat
        // (role = roleberhak) and (waydata.kode_satker = user_satker)
        // (role = roleberhak) and (user_satker in pemrog, adps, kpsj, konstruksi, pavement, structures, metrokobes, jbh, mejd, tanah, gml, lkj, kompetensi, sditjen, buk, ditbang, ditpreservasi, ditjembatan, ditjbh, pjj
        var rolePusat = ['pemrog', 'adps', 'kpsj', 'konstruksi', 'pavement', 'structures', 'metrokobes', 'jbh', 'mejd', 'tanah', 'gml', 'lkj', 'kompetensi', 'sditjen', 'buk', 'ditbang', 'ditpreservasi', 'ditjembatan', 'ditjbh', 'pjj'];

        console.log('child satker: ' + child_satker);
        var aChildSatker = child_satker.replace(/'/g, "").split(',');
        console.log(aChildSatker);

        if (role == roleberhak) {
            if ((waydata.kode_satker == user_satker) && (!isEmpty(user_satker))) {
                //boleh approve
                console.log('boleh aprove, role cocok, satker sama');
                $('#modal-edit').modal('show');
                $('.elem-eval_usulan1').show();
                $('.elem-eval_usulan2').hide();
            } else if (aChildSatker.indexOf(waydata.kode_satker) >= 0) {
                //jika user_satker yang sekarang login , punya child yang salah satunya adalah satker pengusul
                console.log('boleh aprove, role cocok, satker seinduk');
                $('#modal-edit').modal('show');
                $('.elem-eval_usulan1').show();
                $('.elem-eval_usulan2').hide();
            } else {
                if (rolePusat.indexOf(roledesc) >= 0) {
                    //boleh approve
                    console.log('boleh aprove, role cocok, oleh satker pusat');
                    $('#modal-edit').modal('show');
                } else {
                    console.log('gak boleh approve karena meskipun role sama, tapi beda satker');
                    alert('Verifikasi tidak bisa dilakukan (satker tidak sesuai!)');
                }
            }
        } else {
            console.log('gak boleh approve karena rolenya beda');
            alert('Verifikasi tidak bisa dilakukan (role tidak sesuai!)');
        }

    }
    function bindTagging_rj_rams(string_rams){
       var arr_rj_rams =string_rams.split(",");
       for(var i=0; i<= arr_rj_rams.length-2; i++){
           alert(arr_rj_rams[i]);
       }
    }
    //*
    function dtTagging(strTag) {
        console.log('strTag dttagging'+strTag);
        
        var str = strTag.split("::");
        var id = str[1];
        var label = str[0] + '|' + str[1];
        var rujukan = str[0];
        var elt = $('.div-tags');
        elt.tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                        return 'label label-info';
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROG':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to set id of tag
            itemText: 'text' // this will be used to set text of tag
        });

        elt.tagsinput('add', {value: id, text: label, rujukan: rujukan});
    }
    //*/
    
    function dtTaggingEdit(strTag) {
        var str = strTag.split("::");
        var id = str[1];
        var label = str[0] + '|' + str[1];
        var rujukan = str[0];
        var elt = $('.div-tags');
        elt.tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROGRAM':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to set id of tag
            itemText: 'text' // this will be used to set text of tag
        });

        elt.tagsinput('add', {value: id, text: label, rujukan: rujukan});
    }

    function handleKegiatan(el)
    {
        refreshCombobox('kd_output', 30, 'kdgiat', el.value);
        $('#kd_sub_output').empty();
        $('#kd_sub_output').append(new Option("--Pilih--", -1));
        $('#kd_komponen').empty();
        $('#kd_komponen').append(new Option("--Pilih--", -1));

    }

    function handleJnsBelanja(el)
    {
        refreshCombobox('kdakun', 37, 'kdgbkpk', el.value);

        refreshCombobox('ykdakun', 37, 'kdgbkpk', el.value);
    }

    function handleSoutput2(el) {
        var kdgiat = $('#kd_kegiatan-sel').val();
        var kdoutput = $('#kd_output-sel').val();
        var kdsoutput = el.value;
        //var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput + "::" + $('#thang-sel').val();
        var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);
    }

    function handleSoutput(el) {
        var kdgiat = $('#kd_kegiatan').val();
        var kdoutput = $('#kd_output').val();
        var kdsoutput = el.value;
        //var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput + "::" + $('#thang').val();
        var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
        //refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput::thang', valSelect);
        //alert(valSelect);
        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);



        var ykdgiat = $('#kd_kegiatan').val();
        var ykdoutput = $('#kd_output').val();
        var ykdsoutput = el.value;
        //var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput + "::" + $('#thang').val();
        var yvalSelect = ykdgiat + "::" + ykdoutput + "::" + ykdsoutput;
        //refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput::thang', valSelect);
        //alert(valSelect);
        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', yvalSelect);


    }

//    function handleKomponen(el) {
//        var kdkmpnen = el.value;
//        var thang = yearnow;
//
//        //if($('#kd_kegiatan-sel').val() === '2409'){
//        //setInputVal3('hargasat', 50, 'kdkmpnen::thang', kdkmpnen + '::' + thang);
////        }
//
//
//
//    }

    function handleOutput(el) {

        var kdgiat = $('#kd_kegiatan').val();
        var kdoutput = el.value;

        //var valSelect = kdgiat + "::" + kdoutput + "::" + $('#thang').val();
        var valSelect = kdgiat + "::" + kdoutput;
        // refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput::thang', valSelect);
        refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect);


//        var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
//        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);

        var selectedText = el.options[el.selectedIndex].text;

        //alert(selectedText);


        var x = function () {
            var defer = $.Deferred();

//            var vreff = kdgiat + "::" + kdoutput;
            //console.log('a() called');
//            setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);



            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);

            return defer;
        };

        var y = function () {
            var defer = $.Deferred();

            //alert();
            //var sat = $('#satuan').val();


            if (kdoutput == '001' || kdoutput == '002' || kdoutput == '003' || kdoutput == '004' || kdoutput == '005' || kdoutput == '006' || kdoutput == '007' || kdoutput == '008' || kdoutput == '009' || kdoutput == '010' || kdoutput == '011' || kdoutput == '012' || kdoutput == '013')
            {
                $('.divNonFisik').hide();
                $('#detail').prop('disabled', true);

                $('.divRuas').show();
                $("#xid_ruas").prop('disabled', false);
                $("#id_ruas").prop('disabled', false);
                $("#sta_awal").prop('disabled', false);
                $("#sta_akhir").prop('disabled', false);
                $("#treatment").prop('disabled', false);

                $("#xid_jembatan").prop('disabled', false);
                $("#longitude").prop('disabled', false);
                $("#latitude").prop('disabled', false);

            } else
            {
                $('.divNonFisik').show();
                $('#detail').prop('disabled', false);

                $('.divRuas').hide();
                $("#xid_ruas").prop('disabled', true);
                $("#sta_awal").prop('disabled', true);
                $("#sta_akhir").prop('disabled', true);
                $("#treatment").prop('disabled', true);

                $("#xid_jembatan").prop('disabled', true);
                $("#longitude").prop('disabled', true);
                $("#latitude").prop('disabled', true);


            }

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);

            return defer;
        };

        x().then(y);



//        alert(sat);


    }

    function handleOutput2(el) {

        var kdgiat = $('#kd_kegiatan-sel').val();
        var kdoutput = el.value;
        //var valSelect = kdgiat + "::" + kdoutput + "::" + $('#thang-sel').val();
        var valSelect = kdgiat + "::" + kdoutput;
        refreshCombobox4('kd_sub_output-sel', 44, 'kdgiat::kdoutput', valSelect);


//        var valSelect = kdgiat + "::" + kdoutput + "::" + kdsoutput;
//        refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);

        var selectedText = el.options[el.selectedIndex].text;

        //alert(selectedText);


        var x = function () {
            var defer = $.Deferred();

//            var vreff = kdgiat + "::" + kdoutput;
//            //console.log('a() called');
//            setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);



            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);

            return defer;
        };

        var y = function () {
            var defer = $.Deferred();

            //alert();
            var sat = $('#satuan').val();

            if (sat == 'km' || sat == 'm')
            {
                $('.divNonFisik').hide();
                $('#detail').prop('disabled', true);

                $('.divRuas').show();
                $("#xid_ruas").prop('disabled', false);
                $("#sta_awal").prop('disabled', false);
                $("#sta_akhir").prop('disabled', false);
                $("#treatment").prop('disabled', false);

                $("#xid_jembatan").prop('disabled', false);
                $("#longitude").prop('disabled', false);
                $("#latitude").prop('disabled', false);

            } else
            {
                $('.divNonFisik').show();
                $('#detail').prop('disabled', false);

                $('.divRuas').hide();
                $("#xid_ruas").prop('disabled', true);
                $("#sta_awal").prop('disabled', true);
                $("#sta_akhir").prop('disabled', true);
                $("#treatment").prop('disabled', true);

                $("#xid_jembatan").prop('disabled', true);
                $("#longitude").prop('disabled', true);
                $("#latitude").prop('disabled', true);


            }

            setTimeout(function () {
                defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
            }, 1000);

            return defer;
        };

        x().then(y);



//        alert(sat);


    }

    function handleRuas(el)
    {
        refreshCombobox2('xid_jembatan', 32, 'linkid', el.value);
        //modal tambah
        refreshComboboxSTASatker('sta_awal',el.value);
        refreshComboboxSTASatker('sta_akhir',el.value);
        //refreshCombobox3('sta_awal', 35, 'kode_ruas', el.value);
        //refreshCombobox3('sta_akhir', 35, 'kode_ruas', el.value);

        //modal tambah detail
        refreshCombobox2('id_jembatan', 32, 'linkid', el.value);
        refreshComboboxSTASatker('sta_awal-sel',el.value);
        refreshComboboxSTASatker('sta_akhir-sel',el.value);
        
//       refreshCombobox3('wsta_awal', 35, 'kode_ruas', el.value);
//       refreshCombobox3('wsta_akhir', 35, 'kode_ruas', el.value);
       console.log("-----STA AWAl----")
       console.log(el.value);
       refreshComboboxSTASatker('wsta_awal',el.value);
       refreshComboboxSTASatker('wsta_akhir',el.value);
        

        //modal edit detail

        //alert("wsta awal - akhir")
        
        
        //modal edit detail
        refreshCombobox2('wid_jembatan', 32, 'linkid', el.value);
        refreshComboboxSTASatker('wsta_awal-sel',el.value);
        refreshComboboxSTASatker('wsta_akhir-sel',el.value);

        if ($("#xid_ruas").val() != "") {
            $("#detail").val($("#id_ruas").val());
        }
        if ($("#xid_jembatan").val() != "") {
            $("#xdetail").val($("#id_ruas").val());
        }
        
        //modal tambah
        var tirmsv3_wp = $('#tirmsv3_wp').DataTable();
        tirmsv3_wp.ajax.reload();
        
        //modal tambah detail
        var xtirmsv3_wp = $('#xtirmsv3_wp').DataTable();
        xtirmsv3_wp.ajax.reload();
        
        //modal edit detail
        var wtirmsv3_wp = $('#wtirmsv3_wp').DataTable();
        wtirmsv3_wp.ajax.reload();
      
    }

    function handleJembatan(el)
    {
        setInputVal2('longitude', 40, 'id_jembatan', el.value);
        setInputVal2('latitude', 41, 'id_jembatan', el.value);
    }

    function dtEditRowDetail(id) {

        $(".decformat2").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

            var decform = $('.decformat2').val().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");



            $('#hargasat').val(decform);

            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();

            var hasil = volume * hargasat;

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val(hasil);



        });

        $(".decformat").change(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

        });


        $(".decformat2").select(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });
        });


        $('#volume').on("focus", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            //$('#rm').val($(".decformat").val());
            $('#rm').val(hasil);
        });

        $('#volume').on("keyup", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val($(".decformat").val());
            //$('#rm').val(hasil);
        });


        $('#thang-sel').prop('disabled', true);
        // $('#jns_giat').prop('disabled', true);
        $('#kd_kegiatan-sel').prop('disabled', true);
        // $('#kd_output-sel').prop('disabled', true);
        // $('#kd_sub_output-sel').prop('disabled', true);
        //  $('#kd_komponen-sel').prop('disabled', true);
        //  $('#kd_sub_komponen-sel').prop('disabled', true);
        //  $('#nama_sub_komponen-sel').prop('disabled', true);

        $('.div-tags').tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROGRAM':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to InputVal id of tag
            itemText: 'text' // this will be used to set text of tag
        });


        //$('#frm-detail-edit')[0].reset();

        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // c


        $('#modeform').val('edit_detail');
        $('#modal-detail').modal('show'); // show bootstrap modal when complete loaded
        $('.tbhItem').text('Edit Detail Paket');


        $('.rujuk').hide();// Set title to Bootstrap modal title



        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/ajax_edit/detail/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {
                // console.log(data.kd_komponen);
                $('#id_paket').val(data.id_paket);
                $('#id_usulan').val(id);
                $('#thang-sel').val(data.thang);
                $('#id_ppk-sel').val(data.id_ppk);
                $('#kdkppn-sel').val(data.kdkppn);
                $('[name="thang-sel"]').val(data.thang);
                $('[name="id_ppk-sel"]').val(data.id_ppk);
                $('[name="kdkppn-sel"]').val(data.kdkppn);
                $('#kd_sub_komponen-sel').val(data.kd_sub_komponen);
                $('#nama_sub_komponen-sel').val(data.nama_sub_komponen);
                $('[name="kd_sub_komponen-sel"]').val(data.kd_sub_komponen);
                $('[name="nama_sub_komponen-sel"]').val(data.nama_sub_komponen);
                $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);
                console.log(data);
                console.log("-----");
                // $('[name="kdgbkpk"]').val(data.kdgbkpk);
                var jmlmath = Math.round(data.jumlah);
                var rjml = jmlmath + " ";

                var hsmath = Math.round(data.hargasat);
                var rhs = hsmath + " ";


                $('[name="volume"]').val(data.volume);
                $('[name="satuan"]').val(data.satuan);
                $('[name="hargasat"]').val(parseInt(rhs));
                $('[name="totalpagu"]').val(parseInt(rjml));
                $('[name="treatment"]').val(data.treatment);
                $('[name="xid_ruas"]').val(data.no_ruas);
                $('[name="latitude"]').val(data.latitude);
                $('[name="longitude"]').val(data.longitude);
                $('[name="rm"]').val(data.rpm);


                updateCombobox('kd_kegiatan-sel', 5, data.kd_kegiatan);

                if (role == 7) {
                    $('#jns_giat').val('NF');
                    $('.rujuk').hide();
                    $('.divNonFisik').show();
                    $('#detail').prop('disabled', false);
                    $('.divRuas').hide();
                    $("#xid_ruas").prop('disabled', true);
                    $("#sta_awal").prop('disabled', true);
                    $("#sta_akhir").prop('disabled', true);
                    $("#treatment").prop('disabled', true);
                    $("#xid_jembatan").prop('disabled', true);
                    $("#longitude").prop('disabled', true);
                    $("#latitude").prop('disabled', true);
                } else if (role == 3) {
                    $('#jns_giat').val('F');
                    $('.rujuk').show();
                    $('.divNonFisik').hide();
                    $('#detail').prop('disabled', true);
                    $('.divRuas').show();
                    $("#xid_ruas").prop('disabled', false);
                    $("#sta_awal").prop('disabled', false);
                    $("#sta_akhir").prop('disabled', false);
                    $("#treatment").prop('disabled', false);
                    $("#xid_jembatan").prop('disabled', false);
                    $("#longitude").prop('disabled', false);
                    $("#latitude").prop('disabled', false);

                }


                if (data.kd_kegiatan == '2409')
                {
                    $('#jns_giat').val('F');
                    $('.rujuk').show();
                    $('.divNonFisik').hide();
                    $('#detail').prop('disabled', true);
                    $('.divRuas').show();
                    $("#xid_ruas").prop('disabled', false);
                    $("#sta_awal").prop('disabled', false);
                    $("#sta_akhir").prop('disabled', false);
                    $("#treatment").prop('disabled', false);
                    $("#xid_jembatan").prop('disabled', false);
                    $("#longitude").prop('disabled', false);
                    $("#latitude").prop('disabled', false);
                } else {
                    $('#jns_giat').val('NF');
                    $('.rujuk').hide();
                    $('.divNonFisik').show();
                    $('#detail').prop('disabled', false);
                    $('.divRuas').hide();
                    $("#xid_ruas").prop('disabled', true);
                    $("#sta_awal").prop('disabled', true);
                    $("#sta_akhir").prop('disabled', true);
                    $("#treatment").prop('disabled', true);
                    $("#xid_jembatan").prop('disabled', true);
                    $("#longitude").prop('disabled', true);
                    $("#latitude").prop('disabled', true);
                }







                var j = function () {
                    var defer = $.Deferred();
                    updateCombobox('kdgbkpk', 36, data.kdgbkpk);

                    refreshComboboxOutput('kd_output-sel', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);
                    refreshComboboxOutput('kdakun', 37, 'kdgbkpk', data.kdgbkpk, data.kdakun);






                    //console.log('a() called');
//                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
                    //initCombobox('kd_kegiatan-sel', 5);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };
                var a = function () {
                    var defer = $.Deferred();

                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
                    refreshCombobox4('kd_sub_output-sel', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);

//                      var tirmsv3_wp = $('#tirmsv3_wp').DataTable();
//                    var trams_wp = $('#trams_wp').DataTable();
//                    tirmsv3_wp.ajax.reload();
//                    trams_wp.ajax.reload();
//                    
                    //console.log('a() called');
//                      updateCombobox('kd_output-sel', 30, data.kd_output);
//                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
//                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };
                var b = function () {
                    var defer = $.Deferred();
                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
                    refreshCombobox4('kd_komponen-sel', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);

                    //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };
                var c = function () {
                    var defer = $.Deferred();
                    //console.log('a() called');
                    var tlist_detail = $('#tlist_detail').DataTable();
                    tlist_detail.ajax.reload();


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };

                var g = function () {
                    var defer = $.Deferred();

                    // alert(data.id_jembatan);
//                    alert(data.sta_akhir);
                    //refreshComboboxOutput('id_jembatan', 32, 'linkid', ij,data.id_jembatan);
                    refreshComboboxJBT('xid_jembatan', 32, 'linkid', data.no_ruas, data.id_jembatan);

                    refreshComboboxSTA('sta_awal-sel', 35, 'kode_ruas', data.no_ruas, data.sta_awal);
                    refreshComboboxSTA('sta_akhir-sel', 35, 'kode_ruas', data.no_ruas, data.sta_akhir);

                    //$('[name="id_jembatan"]').val(data.longitude);




                    $(".decformat").val(rjml.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

                    $(".decformat2").val(rhs.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));
//                    var rm = data.rpm;
//                    var pln = data.phln;
//                    var rmp = data.rmp;
//                    var sbsn = data.sbsn;
//                    
//                    addFormGroup();
//                    selectFormGroup();





                    //console.log('a() called');

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);
                    return defer;
                };
                //$("#id_jembatan").val(data.id_jembatan);
//                        var i = function () {
//                        var defer = $.Deferred();
//                                //console.log('a() called');
//                                //alert()
//                               
//
//                                    setTimeout(function () {
//                                    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                                    }, 1000);
//                                return defer;
//                        };




                var pbar = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $(".overlay").show();

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;
                };
                var da = get_prov();

                $("#prov").append("<option value=" + da.kd_prov_irmsv3 + ">" + da.nama_prov + "</option>")
                // alert(da.kd_prov_irmsv3);
                refreshComboboxOutput('kabkot', 49, 'kd_prov_irmsv3', da.kd_prov_irmsv3, data.kdkabkota);
                //  refreshCombobox('kabkot', 49, 'kd_prov_irmsv3', da.kd_prov_irmsv3);
                // $("#prov").val(data.kdlokasi);
                //  $("#kabkot").val(data.kdkabkota);

                $.when(
                        pbar().then(j).then(a).then(b).then(g).then(c)
                        // Deferred object (probably Ajax request),
                        ).then(function () {
                    $(".overlay").hide();// All have been resolved (or rejected), do your thing
//                    $('#modal-load').modal('toggle');
                });
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error get data from ajax');
            }
        });
    }
    
    function dtEditRowPaket(id) {
        var id_paket = id;
        //alert(id_paket);
        var data_selected = xhrdata.data.filter(x => x[1] == id_paket)[0];
        //alert(data_selected[2]);
        refreshComboboxOutput('zthang', 21, 'thang', data_selected[2], data_selected[2]);

        updateComboboxAndSelected('zkd_isu', 3, data_selected[27]);

        // updateComboboxAndSelected('zwps_kode', 3,data_selected[27]);

        refreshComboboxOutput('zkd_kegiatan', 5, 'kdgiat', data_selected[10], data_selected[10]);

        setTimeout(set_kegiatan, 8000);//set kegiatan berdasarkan kode

        $("#zkd_sub_komponen").val(data_selected[25]);

        $("#znama_sub_komponen").val(data_selected[26]);
        $("#zid_paket").val(id);


        var id_rprov = $("#id_rprov").val();
        refreshComboboxOutput('zprov', 60, 'kd_prov', id_rprov, id_rprov);

        refreshComboboxOutput('zkd_output', 30, 'kdgiat', data_selected[10], data_selected[19]);

        refreshComboboxOutput('zkd_sub_output', 44, 'kdoutput', data_selected[19], data_selected[20]);

        refreshComboboxOutput('zkd_komponen', 18, 'kdsoutput', data_selected[20], data_selected[24]);

        updateComboboxAndSelected('zjnskontrak', 23, data_selected[28]);

       // updateComboboxAndSelected('zkdkppn', 9, data_selected[29]);
        //refreshComboboxKPPN('zkdkppn', 46, 'kdsatker', kd_satker);
        
        refreshComboboxOutput('zkdkppn',46,'kdsatker', kd_satker, data_selected[29]);
        setTimeout(set_kppn_jakarta, 3000);
        
       // updateComboboxAndSelected('zid_ppk', 11, data_selected[30]);
       
        refreshComboboxOutput('zid_ppk',47,'kdsatker', kd_satker, data_selected[30]);
        //alert(data_selected[30]);

        updateComboboxAndSelected('zwps_kode', 57, data_selected[32]);

        updateComboboxAndSelected('zkdgbkpk', 36, data_selected[37]);

        //updateComboboxAndSelected('zid_ppk',11,data_selected[30]);

        //refreshComboboxOutput('ykdgbkpk', 36, 'kdgbkpk',data_selected[20],data_selected[20]);
        //initCombobox("zkdgbkpk",36);

        initCombobox("zkdakun", 37);

        $("#zrc_ded_status").append("<option selected value=" + data_selected[33] + ">" + data_selected[33] + "</option>");
        $("#zrc_fs_status").append("<option selected " + data_selected[34] + ">" + data_selected[34] + "</option>");
        $("#zrc_lahan_status").append("<option selected " + data_selected[35] + ">" + data_selected[35] + "</option>");
        $("#zrc_doklin_status").append("<option selected " + data_selected[36] + ">" + data_selected[36] + "</option>");

        updateComboboxAndSelected('zkws_kode', 57, data_selected[31]);
        // alert( $('#yjns_giat option:eq(1)').val());
        //alert($("#ykd_kegiatan-sel").val());

        setTimeout(set_kegiatan, 8000);//


        $("#modal-edit-paket").modal("show");

    }
    
    function set_kppn_jakarta(){
        var zhtml_option = [
                "<option value=" + "555" + " >",
                "VI KHUSUS JAKARTA",
                "</option>",
            ].join("\n");
        //alert(zhtml_option);
        $("#zkdkppn").append(zhtml_option);

    }
    
    function dtEditRowPaket2(id) {

//        $('#frm-paket')[0].reset();

        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // c


        $('#modeform').val('edit_paket');
        $('#modal-tambah').modal('show'); // show bootstrap modal when complete loaded
        $('.tbhPaketDetail').text('Edit Paket');// Set title to Bootstrap modal title
//        $('div#block-ct + div.modal-footer').remove();

        $('#blockItmPaket').hide();
//        $('[name="thang"]').prop('disabled', false);
//        $('[name="kd_kegiatan"]').prop('disabled', false);
//        $('[name="kd_output"]').prop('disabled', false);
//        $('[name="kd_sub_output"]').prop('disabled', false);
//        $('[name="kd_komponen"]').prop('disabled', false);
//        $('[name="kd_sub_komponen"]').prop('disabled', false);
//        $('[name="nama_sub_komponen"]').prop('disabled', false);
//        $('[name="id_ppk"]').prop('disabled', false);
//        $('[name="kdkppn"]').prop('disabled', false);


//        $("<div class='modal-footer'><button class='btn btn-sm btn-default' type='button' data-bs-dismiss='modal'>Tutup</button><button class='btn btn-sm btn-success' type='button' onclick='simpanFormPaket()'><i class='fa fa-check'></i>Simpan</button></div> ").insertAfter("#block-ct");




        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/ajax_edit/paket/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {
                var x = $("#wps_kode").val();
                // alert(x);
                $.ajax({
                    //type: "GET",
                    url: "<?php echo base_url('pagu_indikatif/get_kws/') ?>" + data.wps_kode,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (dat) {
                        //console.log("--ajax data--");
                        console.log(data)

                        x = dat;
                        lookkws(x)

                    },
                    failure: function (errMsg) {
                        alert(errMsg);
                    }
                });
                $.ajax({
                    //type: "GET",
                    url: "<?php echo base_url('pagu_indikatif/get_sub/') ?>" + data.kws_kode,
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                        //console.log("--ajax data--");
                        console.log(data)

                        x = data;
                        looksub(x)

                    },
                    failure: function (errMsg) {
                        alert(errMsg);
                    }
                });

                console.log(data);
                console.log("----------");
                //alert(data.jnskontrak);

                $('[name="id_paket"]').val(data.id_paket);
                $('[name="thang"]').val(data.thang);
                updateCombobox('kd_isu', 3, data.kd_isu);
                updateCombobox('kd_kegiatan', 5, data.kd_kegiatan);
                //updateCombobox('jnskontrak', 23, data.jnskontrak);

                // $('[name="kd_output"]').val(data.kd_output);
                var j = function () {
                    var defer = $.Deferred();

                    refreshComboboxOutput('kd_output', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);

                    //console.log('a() called');
                    //refreshCombobox('kd_kegiatan', 5, 'thang', data.thang);
                    //initCombobox('kd_kegiatan', 5);


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;
                };

                var a = function () {
                    var defer = $.Deferred();

                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
                    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);

                    //console.log('a() called');
//                    $('[name="kd_kegiatan"]').val(data.kd_kegiatan);
//                    refreshCombobox('kd_output', 30, 'kdgiat', data.kd_kegiatan);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;
                };

                var b = function () {
                    var defer = $.Deferred();

                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
                    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);

                    //console.log('a() called');
                    //$('[name="kd_output"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;
                };



//                var c = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    updateCombobox('jnskontrak', 23, data.jnskontrak);
//                    //refreshCombobox('jnskontrak', 23, 'thang', data.jnskontrak);
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 500);
//
//                    return defer;
//                };

//                var d = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    //$('[name="kd_komponen"]').val(data.kd_komponen);
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 500);
//
//                    return defer;
//                };

//                var e = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    var tlist_detail = $('#tlist_detail').DataTable();
//                    tlist_detail.ajax.reload();
//
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };

//                var f = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
//                    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect);
//
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };
//
//                var g = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    $('[name="kd_sub_output"]').val(data.kd_sub_output);
//
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };

                $('[name="jnskontrak"]').val(data.jnskontrak);
                $('[name="id_ppk"]').val(data.id_ppk);
                $('[name="kdkppn"]').val(data.kdkppn);
                $('[name="kd_isu"]').val(data.kd_isu);
                $('[name="wps_kode"]').val(data.wps_kode);
                $('[name="kws_kode"]').val(data.kws_kode);
                $('[name="subkw"]').val(data.subkawasan_nama);
                $('[name="rc_ded_status"]').val(data.rc_ded_status);
                $('[name="rc_fs_status"]').val(data.rc_fs_status);
                $('[name="rc_lahan_status"]').val(data.rc_lahan_status);
                $('[name="rc_doklin_status"]').val(data.rc_doklin_status);



//                var i = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    //alert();
//                    var sat = $('#satuan').val();
//
//                    if (sat == 'km' || sat == 'm')
//                    {
//                        $('.divNonFisik').hide();
//                        $('#detail').prop('disabled', true);
//
//                        $('.divRuas').show();
//                        $("#id_ruas").prop('disabled', false);
//                        $("#sta_awal").prop('disabled', false);
//                        $("#sta_akhir").prop('disabled', false);
//                        $("#treatment").prop('disabled', false);
//
//                        $("#id_jembatan").prop('disabled', false);
//                        $("#longitude").prop('disabled', false);
//                        $("#latitude").prop('disabled', false);
//
//                    } else
//                    {
//                        $('.divNonFisik').show();
//                        $('#detail').prop('disabled', false);
//
//                        $('.divRuas').hide();
//                        $("#id_ruas").prop('disabled', true);
//                        $("#sta_awal").prop('disabled', true);
//                        $("#sta_akhir").prop('disabled', true);
//                        $("#treatment").prop('disabled', true);
//
//                        $("#id_jembatan").prop('disabled', true);
//                        $("#longitude").prop('disabled', true);
//                        $("#latitude").prop('disabled', true);
//
//                    }
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };


                var pbar = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $(".overlay").show();


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 500);

                    return defer;


                };



                $.when(
                        //pbar().then(j).then(a).then(b).then(f).then(g).then(c).then(d).then(e)
                        pbar().then(j).then(a).then(b)
                        // Deferred object (probably Ajax request),

                        // Deferred object (probably Ajax request),

                        // Deferred object (probably Ajax request)

                        ).then(function () {


                    $(".overlay").hide();// All have been resolved (or rejected), do your thing
//                    alert('Selesai Menyiapkan Data');

                });





                $('[name="kd_sub_komponen"]').val(data.kd_sub_komponen);
                $('[name="nama_sub_komponen"]').val(data.nama_sub_komponen);

                //$('#archive-preview div').detach();
//                $('#archive-preview').show(); // show photo preview modal
//                //
//
//                if (data.archive)
//                {
//
//                    $('#label-archive').text('Ubah File'); // label photo upload
//                    $('#archive-preview div').html('<input type="checkbox" id="remove_archive" name="remove_archive" value="' + data.archive + '"/>&nbsp;Hapus file saat save<br><br>'); // remove photo
//
//                } else
//                {
//                    $('#label-archive').text('Unggah File'); // label photo upload
//                    $('#archive-preview div').text('(File Tidak Tersedia)');
//                }

            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error get data from ajax');
            }
        });
    }

    function dtEditRow(id) {

        //alert(id);

        $('#frm-detail')[0].reset();

        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // c


        //$('#modeform').val('edit');
        $('#modal-tambah').modal('show'); // show bootstrap modal when complete loaded
        $('.tbhPaketDetail').text('Tambah Detail Paket');// Set title to Bootstrap modal title
        $('#blockItmPaket').show();

//        $('[name="thang"]').prop('readonly', true);
//        $('[name="kd_kegiatan"]').prop('readonly', true);
//        $('[name="kd_output"]').prop('readonly', true);
//        $('[name="kd_sub_output"]').prop('readonly', true);
//        $('[name="kd_komponen"]').prop('readonly', true);
//        $('[name="kd_sub_komponen"]').prop('readonly', true);
//        $('[name="nama_sub_komponen"]').prop('readonly', true);
//        $('[name="id_ppk"]').prop('readonly', true);
//        $('[name="kdkppn"]').prop('readonly', true);


        $('div#block-ct + div.modal-footer').remove();

        //Ajax Load data from ajax
        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/ajax_edit/paket/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {

//                alert(data.kd_kegiatan);

                $('[name="id_paket"]').val(data.id_paket);
                $('[name="thang"]').val(data.thang);

                // $('[name="kd_output"]').val(data.kd_output);
                var j = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    initCombobox('kd_kegiatan', 5);



                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };

                var a = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $('[name="kd_kegiatan"]').val(data.kd_kegiatan);
                    refreshCombobox('kd_output', 30, 'kdgiat', data.kd_kegiatan);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };

                var b = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $('[name="kd_output"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };



                var c = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
                    refreshCombobox4('kd_komponen', 18, 'kdgiat::kdoutput::kdsoutput', valSelect);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };

                var d = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $('[name="kd_komponen"]').val(data.kd_komponen);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 2000);

                    return defer;
                };

                var e = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    var tlist_detail = $('#tlist_detail').DataTable();
                    tlist_detail.ajax.reload();


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };

                var f = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
                    refreshCombobox4('kd_sub_output', 44, 'kdgiat::kdoutput', valSelect);


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };

                var g = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $('[name="kd_sub_output"]').val(data.kd_sub_output);


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };

                var i = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    //alert();
                    var sat = $('#satuan').val();

                    if (sat == 'km' || sat == 'm')
                    {
                        $('.divNonFisik').hide();
                        $('#detail').prop('disabled', true);

                        $('.divRuas').show();
                        $("#xid_ruas").prop('disabled', false);
                        $("#sta_awal").prop('disabled', false);
                        $("#sta_akhir").prop('disabled', false);
                        $("#treatment").prop('disabled', false);

                        $("#xid_jembatan").prop('disabled', false);
                        $("#longitude").prop('disabled', false);
                        $("#latitude").prop('disabled', false);

                    } else
                    {
                        $('.divNonFisik').show();
                        $('#detail').prop('disabled', false);

                        $('.divRuas').hide();
                        $("#xid_ruas").prop('disabled', true);
                        $("#sta_awal").prop('disabled', true);
                        $("#sta_akhir").prop('disabled', true);
                        $("#treatment").prop('disabled', true);

                        $("#xid_jembatan").prop('disabled', true);
                        $("#longitude").prop('disabled', true);
                        $("#latitude").prop('disabled', true);

                    }

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };

                j().then(a).then(b).then(f).then(g).then(c).then(d).then(e).then(i);



                $('[name="kd_sub_komponen"]').val(data.kd_sub_komponen);
                $('[name="nama_sub_komponen"]').val(data.nama_sub_komponen);

                //$('#archive-preview div').detach();
                $('#archive-preview').show(); // show photo preview modal
                //

                if (data.archive)
                {

                    $('#label-archive').text('Ubah File'); // label photo upload
                    $('#archive-preview div').html('<input type="checkbox" id="remove_archive" name="remove_archive" value="' + data.archive + '"/>&nbsp;Hapus file saat save<br><br>'); // remove photo

                } else
                {
                    $('#label-archive').text('Unggah File'); // label photo upload
                    $('#archive-preview div').text('(File Tidak Tersedia)');
                }

            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error get data from ajax');
            }
        });
    }

    function dtHistory($db, id) {
        //console.log(id);
        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/history/" + id + "/" + $db,
            type: "GET",
//            data: {id:id},
            dataType: "html",
            success: function (data)
            {
                $('#content-history').html(data);
                $('#modal-history').modal('show');
                $('.block-title').text('History Paket');// Set title to Bootstrap modal title
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error get data from ajax');
            }

        });
    }

    function dtTambahRowPaket() {
    
        var kd_kegiatan = $("#kd_kegiatan").val();
        initCombobox('thang', 28);
        initCombobox('kd_isu', 3);       
        bind_wps_by_province();
        initCombobox('kd_kegiatan', 5);
        var html_jns_kegiatan=["<option value='#'>",
                                    "Pilih",
                          "</option>",
                          "<option value='F'>",
                                    "Fisik",
                          "</option>",
                          "<option value='NF'>",
                                    "Non Fisik",
                          "</option>",
                         ].join("\n");
        $("#jns_giat").empty();
        $("#jns_giat").append(html_jns_kegiatan);
        $("#kd_output").empty();
        $("#kws_kode").empty();
        $("#subkw").empty();
        //initCombobox('kd_output',30);
        $("#kd_sub_output").empty();
        $("#kd_komponen").empty();
        
        initCombobox('jnskontrak', 23);
        refreshCombobox('id_ppk', 47, 'kdsatker', kd_satker);      
        refreshComboboxKPPN('kdkppn', 46, 'kdsatker', kd_satker);
        
        $(".multiple-form-group").not(':first').remove();
        //$(".input-group-btn").empty();
        $(".input-group-btn").children(".btn-remove").text("+");
        $(".input-group-btn").children(".btn-remove").addClass('btn-success btn-add');
        $(".input-group-btn").children(".btn-add").removeClass("btn-remove" );
        $(".input-group-btn").children(".btn-add").removeClass("btn-danger" );
        $("#xrm").val("");
        //mengosongkan nilai tagging sebelumnya
        obj_rj_rams={};
        obj_rj_irms  ={};
        obj_rj_renstra ={};
        obj_rj_eprogram={};
        obj_rj_dpr={};
        obj_rj_pemda={};
        obj_rj_sipro={};
        //btn btn-danger btn-remove
        var html_option = [
                "<option value=" + "#" + " >",
                    "--Pilih--",
                "</option>",
                "<option value=" + "siap" + " >",
                    "Siap",
                "</option>",
                "<option value=" + "tidak_siap" + " >",
                    "Tidak Siap",
                "</option>",
            ].join("\n");
        $("#rc_ded_status").empty();    
        $("#rc_ded_status").append(html_option);
        
        $("#rc_fs_status").empty();    
        $("#rc_fs_status").append(html_option);
        
        $("#rc_lahan_status").empty();    
        $("#rc_lahan_status").append(html_option);
        
        $("#rc_doklin_status").empty();    
        $("#rc_doklin_status").append(html_option);
        
        initCombobox('kdgbkpk', 36);
        
        $("#kdakun").empty();
        var id_rprov=$("#id_rprov").val();
        //alert(id_rprov)'
        
         refreshCombobox('kabkot', 61, 'kd_prov', id_rprov);
//        refreshCombobox('xid_ruas', 33, 'kode_satker', kd_satker);
        
        //initComboboxRuasSatker('id_ruas');
        initComboboxRuasSatker('xid_ruas');
        
       
        
        $("#modal-tambah").find('input:text').val('');  
        //alert(role);
        if (role == 7) {
            $('#jns_giat').val('NF');
            $('.rujuk').hide();
            $('.divNonFisik').show();
            $('#detail').prop('disabled', false);
            $('.divRuas').hide();
            $("#xid_ruas").prop('disabled', true);
            $("#sta_awal").prop('disabled', true);
            $("#sta_akhir").prop('disabled', true);
            $("#treatment").prop('disabled', true);
            $("#xid_jembatan").prop('disabled', true);
            $("#longitude").prop('disabled', true);
            $("#latitude").prop('disabled', true);
        } else if (role == 3) {
            $('#jns_giat').val('F');
            $('.rujuk').show();
            $('.divNonFisik').css("display", "none");
            $('#detail').prop('disabled', true);
            $('.divRuas').css("display", "");
            $("#xid_ruas").prop('disabled', false);
            $("#sta_awal").prop('disabled', false);
            $("#sta_akhir").prop('disabled', false);
            $("#treatment").prop('disabled', false);
            $("#xid_jembatan").prop('disabled', false);
            $("#longitude").prop('disabled', false);
            $("#latitude").prop('disabled', false);

        }

        //alert(kd_kegiatan);

//                if (kd_kegiatan == '2409')
//                {
//                    $('#jns_giat').val('F');
//                    $('.rujuk').show();
//                    $('.divNonFisik').hide();
//                    $('#detail').prop('disabled', true);
//                    $('.divRuas').show();
//                    $("#id_ruas").prop('disabled', false);
//                    $("#sta_awal").prop('disabled', false);
//                    $("#sta_akhir").prop('disabled', false);
//                    $("#treatment").prop('disabled', false);
//                    $("#id_jembatan").prop('disabled', false);
//                    $("#longitude").prop('disabled', false);
//                    $("#latitude").prop('disabled', false);
//                } else {
//                    $('#jns_giat').val('NF');
//                    $('.rujuk').hide();
//                    $('.divNonFisik').show();
//                    $('#detail').prop('disabled', false);
//                    $('.divRuas').hide();
//                    $("#id_ruas").prop('disabled', true);
//                    $("#sta_awal").prop('disabled', true);
//                    $("#sta_akhir").prop('disabled', true);
//                    $("#treatment").prop('disabled', true);
//                    $("#id_jembatan").prop('disabled', true);
//                    $("#longitude").prop('disabled', true);
//                    $("#latitude").prop('disabled', true);
//                }
        /**
        $(".decformat2").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

            var decform = $('.decformat2').val().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");



            $('#hargasat').val(decform);

            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();

            var hasil = volume * hargasat;

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val(hasil);



        });
        **/
       
        $(".decformat").change(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

        });


        $(".decformat2").select(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });
        });


        $('#volume').on("focus", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            //$('#rm').val($(".decformat").val());
            $('#rm').val(hasil);
        });

        $('#volume').on("keyup", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val($(".decformat").val());
            //$('#rm').val(hasil);
        });






//
//        $('#kd_komponen-sel').on("change", function () {
//            var kdkmpnen = $('#kd_komponen-sel').val();
//
//            setInputVal10('hargasat', 50, 'kdkmpnen', kdkmpnen);
//
//
//            var hargasat = $('#hargasat').val();
//            var volume = $('#volume').val();
//////            console.log(hargasat);
//////            console.log(volume);
//            var hasil = volume * hargasat;
//            //console.log('dsjhsf');
//
////            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
//
//            $("#totalpagu").val(hasil);
//            $('#rm').val(hasil);
//        });

//        $('#hargasat').keyup(function (event) {
//                var hargasat = $('#hargasat').val();
//            var volume = $('#volume').val();
//            
////            alert(parseFloat(volume * hargasat));
//            $('#jumlah').val(volume * hargasat);
//
//            // format number
//          
//        });








        //initCombobox('id_ruas', 33);
        initCombobox('kdgbkpk', 36);

        //initCombobox('id_satuan', 26);
        //modal tambah 
        multipleSelect('kdsdana', 38);

        //modal edit
        multipleSelect('ykdsdana', 38);
        
        //modal edit
        //multipleSelect('wkdsdana', 38);
       
        initCombobox('ykdgbkpk', 36);
        initCombobox('treatment', 42);




//        $(".div-tags").tagsinput('removeAll');
        // $('<div class="div-tags"></div>').insertBefore( "#tags" );
        // $('<input type="text" placeholder="">').insertAfter( ".div-tags" );
        //$('.div-tags").tagsinput('add', []);
        $('.div-tags').tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROGRAM':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to InputVal id of tag
            itemText: 'text' // this will be used to set text of tag
        });


        $('#modeform').val('tambah_detail');
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
//        $('#modalTitle').text('Tambah Data Detail');
        //$('#modal-detail').modal('show');
        $('#modal-tambah').modal('show');
        $('.tbhItem').text('Tambah Paket Pagu');



//        $('#thang-sel').prop('disabled', true);
//        $('#kd_kegiatan-sel').prop('disabled', true);
//        $('#kd_output-sel').prop('disabled', false);
//        $('#kd_sub_output-sel').prop('disabled', false);
//        $('#kd_komponen-sel').prop('disabled', false);
//        $('#kd_sub_komponen-sel').prop('disabled', true);
//        $('#nama_sub_komponen-sel').prop('disabled', true);
//        $('#id_ppk-sel').prop('disabled', false);
//        $('#kdkppn-sel').prop('disabled', false);





//        if (isFisik == '2409')
//        {
//
//            $('#jns_giat').on('change', function () {
//                var jnsg = $('#jns_giat').val();
//
//                //var jnsg = $('#jns_giat').val();
//
//
//                if (jnsg == 'F')
//                {
//                    $('.rujuk').show();
//                    $('.divNonFisik').hide();
//                    $('#detail').prop('disabled', true);
//                    $('.divRuas').show();
//                    $("#id_ruas").prop('disabled', false);
//                    $("#sta_awal").prop('disabled', false);
//                    $("#sta_akhir").prop('disabled', false);
//                    $("#treatment").prop('disabled', false);
//                    $("#id_jembatan").prop('disabled', false);
//                    $("#longitude").prop('disabled', false);
//                    $("#latitude").prop('disabled', false);
//                } else {
//                    $('.rujuk').hide();
//                    $('.divNonFisik').show();
//                    $('#detail').prop('disabled', false);
//                    $('.divRuas').hide();
//                    $("#id_ruas").prop('disabled', true);
//                    $("#sta_awal").prop('disabled', true);
//                    $("#sta_akhir").prop('disabled', true);
//                    $("#treatment").prop('disabled', true);
//                    $("#id_jembatan").prop('disabled', true);
//                    $("#longitude").prop('disabled', true);
//                    $("#latitude").prop('disabled', true);
//                }
//
//                //alert(jnsg);
//
//
//            });
//
//
//            $('#jns_giat').val('F');
//            $('.rujuk').show();
//            $('.divNonFisik').hide();
//            $('#detail').prop('disabled', true);
//            $('.divRuas').show();
//            $("#id_ruas").prop('disabled', false);
//            $("#sta_awal").prop('disabled', false);
//            $("#sta_akhir").prop('disabled', false);
//            $("#treatment").prop('disabled', false);
//            $("#id_jembatan").prop('disabled', false);
//            $("#longitude").prop('disabled', false);
//            $("#latitude").prop('disabled', false);
//        } else {
//            $('#jns_giat').val('NF');
//            $('#jns_giat').hide();
//            $('.rujuk').hide();
//            $('.divNonFisik').show();
//            $('#detail').prop('disabled', false);
//            $('.divRuas').hide();
//            $("#id_ruas").prop('disabled', true);
//            $("#sta_awal").prop('disabled', true);
//            $("#sta_akhir").prop('disabled', true);
//            $("#treatment").prop('disabled', true);
//            $("#id_jembatan").prop('disabled', true);
//            $("#longitude").prop('disabled', true);
//            $("#latitude").prop('disabled', true);
//        }




//        $.ajax({
//            url: "<?php // echo base_url();         ?>pagu_indikatif/ajax_edit/paket/" + id,
//            type: "GET",
//            dataType: "JSON",
//            success: function (data)
//            {
//
//
//                $('[name="id_paket"]').val(data.id_paket);
//                $('#thang-sel').val(data.thang);
//                $('#id_ppk-sel').val(data.id_ppk);
//                $('#kdkppn-sel').val(data.kdkppn);
//                $('[name="thang-sel"]').val(data.thang);
//                $('[name="id_ppk-sel"]').val(data.id_ppk);
//                $('[name="kdkppn-sel"]').val(data.kdkppn);
//                $('#kd_sub_komponen-sel').val(data.kd_sub_komponen);
//                $('#nama_sub_komponen-sel').val(data.nama_sub_komponen);
//                $('[name="kd_sub_komponen-sel"]').val(data.kd_sub_komponen);
//                $('[name="nama_sub_komponen-sel"]').val(data.nama_sub_komponen);
//
//
//
//
//                updateCombobox('kd_kegiatan-sel', 5, data.kd_kegiatan);
//
//                if (data.kd_kegiatan == '2409')
//                {
//                    $('#jns_giat').val('F');
//
//                } else
//                {
//                    $('#jns_giat').val('NF');
//
//                }
//
//
//                $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);
//
//
//
//
//
//                var j = function () {
//                    var defer = $.Deferred();
//
//
//
//
//                    refreshComboboxOutput('kd_output-sel', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);
//
//                    //console.log('a() called');
////                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
//                    //initCombobox('kd_kegiatan-sel', 5);
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
//                var a = function () {
//                    var defer = $.Deferred();
//
//                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
//                    refreshCombobox4('kd_sub_output-sel', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);
//                    //console.log('a() called');
////                      updateCombobox('kd_output-sel', 30, data.kd_output);
////                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
////                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
//                var b = function () {
//                    var defer = $.Deferred();
//                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
//                    refreshCombobox4('kd_komponen-sel', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);
//
//
//
//                    //console.log('a() called');
////                    $('#kd_output-sel').val(data.kd_output);
////                    $('[name="kd_output-sel"]').val(data.kd_output);
////                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
////                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
//                var c = function () {
//                    var defer = $.Deferred();
//                    setInputVal10('hargasat', 50, 'kdkmpnen', data.kd_komponen);
//
//
//
//
//
//
//
//                    //console.log('a() called');
//                    var tlist_detail = $('#tlist_detail').DataTable();
//                    tlist_detail.ajax.reload();
//
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
//
//                var d = function () {
//                    var defer = $.Deferred();
//                    //console.log('a() called');$(".decformat2").val($('#hargasat').val());
//                    //$(".decformat2").val($('#hargasat').val());
//
//                    $(".decformat2").trigger("select");
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//                    return defer;
//                };
////                        var i = function () {
////                        var defer = $.Deferred();
////                                //console.log('a() called');
////                                //alert()
////                               
////
////                                    setTimeout(function () {
////                                    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
////                                    }, 1000);
////                                return defer;
////                        };
//
//
//
//
//                var pbar = function () {
//                    var defer = $.Deferred();
//
//                    //console.log('a() called');
//                    $(".overlay").show();
//
//                    setTimeout(function () {
//                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                    }, 1000);
//
//                    return defer;
//                };
//
//
//
//                $.when(
//                        pbar().then(j).then(a).then(b).then(c).then(d)
//                        // Deferred object (probably Ajax request),
//                        ).then(function () {
//                    $(".overlay").hide();// All have been resolved (or rejected), do your thing
////                    $('#modal-load').modal('toggle');
//                });
//            },
//            error: function (jqXHR, textStatus, errorThrown)
//            {
//                alert('Error Tambah Item Paket');
//            }
//        });
//        $("#prov").empty();
//        var data = get_prov();
//        //  alert(data.nama_prov);
//        $("#prov").append("<option value=" + data.kd_prov_irmsv3 + ">" + data.nama_prov + "</option>")
//        refreshCombobox('kabkot', 49, 'kd_prov_irmsv3', data.kd_prov_irmsv3);
//    




        //        console.log('nambah row');
//
//        $('#frm-paket')[0].reset();
//        $('#modeform').val('tambah_paket');
//        $('.form-group').removeClass('has-error'); // clear error class
//        $('.help-block').empty(); // clear error string
//        $('#modal-tambah').modal('show');
//        $('.tbhPaketDetail').text('Tambah Paket');
//
//
//        initCombobox('jnskontrak', 23);
//
//        if (role == 3)
//        {
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//            $(".ct option[value='X']").remove();
//        }




//        $("#kd_kegiatan").change(function () {
//            var kdgiat = $('#kd_kegiatan').val();
//            var kdkmpnen = $('#kd_komponen').val();
//            var thang = yearnow;
//
//
//
//
////            if (kdgiat == '2409') {
////                setInputVal3('hargasat', 50, 'kdkmpnen::thang', kdkmpnen + '::' + thang);
////            }
//
//
//        });



        //$('#modalTitle').text('Tambah Paket');
//        $('[name="thang"]').prop('disabled', false);
//        $('[name="kd_kegiatan"]').prop('disabled', false);
//        $('[name="kd_output"]').prop('disabled', false);
//        $('[name="kd_sub_output"]').prop('disabled', false);
//        $('[name="kd_komponen"]').prop('disabled', false);
//        $('[name="kd_sub_komponen"]').prop('disabled', false);
//        $('[name="nama_sub_komponen"]').prop('disabled', false);
//        $('[name="id_ppk"]').prop('disabled', false);
//        $('[name="kdkppn"]').prop('disabled', false);

//        $('#archive-preview').hide(); // hide photo preview modal

//        $('#label-archive').text('Upload File'); // label photo upload

//          var tlist_detail = $('#tlist_detail').DataTable();
        //$('#tlist_detail').DataTable().clear().draw();

    }

    // modal detail
    function set_kegiatan() {
        if ($("#ykd_kegiatan-sel").val() == "2409") {
            //$("#yjns_giat").val("F");
            $('#yjns_giat option:eq(1)').attr('selected', 'selected');
            ;
        }
        if ($("#zkd_kegiatan").val() == "2409") {
            //$("#yjns_giat").val("F");
            $('#zjns_giat option:eq(1)').attr('selected', 'selected');
            ;
        }

        if ($("#wkd_kegiatan").val() == "2409") {
            //$("#yjns_giat").val("F");
            $('#wjns_giat option:eq(1)').attr('selected', 'selected');
            ;
        }
    }
    
     function get_akun_by_kdgbkpk(kd_jns_belanja) {
        var x = null;
        var ajaxurl = "<?php echo base_url('pagu_indikatif/get_akun_by_kdgbkpk') ?>"+"/"+kd_jns_belanja;
        $.ajax({
            type: "GET",
            url: ajaxurl,
            dataType: "json",
            data: "",
            async: false,
            success: function (response) {
//                console.log("javascript response")
//                console.log(typeof response)
                x = response;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    function bind_jenis_akun(kd_jns_belanja){
        var objakun=get_akun_by_kdgbkpk(kd_jns_belanja);
           
        for(var i=0; i<= objakun.length-1; i++){
            $("#ykdakun").append("<option value="+objakun[i].kdakun+">"+objakun[i].nmakun+"</option>");
        }
    }
    
    function dtTambahRowDetail(id, isFisik) {
        obj_rj_rams={};
        obj_rj_irms={};
        obj_rj_renstra={};
        obj_rj_eprogram={};
        obj_rj_dpr={};
        obj_rj_sipro={};
        obj_rj_pemda={};
        $(".ctk-tb-detail .input-group").not(':first').remove();
        $( ".ctk-tb-detail .btn-remove" ).removeClass( "btn-danger" ).addClass( "btn-success");
        $( ".ctk-tb-detail .btn-success" ).removeClass("btn-remove").addClass( "btn-add");
        $(".btn-add").empty();
        $(".btn-add").append("+");
        $("#yrm").val("");
        var id_paket = id;
        var data_selected = xhrdata.data.filter(x => x[1] == id_paket)[0];
        //alert(data_selected[2]);
        refreshComboboxOutput('ythang-sel', 21, 'thang', data_selected[2], data_selected[2]);

        refreshComboboxOutput('ykd_kegiatan-sel', 5, 'kdgiat', data_selected[10], data_selected[10]);

        refreshComboboxOutput('ykd_output-sel', 30, 'kdgiat', data_selected[10], data_selected[19]);

        refreshComboboxOutput('ykd_sub_output-sel', 44, 'kdoutput', data_selected[19], data_selected[20]);

        refreshComboboxOutput('ykd_komponen-sel', 18, 'kdsoutput', data_selected[20], data_selected[24]);
 
        var kd_jns_belanja= data_selected[37];      
        bind_jenis_akun(kd_jns_belanja);
   
        updateComboboxAndSelected('ykdgbkpk', 36, data_selected[37]);
       
        setTimeout(set_kegiatan, 8000);//




        $("#ykd_sub_komponen-sel").val(data_selected[25]);
        $("#ynama_sub_komponen-sel").val(data_selected[26]);
        $("#yid_paket").val(id_paket);

        var id_rprov = $("#id_rprov").val();
        refreshComboboxOutput('yprov', 60, 'kd_prov', id_rprov, id_rprov);
        refreshCombobox('ykabkot', 61, 'kd_prov', id_rprov);
        
        var yrj_rams        ="";
        var yrj_irms        ="";
        var yrj_renstra     ="";
        var yrj_eprogram    ="";
        var yrj_dpr         ="";


        //refreshComboboxOutput('ykd_kegiatan-sel', 21, 'kdgiat',data_selected[10],data_selected[10]);
        // $("#ythang").val(data_selected[2]);
        //alert
        //$("#ythang-sel").val(data_selected[2]);
        //document.write( initCombobox('thang-sel', 28););
        //initCombobox('ythang-sel', 28);
        $(".decformat2").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

            var decform = $('.decformat2').val().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replace(/\D/g, "");



            $('#hargasat').val(decform);

            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();

            var hasil = volume * hargasat;

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val(hasil);



        });

        $(".decformat").change(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });

        });


        $(".decformat2").select(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;

            // format number
            $(this).val(function (index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");
            });
        });


        $('#volume').on("focus", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            //$('#rm').val($(".decformat").val());
            $('#rm').val(hasil);
        });

        $('#volume').on("keyup", function () {
            var hargasat = $('#hargasat').val();
            var volume = $('#volume').val();
////            console.log(hargasat);
////            console.log(volume);
            var hasil = volume * hargasat;
            //console.log('dsjhsf');

//            var vHasil =  hasil.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ".");

            $("#totalpagu").val(hasil);
            $(".decformat").val($("#totalpagu").val().replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, "."));

            $('#rm').val($(".decformat").val());
            //$('#rm').val(hasil);
        });



        //initCombobox('id_ruas', 33);
        initCombobox('kdgbkpk', 36);
        //initCombobox('id_satuan', 26);
        multipleSelect('kdsdana', 38);
        initCombobox('treatment', 42);
        multipleSelect('ykdsdana', 38);

        $('.div-tags').tagsinput({
            tagClass: function (item) {
                switch (item.rujukan) {
                    case 'DPR':
                    case 'PEMDA':
                        return 'label label-default';
                    case 'SIPRO':
                        return 'label label-primary';
                    case 'IRMS':
                        return 'label label-success';
                    case 'RAMS':
                        return 'label label-info';
                    case 'EPROGRAM':
                        return 'label label-warning';
                    case 'RENSTRA':
                        return 'label label-danger';
                }
            },
            allowDuplicates: true,
            itemValue: 'value', // this will be used to InputVal id of tag
            itemText: 'text' // this will be used to set text of tag
        });


        $('#modeform').val('tambah_detail');
        $('.form-group').removeClass('has-error'); // clear error class
        $('.help-block').empty(); // clear error string
//        $('#modalTitle').text('Tambah Data Detail');
        //$('#modal-detail').modal('show');
        $('#modal-detail').modal('show');
        $('.tbhItem').text('Tambah Detail Paket');



        $('#thang-sel').prop('disabled', true);
        $('#kd_kegiatan-sel').prop('disabled', true);
        $('#kd_output-sel').prop('disabled', false);
        $('#kd_sub_output-sel').prop('disabled', false);
        $('#kd_komponen-sel').prop('disabled', false);
        $('#kd_sub_komponen-sel').prop('disabled', true);
        $('#nama_sub_komponen-sel').prop('disabled', true);
        $('#id_ppk-sel').prop('disabled', false);
        $('#kdkppn-sel').prop('disabled', false);





        if (isFisik == '2409')
        {

            $('#jns_giat').on('change', function () {
                var jnsg = $('#jns_giat').val();

                //var jnsg = $('#jns_giat').val();


                if (jnsg == 'F')
                {
                    $('.rujuk').show();
                    $('.divNonFisik').hide();
                    $('#detail').prop('disabled', true);
                    $('.divRuas').show();
                    $("#id_ruas").prop('disabled', false);
                    $("#sta_awal").prop('disabled', false);
                    $("#sta_akhir").prop('disabled', false);
                    $("#treatment").prop('disabled', false);
                    $("#xid_jembatan").prop('disabled', false);
                    $("#longitude").prop('disabled', false);
                    $("#latitude").prop('disabled', false);
                } else {
                    $('.rujuk').hide();
                    $('.divNonFisik').show();
                    $('#detail').prop('disabled', false);
                    $('.divRuas').hide();
                    $("#id_ruas").prop('disabled', true);
                    $("#sta_awal").prop('disabled', true);
                    $("#sta_akhir").prop('disabled', true);
                    $("#treatment").prop('disabled', true);
                    $("#xid_jembatan").prop('disabled', true);
                    $("#longitude").prop('disabled', true);
                    $("#latitude").prop('disabled', true);
                }

                //alert(jnsg);


            });


            $('#jns_giat').val('F');
            $('.rujuk').show();
            $('.divNonFisik').hide();
            $('#detail').prop('disabled', true);
            $('.divRuas').show();
            $("#id_ruas").prop('disabled', false);
            $("#sta_awal").prop('disabled', false);
            $("#sta_akhir").prop('disabled', false);
            $("#treatment").prop('disabled', false);
            $("#xid_jembatan").prop('disabled', false);
            $("#longitude").prop('disabled', false);
            $("#latitude").prop('disabled', false);
        } else {
            $('#jns_giat').val('NF');
            $('#jns_giat').hide();
            $('.rujuk').hide();
            $('.divNonFisik').show();
            $('#detail').prop('disabled', false);
            $('.divRuas').hide();
            $("#id_ruas").prop('disabled', true);
            $("#sta_awal").prop('disabled', true);
            $("#sta_akhir").prop('disabled', true);
            $("#treatment").prop('disabled', true);
            $("#xid_jembatan").prop('disabled', true);
            $("#longitude").prop('disabled', true);
            $("#latitude").prop('disabled', true);
        }




        $.ajax({
            url: "<?php echo base_url(); ?>pagu_indikatif/ajax_edit/paket/" + id,
            type: "GET",
            dataType: "JSON",
            success: function (data)
            {

//                alert(data.id_ppk);
//             alert(data.kdkppn);

                $('[name="id_paket"]').val(data.id_paket);
                $('#thang-sel').val(data.thang);
                $('#id_ppk-sel').val(data.id_ppk);
                $('#kdkppn-sel').val(data.kdkppn);
                $('[name="thang-sel"]').val(data.thang);
                $('[name="id_ppk-sel"]').val(data.id_ppk);
                $('[name="kdkppn-sel"]').val(data.kdkppn);
                $('#kd_sub_komponen-sel').val(data.kd_sub_komponen);
                $('#nama_sub_komponen-sel').val(data.nama_sub_komponen);
                $('[name="kd_sub_komponen-sel"]').val(data.kd_sub_komponen);
                $('[name="nama_sub_komponen-sel"]').val(data.nama_sub_komponen);




                updateCombobox('kd_kegiatan-sel', 5, data.kd_kegiatan);

                if (data.kd_kegiatan == '2409')
                {
                    $('#jns_giat').val('F');

                } else
                {
                    $('#jns_giat').val('NF');

                }


                $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);





                var j = function () {
                    var defer = $.Deferred();




                    refreshComboboxOutput('kd_output-sel', 30, 'kdgiat', data.kd_kegiatan, data.kd_output);

                    //console.log('a() called');
//                    refreshCombobox('kd_kegiatan-sel', 5, 'thang', data.thang);
                    //initCombobox('kd_kegiatan-sel', 5);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };
                var a = function () {
                    var defer = $.Deferred();

                    var valSelect = data.kd_kegiatan + "::" + data.kd_output;
                    refreshCombobox4('kd_sub_output-sel', 44, 'kdgiat::kdoutput', valSelect, data.kd_sub_output);
                    //console.log('a() called');
//                      updateCombobox('kd_output-sel', 30, data.kd_output);
//                    $('#kd_kegiatan-sel').val(data.kd_kegiatan);
//                    $('[name="kd_kegiatan-sel"]').val(data.kd_kegiatan);

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };
                var b = function () {
                    var defer = $.Deferred();
                    var valSelect = data.kd_kegiatan + "::" + data.kd_output + "::" + data.kd_sub_output;
                    refreshCombobox4('kd_komponen-sel', 18, 'kdgiat::kdoutput::kdsoutput', valSelect, data.kd_komponen);



                    //console.log('a() called');
//                    $('#kd_output-sel').val(data.kd_output);
//                    $('[name="kd_output-sel"]').val(data.kd_output);
//                    var vreff = data.kd_kegiatan + "::" + data.kd_output;
//                    setInputVal('satuan', 39, 'kdgiat::kdoutput', vreff);
                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };
                var c = function () {
                    var defer = $.Deferred();
                    setInputVal10('hargasat', 50, 'kdkmpnen', data.kd_komponen);







                    //console.log('a() called');
                    var tlist_detail = $('#tlist_detail').DataTable();
                    tlist_detail.ajax.reload();


                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };

                var d = function () {
                    var defer = $.Deferred();
                    //console.log('a() called');$(".decformat2").val($('#hargasat').val());
                    //$(".decformat2").val($('#hargasat').val());

                    $(".decformat2").trigger("select");

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);
                    return defer;
                };
//                        var i = function () {
//                        var defer = $.Deferred();
//                                //console.log('a() called');
//                                //alert()
//                               
//
//                                    setTimeout(function () {
//                                    defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
//                                    }, 1000);
//                                return defer;
//                        };




                var pbar = function () {
                    var defer = $.Deferred();

                    //console.log('a() called');
                    $(".overlay").show();

                    setTimeout(function () {
                        defer.resolve(); // When this fires, the code in a().then(/..../); is executed.
                    }, 1000);

                    return defer;
                };



                $.when(
                        pbar().then(j).then(a).then(b).then(c).then(d)
                        // Deferred object (probably Ajax request),
                        ).then(function () {
                    $(".overlay").hide();// All have been resolved (or rejected), do your thing
//                    $('#modal-load').modal('toggle');
                });
            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error Tambah Item Paket');
            }
        });
        $("#prov").empty();
        var data = get_prov();
        //  alert(data.nama_prov);
        $("#prov").append("<option value=" + data.kd_prov_irmsv3 + ">" + data.nama_prov + "</option>")
        refreshCombobox('kabkot', 49, 'kd_prov_irmsv3', data.kd_prov_irmsv3);
    }
    function get_prov() {
        var x = null;
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_prov') ?>",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)
                x = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    function bind_combo_prov() {


    }

    function dtDeleteRow(type, id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {
            // ajax delete data to database
            /***
            $.ajax({
                url: "<?php echo base_url(); ?>pagu_indikatif/ajax_delete/" + type + "/" + id,
                type: "POST",
                data: function ( d ) {
			d.<? php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>";
		},
                dataType: "JSON",
                success: function (data)
                {
                    //if success reload ajax table
                    if (type == 'paket')
                    {
                        $('#modal-tambah').hide();
                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload();
                    } else
                    {

                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload();
                    }
                    //();
                },
                error: function (jqXHR, textStatus, errorThrown)
                {
                    alert('Error deleting data');
                }
            });
            ***/
               
            var url = "<?php echo base_url();?>"+"pagu_indikatif/ajax_delete/" + type + "/" + id;
            var params = {"formData":{}, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params)
                    .done(function (data) {
                        //console.log(data);
                        //alert("Data saved " + data);

                         var tlist_paket = $('#tlist_paket').DataTable();
                         tlist_paket.ajax.reload();              
                    })
                    .fail(function () {
                        alert("error");
                    })
            
            
        }
    }

    function simpanFormPaket() {

        var mode = $('#modeform').val();
        var url;



        var formDataPaket = new FormData($("#frm-paket")[0]);
        console.log(formDataPaket);


        if (mode == 'tambah_paket') {
            url = "<?php echo base_url(); ?>pagu_indikatif/ajax_add/paket";

        } else if (mode == 'edit_paket') {
            url = "<?php echo base_url(); ?>pagu_indikatif/ajax_update/paket";
        }
        //alert(url);
        // ajax adding data to database        
        $.ajax({
            url: url,
            type: "POST",
            data: formDataPaket,
            dataType: "JSON",
            //async : false,
            //cache : false,
            contentType: false,
            processData: false,
            success: function (data)
            {

//                console.log(data);

                if (data.status) //if success close modal and reload ajax table
                {
                    $("#modal-tambah").modal('hide');
                    var tlist_paket = $('#tlist_paket').DataTable();


                    tlist_paket.ajax.reload();

                } else
                {

//                    for (var i = 0; i < data.inputerror.length; i++)
//                    {
//                        $('[name="' + data.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
//                        $('[name="' + data.inputerror[i] + '"]').next().text(data.error_string[i]); //select span help-block class set text error string
//                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
    }


    function simpanForm() {
    
    
        var phln = 0;
        var sbsn = 0;
        var jumlah = 0;
        var volume = 0;
        var rmp = 0;

        if ($("#id_ruas").val() != "-1" || $("#id_ruas").val() != "#") {

            if ($("#id_jembatan").val() == "#" || $("#id_jembatan").val() == "-1") {

                $("#ydetail").val($("#id_ruas option:selected").text());
            } else {


                $("#ydetail").val($("#id_jembatan option:selected").text());


            }
        }
        
        checkOutSumberdanaDetail();
        checkOutPenandaanDetail();
        //console.log("---json sumber dana--");
        //console.log(JSON.stringify(arr_sumber_dana));
        
        var uraian = $("#ydetail").val();
        var objmasterdetail = {
            "kd_kegiatan": $("#ykd_kegiatan-sel").val(), //master
            "kd_output": $("#ykd_output-sel").val(),
            "kd_sub_output": $("#ykd_sub_output-sel").val(),
            "kd_komponen": $("#ykd_komponen-sel").val(),
            "kd_sub_komponen": $("#ykd_sub_komponen-sel").val(),
            "thang": $("#ythang-sel").val(),
            "satuan": $("#ysatuan").val(),
            
            "volume": $("#volume").val(),
            "kdgbkpk": $("#ykdgbkpk").val(),
            "kdakun": $("#ykdakun").val(),
            
            "jumlah": $("#yjumlah").val().replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"),
            "hargasat":$("#yhargasat").val().replace(/(\d+).(?=\d{3}(\D|$))/g, "$1"),
           
            "kdlokasi": $("#yprov").val(),
            "kdkabkota": $("#ykabkot").val(),
           
            "id_jembatan": $("#yid_jembatan").val(),
            "id_ruas": $("#id_ruas").val(),
            "sta_awal": $("#sta_awal-sel").val(),
            "sta_akhir": $("#sta_akhir-sel").val(),
            "longitude": $("#ylongitude").val(),
            "latitude": $("#ylatitude").val(),
            "id_paket": $("#yid_paket").val(),
            "obj_sumber_dana":JSON.stringify(arr_sumber_dana),
            "kd_program" :"08",
            "detail": uraian,
            "rj_rams":JSON.stringify(obj_rj_rams),
            "rj_irms":JSON.stringify(obj_rj_irms),
            "rj_renstra":JSON.stringify(obj_rj_renstra),
            "rj_eprogram":JSON.stringify(obj_rj_eprogram),
            "rj_dpr":JSON.stringify(obj_rj_dpr),
            "rj_pemda":JSON.stringify(obj_rj_pemda),
            "rj_sipro":JSON.stringify(obj_rj_sipro)
        };
        
        var url = "<?php echo base_url("pagu_indikatif/simpan_detail") ?>"
        
        var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
          
        $.post(url, params).done(function (data) {
             var tlist_paket = $('#tlist_paket').DataTable();
             tlist_paket.ajax.reload();                      
              $('#modal-detail').modal('hide');
         })
         .fail(function () {
             alert("error");
         })
        
        /****
        $.ajax({
            type: "POST",
            url: url,
            // The key needs to match your method's input parameter (case-sensitive).
            data: JSON.stringify({data_master_detail: objmasterdetail}),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (response)
            {


                if (response.status) //if success close modal and reload ajax table
                {

                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload();
                   
                    $('#frm-detail')[0].reset();

                    $('#modal-detail').modal('hide'); //or  $('#IDModal').modal('hide');
                  
                } else
                {
                    for (var i = 0; i < response.inputerror.length; i++)
                    {
                        $('[name="' + response.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + response.inputerror[i] + '"]').next().text(response.error_string[i]); //select span help-block class set text error string
                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
        ****/



    }

    function preview()
    {

        $('#imagepreview').attr('src', $('#imageresource').attr('src')); // here asign the image to the modal when the user click the enlarge link
        $('#imagemodal').modal('show'); // imagemodal is the id attribute assigned to the bootstrap modal, then i use the show function

    }

    function upload_attachment(id) {
        //console.log(xhrdata.data);
        //alert(JSON.stringify(xhrdata));
        var data_selected = xhrdata.data.filter(x => x[1] == id)[0];
        // console.log("data_selected");
        //console.log(data_selected);
        var xiduser = id_user_get;
        var xidusulan = data_selected[1];
        var xthang = data_selected[2];
        var str_src = "<?php echo base_url('/upload_pagu_indikatif/fileupload_pagu_indikatif?id_user='); ?>" + xiduser + "&id_usulan=" + xidusulan + "&thang=" + xthang;
        //alert(str_src);
        $("#iframeupload").attr("src", str_src);
        $("#modal-upload").modal("show");
    }

    function download_attachment(id) {

        listing_attachment2(id);



        $("#modal-download").modal("show");
    }

    function close_modal_attachment() {

        $("#modal-download").modal("hide");
        // location.reload();
    }

    var table_attachment = null;

    function listing_attachment2(id) {
        //alert(id);

        var data_selected = xhrdata.data.filter(x => x[1] == id)[0];
        var xiduser = role;
        // var xidusulan = data_selected[1];

        //alert(id+"xxxxx"+role)
        var xthang = data_selected[2];
        if ($.fn.dataTable.isDataTable('#table_id2')) {
            table_attachment = $('#table_id2').DataTable();
        } else {
            table_attachment = $('#table_id2').DataTable({
                "createdRow": function (row, data, index) {
                    var ico_class = get_extentsion_file(data[0]);
                    var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp"
                    $('td', row).eq(0).prepend(html_icon);
                },
                "draw": 0,
                "columnDefs": [{"orderable": true, "targets": [0]}],
                "order": [[0, "desc"]],
                "processing": true,
                "serverSide": true,
                "ajax": {
                    type: "POST",
                    url: "<?php echo base_url(); ?>pagu_indikatif/ssp_attachment",
                    data: function (d) {
                        d.id = id;
                        d.role = role;
                    }
                },
                "aoColumnDefs": [{
                        "aTargets": [0],
                        "mRender": function (data, type, full) {
                            //console.log("full attachment");
                            //console.log(full);
                            var htm = '';
                            switch (full[3]) {
                                case '47':
                                    htm += full[0] + "<br><h6 style='color:#903509;'>Berkas File Usulan DPR<h6>";
                                    break;
                                case '6':
                                    htm += full[0] + "<br><h6 style='color:#5c9009;'>Berkas File Yang Telah Ditelaah Oleh KPSJ</h6>";
                                    break;
                                case '60':
                                    htm += full[0] + "<br><h6 style='color:#186d71;'>Berkas File Yang Telah Ditelaah Oleh BALAI<h6>";
                                    break;
                                case null:
                                    htm += "...";
                                    break;
                            }

                            return htm;
                        }
                    },
                    {
                        "aTargets": [1],
                        "mRender": function (data, type, full) {
                            //console.log("full attachment");
                            //console.log(full);
                            var data_full_attachment = full[0];
                            var html_button = [
                                "<button onclick=download_file('" + data_full_attachment + "') class='btn btn-primary btn-xs'>",
                                "<i class='fa fa-download'>",
                                "</i>",
                                "</button>",
                            ].join("\n");
                            return html_button;
                        }
                    }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "Memuat...",
                    "processing": "Memroses...",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
            });
            table_attachment.on('xhr', function () {
                xhrdata1 = table_attachment.ajax.json();

                //        console.log('xhr data: ' );
                console.log(xhrdata1);
            });




//            table_attachment.reload();
            //});

            $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        }
        //alert("Halaman akan direload harap menunggu...")
    }

    function download_file(data) {
        //  alert(data);
        //var data = table.row( $(this).parents('tr') ).data();
        var filname = data;
        window.open('<?php echo base_url("/pagu_indikatif/download/"); ?>' + filname, '_blank');
    }
    
    function get_jenis_belanja(kd_jenis_belanja){
        var x = null;
        $.ajax({
            //type: "GET",
            url: "<?php echo base_url('pagu_indikatif/get_jenis_belanja') ?>",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                //console.log("--ajax data--");
                console.log(data)
                x = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    
    function bind_kode_jenis_belanja(kd_jenis_belanja){
        var obj_jenis_belanja=get_jenis_belanja(kd_jenis_belanja);
           
        for(var i=0; i<= obj_jenis_belanja.length-1; i++){
            //alert(objruas[i].linkname);
            if(no_ruas==obj_jenis_belanja[i].no_ruas){
                $("#wid_ruas").append("<option selected value="+obj_jenis_belanja[i].no_ruas+">"+obj_jenis_belanja[i].linkname+"</option>");
            }else{
                $("#wid_ruas").append("<option value="+obj_jenis_belanja[i].no_ruas+">"+obj_jenis_belanja[i].linkname+"</option>");
            }
        }
    }
    
    function get_extentsion_file(file) {
        var extension = file.substr((file.lastIndexOf('.') + 1));
        switch (extension) {
            case 'jpg':
            case 'png':
            case 'jpeg':
            case 'gif':
                //alert('was jpg png gif');
                return 'fa fa-image';  // There's was a typo in the example where
                break;                         // the alert ended with pdf instead of gif.
            case 'zip':
            case 'rar':
                //alert('was zip rar');
                return 'fa fa-file-archive-o'
                break;
            case 'pdf':
                //alert('was pdf');
                return 'fa fa-file-pdf-o';
            case 'xlsx':
                return 'fa fa-file-excel-o';
                break;
            default:
                "fa fa-file"
        }
    }


//     var obj_arahan = {
//            "sa1thn_id": sa1thn_id,
//            "tahun_anggaran": data_selected[1],
//            "kawasan_nama": data_selected[2],
//            "subkawasan_nama": data_selected[3],
//            "kegiatan_nama": data_selected[4],
//            "suboutput_nama": data_selected[5],
//            "output_nama": data_selected[6],
//            "sub_aktivitas": data_selected[7],
//            "satuan_output": data_selected[8],
//            "volume": data_selected[9],
//            "rpm": data_selected[10],
//            "phln": data_selected[11],
//            "sbsn": data_selected[12],
//            "rmp": data_selected[13],
//            "unit_id": data_selected[14],
//            "program_id": data_selected[15],
//            "kd_kegiatan": data_selected[16],
//            "kd_output": data_selected[17],
//            "kd_suboutput": data_selected[18],
//            "provinsi_id": data_selected[19],
//            "kabkot": data_selected[20],
//            "jenis_kontrakID": data_selected[21],
//            "rc_FS": data_selected[22],
//            "rc_DED": data_selected[23],
//            "rc_Dokling": data_selected[24],
//            "rc_lahan": data_selected[25],
//            "wps_kode": data_selected[26],
//            "kws_kode": data_selected[27],
//            "status_konreg": data_selected[28],
//            "status_verifikasi": data_selected[29],
//            "status_rakor": data_selected[30],
//            "catatan": data_selected[31],
//            "kd_isu": data_selected[32],
//            "kd_komponen": $("#kd_komponen").val(),
//            "kd_sub_komponen": $("#kd_sub_komponen").val(),
//            "nama_sub_komponen": $("#nama_sub_komponen").val(),
//            "kd_jns_belanja": $("#kdgbkpk").val(),
//            "kdakun": $("#kdakun").val(),
//            "kdkppn": $("#kdkppn").val(),
//            "id_ppk": $("#id_ppk").val(),
//            "rc_ded_status": $("#rc_ded_status").val(),
//            "rc_fs_status": $("#rc_fs_status").val(),
//            "rc_lahan_status": $("#rc_lahan_status").val(),
//            "rc_doklin_status": $("#rc_doklin_status").val(),
//            "jnskontrak": $("#jnskontrak").val(),
//            "id_ruas": $("#id_ruas").val(),
//            "sta_awal": $("#sta_awal").val(),
//            "sta_akhir": $("#sta_akhir").val(),
//            "id_jembatan": $("#id_jembatan").val(),
//            "longitude": $("#longitude").val(),
//            "latitude": $("#latitude").val(),
//            //"id_paket":$("#").val(),
//            "volume": data_selected[9],
//            "satuan": data_selected[8],
//            "hargasat": $("#hargasat").val(),
//            "jumlah": data_selected[10],
//        };
    function simpan_form_pagu(){
        var phln = 0;
        var sbsn = 0;
        var jumlah = 0;
        var volume = 0;
        var rmp = 0;
        var id_ruas = $("#xid_ruas").val();

        var id_jembatan = $("#xid_jembatan").val();
        var sta_awal = $("#sta_awal").val();
        var sta_akhir = $("#sta_akhir").val();
        var longitude = $("#longitude").val();
        var latitude = $("#latitude").val();

        //alert(longitude);
        //alert(latitude);
        if ($("#xid_ruas").val() != "-1" || $("#xid_ruas").val() != "#") {

            if ($("#xid_jembatan").val() == "#" || $("#xid_jembatan").val() == "-1") {

                $("#xdetail").val($("#xid_ruas option:selected").text());
            } else {


                $("#xdetail").val($("#xid_jembatan option:selected").text());


            }
        }


        var uraian = $("#xdetail").val();
        $("#xtotalpagu").val($(".decformat").val());
        if (check_rc(obj_data_cart.phln) == "#") {
            phln = 0;
        }

        if (check_rc(obj_data_cart.sbsn) == "#") {
            sbsn = 0;
        }

        if (check_rc(obj_data_cart.rmp) == "#") {
            rmp = 0;
        }

        if ($("#xtotalpagu").val() == "#") {
            jumlah = 0;
        } else {
            jumlah = $("#xtotalpagu").val();
        }

        if ($("#xvolume").val() == "#") {
            volume = 0;
        } else {
            volume = $("#xvolume").val();
        }
        var wps_kode = $("#wps_kode").val().split('::')[0];
        var kws_kode = $("#kws_kode").val().split('::')[1];
        var sub_kawasan_nama = $("#subkw").val();
        //alert("jumlah :"+jumlah);
        //alert("volume :"+volume);

        //alert($("#xnama_sub_komponen").val());

        //alert(id_jembatan);
        // alert(id_ruas);
        var xhargasat=$("#xhargasat").val();
        var xjumlah=$("#xjumlah").val();
        var xrm    =$("#xrm").val();
        
        checkOutSumberdana();
        checkOutPenandaan();
        //console.log("json rj IRMS");
        //console.log(JSON.stringify(obj_rj_irms));
        var tags_val=JSON.stringify(rujukan_all).replace(/\\/g, '');
        tags_val= tags_val.replace(/\"{/g,'{');
        tags_val= tags_val.replace(/\}"/g,'}');
        var xobj_usulan = {
            "thang": $("#thang").val(),
            //"kawasan_nama": data_selected[2],//not exists in usulan indikatif
            //"subkawasan_nama": data_selected[3],
            //"kegiatan_nama": data_selected[4],
            //"suboutput_nama": data_selected[5],
            //"output_nama": data_selected[6],
            //"sub_aktivitas": data_selected[7],
            //"satuan_output": data_selected[8],
            "volume": volume,
            //"rpm": data_selected[10],
            "phln": phln, //not exists in form exists in table
            "sbsn": sbsn, //not exists in form exists in table
            "rmp": rmp, //not exists in form exists in table
            "kd_unit": obj_data_cart.kd_unit, //not exists in form exists in table
            "kd_program": obj_data_cart.kd_program, //not exists in form exists in table
            "kd_kegiatan": $("#kd_kegiatan").val(), //not exists in form exists in table
            "kd_output": $("#kd_output").val(),
            "kd_sub_output": $("#kd_sub_output").val(),
            "kdlokasi": $("#prov").val(), //kode provinsi
            "kdkabkota": $("#kabkot").val(),
            "id_jenis_kontrak": $("#jnskontrak").val(),
            //"rc_FS": data_selected[22],
            //"rc_DED": data_selected[23],
            //"rc_Dokling": data_selected[24],
            //"rc_lahan": data_selected[25],
            "wps_kode": wps_kode,
            "kws_kode": kws_kode,
            "sub_kawasan_nama":$("#subkw").val(),
            //"status_konreg": data_selected[28],
            //"status_verifikasi": data_selected[29],
            //"status_rakor": data_selected[30],
            //"catatan": data_selected[31],
            "kd_isu": $("#kd_isu").val(),
            "kd_komponen": $("#kd_komponen").val(),
            "kd_sub_komponen": $("#kd_sub_komponen").val(),
            "nama_sub_komponen": $("#xnama_sub_komponen").val(), //tanpa x id elemen sama dengan modal detail 
            "kd_jns_belanja": $("#kdgbkpk").val(), //yang ada pada form sama dengan jenis belanja
            "kdgbkpk": $("#kdgbkpk").val(),
            "kdakun": $("#kdakun").val(),
            "kdkppn": $("#kdkppn").val(),
            "id_ppk": $("#id_ppk").val(),
            "rc_ded_status": $("#rc_ded_status").val(),
            "rc_fs_status": $("#rc_fs_status").val(),
            "rc_lahan_status": $("#rc_lahan_status").val(),
            "rc_doklin_status": $("#rc_doklin_status").val(),
            //JSON.stringify(arr_rj_rams).replace(/[\[\]&]+/g, '')
            "rj_rams":JSON.stringify(obj_rj_rams),
            "rj_irms":JSON.stringify(obj_rj_irms),
            "rj_renstra":JSON.stringify(obj_rj_renstra),
            "rj_eprogram":JSON.stringify(obj_rj_eprogram),
            "rj_dpr":JSON.stringify(obj_rj_dpr),
            "rj_pemda":JSON.stringify(obj_rj_pemda),
            "rj_sipro":JSON.stringify(obj_rj_sipro),
            "tags_val":tags_val,
            //"jnskontrak": $("#jnskontrak").val(),
            "id_ruas": id_ruas,
            "sta_awal": sta_awal,
            "sta_akhir": sta_akhir,
            "id_jembatan": id_jembatan,
            "longitude": longitude,
            "latitude": latitude,
            "detail": uraian,
            "satuan": $("#xsatuan").val(),
            "hargasat":xhargasat.replace(/\D/g,''),
            "jumlah":xjumlah.replace(/\D/g, ''),
            "rm":xrm.replace(/\D/g, ''),
            "obj_sumber_dana":JSON.stringify(arr_sumber_dana),
            "<?php echo $this->security->get_csrf_token_name();?>":"<?php echo $this->security->get_csrf_hash();?>",
            
        };
            //console.log("---arr sumber dana---")
            //console.log(arr_sumber_dana);
            var url = "<?php echo base_url('pagu_indikatif/save_usulan_pagu'); ?>";
            
            var params = {"formData": xobj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            
                   $.post(url, params).done(function (data) {
                        var tlist_paket = $('#tlist_paket').DataTable();
                        tlist_paket.ajax.reload();                      
                        $('#modal-tambah').modal('hide');
                    })
                    .fail(function () {
                        alert("error");
                    })
            
    }



    function update_form_pagu() {

        var objmasterdetail = {
            "kd_kegiatan": $("#zkd_kegiatan").val(), //master
            "kd_output": $("#zkd_output").val(),
            "kd_sub_output": $("#zkd_sub_output").val(),
            "kd_komponen": $("#zkd_komponen").val(),
            "kd_sub_komponen": $("#zkd_sub_komponen").val(),
            "nama_sub_komponen": $("#znama_sub_komponen").val(),
            "thang": $("#zthang").val(),
            //"satuan" :$("#ysatuan").val(),
            //"id_user" :$("#").val(),
            //"created_by":$("#").val(),
            //"volume":$("#volume").val(),
            "kdgbkpk": $("#zkdgbkpk").val(),
            //"kdakun":$("#ykdakun").val(),
            //"phln":$("#").val(),
            //"rmp":$("#").val(),
            //"sbsn":$("#").val(),
            //"jumlah":$("#yjumlah").val().replace( /^\D+/g, ''),
            //"kdkppn":$("#").val(),
            //"kdsatker":$("#").val(),
            "kdlokasi": $("#zprov").val(),
            //"kdkabkota":$("#ykabkot").val(),
            "kd_isu": $("#zkd_isu").val(),
            //"wps_kode":$("#zwps_kode").val().split('::')[0],
            //"kws_kode":$("#zkws_kode").val().split('::')[1],
            "id_paket": $("#zid_paket").val(),
            "jnskontrak": $("#zjnskontrak").val(),
            "kdkppn": $("#zkdkppn").val(),
            "id_ppk": $("#zid_ppk").val(),
            "rc_ded_status": $("#zrc_ded_status").val(),
            "rc_fs_status": $("#zrc_fs_status").val(),
            "rc_lahan_status": $("#zrc_lahan_status").val(),
            "rc_doklin_status": $("#zrc_doklin_status").val(),
        };
       
        
        var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        var url = "<?php echo base_url("pagu_indikatif/update_paket_pagu") ?>";
        
        $.post(url, params).done(function (data) {
             var tlist_paket = $('#tlist_paket').DataTable();
             tlist_paket.ajax.reload();                      
              $('#modal-edit-paket').modal('hide');
         })
         .fail(function () {
             alert("error");
         })
        
        /****
        $.ajax({
            type: "POST",
            url: url,
            // The key needs to match your method's input parameter (case-sensitive).
            data: JSON.stringify({data_master_detail: objmasterdetail}),
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (response)
            {
                if (response.status) //if success close modal and reload ajax table
                {
                    var tlist_paket = $('#tlist_paket').DataTable();
                    tlist_paket.ajax.reload();                  
                    $('#modal-edit-paket').modal('hide'); //or  $('#IDModal').modal('hide');                   
                } else
                {
                    for (var i = 0; i < response.inputerror.length; i++)
                    {
                        $('[name="' + response.inputerror[i] + '"]').parent().parent().addClass('has-error'); //select parent twice to select div form-group class and add has-error class
                        $('[name="' + response.inputerror[i] + '"]').next().text(response.error_string[i]); //select span help-block class set text error string
                    }
                }


            },
            error: function (jqXHR, textStatus, errorThrown)
            {
                alert('Error adding / update Paket');
            }
        }
        );
     * ***/
  
      


        


    }





    function handle_form_fisik(element) {
        var field_value = element.value;
        if (field_value == "F") {
            $(".divRuas").css("display", "");
            $(".divNonFisik").css("display", "none");
        } else {
            $(".divRuas").css("display", "none");
            $(".divNonFisik").css("display", "");
        }
    }

    //reff_index= table_name
    function refreshComboboxString(divname, refindex, refresh_field, refresh_value) {
        url = "<?= base_url(); ?>lookup/refreshlookstringid/" + refindex + "/" + refresh_field + "/" + refresh_value;

        $.get(url).done(function (data) {
            jdata = JSON.parse(data);
            $('#' + divname).empty();
            $('#' + divname).append(new Option("--Pilih--", -1));
            $.each(jdata, function (i, el) {
                $('#' + divname).append(new Option(el.val, el.id));
            });

            //if (selvalue != '') $('#'+divname).val(selvalue)
        })
                .fail(function () {
                    alert("error");
                })
                .always(function () {
                    // alert("finished");
                });
    }

    function updateComboboxAndSelected(divname, refindex, selvalue) {
        url = "<?= base_url(); ?>lookup/fieldlook/" + refindex;

        $.get(url).done(function (data) {
            var jdata = JSON.parse(data);
            $('#' + divname).empty();
            $('#' + divname).append("<option value='#' >" + "--Pilih--" + "</option>");
            //alert(selvalue);
            for (var i = 0; i <= jdata.length - 1; i++) {
                if (jdata[i].id == selvalue) {
                    //alert("BBBB");
                    $('#' + divname).append("<option selected value=" + jdata[i].id + " >" + jdata[i].val + "</option>");
                } else {
                    $('#' + divname).append("<option value=" + jdata[i].id + " >" + jdata[i].val + "</option>");
                }
            }


        })
                .fail(function () {
                    alert("error");
                })
                .always(function () {
                    // alert("finished");
                });
    }
    
    function numberWithCommas(x) {
        //alert(x);
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    function toObject(arr) {
        var rv = {};
        for (var i = 0; i < arr.length; ++i)
          if (arr[i] !== undefined) rv[i] = arr[i];
        return rv;
    }
    
    function getValue(element){
        var elementvalue=element.value;
        console.log("--element value----");
        console.log(element.value);
        if($(".dynamic-input input[type=text]").length > 1){
            var gsumber_dana=$(".dynamic-input input[type=text]").val();
            gsumber_dana=gsumber_dana.replace(/\D/g,'');//masih bertipe string
            
            var new_gsumber_dana=parseFloat(gsumber_dana) - element.value;
            
            $(".dynamic-input input[type=text]").first().val(numberWithCommas(new_gsumber_dana));
            element.value=numberWithCommas(elementvalue);
        }
        //alert(gsumber_dana+"=>"+element.value);
        
        
    }
    
    function getValueTbDetail(element){
        var elementvalue=element.value;
        console.log("--element value----");
        console.log(element.value);
        if($(".tb-detail").length > 1){
            var gsumber_dana=$(".tb-detail").val();
            gsumber_dana=gsumber_dana.replace(/\D/g,'');//masih bertipe string
            console.log("gsumberdana")
            console.log(gsumber_dana);
            var new_gsumber_dana=parseFloat(gsumber_dana) - element.value;
            console.log("new gsumberdana");
            console.log(new_gsumber_dana);
            $(".tb-detail").first().val(numberWithCommas(new_gsumber_dana));
            element.value=numberWithCommas(elementvalue);
        }
        //alert(gsumber_dana+"=>"+element.value);
        
        
    }
    var arr_sumber_dana=[];
    function checkOutSumberdana(){
        //alert("sumber dana checked out");
        for(var i=0; i<= $(".dynamic-input input[type=text]").length-1; i++){
            //alert("label=>"+$(".concept:eq("+i+")").text() +" value=>"+$(".dynamic-input input[type=text]:eq("+i+")").val());
            var sdlabel=$(".concept:eq("+i+")").text();
            var sdvalue=$(".dynamic-input input[type=text]:eq("+i+")").val();
            var obj_sumber_dana={};
            obj_sumber_dana[sdlabel]=sdvalue.replace(/\D/g,''); 
            arr_sumber_dana[i]=obj_sumber_dana;
        }
       //alert(JSON.stringify(arr_sumber_dana));
    }
    

    
     function checkOutSumberdanaDetail(){
        //alert("sumber dana checked out");
        //var arr_sumber_dana=[];
        for(var i=0; i<= $(".dynamic-input-frm-tambah-detail input[type=text]").length; i++){
           
            var sdlabel=$(".concept:eq("+i+")").text();
            var sdvalue=$(".dynamic-input input[type=text]:eq("+i+")").val();
            var obj_sumber_dana={};
            obj_sumber_dana[sdlabel]=sdvalue.replace(/\D/g,''); 
            arr_sumber_dana[i]=obj_sumber_dana;
        }
        
        var new_arr_sumberdana=[];

        //membuang nilai null
        for(var i=1; i<= arr_sumber_dana.length; i++ ){
            if(typeof arr_sumber_dana[i]== 'object'){                
                 new_arr_sumberdana[i]=arr_sumber_dana[i];                
            }
           

        }

        var filtered = new_arr_sumberdana.filter(function (el) {
          return el != null;
        });
        
        arr_sumber_dana = filtered;
    }
    
    
    
    
    var arr_rj_rams=[];
    var obj_rj_rams={};
    
    var arr_rj_irms=[];
    var obj_rj_irms={};
    
    var arr_rj_renstra=[];
    var obj_rj_renstra={};
    
    var arr_rj_eprogram=[];
    var obj_rj_eprogram={};
    
    var arr_rj_dpr=[];
    var obj_rj_dpr={};
    
    var arr_rj_pemda=[];
    var obj_rj_pemda={};
    
    var arr_rj_sipro=[];
    var obj_rj_sipro={};
    var rujukan_all=[];
    function checkOutPenandaan(){
        //alert($("#md-tbh-penandaan .label-info").length);
        for(var i=0; i<= $("#md-tbh-penandaan .tag").length-1; i++){
            var obj_rujukan={};
            //alert($("#md-tbh-penandaan .label-info:eq("+i+")").text());
            var tagged_rujukan=$("#md-tbh-penandaan .tag:eq("+i+")").text();
            //console.log("--rujukan--")
            //console.log(tagged_rujukan);
            
            //alert(tagged_rujukan);
            if(tagged_rujukan.split("|")[0]=="RAMS"){
                //alert(tagged_rujukan.split("|")[0]);
                arr_rj_rams[i]=tagged_rujukan.split("|")[1];
                obj_rujukan={value:tagged_rujukan.split("|")[1],text:tagged_rujukan,rujukan:"RAMS"};
            }else if(tagged_rujukan.split("|")[0]=="IRMS"){
                arr_rj_irms[i]=tagged_rujukan.split("|")[1];
                obj_rujukan={value:tagged_rujukan.split("|")[1],text:tagged_rujukan,rujukan:"IRMS"};
                
            }else if(tagged_rujukan.split("|")[0]=="RENSTRA"){
               arr_rj_renstra[i]=tagged_rujukan.split("|")[1]; 
               obj_rujukan={value:tagged_rujukan.split("|")[1],text:tagged_rujukan,rujukan:"RENSTRA"};
            }else if(tagged_rujukan.split("|")[0]=="EPROG"){
               arr_rj_eprogram[i]=tagged_rujukan.split("|")[1];
               obj_rujukan={value:tagged_rujukan.split("|")[1],text:tagged_rujukan,rujukan:"EPROG"};
            }else if(tagged_rujukan.split("|")[0]=="DPR"){
               arr_rj_dpr[i]=tagged_rujukan.split("|")[1]; 
               obj_rujukan={value:tagged_rujukan.split("|")[1],text:tagged_rujukan,rujukan:"DPR"};
            }else if(tagged_rujukan.split("|")[0]=="PEMDA"){
               arr_rj_pemda[i]=tagged_rujukan.split("|")[1]; 
               obj_rujukan={value:tagged_rujukan.split("|")[1],text:tagged_rujukan,rujukan:"PEMDA"};
            }
            else if(tagged_rujukan.split("|")[0]=="SIPRO"){
               arr_rj_sipro[i]=tagged_rujukan.split("|")[1]; 
               obj_rujukan={value:tagged_rujukan.split("|")[1],text:tagged_rujukan,rujukan:"SIPRO"};
            }
            //alert(tagged_rujukan.split("|")[1]);
            //[{"value":"3429","text":"SIPRO|3429","rujukan":"SIPRO"}]
            rujukan_all[i]=JSON.stringify(obj_rujukan);
            
        }
//        console.log("Rujukan All");
//        console.log(rujukan_all);
        
        obj_rj_rams=toObject(arr_rj_rams);       
        obj_rj_irms=toObject(arr_rj_irms);
        obj_rj_renstra=toObject(arr_rj_renstra);
        obj_rj_dpr=toObject(arr_rj_dpr);
        obj_rj_eprogram=toObject(arr_rj_eprogram);
        obj_rj_sipro=toObject(arr_rj_sipro);
        obj_rj_pemda=toObject(arr_rj_pemda);

    }
    
    
    function checkOutPenandaanDetail(){
        //alert($("#md-tbh-penandaan .label-info").length);
        for(var i=0; i<= $("#md-tbh-penandaan .tag").length-1; i++){
            //alert($("#md-tbh-penandaan .label-info:eq("+i+")").text());
            var tagged_rujukan=$("#md-tbh-penandaan .tag:eq("+i+")").text();
            //alert(tagged_rujukan);
            if(tagged_rujukan.split("|")[0]=="RAMS"){
                //alert(tagged_rujukan.split("|")[0]);
                arr_rj_rams[i]=tagged_rujukan.split("|")[1];
            }else if(tagged_rujukan.split("|")[0]=="IRMS"){
                arr_rj_irms[i]=tagged_rujukan.split("|")[1];
                
            }else if(tagged_rujukan.split("|")[0]=="RENSTRA"){
               arr_rj_renstra[i]=tagged_rujukan.split("|")[1]; 
            }else if(tagged_rujukan.split("|")[0]=="EPROG"){
               arr_rj_eprogram[i]=tagged_rujukan.split("|")[1];
            }else if(tagged_rujukan.split("|")[0]=="DPR"){
               arr_rj_dpr[i]=tagged_rujukan.split("|")[1]; 
            }
            else if(tagged_rujukan.split("|")[0]=="PEMDA"){
               arr_rj_pemda[i]=tagged_rujukan.split("|")[1]; 
            }
            else if(tagged_rujukan.split("|")[0]=="SIPRO"){
               arr_rj_sipro[i]=tagged_rujukan.split("|")[1]; 
            }
            //alert(tagged_rujukan.split("|")[1]);
        }

        obj_rj_rams=toObject(arr_rj_rams);       
        obj_rj_irms=toObject(arr_rj_irms);
        obj_rj_renstra=toObject(arr_rj_renstra);
        obj_rj_dpr=toObject(arr_rj_dpr);
        obj_rj_eprogram=toObject(arr_rj_eprogram);
        obj_rj_sipro=toObject(arr_rj_sipro);
        obj_rj_pemda=toObject(arr_rj_pemda);

    }
    
function set_rams(){
  return $("#rj_rams").val();
}

function set_irms(){
   return $("#rj_irms").val();
}

function set_renstra(){
   return $("#rj_renstra").val();
}

function set_eprog(){
   return $("#rj_eprog").val();
}

function set_dpr(){
   return $("#rj_dpr").val();
}

function set_sipro(){
   return $("#rj_sipro").val();
}
function set_pemda(){
   return $("#rj_pemda").val();
}

function delete_tagss(id){
          //alert(element.id);
          var id_button=id;
          //alert((JSON.stringify(id_button)));
          $('#'+id_button).parents('.tag').remove();
          
          
          //alert($('#'+id_button).text());
          
}

function bindTagging2(string_tagging){
  //alert(1);
  $(".bootstrap-tagsinput").remove();
       var arr_rj_rams =string_tagging.split(",");
  		   var html_tag_parent=[
         "<div class='bootstrap-tagsinput'>",
         "<div>"
         ].join("\n");
       $("#md-edit-penandaan").children(".div-tags").append(html_tag_parent);
       var arr_tagging=string_tagging.split(",");
       var newlinenumber=0;
       for(var i=0; i<= arr_tagging.length-2; i++){
           var taggkey=arr_tagging[i].split("|");
         //alert(taggkey[0]);
         var taggclass="";
         if(taggkey[0]=='RAMS'){
           taggclass='-info';
         }else if(taggkey[0]=='IRMS'){
           taggclass='-success';
         }else if(taggkey[0]=='RENSTRA'){
           taggclass='-danger';
         }
         else if(taggkey[0]=='EPROG'){
           taggclass='-warning';
         }
         else if(taggkey[0]=='SIPRO'){
           taggclass='-primary';
         }
         else if(taggkey[0]=='PEMDA'){
           taggclass='-default';
         }
          else if(taggkey[0]=='DPR'){
           taggclass='-info';
         }
        
    
    		//var arr_
    		//$("#md-edit-penandaan").children(".div-tags").append(html_tag);
          var html_tag_childrens=["<span class='tag label label"+taggclass+"'>",
                                      arr_tagging[i],
                                      "<span data-role='remove' id='"+taggkey[1]+"' onclick='delete_tagss(this.id)'>",
                                          "",
                                      "</span>",
                                   "</span>"].join("\n");
         $("#md-edit-penandaan").children(".div-tags").children(".bootstrap-tagsinput").append(html_tag_childrens);
         
         if(newlinenumber >=6 ){
            $("#md-edit-penandaan").children(".div-tags").children(".bootstrap-tagsinput").append("<br/>");
            newlinenumber=0;
         }
         
         newlinenumber ++;
       }    
}





function extract_tags(){
 var arreditrams    =[];
 var arreditirmsv3  =[];
 var arreditrenstra =[];
 var arrediteprog   =[];
 var arreditdpr	    =[]; 
 var arreditsipro   =[];
 var arreditpemda   =[];
 var objedittag     ={};
 var data_tag=$("#md-edit-penandaan").find('.tag').not($("#md-edit-penandaan").find('.tag').children());
 var i=0;
 
  
 data_tag.each(function() {
    
            var data_text = $(this).text();
            var tagkey=data_text.split("|")[0].trim();
            var tagvalue=data_text.split("|")[1].trim();
            
            console.log("data text");
            console.log(data_text);
            
            var find = '\n';
            var re = new RegExp(find, 'g');
            tagvalue = tagvalue.replace(re, '');
            
            tagvalue = tagvalue.replace("X","");
            
            
            var obj_rujukan={};
            //var newdatatext=
            data_text=data_text.trim();
            data_text=data_text.replace(re, '');
            data_text=data_text.replace("X","");
            
            
            if(tagkey=="RAMS" && tagvalue){
	          arreditrams[i] = tagvalue;
	          objedittag[tagkey]=Object.assign({},arreditrams);
                  obj_rujukan={value:tagvalue,text:data_text,rujukan:"RAMS"};
	         
	        
	    	}else if(tagkey == 'IRMS' && tagvalue){
	    	  arreditirmsv3[i] = tagvalue;
	    	  objedittag[tagkey]=Object.assign({},arreditirmsv3);
                  obj_rujukan={value:tagvalue,text:data_text,rujukan:"IRMS"};
	    	 
	    	  
	    	}else if(tagkey == 'RENSTRA' && tagvalue){
	    	  arreditrenstra[i] =tagvalue;
	    	  objedittag[tagkey]=arreditrenstra;
	    	  objedittag[tagkey]=Object.assign({},arreditrenstra);
                  obj_rujukan={value:tagvalue,text:data_text,rujukan:"RENSTRA"};
	    	  
	    	}else if(tagkey == 'EPROG' && tagvalue){
	    	  arrediteprog[i] =tagvalue;
	    	  objedittag[tagkey]=arrediteprog;
	    	  objedittag[tagkey]=Object.assign({},arrediteprog);
                  obj_rujukan={value:tagvalue,text:data_text,rujukan:"EPROG"};
	    	 
	    	}else if(tagkey == 'PEMDA' && tagvalue){
	    	  arreditpemda[i] =tagvalue;
	    	  objedittag[tagkey]=arreditpemda;
	    	  objedittag[tagkey]=Object.assign({},arreditpemda);
                  obj_rujukan={value:tagvalue,text:data_text,rujukan:"PEMDA"};
	    	 
	    	}else if(tagkey == 'SIPRO' && tagvalue){
	    	  arreditsipro[i] =tagvalue;
	    	  objedittag[tagkey]=arreditsipro;
	    	  objedittag[tagkey]=Object.assign({},arreditsipro);
                  obj_rujukan={value:tagvalue,text:data_text,rujukan:"SIPR0"};
	    	 
	    	}
                else if(tagkey == 'DPR' && tagvalue){
	   
	    	   arreditdpr[i] =tagvalue;
    	    	  var find = ' ';
                  var re = new RegExp(find, 'g');
    
                  tagvalue = tagvalue.replace(re, '');
                  objedittag[tagkey]=objedittag[tagkey]=Object.assign({},arreditdpr);
                  obj_rujukan={value:tagvalue,text:data_text,rujukan:"DPR"};
                  
             
      
	    	}
	    
	    	
	    	i++;
                rujukan_all.push(obj_rujukan);
		});
	
		//console.clear();
	        //console.log(objedittag);
		///console.log(arreditrams);
		console.log("rujukan all di dalam extract tag"+rujukan_all);
		return objedittag;
}

function setSumberDana(){
    //alert(7777);
    var objsumberdana=JSON.parse($("#objsumberdana").val());
    var html_select=$("#htmldropdownsdana").val();
    const keys = Object.keys(objsumberdana);
    
//    console.log("---sumber dana---")
//    console.log(html_select);
    
    $(".mdl-edit-detail").empty();
    var i=0;
    for (const key of keys) {
        var btnlabelclass='';
        if(key=='phln'){
            btnlabelclass='primary';
        }else if(key=='rmp'){
            btnlabelclass='success'
        }else if(key=='pnpb'){
            btnlabelclass='danger'
        }else if(key=='blu'){
            btnlabelclass='warning'
        }else if(key=='sbsn'){
            btnlabelclass='info'
        }else if(key== 'opr'){
            btnlabelclass= 'dark'
        }else if(key== 'pdp'){
            btnlabelclass= 'secondary'
        }else if(key== 'pdn'){
            btnlabelclass= 'outline'
        }else if(key== 'rpm'){
            btnlabelclass= 'default'
        }
        
        
        var sdalabel=key.toUpperCase();
        var sdavalue=objsumberdana[key];
        sdavalue=sdavalue.split(".")[0];
          //var whargasat=$("#whargasat").val().split(".")[0];
         //$("#whargasat").val(numberWithCommas(whargasat));
         
         //var wjumlah=$("#wjumlah").val().split(".")[0];
         //$("#wjumlah").val(numberWithCommas(wjumlah));
        var x= Object.entries(objsumberdana);
        var buttonactionclass='';
        var buttonactionicon='-';
         //alert(i +'=>'+keys.length-1);
         if(i != keys.length-1){
        
            buttonactionclass="danger btn-remove-sda";
            buttonactionicon ="-";
         }else{
            buttonactionclass="success btn-add-sda";
            buttonactionicon ="+";
         }
        
          var html_container=['<div class="form-group multiple-form-group input-group">',
                                '<div class="input-group-btn input-group-select">',
                                    '<button type="button" class="btn btn-'+btnlabelclass+' dropdown-toggle" data-toggle="dropdown">',
                                        '<span class="concept">'+sdalabel+'</span> <span class="caret"></span>',
                                    '</button>',
                                    '<ul class="dropdown-menu" role="menu" id="wkdsdana">',
                                        html_select,
                                    '</ul>',
                                    '</div>',
                                    '<input value='+numberWithCommas(sdavalue)+' onchange="getValue(this);" id="xrm" name="xrm" class="form-control valuesdana" type="text">',
                                    '<span class="input-group-btn">',
                                       '<button id="btn-action-sda-'+i+'" type="button" class="btn btn-'+buttonactionclass+'">'+buttonactionicon+'</button>',
                                    '</span>',
                                '</div>',
                           ].join('\n')
           $(".mdl-edit-detail").append(html_container);
           console.log(i);
           i++;
    }
    
    $(".btn-add-sda").unbind( "click" );
    $(".btn-remove-sda").unbind( "click" );
    $(".btn-add-sda").click(function(){
        var buttonid="#"+this.id;
        $(buttonid).parents('.input-group').remove();
        $(buttonid).remove();
        $(buttonid).removeClass("btn-add-sda");
        $(buttonid).removeClass("btn-success");
          
        $(buttonid).addClass("btn-danger");
        $(buttonid).addClass("btn-remove-sda");
        $(buttonid).text("-");
        setSingleSumberDana(buttonid);
    });
    
    $(".btn-remove-sda").click(function(){
     var buttonid="#"+this.id;
    
     $(buttonid).parents('.form-group.multiple-form-group.input-group').remove();
    });
}

function setSingleSumberDana(lastid){
     //alert(lastid.split('-')[3]);
     var idplus=lastid.split('-')[3];
     var newid= parseInt(idplus)+1;
     var html_container=['<div class="form-group multiple-form-group input-group">',
                                '<div class="input-group-btn input-group-select">',
                                    '<button type="button" class="btn btn-'+''+' dropdown-toggle" data-toggle="dropdown">',
                                        '<span class="concept">'+''+'</span> <span class="caret"></span>',
                                    '</button>',
                                    '<ul class="dropdown-menu" role="menu" id="kdsdana">',
                                        '',
                                    '</ul>',
                                    '</div>',
                                    '<input onchange="getValue(this);" id="xrm" name="xrm" class="form-control valuesdana" type="text">',
                                    '<span class="input-group-btn">',
                                       '<button id="btn-action-sda-'+newid+'" type="button" class="btn btn-success btn-add-sda">'+'+'+'</button>',
                                    '</span>',
                                '</div>',
                           ].join('\n')
           $(".contacts").append(html_container);
           
    $(".btn-add-sda").unbind( "click" );
    $(".btn-remove-sda").unbind( "click" );
    $(".btn-add-sda").click(function(){
        var buttonid="#"+this.id;
        $(buttonid).parents('.input-group').remove();
        $(buttonid).remove();
        $(buttonid).removeClass("btn-add-sda");
        $(buttonid).removeClass("btn-success");
          
        $(buttonid).addClass("btn-danger");
        $(buttonid).addClass("btn-remove-sda");
        $(buttonid).text("-");
        setSingleSumberDana(buttonid);
    });
    
     $(".btn-remove-sda").click(function(){
         var buttonid="#"+this.id;
         //alert(buttonid);
         $(buttonid).parents('.form-group.multiple-form-group.input-group').remove();
     });
}


//var obj_wsumberdana=[];
var arr_wsumberdana=[];
$(".mdl-edit-detail").click(function(){
     //alert($(".mdl-edit-detail").find('input').val());   
     $(".mdl-edit-detail").find('input').each(function(index,data) {
         var value = $(this).val();
         //alert($(this).prev().text());
         //alert(value);
         
         var sdanakey=$(this).prev().text();
         var xkey=sdanakey.replace(/\n/ig, '');
         xkey=xkey.replace(/\s/g, "");
         //xkey=sdanakey.replace(/\D/g,'');
         arr_wsumberdana[xkey.toLowerCase()]=value.split('.')[0];
     });
     console.log(arr_wsumberdana);
});


function multipleSelect(divname, refindex) {
    //alert(divname);
    url = "<?= base_url(); ?>lookup/fieldlook/" + refindex;

    $.get(url).done(function (data) {
        jdata = JSON.parse(data);
//            console.log("---parent--");
//            console.log(data);
        //$('#'+divname).empty();
        //$('#'+divname).prepend("<li>--Pilih--</li>");
        var htmlsumberdana="";
        $.each(jdata, function (i, el) {
            $('#' + divname).append("<li><a href='#" + el.id + "'>" + el.val + "</a></li>");
            htmlsumberdana += "<li><a href='#" + el.id + "'>" + el.val + "</a></li>";
        });
        $("#htmldropdownsdana").val(htmlsumberdana);
        //if (selvalue != '') $('#'+divname).val(selvalue)
    })
            .fail(function () {
                alert("error");
            })
            .always(function () {
                // alert("finished");
            });
}

</script>
