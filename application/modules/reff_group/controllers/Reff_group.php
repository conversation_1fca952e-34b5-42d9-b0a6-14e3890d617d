<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Reff_group extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        elseif ($this->session->users['id_user_group'] != 1) {
            echo  "<script>
                     alert('<PERSON>ks<PERSON> !!');
                     history.go(-1);
                   </script>";
         
        }
        $this->load->database();
        $this->load->helper('dtssp'); //datatable server side processing helper
    }

    public function index() {
        header("Access-Control-Allow-Origin: *");
        $data = array();
        /*         * keterangan parameter
          $this->template->load('default_layout', 'contents' , 'index', $data);
          1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
          2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
          3.index = nama view yang akan di load
         * */
        $title = "Akses";
        $js_file = $this->load->view('reff_group/js_file', '', true);
        $modal_filter = $this->load->view('reff_group/modal_filter', '', true);
        $modal_tambah = $this->load->view('reff_group/modal_tambah', '', true);
        $data = array("modal_filter" => $modal_filter,
            "modal_tambah" => $modal_tambah,
            "title" => $title,
        );
        $this->template->set('title', $title);
        $this->template->set('jv_script', $js_file);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }

    public function ssp() {
        $table = 'v_group_module';
        $primaryKey = 'kode_module';
        $columns = array(
            array('db' => 'nama_module', 'dt' => 0),
            array('db' => 'url', 'dt' => 1),
            array('db' => 'nama', 'dt' => 2),
            array('db' => 'kode_module', 'dt' => 3),
            array('db' => 'id', 'dt' => 4),
            array('db' => 'id_user_group', 'dt' => 5)
        );

        datatable_ssp($table, $primaryKey, $columns);
    }

    public function addform() {

        $param = $this->input->post('formData');
        $cek = $this->db->get_where('group_modules', $param);
        if ($cek->num_rows() > 0) {
            echo "Data tersebut sudah ada";
        } else {
            $res = $this->db->insert('group_modules', $param);
            if (!$res) {
                echo "Gagal";
            } else {
                echo "Berhasil Menambah Data";
            }
        }
    }

    public function editform() {

        $param = $this->input->post('formData');

            $id = $param["id"];
            unset($param ['id']);
            $this->db->where('id', $id);
            $res = $this->db->update('group_modules', $param);

            //echo $this->db->last_query();

            if (!$res) {
                echo "Gagal";
            } else {
                echo "Berhasil Merubah Data";
            }
        
    }

    public function deleteform() {

        $id = $this->input->post('id');

        $this->db->where('id', $id);
        $res = $this->db->delete('group_modules');

        // echo $this->db->last_query();

        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

    public function listing() {
        $url = "http://localhost:12892/referensi/fraksi/listall";
        echo $this->get_data_module($url);
    }

    function get_response() {

        $url = '************:12890/testdata/listuser'; //url di set global

        $ch = curl_init($url);

        $header = [];
        $header[] = 'Content-type: application/json';
        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
        $header[] = "Cache-Control: no-cache";
        $header[] = "accept-encoding:*";
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);

        if (!$result) {
            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
            die("Koneksi Gagal");
        } else {
            return $result;
            //print_r($result);
        }
    }
}
