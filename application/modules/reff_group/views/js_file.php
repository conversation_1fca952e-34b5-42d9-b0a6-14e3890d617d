<script>
    var xhrdata = null;
    var table = null;
    function clear_input() {
        $("#formData :input").val("");
    }

    function listing() {
        table = $('#table_id').DataTable({
            "draw": 0,
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3]}],
            //"columns": [
            //{ "name": "kd_prov_irmsv3" },
            //{ "name": "kd_prov_rkakl" },
            //{ "name": "nama_dapil" },
            //{ "name": "kd_dapil" }
            //],
            "order": [[2, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>/reff_group/ssp"},
            "aoColumnDefs": [
                {
                    "aTargets": [3],
                    "mRender": function (data, type, row) {

                        var id = row[4];


                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "<i class='fa fa-pencil'>",
                            "</i>",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fast backward ui icon'></i>",
                    "last": "<i class='fast forward ui icon'></i>",
                    "next": "<i class='step forward ui icon'></i>",
                    "previous": "<i class='step backward ui icon'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();

            console.log(xhrdata);
        });
        //});


    }


    function dtEditRow(id) {
//   / alert(id);

       console.log(xhrdata);

        var data_selected = xhrdata.data.filter(x => x[4] == id)[0];  //46

        var waydata = {
            id: data_selected[4],
            kode_module: data_selected[3],
            id_user_group: data_selected[5]
        }

        way.set('formData', waydata);


        $('#modalTitle').text('Edit Data');
        $('#modeform').val('edit');
        $('#modal-tambah').modal('show');
    }


    function dtTambahRow() {
      $('#frm-tambah')[0].reset();

        $('#modalTitle').text('Tambah ');
        $('#modeform').val('tambah');
        $('#modal-tambah').modal('show');
    }


    function dtDeleteRow(id) {
        //console.log('deleting ' + id);

        url = "<?php echo base_url();?>/reff_group/deleteform";

        //console.log(url);

        var r = confirm("Data yang anda pilih akan kami hapus!, apakah anda yakin mau melanjutkan");
        if (r == true) {
            $.post(url, {id: id}).done(function (data) {
//                console.log(data);

                table.ajax.reload();
                // clear_input();

            })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                        alert("finished");
                    });
        }


    }


    function simpanForm() {
        var mode = $('#modeform').val();
        
     var id = $('#id_user_group').val();
        var url;
        var serializeData = way.get('formData');
        // console.log(serializeData);

        console.log(id);
        if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>reff_group/addform";
            serializeData.id_sub_user_group =0;
            serializeData.id_sub_sub_user_group =0;

        } else if (mode == 'edit') {
            url = "<?php echo base_url(); ?>reff_group/editform"; 
        }

                 var params = {"formData": serializeData};
        // way.set('formData', serializeData);        
                 $.post(url, params).done(function (data) {
//                    console.log(data)
                    table.ajax.reload();

                    alert(data);
                });
//                .fail(function () {
//                    alert("error");
//                })
//                .always(function () {
//                    // alert("finished");
//                });


    }

    $(document).ready(function () {
        listing();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        //bind_modul();
        //bind_level();
        initCombobox('kode_module', 31);
        initCombobox('id_user_group', 27);
    });



</script>
