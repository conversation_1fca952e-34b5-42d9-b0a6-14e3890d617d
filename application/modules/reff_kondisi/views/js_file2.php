<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var table = null;

    function close_alert() {
        $("#alert_information").css({display: "none"});
    }
    
    function listing3() {
        table = $('#table_id').DataTable({
            "draw": 0,
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>jnsbmn/ssp", data: function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
                // d.thang = $("#fthang").val();
            }},
            "aoColumnDefs": [
                // {
                //     "aTargets": [7],
                //     "visible" : false
                // },  
                {
                    "aTargets": [3],
                    "mRender": function (data, type, full) {
                        var id = full[2];
                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "<i class='fa fa-pencil'>",
                            "</i>",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>",
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fast backward ui icon'></i>",
                    "last": "<i class='fast forward ui icon'></i>",
                    "next": "<i class='step forward ui icon'></i>",
                    "previous": "<i class='step backward ui icon'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            },
            "responsive": true
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
        });

        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
    }
    
    function dtEditRow(id) {
        var data_selected = xhrdata.data.filter(x => x[2] === id)[0];
        alert(data_selected[1])
        var thangchoose = 2021;
        // updateComboboxAndSelectedWoThang("thang", 21, thangchoose);
        refreshComboboxOutput6WoThang('kd_prov', 6, 'tahun', 2021, data_selected[6]);
        refreshComboboxOutput6WoThang('kd_kabkot', 7, 'kd_prov', data_selected[6], data_selected[7]);
        refreshComboboxOutput6WoThang('kd_kecamatan', 8, 'kd_kabkot', data_selected[7], data_selected[8]);
     
        $("#frm-tambah #kelurahan").val(data_selected[3]);
        $("#frm-tambah #id").val(data_selected[9]);
    
            //$("#kd_kab_kota_bpiw").html(prov.);
            $("#kd_kel").html(data_selected[8]);
        
              

        $("#frm-tambah #kd_kec2").val( data_selected[4] !== null ? data_selected[3].substring(7,10) : '');
        
        $("#modalTitle").text("Edit Kelurahan");
        $("#modeform").val("edit");
        $("#btn-simpan").show();
        $('#modal-tambah').modal('show');
    }

    function simpanForm() {
        var mode = $('#modeform').val();
        var url;

        var kd_prov = $("#frm-tambah #kd_prov").val();
        // var kd_kab_kota_bpiw = $("#frm-tambah #kd_kab_kota_bpiw").html() + $("#frm-tambah #kd_kab_kota_bpiw2").val();
        //var id_kabkot = $("#frm-tambah #id_kabkot").val();
        var kelurahan = $("#frm-tambah #kelurahan").val();
        var kd_kec = $("#frm-tambah #kd_kecamatan").val();
        // var kd_kab_irmsv3 = $("#frm-tambah #kd_kab_irmsv3").html() + $("#frm-tambah #kd_kab_irmsv32").val();
        // var kd_kab_rams = $("#frm-tambah #kd_kab_rams").html() + $("#frm-tambah #kd_kab_rams2").val();
        var kd_lur = $("#frm-tambah #kd_kel").html() + $("#frm-tambah #kd_kel2").val();
        // var kd_kab_rkakl = $("#frm-tambah #kd_kab_rkakl").html() + $("#frm-tambah #kd_kab_rkakl2").val();

        var data = {
            "id": $("#id").val(),
            "tahun": 2021,
            "kd_prov": kd_prov,//kd_prov,
            // "kd_kab_kota_bpiw": kd_kab_kota_bpiw,
            "kd_lurah": kd_lur,
            "kd_kec": kd_kec,
            // "kd_kab_irmsv3": kd_kab_irmsv3,
            // "kd_kab_rams": kd_kab_rams,
            // "kd_kab_bps": kd_kab_bps,
            // "kd_kab_rkakl": kd_kab_rkakl,
            "nama_lurah": kelurahan
        };

        if (mode === 'tambah') {
            url = "<?php echo base_url(); ?>jnsbmn/addform";
        } else if (mode == 'edit') {
            url = "<?php echo base_url(); ?>jnsbmn/editform";
        }
        var params = {"formData": data, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        if ($(".help-block").text() != "" || $("#kabkota").val() == "" || $("#kd_prov").val() == "") {
            alert("Insert data gagal!, Harap periksa dan melengkapi semua pengisian");
        } else {
            $.post(url, params)
                    .done(function (data) {
                        var list = $('#table_id').DataTable();
                        list.ajax.reload();
                        //table.ajax.reload();
                        $("#alert-content").empty();
                        $("#alert-content").append(data);
                        $("#alert_information").css({display: "block"});
                        setTimeout(close_alert, 3000);
                    })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                    });
            $("#modal-tambah").modal("hide");
        }
    }
    
    function get_prov(prov, thang) {
        var x = null;
        $.ajax({
            url: "<?php echo base_url('jnsbmn/get_prov') ?>" + "/" + prov + "/" + thang,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            async: false,
            success: function (data) {
                x = data;
            },
            failure: function (errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    
    function changeProv(el){
        var thang = $("#thang").val();
        // var prov = get_prov(el.value, 2021);
        refreshComboboxString('kd_kabkot', 7, 'kd_prov', el.value,);
        
            //$("#kd_kab_kota_bpiw").html(prov.);
            $("#kd_kec").html(el.value);
         
    }
    function changeKab(el){
             refreshComboboxString('kd_kecamatan', 8, 'kd_kabkot', el.value,);
            $("#kd_kec").html(el.value);
         
    }
    function changeKec(el){
            $("#kd_kel").html(el.value);
         
    }
    
    function dtDeleteRow(id) {
        //console.log('deleting ' + id);
        url = "<?php echo base_url(); ?>jnsbmn/deleteform";
        var r = confirm("Data yang anda pilih akan kami hapus!, apakah anda yakin mau melanjutkan");
        if (r == true) {
            $.post(url, {id: id, thang: 2021, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"}).done(function (data) {
                $("#alert-content").empty();
                $("#alert-content").append("<h4 style='color:green;'><i class='fa fa-check'></i> Berhasil Menghapus Data<h4>");
                $("#alert_information").css({display: "block"});
                table.ajax.reload();
                setTimeout(close_alert, 3000);
            })
            .fail(function () {
                alert("error");
            })
            .always(function () {
                //alert("finished");
            });
        }
    }
    
    function changeThang(el){
        refreshComboboxOutput6WoThang('kd_prov', 55, 'thang', el.value);
    }
    
    function tambah() {
        $("#id_kabkot").prop('disabled', false);
        $("input").val("");
        //initCombobox("kd_prov", 25);
        var thangchoose = $("#fthang").val();
        updateComboboxAndSelectedWoThang("thang", 21, thangchoose);
        refreshComboboxOutput6WoThang('kd_prov', 6, 'tahun', 2021);
        $("#kab_kota").val("");
        $("#kd_kabkot").html("");
        $("#kd_kecamatan").html("");
        $("#kd_kab_irmsv3").html("");
        $("#kd_kab_rams").html("");
        $("#kd_kab_bps").html("");
        $("#kd_kab_rkakl").html("");
        $("#modeform").val("tambah");
        $('#modalTitle').text('Tambah Data');
        $("#modal-tambah").modal("show");
    }

//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
    function initCombobox(divname, refindex) {
        url = "<?php echo base_url('/lookup/fieldlook/'); ?>" + refindex;

        $.get(url).done(function (data) {
            jdata = JSON.parse(data);
            $('#' + divname).empty();
            $('#' + divname).append(new Option("--Pilih--", -1));
            $.each(jdata, function (i, el) {
                $('#' + divname).append(new Option(el.val, el.id));
            });

            //if (selvalue != '') $('#'+divname).val(selvalue)
        })
                .fail(function () {
                    alert("error");
                })
                .always(function () {
                    // alert("finished");
                });
    }
    function updateCombobox(divname, refindex, selvalue) {
        url = "<?php echo base_url(); ?>lookup/fieldlook/" + refindex;

        $.get(url).done(function (data) {
            jdata = JSON.parse(data);
            $('#' + divname).empty();
            $.each(jdata, function (i, el) {
                $('#' + divname).append(new Option(el.val, el.id));
            });

            if (selvalue != '')
                $('#' + divname).val(selvalue)
        })
                .fail(function () {
                    alert("error");
                })
                .always(function () {
                });
    }




//divname: id div tempat si select berada (combobox)
//refindex: id referensi yang akan digunakan untuk mengisi combobox, contoh: r_dapil --> 1
    var html_select_peran = [
        "<div class='form-group' id='form-peran'>",
        "<label for='exampleInputPassword1'>Peran</label>",
        "<select onchange='getChildren(this)' class='form-control' id='id_user_group'>",
        "<option>--Pilih--</option>",
        "</select>",
        "</div>",
    ].join("\n");


    var BaseFormValidation = function () {

        // Init Material Forms Validation, for more examples you can check out https://github.com/jzaefferer/jquery-validation
        var initValidationMaterial = function () {
            jQuery('.js-validation-material').validate({
                ignore: [],
                errorClass: 'help-block text-right animated fadeInDown',
                errorElement: 'div',
                errorPlacement: function (error, e) {
                    jQuery(e).parents('.form-group > div').append(error);
                },
                highlight: function (e) {
                    var elem = jQuery(e);

                    elem.closest('.form-group').removeClass('has-error').addClass('has-error');
                    elem.closest('.help-block').remove();
                },
                success: function (e) {
                    var elem = jQuery(e);

                    elem.closest('.form-group').removeClass('has-error');
                    elem.closest('.help-block').remove();
                },
                rules: {
                    'val-username2': {
                        required: true,
                        minlength: 3
                    },
                    'val-email2': {
                        required: true,
                        email: true
                    },
                    'val-password2': {
                        required: true,
                        minlength: 5
                    },
                    'val-confirm-password2': {
                        required: true,
                        equalTo: '#val-password2'
                    },
                    'val-select22': {
                        required: true
                    },
                    'val-select2-multiple2': {
                        required: true,
                        minlength: 2
                    },
                    'val-suggestions2': {
                        required: true,
                        minlength: 5
                    },
                    'val-skill2': {
                        required: true
                    },
                    'val-currency2': {
                        required: true,
                        currency: ['$', true]
                    },
                    'val-website2': {
                        required: true,
                        url: true
                    },
                    'val-phoneus2': {
                        required: true,
                        phoneUS: true
                    },
                    'val-digits2': {
                        required: true,
                        digits: true
                    },
                    'val-number2': {
                        required: true,
                        number: true
                    },
                    'val-range2': {
                        required: true,
                        range: [1, 5]
                    },
                    'val-terms2': {
                        required: true
                    }
                    ,
                    'id_kabkot': {
                        required: true,
                        number: true
                    }
                    ,
                    'provinsi': {
                        required: true,
                    }
                    ,
                    'kabkota': {
                        required: true,
                    }
                    ,
                    'kd_prov': {
                        required: true
                    }
                    ,
                    'kd_kab_kota_bpiw': {
                        number: true
                    }
                    ,
                    'kd_kab_irmsv3': {
                        number: true
                    }
                    ,
                    'kd_kab_rams': {
                        number: true
                    }
                    ,
                    'kd_kab_bps': {
                        number: true
                    }
                    ,
                    'kd_kab_rkakl': {
                        number: true
                    }
                },
                messages: {
                    'val-username2': {
                        required: 'Please enter a username',
                        minlength: 'Your username must consist of at least 3 characters'
                    },
                    'val-email2': 'Please enter a valid email address',
                    'val-password2': {
                        required: 'Please provide a password',
                        minlength: 'Your password must be at least 5 characters long'
                    },
                    'val-confirm-password2': {
                        required: 'Please provide a password',
                        minlength: 'Your password must be at least 5 characters long',
                        equalTo: 'Please enter the same password as above'
                    },
                    'val-select22': 'Please select a value!',
                    'val-select2-multiple2': 'Please select at least 2 values!',
                    'val-suggestions2': 'What can we do to become better?',
                    'val-skill2': 'Please select a skill!',
                    'val-currency2': 'Please enter a price!',
                    'val-website2': 'Please enter your website!',
                    'val-phoneus2': 'Please enter a US phone!',
                    'val-digits2': 'Please enter only digits!',
                    'val-number2': 'Please enter a number!',
                    'val-range2': 'Please enter a number between 1 and 5!',
                    'val-terms2': 'You must agree to the service terms!'
                }, 'id_kabkot2': {
                    required: 'Please enter a username',
                    minlength: 'Your username must consist of at least 3 characters'
                },
                'id_kabkot': {
                    required: 'Please enter a ID Kabupupaten/kota'
                }
                ,
                'provinsi': {
                    required: 'Please enter a Provinsi'
                }
                ,
                'kabkota': {
                    required: 'Please enter a Kabupaten Kota'
                },
                'kd_prov': {
                    required: 'Please enter a Kode Provinsi',
                },
                'kd_prov': 'Please enter a number!',
                'kd_kab_kota_bpiw': 'Please enter a number!',
                'kd_kab_bps': 'Please enter a number!',
                'kd_kab_rkakl': 'Please enter a number!',
                'kd_kab_rams': 'Please enter a number!'
            });
        };

        return {
            init: function () {
                // Init Bootstrap Forms Validation

                // Init Material Forms Validation
                initValidationMaterial();

                // Init Validation on Select2 change
                jQuery('.js-select2').on('change', function () {
                    jQuery(this).valid();
                });
            }
        };
    }();

// Initialize when page loads
    jQuery(function () {
        BaseFormValidation.init();
    });
    
    $(document).ready(function () {
        // updateComboboxAndSelectedWoThang('fthang', 21, thangs);
        listing3();
        //initCombobox("kd_prov", 25);
        //bind_provinsi_tambah();
        $('#fthang').change( function() {
            table.draw();
        } );
    });
</script>
