<script src="https://cdn.datatables.net/1.11.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/0.5.0-beta4/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.4/jspdf.min.js"></script>

<script>
var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
var xhrdata = null;
var table = null;
var dataTableData
var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
var roledesc = "<?php echo $this->session->users['role']; ?>";

function clear_input() {
    $("#frm-tambah :input").val("");
}

function listing() {

    table = $('#dt-server-processing').DataTable({
        "draw": 0,
        scrollCollapse: true,
        // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
        "order": [
            [0, "asc"]
        ],
        "processing": false,
        "serverSide": false,
        "ajax": {
            type: "POST",
            url: "<?php echo base_url();?>risalah_ptp/ssp_paket",
            "data": function(d) {
                d.<?php echo $this->security->get_csrf_token_name();?> =
                    "<?php echo $this->security->get_csrf_hash();?>",
                    d.kd_prov = $('#ikd_prov').val(),
                    d.tahun_data = $('#itahun_data').val()

            }
        },

        "columnDefs": [{
                "aTargets": [0],
                "mRender": function(data, type, full, meta) {
                    // console.log(full)
                    return meta.row + meta.settings._iDisplayStart + 1;
                }
            },
            {
                "aTargets": [1],
                "mRender": function(data, type, full) {
                    return full[5]
                }
            },
            {
                "aTargets": [2],
                "mRender": function(data, type, full) {
                    return full[6];
                }
            },
            {
                "aTargets": [3],
                "mRender": function(data, type, full) {
                    return full[7];
                }
            },
            {
                "aTargets": [4],
                "mRender": function(data, type, full) {
                    return full[8];
                }
            },
            {
                "aTargets": [5],
                "mRender": function(data, type, row) {
                    var id = row[0];
                    var html_button = [
                        '<a href="<?=base_url()?>risalah_ptp/export_pdf/' + id +
                        '" target="_blank" class="btn btn-warning btn-xs">Export PDF</a>',
                        "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                        "Edit",
                        "</button>",
                        "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                        "Hapus",
                        "</button>",
                        // "<button onclick= dtUpload('" + id + "') class='btn btn-warning btn-xs'>",
                        // "Upload",
                        // "</button>",
                        //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                    ].join("\n");
                    return html_button;
                }
            }
            // {
            //     "aTargets": [5],
            //     "mRender": function (data, type, full) {
            //         return '';
            //     }
            // },
            // {
            //     "aTargets": [5],
            //     "mRender": function (data, type, full) {
            //         return full[5];
            //     }
            // },
            // {
            //     "aTargets": [5],
            //     "mRender": function (data, type, full) {
            //         return full[1];
            //     }
            // },
            // {
            //     "aTargets": [6],
            //     "mRender": function (data, type, full) {
            //         return full[3];
            //     }
            // },
            // {
            //     "aTargets": [7],
            //     "mRender": function (data, type, full) {
            //         return full[9];
            //     }
            // },
            // {
            //     "aTargets": [7],
            //     "mRender": function (data, type, full) {
            //         return full[];
            //     }
            // },
            // {
            //     "aTargets": [9],
            //     "mRender": function (data, type, full) {
            //         return full[10];
            //     }
            // },
            // {
            //     "aTargets": [5],
            //     "mRender": function (data, type, row) {
            //         var id = row[0];
            //         var html_button = [
            //             "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
            //             "Edit",
            //             "</button>",
            //             "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
            //             "Hapus",
            //             "</button>",
            //             // "<button onclick= dtUpload('" + id + "') class='btn btn-warning btn-xs'>",
            //             // "Upload",
            //             // "</button>",
            //             //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
            //         ].join("\n");
            //         return html_button;
            //     }
            // }
        ],
        // "ColumnFilterWidgets": {
        //    "aiExclude": [ 1 ],
        //    "sSeparator": '<br>'
        // },
        "language": {
            "decimal": "",
            "emptyTable": "Data tidak ditemukan",
            "info": "Data _START_ s/d _END_ dari _TOTAL_",
            "infoEmpty": "Tidak ada data",
            "infoFiltered": "(tersaring dari _MAX_)",
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": "_MENU_  data per halaman",
            "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
            "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
            "search": "Cari:",
            "zeroRecords": "Tidak ada data ditemukan",
            // "paginate": {
            //     "first": "<i class='fast backward ui icon'></i>",
            //     "last": "<i class='fast forward ui icon'></i>",
            //     "next": "<i class='step forward ui icon'></i>",
            //     "previous": "<i class='step backward ui icon'></i>"
            // },
            "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                "sortDescending": ": aktifkan untuk mengurutkan turun"
            }
        }
    });

    table.on('xhr', function() {
        xhrdata = table.ajax.json();
        //console.log(xhrdata);
    });
    //});
}

function dtDeleteRow(id) {
    if (confirm('Yakin untuk menghapus data ini?')) {

        // var url = "<?php echo base_url(); ?>" + "pemegang_saham/ajax_delete/" + id + "/" + yearnow;
        var url = "<?php echo base_url(); ?>" + "risalah_ptp/ajax_delete/" + id;
        var params = {
            "formData": {},
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
        };
        $.post(url, params)
            .done(function(data) {

                var table = $('#dt-server-processing').DataTable();
                Swal.fire(
                    'Sukses!',
                    'Data Berhasil dihapus!',
                    'success'
                )
                table.ajax.reload();
            })
            .fail(function() {
                alert("error");
            })
    }
}




function dtEditRow(id) {

    function start(callback) {
        $("#modal-edit").modal("show");

        data_selected = xhrdata.data.filter(x => x[0] == id)[0];

        $("#xkd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
            refreshSelectboot2('xdasar_kabkota1', 20, 'xkd_prov', this.value);
            console.log(this.value)
        });

        $('#xid').val(id);
    }


    function a() {
        var prov = data_selected[10].slice(0, 2)

        initCombobox('xkd_prov', 19);
        refreshSelectboot2('xdasar_kabkota1', 20, 'kd_prov', prov);
        refreshSelectboot2('xlokasi_surat', 20, 'kd_prov', prov);
        refreshSelectboot2('xmohon_kdcamat', 21, 'kd_kabkot', data_selected[10]);
        refreshSelectboot2('xmohon_kdlurah', 25, 'kd_camat', data_selected[22]);

    };

    function b() {


        $('body div#modal-edit.modal').one('shown.bs.modal', function(e) {
            $('#xkd_prov').val(data_selected[10].slice(0, 2)).selectpicker('refresh');
            $('#xdasar_kabkota1').val(data_selected[10]).selectpicker('refresh');
            $('#xmohon_kdcamat').val(data_selected[22]).selectpicker('refresh');
            $('#xmohon_kdlurah').val(data_selected[21]).selectpicker('refresh');
            $('#xlokasi_surat').val(data_selected[10]).selectpicker('refresh');


            $("#xkd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('xdasar_kabkota1', 20, 'kd_prov', this.value);
                refreshSelectboot2('xdasar_kabkota2', 20, 'kd_prov', this.value);
                refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', this.value);
                refreshSelectboot2('xlokasi_surat', 20, 'kd_prov', this.value);
            });
            $("#xdasar_kabkota1").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('xmohon_kdcamat', 21, 'kd_kabkot', this.value);
            });
            $("#xmohon_kdcamat").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('xmohon_kdlurah', 25, 'kd_camat', this.value);
            });
        });


        $(".decformat").keyup(function(event) {
            if (event.which >= 37 && event.which <= 40)
                return;
            // format number
            $(this).val(function(index, value) {
                return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            });
        });



        $('#xnomor').val(data_selected[2]);
        $('#xtanggal').val(data_selected[3]);
        $('#xdasar_tgl').val(data_selected[4]);
        $('#xdasar_nama').val(data_selected[5]);
        $('#xdasar_nik').val(data_selected[6]);
        $('#xdasar_nib').val(data_selected[7]);
        $('#xdasar_alamat').val(data_selected[8]);
        $('#xdasar_an').val(data_selected[9]);
        $('#xdasar_kabkota1').val(data_selected[10]);
        $('#xdasar_kabkota2').val(data_selected[11]);
        $('#xdasar_noperda').val(data_selected[12]);
        $('#xdasar_thnperda').val(data_selected[13]);
        $('#xdasar_noba1').val(data_selected[14]);
        $('#xdasar_tglba1').val(data_selected[15]);
        $('#xdasar_noba2').val(data_selected[16]);
        $('#xdasar_tglba2').val(data_selected[17]);
        $('#xmohon_jalan').val(data_selected[18]);
        $('#xmohon_nomor').val(data_selected[19]);
        $('#xmohon_rtrw').val(data_selected[20]);
        // $('#xmohon_kdlurah').val(data_selected[21]);
        // $('#xmohon_kdcamat').val(data_selected[22]);
        $('#xmohon_kdkabkot').val(data_selected[23]);
        $('#xmohon_luas_m2').val(data_selected[24]);
        $('#xmohon_g').val(data_selected[25]);
        $('#xmohon_gq').val(data_selected[26]);
        $('#xmohon_rcng').val(data_selected[27]);
        $('#xmohon_kdkbli').val(data_selected[28]);
        $('#xmohon_fkws').val(data_selected[29]);
        $('#xmohon_kws1').val(data_selected[30]);
        $('#xmohon_kws2').val(data_selected[31]);
        $('#xmohon_kws3').val(data_selected[32]);
        $('#xmohon_kws4').val(data_selected[33]);
        $('#xmohon_kws5').val(data_selected[34]);
        $('#xluas_kws1m2').val(data_selected[35]);
        $('#xluas_kws1persen').val(data_selected[36]);
        $('#xluas_kws2m2').val(data_selected[37]);
        $('#xluas_kws2persen').val(data_selected[38]);
        $('#xluas_kws3m2').val(data_selected[39]);
        $('#xluas_kws3persen').val(data_selected[40]);
        $('#xluas_kws4m2').val(data_selected[41]);
        $('#xluas_kws4persen').val(data_selected[42]);
        $('#xluas_kws5m2').val(data_selected[43]);
        $('#xluas_kws5persen').val(data_selected[44]);
        $('#xmohon_kesesuaian').val(data_selected[45]);
        $('#xlongitude').val(data_selected[46]);
        $('#xlatitude').val(data_selected[47]);
        $('#xsimpul_sedia_luasm2').val(data_selected[48]);
        $('#xsimpul_sedia_luaspersen').val(data_selected[49]);
        $('#xsimpul_total_tdksedia_luasm2').val(data_selected[50]);
        $('#xsimpul_total_tdksedia_luaspersen').val(data_selected[51]);
        $('#xsimpul_tdksedia_ilok_luasm2').val(data_selected[52]);
        $('#xsimpul_tdksedia_ilok_luaspersen').val(data_selected[53]);
        $('#xsimpul_tdksedia_skpl_luasm2').val(data_selected[54]);
        $('#xsimpul_tdksedia_skpl_luaspersen').val(data_selected[55]);
        $('#xsimpul_tdksedia3_ket').val(data_selected[56]);
        $('#xsimpul_tdksedia3_luasm2').val(data_selected[57]);
        $('#xsimpul_tdksedia3_luaspersen').val(data_selected[58]);
        $('#xsimpul_tdksedia4_ket').val(data_selected[59]);
        $('#xsimpul_tdksedia4_luasm2').val(data_selected[60]);
        $('#xsimpul_tdksedia4_luaspersen').val(data_selected[61]);
        $('#xsimpul_tdksedia5_ket').val(data_selected[62]);
        $('#xsimpul_tdksedia5_luasm2').val(data_selected[63]);
        $('#xsimpul_tdksedia5_luaspersen').val(data_selected[64]);
        $('#xsimpul_total_sediasyarat_luasm2').val(data_selected[65]);
        $('#xsimpul_total_sediasyarat_luaspersen').val(data_selected[66]);
        $('#xsimpul_sediasyarat1_luasm2').val(data_selected[67]);
        $('#xsimpul_sediasyarat1_luaspersen').val(data_selected[68]);
        $('#xsimpul_sediasyarat2_luasm2').val(data_selected[69]);
        $('#xsimpul_sediasyarat2_luaspersen').val(data_selected[70]);
        $('#xsimpul_sediasyarat3_luasm2').val(data_selected[71]);
        $('#xsimpul_sediasyarat3_luaspersen').val(data_selected[72]);
        $('#xsimpul_sediasyarat4_ket').val(data_selected[73]);
        $('#xsimpul_sediasyarat4_luasm2').val(data_selected[74]);
        $('#xsimpul_sediasyarat4_luaspersen').val(data_selected[75]);
        $('#xsimpul_sediasyarat5_ket').val(data_selected[76]);
        $('#xsimpul_sediasyarat5_luasm2').val(data_selected[77]);
        $('#xsimpul_sediasyarat5_luaspersen').val(data_selected[78]);
        $('#xsimpul_ketentuan_gq1').val(data_selected[79]);
        $('#xsimpul_ketentuan_gq2').val(data_selected[80]);
        $('#xsimpul_ketentuan_gq3').val(data_selected[81]);
        $('#xsimpul_ketentuan_gq4').val(data_selected[82]);
        $('#xsimpul_ketentuan_gq5').val(data_selected[83]);
        $('#xsimpul_ketentuan_gp1').val(data_selected[84]);
        $('#xsimpul_ketentuan_gp2').val(data_selected[85]);
        $('#xsimpul_ketentuan_gp3').val(data_selected[86]);
        $('#xsimpul_ketentuan_gp4').val(data_selected[87]);
        $('#xsimpul_ketentuan_gp5').val(data_selected[88]);
        $('#xsimpul_ketentuan_hat1').val(data_selected[89]);
        $('#xsimpul_ketentuan_hat2').val(data_selected[90]);
        $('#xsimpul_ketentuan_hat3').val(data_selected[91]);
        $('#xsimpul_ketentuan_hat4').val(data_selected[92]);
        $('#xsimpul_ketentuan_hat5').val(data_selected[93]);
        // $('#xlokasi_surat').val(data_selected[94]);
        $('#xtgl_surat').val(data_selected[95]);
        $('#xtim_ketua').val(data_selected[96]);
        $('#xnip_ketua').val(data_selected[97]);
        $('#xtim_sekretaris').val(data_selected[98]);
        $('#xnip_sekretaris').val(data_selected[99]);
        $('#xtim_member1').val(data_selected[100]);
        $('#xnip_member1').val(data_selected[101]);
        $('#xtim_member2').val(data_selected[102]);
        $('#xnip_member2').val(data_selected[103]);
        $('#xtim_member3').val(data_selected[104]);
        $('#xnip_member3').val(data_selected[105]);
        $('#xtim_member4').val(data_selected[106]);
        $('#xnip_member4').val(data_selected[107]);
        $('#xtim_member5').val(data_selected[108]);
        $('#xnip_member5').val(data_selected[109]);

        $.get("<?=base_url() ?>risalah_ptp/get_edit/" + id, {},
            function(data) {
                data = JSON.parse(data)
                afk = data.afk
                tt = data.tt
                tb = data.tb
                milik = data.milik
                manfaat = data.manfaat
                peralihan = data.peralihan
                anggota = data.anggota
                var xxnumAfk = 0;
                afk.forEach((e, k) => {
                    btn =
                        '<button type="button" onclick="xaddAfk()" class="btn btn-success" style="padding:10px"> + </button>'
                    if (k != 0) {
                        btn = '<button type="button" onclick="removeAdd(\'xxdivAfk' + k +
                            '\')" class="btn btn-danger" style="padding:10px"> - </button>'
                    }
                    var str = '<div id="xxdivAfk' + k +
                        '" class="col-md-11 offset-md-1"> <div class="form-group row"> <div class="col-md-5"> <div class="form-group row"> <div class="col-md-12"> <input type="text" class="form-control" name="xafk_kw[]" id="xxafk_kw' +
                        k + '" value="' + e.kawasan +
                        '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group row"> <div class="col-md-12"> <input type="text" class="form-control" name="xafk_luas[]" id="xxafk_luas' +
                        k + '" value="' + e.luasm2 +
                        '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group row"> <div class="col-md-12"> <input type="text" class="form-control" name="xafk_persen[]" id="xxafk_persen' +
                        k + '" value="' + e.persentase + '"> </div> </div> </div> <div class="col-md-1">' +
                        btn + ' </div></div> </div>';

                    k++
                    $('#xdivAfk').append(str)
                });

                var xxnumTt = 0
                tt.forEach((e, k) => {
                    btn =
                        '<button type="button" onclick="xaddTt()" class="btn btn-success" style="padding:10px"> + </button>'
                    if (k != 0) {
                        btn = '<button type="button" onclick="removeAdd(\'xxdivTt' + k +
                            '\')" class="btn btn-danger" style="padding:10px"> - </button>'
                    }
                    var str = '<div id="xxdivTt' + xxnumTt +
                        '" class="col-md-11 offset-md-1"> <div class="form-group row" style="padding-left:5%"> <div class="col-md-5"> <textarea class="form-control" name="xtt_ket[]" id="xtt_ket' +
                        xxnumTt + '" cols="30" rows="3">' + e.kawasan +
                        '</textarea> </div> <div class="col-md-3"> <input type="text" class="form-control" name="xtt_luas[]" id="xtt_luas' +
                        xxnumTt + '" value="' + e.luasm2 +
                        '"> </div> <div class="col-md-3"> <input type="text" class="form-control" name="xtt_persen[]" id="xtt_persen' +
                        xxnumTt + '" value="' + e.persentase + '"> </div><div class="col-md-1"> ' + btn +
                        '</div> </div> </div>';

                    xxnumTt++
                    $('#xdivTt').append(str)
                })
                var xxnumTb = 0
                $('#xdivTb').html('')
                tb.forEach((e, k) => {
                    btn =
                        '<button type="button" onclick="xaddTb()" class="btn btn-success" style="padding:10px"> + </button>'
                    if (k != 0) {
                        btn = '<button type="button" onclick="removeAdd(\'xxdivTb' + k +
                            '\')" class="btn btn-danger" style="padding:10px"> - </button>'
                    }
                    var str = '<div id="xxdivTb' + xxnumTb +
                        '" class="col-md-11 offset-md-1"> <div class="form-group row" style="padding-left:5%"> <div class="col-md-5"> <textarea class="form-control" name=xtb_ket[]" id="xtb_ket' +
                        xxnumTb + '" cols="30" rows="3">' + e.kawasan +
                        '</textarea> </div> <div class="col-md-3"> <input type="text" class="form-control" name="xtb_luas[]" id="xtb_luas' +
                        xxnumTb + '" value="' + e.luasm2 +
                        '"> </div> <div class="col-md-3"> <input type="text" class="form-control" name="xtb_persen[]" id="xtb_persen' +
                        xxnumTb + '" value="' + e.persentase + '"> </div><div class="col-md-1"> ' + btn +
                        '</div> </div> </div>';

                    xxnumTb++
                    $('#xdivTb').append(str)
                })

                $('#xdivMilik').html('')
                milik.forEach((e, k) => {
                    btn =
                        '<button type="button" onclick="xaddMilik()" class="btn btn-success" style="padding:10px"> + </button>'
                    if (k != 0) {
                        btn = '<button type="button" onclick="removeAdd(\'xxdivMilik' + k +
                            '\')" class="btn btn-danger" style="padding:10px"> - </button>'
                    }
                    var str = '<div class="row" id="xxdivMilik' + k +
                        '"><div  class="col-md-10 offset-md-1"> <textarea class="form-control" style="margin-bottom:20px" name="xmilik[]" id="xxmilik' +
                        k + '" cols="30" rows="3">' + e.keterangan +
                        '</textarea></div> <div class="col-md-1"> ' + btn + ' </div> </div>';

                    $('#xdivMilik').append(str)
                })

                $('#xdivManfaat').html('')
                manfaat.forEach((e, k) => {
                    btn =
                        '<button type="button" onclick="xaddManfaat()" class="btn btn-success" style="padding:10px"> + </button>'
                    if (k != 0) {
                        btn = '<button type="button" onclick="removeAdd(\'xxdivManfaat' + k +
                            '\')" class="btn btn-danger" style="padding:10px"> - </button>'
                    }
                    var str = '<div class="row" id="xxdivManfaat' + k +
                        '"><div  class="col-md-10 offset-md-1"> <textarea class="form-control" style="margin-bottom:20px" name="xmanfaat[]" id="xxmanfaat' +
                        k + '" cols="30" rows="3">' + e.keterangan +
                        '</textarea></div> <div class="col-md-1"> ' + btn + ' </div> </div>';

                    $('#xdivManfaat').append(str)
                })

                $('#xdivPeralihan').html('')
                peralihan.forEach((e, k) => {
                    btn =
                        '<button type="button" onclick="xaddPeralihan()" class="btn btn-success" style="padding:10px"> + </button>'
                    if (k != 0) {
                        btn = '<button type="button" onclick="removeAdd(\'xxdivPeralihan' + k +
                            '\')" class="btn btn-danger" style="padding:10px"> - </button>'
                    }
                    var str = '<div class="row" id="xxdivPeralihan' + k +
                        '"><div  class="col-md-10 offset-md-1"> <textarea class="form-control" style="margin-bottom:20px" name="xperalihan[]" id="xxperalihan' +
                        k + '" cols="30" rows="3">' + e.keterangan +
                        '</textarea></div> <div class="col-md-1"> ' + btn + ' </div> </div>';

                    $('#xdivPeralihan').append(str)
                })


                $('#xdivAnggota').html('')
                anggota.forEach((e, k) => {
                    btn =
                        '<button type="button" onclick="xaddAnggota()" class="btn btn-success" style="padding:10px"> + </button>'
                    if (k != 0) {
                        btn = '<button type="button" onclick="removeAdd(\'xxdivAnggota' + k +
                            '\')" class="btn btn-danger" style="padding:10px"> - </button>'
                    }
                    var str = '<div class="row" id="xxdivAnggota' + k +
                        '"><div class="col-md-11 offset-md-1"> <div class="form-group row"> <div class="col-md-6 "> <div class="form-group row"> <div class="col-md-12"> <input type="text" class="form-control" name="xanggota[]" id="xanggota' +
                        k +
                        '" value="' + e.nama +
                        '"> </div> </div> </div> <div class="col-md-4"> <div class="form-group row">  <div class="col-md-12"> <input type="text" class="form-control" name="xnip[]" id="xnip' +
                        k +
                        '" value="' + e.nip +
                        '"> </div> </div> </div> <div class="col-md-1"> <div class="form-group row">  <div class="col-md-12">' +
                        btn + '</div> </div> </div> </div> </div></div>';
                    $('#xdivAnggota').append(str)
                })

            }
        );
    }

    $.when($.ajax(start())).then(a()).then(b());
}

function refreshSelectpicker() {
    // removeSelectpicker('kd_kabkot')
    $('select').selectpicker('refresh')
}

function dtTambahRow() {

    const myTimeout = setTimeout(refreshSelectpicker, 100);
    $('#modal-tambah').modal('show');
    // $('#frm-tambah').trigger('reset');
    clear_input()
    initCombobox('kd_prov', 19);
    initCombobox('kd_kabkot', 20);
    // initCombobox('id_tema', 33);



    $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        refreshSelectboot2('dasar_kabkota1', 20, 'kd_prov', this.value);
        refreshSelectboot2('dasar_kabkota2', 20, 'kd_prov', this.value);
        refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
        refreshSelectboot2('lokasi_surat', 20, 'kd_prov', this.value);
    });
    $("#dasar_kabkota1").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        refreshSelectboot2('mohon_kdcamat', 21, 'kd_kabkot', this.value);
    });
    $("#mohon_kdcamat").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
        refreshSelectboot2('mohon_kdlurah', 25, 'kd_camat', this.value);
    });

    $(".decformat").keyup(function(event) {
        if (event.which >= 37 && event.which <= 40)
            return;
        // format number
        $(this).val(function(index, value) {
            return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        });
    });




}

function simpanForm() {

    if ($("#frm-tambah").valid() === true) {
        var xobj_usulan = {
            "kd_kabkot": $("#kd_kabkot").val(),
            "kd_prov": $("#kd_prov").val(),
            "kd_status": $("#kd_status").val(),
            "tahun_data": $("#tahun_data").val(),
            "luas": $("#luas").val(),
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
        };


        var url = "<?php echo base_url(); ?>" + "risalah_ptp/save_form";
        var params = {
            "formData": xobj_usulan,
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
        };
        $.post(url, params).done(function(data) {


                var table = $('#dt-server-processing').DataTable();
                table.ajax.reload();
                $('#modal-tambah').modal('hide');

            })
            .fail(function() {
                alert("error");
            });
    } else {
        alert("Data gagal disimpan, harap periksa informasi di setiap field isian");
        $(".error").css("color", "red");
    }
}

function updateForm() {
    var id = $("#xid").val();
    var data_selected = xhrdata.data.filter(x => x[0] == id)[0];

    if ($("#frm-edit").valid() === true) {

        var formData = new FormData(this);
        formData.append("<?php echo $this->security->get_csrf_token_name(); ?>",
            "<?php echo $this->security->get_csrf_hash(); ?>");
        var url = "<?php echo base_url("risalah_ptp/update_form") ?>";
        $.post(url, formData).done(function(data) {
                if (data == '1') {
                    alert('Data Sudah Ada');
                } else {
                    // var table = $('#dt-server-processing').DataTable();
                    // table.ajax.reload();
                    // $('#modal-edit').modal('hide');
                }
            })
            .fail(function() {
                alert("error");
            });
    } else {
        alert(
            "Data gagal diupdate, silahkan lihat informasi pada setiap field isian untuk memeriksa kesalahan entri data anda"
        );
        $(".error").css("color", "red");
    }
}

function convertTo(datastring) {
    var str = datastring;
    var s1 = str.replace('{', '[');

    return eval(s1.replace('}', ']'));
}

function bind_combo_induk(thang, selval) {
    var data = get_induk(thang);
    var objthang = JSON.parse(data);
    $("#kdinduk").empty();
    $('#kdinduk').append("<option value='' >" + "--Pilih--" + "</option>");
    for (var i = 0; i <= objthang.length - 1; i++) {
        $("#kdinduk").append("<option value=" + objthang[i].kdsatker + ">" + objthang[i].nmsatker + "</option>");
    }
    if (selval) {
        $("#kdinduk").val(selval);
    }
}

function get_induk(thang) {
    var x = null;
    $.ajax({
        type: "GET",
        async: false,
        url: "<?php echo base_url('risalah_ptp/get_induk/') ?>" + thang,
        success: function(response) {
            x = response;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return x;
}





function dtdetail(id) {
    //console.log(xhrdata);

    var data_selected = xhrdata.data.filter(x => x[9] === id)[0]; //46

    var waydata = {
        thang: $("#fthang").val(),
        ibukota: data_selected[2],
        kd_prov: data_selected[0],
        kd_prov_bps: data_selected[3],
        kd_prov_irmsv3: data_selected[4],
        kd_prov_krisna: data_selected[5],
        kd_prov_rams: data_selected[6],
        kd_prov_rkakl: data_selected[7],
        kd_prov_sipro: data_selected[8],
        nama_prov: data_selected[1]

    }

    way.set('formData', waydata);
    $('#modalTitle').text('Detail');
    //        $('#modeform').val('edit');
    $("input").prop('disabled', true);
    $("#hid").hide("slow");
    $("#hida").hide("slow");
    $('#modal-tambah').modal('show');
}






function close_alert() {
    $("#alert_information").css({
        display: "none"
    });
}

function bind_combo_thang(selval) {
    var data = get_thang();
    var objthang = JSON.parse(data);
    for (var i = 0; i <= objthang.length - 1; i++) {
        $("#fthang").append("<option value=" + objthang[i].thang + ">" + objthang[i].uraian + "</option>");
    }
    if (selval) {
        $("#fthang").val(selval);
    }
}

function get_thang() {
    var x = null;
    $.ajax({
        type: "GET",
        async: false,
        url: "<?php echo base_url('risalah_ptp/get_thang') ?>",
        success: function(response) {
            x = response;
        },
        failure: function(errMsg) {
            alert(errMsg);
        }
    });
    return x;
}

function dtUpload(id) {
    // alert(id)
    $("#id_up").val(id);
    var tab = $('#table_id2').DataTable();
    tab.destroy()
    $('#table_id2 td').empty();
    listing_attachment2(id);
    $("#modal-download").modal("show");
}



var table_attachment = null;

function listing_attachment2(id) {

    // var data_selected = jalanjson.data.filter(x => x[0] == id)[0];
    //   var xiduser = role;
    // var xthang = data_selected[1];
    // if ($.fn.dataTable.isDataTable('#table_id2')) {

    //     table_attachment = $('#table_id2').DataTable();
    // } else {

    table_attachment = $('#table_id2').DataTable({

        "draw": 0,
        "columnDefs": [{
            "orderable": true,
            "targets": [0]
        }],
        "order": [
            [0, "desc"]
        ],
        "processing": true,
        "serverSide": true,
        "ajax": {
            type: "POST",
            url: "<?php echo base_url(); ?>risalah_ptp/ssp_attachment",
            data: function(d) {
                d.id = id;
                // d.role = role;
                d.<?php echo $this->security->get_csrf_token_name(); ?> =
                    "<?php echo $this->security->get_csrf_hash(); ?>";
            }
        },
        "columnDefs": [{
                "aTargets": [0],
                "mRender": function(data, type, full) {
                    var ico_class = get_extentsion_file(full[0]);
                    var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp";
                    var subs_img = full[0].substr(0, 6);
                    if (ico_class == "feather icon-image") {
                        html_icon =
                            "<a target='_blank' class='fancybox' rel='group' href='<?php echo base_url();?>uploads/" +
                            subs_img + "/" + full[0] +
                            "'><img class='img-responsive' style='width:40%;' src='<?php echo base_url();?>uploads/" +
                            subs_img + "/" + full[0] + "'></a>";
                    }
                    return html_icon + "<br>" + full[3];
                    // alert("ASd")
                }
            },
            {
                "aTargets": [1],
                "mRender": function(data, type, full) {
                    console.log("full attachment");
                    console.log(full);
                    var data_full_attachment = full[0];
                    var dire = data_full_attachment.substr(0, 6);
                    var html_button = [
                        "<a target='_blank' class='btn btn-primary' href='<?php echo base_url(); ?>uploads/" +
                        dire + "/" + data_full_attachment +
                        "'><i class='feather icon-download'></i></a>",
                        "<a target='_blank' class='btn btn-danger' onclick=hapus_lampiran('" + full[
                            1] + "')> <i class='feather icon-trash-2'></i></a>",
                    ].join("\n");
                    return html_button;
                }
            }
        ],
        "language": {
            "decimal": "",
            "emptyTable": "Data tidak ditemukan",
            "info": "Data _START_ s/d _END_ dari _TOTAL_",
            "infoEmpty": "Tidak ada data",
            "infoFiltered": "(tersaring dari _MAX_)",
            "infoPostFix": "",
            "thousands": ",",
            "lengthMenu": "_MENU_  data per halaman",
            "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
            "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
            "search": "Cari:",
            "zeroRecords": "Tidak ada data ditemukan",
            "paginate": {
                "first": "<i class='fast backward ui icon'></i>",
                "last": "<i class='fast forward ui icon'></i>",
                "next": "<i class='step forward ui icon'></i>",
                "previous": "<i class='step backward ui icon'></i>"
            },
            "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                "sortDescending": ": aktifkan untuk mengurutkan turun"
            }
        }
    });
    table_attachment.on('xhr', function() {
        xhrdata1 = table_attachment.ajax.json();
        console.log(xhrdata1);
    });

    $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
}

// }


function get_extentsion_file(file) {
    var extension = file.substr((file.lastIndexOf('.') + 1));
    switch (extension) {
        case 'jpg':
        case 'png':
        case 'PNG':
        case 'jpeg':
        case 'gif':
        case 'JPG':
            return 'feather icon-image'; // There's was a typo in the example where
            break; // the alert ended with pdf instead of gif.
        case 'zip':
        case 'rar':
            //alert('was zip rar');
            return 'feather icon-archive';
            break;
        case 'pdf':
            return 'feather icon-file-text';
        case 'xlsx':
            return 'feather icon-file-text';
            break;
        default:
            return 'feather icon-file-text';

    }
}

function hapus_lampiran(id) {
    if (confirm('Yakin untuk menghapus data ini?')) {

        var url = "<?php echo base_url(); ?>" + "risalah_ptp/hps_lampiran/" + id;
        var params = {
            "formData": {},
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
        };
        $.post(url, params)
            .done(function(data) {
                var tlist_paket = $('#tlist_paket').DataTable();
                tlist_paket.ajax.reload()
                var tab = $('#table_id2').DataTable();
                tab.ajax.reload();;
            })
            .fail(function() {
                alert("error");
            })
    }
}


$(document).ready(function() {

    fetch('<?= base_url() ?>risalah_ptp/get_tema')
        .then(response => response.json())
        .then(data => {
            $("#id_tema").empty();
            $('#id_tema').append("<option selected valu='0' >" + "--Pilih--" + "</option>");
            for (var i = 0; i <= data.length - 1; i++) {
                $("#id_tema").append("<option value=" + data[i].kode_module + ">" + data[i].nama_module +
                    "</option>");
            }

            $("#id_tema").val(0);
            // $("#id_tema").selectpicker('refresh');

        })
        .catch(error => {
            // Handle errors
            console.error('Error:', error);
        });

    // bind_combo_thang(thangs);


    // var map = L.map('map').setView([-2.7521401146517785, 116.07226320582281], 5);
    // var osmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
    // // var gl = L.mapboxGL({
    // //     style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
    // // }).addTo(map)
    //     // a layer group, used here like a container for markers
    // var markersGroup = L.layerGroup();
    // map.addLayer(markersGroup);

    // map.on('click', function(e) {
    //     // get the count of currently displayed markers
    //     markersGroup.clearLayers();
    //     console.log(e.latlng.lat)
    //     $('#latitude').val(e.latlng.lat)
    //     $('#longitude').val(e.latlng.lng)

    //     var marker = L.marker(e.latlng).addTo(markersGroup);

    // });

    // initCombobox('ikd_prov', 19);
    // setTimeout(function() {
    //     $('#ikd_prov').selectpicker();
    //     $('#itahun_data').selectpicker();
    // }, 100);
    //     listing();
    $('.numberonly').keypress(function(e) {
        var charCode = (e.which) ? e.which : event.keyCode

        if (String.fromCharCode(charCode).match(/[^0-9]/g))

            return false;

    });
    $('.float-number').keypress(function(event) {
        if ((event.which != 46 || $(this).val().indexOf('.') != -1) && (event.which < 48 || event
                .which > 57)) {
            event.preventDefault();
        }
    });

    // $('#id_bujt, #id_ruas, #id_jnsdana, #id_refbank, #id_pt').select2({
    //     dropdownParent: $('#modal-tambah'),
    //     // width: 'resolve',
    //     theme: 'classic'
    // });

    // $('#xid_bujt, #xid_ruas, #xid_jnsdana, #xid_refbank, #xid_pt').select2({
    //     dropdownParent: $('#modal-edit'),
    //     theme: 'classic',
    // });

    // $('#xid_refbank, #xid_pt').select2({
    //     tags: true
    // });
    $('#frm-tambah').submit(function(e) {
        console.log('masuk sini')
        e.preventDefault();
        var formData = new FormData(this);
        formData.append("<?php echo $this->security->get_csrf_token_name(); ?>",
            "<?php echo $this->security->get_csrf_hash(); ?>");


        $.ajax({
            url: '<?php echo base_url(); ?>risalah_ptp/save_form',
            type: "post",
            data: formData,
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                var tab = $('#dt-server-processing').DataTable();
                tab.ajax.reload();
                // $('.modal').hide()
                Swal.fire(
                    'Sukses!',
                    'Data Tersimpan!',
                    'success'
                )
            }
        });
    });
    $('#frm-edit').submit(function(e) {
        console.log('masuk sini')
        e.preventDefault();
        var formData = new FormData(this);
        formData.append("<?php echo $this->security->get_csrf_token_name(); ?>",
            "<?php echo $this->security->get_csrf_hash(); ?>");


        $.ajax({
            url: '<?php echo base_url(); ?>risalah_ptp/update_form',
            type: "post",
            data: formData,
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                var tab = $('#dt-server-processing').DataTable();
                // tab.ajax.reload();
                // $('.modal').hide()
                // Swal.fire(
                //     'Sukses!',
                //     'Data Tersimpan!',
                //     'success'
                // )
            }
        });
    });

    $('body div#modal-tambah.modal').one('shown.bs.modal', function(e) {
        $(this).find('div.modal-content select').selectpicker();
    });


    $('body div#modal-edit.modal').one('shown.bs.modal', function(e) {
        $(this).find('div.modal-content select').selectpicker();
    });

    if (role == 7 || role == 8) {
        var kabkot = "<?=$this->session->users['kd_kabkot']?>"
        var url = '<?=base_url()?>lookup/getProvKab/' + kabkot
        $.get(url, function(data) {
            data = JSON.parse(data)
            var kab = data.kab
            var prov = data.prov
            var selectProv = document.getElementById("ikd_prov");
            selectProv.innerHTML = "";
            var options = document.createElement("option");
            options.text = prov.nama_prov;
            options.value = prov.kd_prov; // Change to the desired value
            selectProv.appendChild(options);
            $('#ikd_prov').selectpicker()
            var select = document.getElementById("ikd_kabkot");
            select.innerHTML = "";
            var option = document.createElement("option");
            option.text = kab.nama_kabkot;
            option.value = kab.kd_kabkot; // Change to the desired value
            select.appendChild(option);
            $('#ikd_kabkot').selectpicker()
            listing();
        });
    } else if (role == 9 || role == 10) {

        var prov = "<?=$this->session->users['kd_prov']?>"

        var url = '<?=base_url()?>peta2/getProv/' + prov
        $.get(url, function(data) {
            data = JSON.parse(data)
            console.log(data)
            var kab = data.kab
            var prov = data.prov
            var select = document.getElementById("ikd_prov");
            select.innerHTML = "";
            var option = document.createElement("option");
            option.text = prov.nama_prov;
            option.value = prov.kd_prov; // Change to the desired value
            select.appendChild(option);
            var select = document.getElementById("ikd_kabkot");
            select.innerHTML = "";
            var option = document.createElement("option");
            option.text = '-- Pilih Kab/kota -- ';
            option.value = ''; // Change to the desired value
            select.appendChild(option);
            $.each(kab, function(index, v) {
                var option = document.createElement("option");
                option.text = v.nama_kabkot;
                option.value = v.kd_kabkot; // Change to the desired value
                select.appendChild(option);
            });
            listing();
        });
    } else {

        listing();
        initCombobox('ikd_prov', 19);
        setTimeout(loadData, 500);
        $("#ikd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
            refreshSelectboot2('ikd_kabkot', 20, 'kd_prov', this.value);
            if (this.value == '') {
                $('#ikd_kabkot').empty();
                $('#ikd_kabkot').append(new Option("--Pilih--", ''));
                $('#ikd_kabkot').selectpicker('refresh');
            }
        });
    }

    // initCombobox('ikd_prov', 19);
    // setTimeout(loadData,500);
    // $("#ikd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
    //     refreshSelectboot2('ikd_kabkot', 20, 'kd_prov', this.value);
    // });

    // $('#dt-server-processing').on('click', 'tr', function () {
    //     // var table = $('#dt-server-processing').DataTable();
    //     // var row = table.row( this ).data();
    //     // console.log(data);
    //     var tr = $(this).closest('tr');
    //     var row = table.row(tr);

    //     if (row.child.isShown()) {
    //         // This row is already open - close it
    //         row.child.hide();
    //         tr.removeClass('shown');
    //     } else {
    //         // console.log(row.data()[0])
    //         var kdProv = row.data()[0];
    //         var tahun = row.data()[3];
    //         var layer = null;
    //         // Open this row
    //         $.get("<?php //echo base_url(); ?>risalah_ptp/getKab/" + kdProv+'/'+tahun+'/'+layer, function(data) {
    //             data = JSON.parse(data)
    //             // series = data;
    //         //    console.log(data['html']) 

    //             row.child(data['html']).show();
    //             tr.addClass('shown');
    //         });
    //     }
    // })


});

function filterChange(val) {
    loadData()
    $('#dt-server-processing').dataTable().fnClearTable();
    $('#dt-server-processing').dataTable().fnDestroy();
    // listing()
}

function loadData(params) {
    $('#ikd_prov').selectpicker();
    $('#ikd_kabkot').selectpicker();

}

function expline() {

    // Create a new jsPDF instance
    var doc = new jsPDF({
        orientation: 'landscape', // Set the orientation to 'landscape'
        unit: 'mm', // Set the unit to millimeters (you can change it to other units if needed)
        format: [297, 210] // Set custom landscape dimensions (width, height)
    });
    // Add content to the PDF
    doc.rect(20, 20, 10, 30); // (x, y, width, height)
    doc.rect(20, 20, 20, 30); // (x, y, width, height)
    doc.text('No', 20, 25); // Add text inside the bordered area
    doc.text('No', 30, 25); // Add text inside the bordered area
    // Generate the PDF as a data URI
    var pdfDataUri = doc.output('datauri');

    // Create a Blob from the data URI
    var pdfBlob = dataURItoBlob(pdfDataUri);

    // Create a URL for the Blob
    var pdfUrl = URL.createObjectURL(pdfBlob);

    // Open the PDF in a new tab
    window.open(pdfUrl, '_blank');
}

function exp() {
    var kd_prov = '00';
    var tahun_data = '00';
    if ($('#ikd_prov').val() != '') {
        kd_prov = $('#ikd_prov').val()
    }
    if ($('#itahun_data').val() != '') {
        tahun_data = $('#itahun_data').val()
    }
    var urlToOpen = "<?php echo base_url(); ?>risalah_ptp/export_pdf/";

    // Open the URL in a new tab
    window.open(urlToOpen, '_blank');
}

function expassass() {
    // Define the HTML element to convert
    // console.log(dataTableData)
    var str = '<tr>' +
        '<th rowspan="4" width="10%">No</th>' +
        '<th>ProvinsiSubjek PTP PKKPR Nonberusaha</th>' +
        '<th>PTP</th>' +
        '<th>Letak</th>' +
        '<th>Kegiatan</th>' +
        '<th>PKKPR Nonberusaha</th>' +
        '<th rowspan="4">Keterangan</th>' +
        '</tr>' +
        '<tr>' +
        '<th>a. Pemohon</th>' +
        '<th>(1) No PTP</th>' +
        '<th>a) Kab/Kota</th>' +
        '<th rowspan="3">Rencana kegiatan</th>' +
        '<th>1. No KKPR</th>' +
        '</tr>' +
        '<tr>' +
        '<th rowspan="2">b. Bertindak Atas Nama</th>' +
        '<th>(2) Tanggal PTP</th>' +
        '<th>b) Kecamatan</th>' +
        '<th>2. Tanggal KKPR</th>' +
        '</tr>' +
        '<tr>' +
        '<th>(3) Luas (m2)</th>' +
        '<th>c) Kelurahan/Desa</th>' +
        '<th>3. Luas (m2)</th>' +
        '</tr>'
    var no = 0
    $.each(dataTableData, function(index, value) {
        if (index < 10) {
            no++
            str += '<tr>' +
                '<td rowspan="3">' + no + '</td>' +
                '<td>a. ' + value[7] + '</td>' +
                '<td>(1) ' + value[15] + '</td>' +
                '<td>a) ' + value[22] + '</td>' +
                '<td rowspan="3">' + value[10] + '</td>' +
                '<td>1. -</td>' +
                '</tr>' +
                '<tr>' +
                '<td rowspan="2">b. - </td>' +
                '<td>(2) ' + value[16] + '</td>' +
                '<td>b) ' + value[21] + '</td>' +
                '<td>2. -</td>' +
                '</tr>' +
                '<tr>' +
                '<td>(3) ' + value[18] + '</td>' +
                '<td>c) ' + value[20] + '</td>' +
                '<td>3. 0</td>' +
                '</tr>'
        }
    });
    $('#tabExport').html(str)
    exportPdf()
    // convertTableToPDF()
}

function exportPdf() {
    var element = document.getElementById('exports');

    // Create a new jsPDF instance
    // var doc = new jsPDF();
    var doc = new jsPDF({
        orientation: 'landscape', // 'portrait' (default) or 'landscape'
        unit: 'mm', // 'mm' (default), 'pt', 'cm', or 'in'
        format: 'a4' // 'a4', 'letter', or [width, height]
    });
    // Use html2canvas to capture the HTML element as an image
    html2canvas(element).then(function(canvas) {
        // Convert the canvas image to a data URL
        var imgData = canvas.toDataURL('image/png');

        // Add the image to the PDF
        doc.addImage(imgData, 'PNG', 30, 10, 230, 0);

        // Save or display the PDF
        doc.save('html_to_pdf.pdf');
    });
}

function convertTableToPDFs() {
    // Create a new jsPDF instance
    var doc = new jsPDF({
        orientation: 'landscape', // 'portrait' (default) or 'landscape'
        unit: 'mm', // 'mm' (default), 'pt', 'cm', or 'in'
        format: 'a4' // 'a4', 'letter', or [width, height]
    });

    // Define the HTML table element
    var table = document.getElementById('exports');

    // Function to add a new page and reset the Y position
    function addNewPage() {
        doc.addPage();
        yPosition = 10; // Reset Y position to the top of the page
    }

    // Initialize Y position for content placement
    var yPosition = 10;

    // Function to capture table as an image and add it to the PDF
    function captureTableAsImage() {
        html2canvas(table, {
            onrendered: function(canvas) {
                var imgData = canvas.toDataURL('image/png');
                doc.addImage(imgData, 'PNG', 10, yPosition, 0, 0);
                yPosition += canvas.height; // Update Y position
                if (yPosition > 270) {
                    addNewPage(); // Start a new page if content exceeds the page height
                }
                checkAndSavePDF(); // Check if there are more pages
            }
        });
    }

    // Function to check and save the PDF
    function checkAndSavePDF() {
        if (yPosition <= 270) {
            doc.save('table_to_multi_page.pdf'); // Save the PDF
        } else {
            captureTableAsImage(); // Continue capturing content on new pages
        }
    }

    // Start capturing the table content
    captureTableAsImage();
}



var xnumAfk = 0

function xaddAfk() {
    var str = '<div id="xdivAfk' + xnumAfk +
        '" class="col-md-11 offset-md-1"> <div class="form-group row"> <div class="col-md-5"> <div class="form-group row"> <div class="col-md-12"> <input type="text" class="form-control" name="xafk_kw[]" id="xafk_kw' +
        xnumAfk +
        '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group row"> <div class="col-md-12"> <input type="text" class="form-control" name="xafk_luas[]" id="xafk_luas' +
        xnumAfk +
        '"> </div> </div> </div> <div class="col-md-3"> <div class="form-group row"> <div class="col-md-12"> <input type="text" class="form-control" name="xafk_persen[]" id="xafk_persen' +
        xnumAfk + '"> </div> </div> </div> <div class="col-md-1"> <button type="button" onclick="removeAdd(\'xdivAfk' +
        xnumAfk + '\')" class="btn btn-danger" style="padding:10px"> - </button> </div></div> </div>';

    xnumAfk++
    $('#xdivAfk').append(str)

}



var xnumTt = 1;

function xaddTt() {
    var str = '<div id="divTs' + xnumTt +
        '" class="col-md-11 offset-md-1"> <div class="form-group row" style="padding-left:5%"> <div class="col-md-5"> <textarea class="form-control" name="xts_ket[]" id="xts_ket' +
        xnumTt +
        '" cols="30" rows="3"></textarea> </div> <div class="col-md-3"> <input type="text" class="form-control" name="xts_luas[]" id="xts_luas' +
        xnumTt +
        '"> </div> <div class="col-md-3"> <input type="text" class="form-control" name="xts_persen[]" id="xts_persen' +
        xnumTt + '"> </div><div class="col-md-1"> <button type="button" onclick="removeAdd(\'divTs' + xnumTt +
        '\')" class="btn btn-danger" style="padding:10px"> - </button> </div> </div> </div>';
    xnumTt++
    $('#xdivTt').append(str)

}



var xnumTb = 1;

function xaddTb() {
    var str = '<div id="xdivTb' + xnumTb +
        '" class="col-md-11 offset-md-1"> <div class="form-group row" style="padding-left:5%"> <div class="col-md-5"> <textarea class="form-control" name="xtb_ket[]" id="xtb_ket' +
        xnumTb +
        '" cols="30" rows="3"></textarea> </div> <div class="col-md-3"> <input type="text" class="form-control" name="xtb_luas[]" id="xtb_luas' +
        xnumTb +
        '"> </div> <div class="col-md-3"> <input type="text" class="form-control" name="xtb_persen[]" id="xtb_persen' +
        xnumTb + '"> </div><div class="col-md-1"> <button type="button" onclick="removeAdd(\'xdivTb' + xnumTb +
        '\')" class="btn btn-danger" style="padding:10px"> - </button> </div> </div> </div>';

    xnumTb++
    $('#xdivTb').append(str)

}




var xnumMilik = 1;

function xaddMilik() {
    var str = '<div class="row" id="xdivMilik' + xnumMilik +
        '"><div  class="col-md-10 offset-md-1"> <textarea class="form-control" style="margin-bottom:20px" name="xmilik[]" id="xmilik' +
        xnumMilik +
        '" cols="30" rows="3"></textarea> </div> <div class="col-md-1"> <button type="button" onclick="removeAdd(\'xdivMilik' +
        xnumMilik + '\')" class="btn btn-danger" style="padding:10px"> - </button> </div> </div>';

    numMilik + 1
    $('#xdivMilik').append(str)

}

var xnumManfaat = 1;

function xaddManfaat() {
    var str = '<div class="row" id="xdivManfaat' + xnumManfaat +
        '"><div  class="col-md-10 offset-md-1"> <textarea class="form-control" style="margin-bottom:20px" name="xmanfaat[]" id="xmanfaat' +
        xnumManfaat +
        '" cols="30" rows="3"></textarea> </div> <div class="col-md-1"> <button type="button" onclick="removeAdd(\'xdivManfaat' +
        xnumManfaat + '\')" class="btn btn-danger" style="padding:10px"> - </button> </div> </div>';

    xnumManfaat + 1
    $('#xdivManfaat').append(str)

}


var xnumPeralihan = 1;

function xaddPeralihan() {
    var str = '<div class="row" id="xdivPeralihan' + numPeralihan +
        '"><div  class="col-md-10 offset-md-1"> <textarea class="form-control" style="margin-bottom:20px" name="xperalihan[]" id="xperalihan' +
        numPeralihan +
        '" cols="30" rows="3"></textarea> </div> <div class="col-md-1"> <button type="button" onclick="removeAdd(\'xdivPeralihan' +
        numPeralihan + '\')" class="btn btn-danger" style="padding:10px"> - </button> </div> </div>';

    numPeralihan + 1
    $('#xdivPeralihan').append(str)
}

var xnumAnggota = 1

function xaddAnggota(params) {

    var str = '<div class="row" id="xdivAnggota' + xnumAnggota +
        '"><div class="col-md-11 offset-md-1"> <div class="form-group row"> <div class="col-md-6 "> <div class="form-group row"> <div class="col-md-12"> <input type="text" class="form-control" name="xanggota[]" id="xanggota' +
        xnumAnggota +
        '" value=""> </div> </div> </div> <div class="col-md-4"> <div class="form-group row">  <div class="col-md-12"> <input type="text" class="form-control" name="xnip[]" id="xnip' +
        xnumAnggota +
        '" value=""> </div> </div> </div> <div class="col-md-1"> <div class="form-group row">  <div class="col-md-12"><button type="button" onclick="removeAdd(\'xdivAnggota' +
        xnumAnggota +
        '\')" class="btn btn-danger" style="padding:10px"> - </button></div> </div> </div> </div> </div></div>';
    $('#xdivAnggota').append(str)

}
</script>