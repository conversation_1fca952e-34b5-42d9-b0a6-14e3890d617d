<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Risalah_ptp extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
		$this->load->library('pdf');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $satker = $this->session->users['kd_bujt'];
        $title = "Risalah PTP";

        $js_file = $this->load->view('risalah_ptp/js_file', '', true);
        $modal_tambah = $this->load->view('risalah_ptp/modal_tambah', '', true);
        $modal_edit = $this->load->view('risalah_ptp/modal_edit', '', true);
        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "title" => $title,
            "jv_script" => $js_file
        );
        
        $this->load->view('index', $data);
    }
    public function getKab($kdProv,$tahun,$layer=null)
    {
        $layer='spatial.v_d_ptp_non_berusaha'; 
        $this->db->where('kdppum', $kdProv);
        $this->db->where('tahun_data', $tahun);
        $data = $this->db->get($layer.'_drill')->result();
            
        $str = '';
        $no = 0;
        foreach ($data as $key => $value) {
            if($no < 4){
                $str .= '<td>- '.$value->wadmkk.'<td>';
            }else{
                $str .= '<td>- '.$value->wadmkk.'<td></tr><tr><td style="width:100px"></td>';
                $no=-1;
            }
            $no++;
        }
        $str = substr($str, 0, -2);
        $html = '<table cellpadding="5" cellspacing="0" border="0" style="width:100%;padding-left:50px;table-layout: fixed;">
        <tr><td style="width:100px">List Kabupaten:</td>'.$str.'</tr></table>';
            
        echo json_encode(['html'=>$html]);
    }

    public function save_form() {

        // echo "<pre>";
        // print_r ($_POST);
        // echo "</pre>";exit();
        $data_detail = [ 
            'id_ptp' => $this->input->post('id_ptp',true),
            'nomor' => $this->input->post('nomor',true),
            'tanggal' => $this->input->post('tanggal',true),
            'dasar_tgl' => $this->input->post('dasar_tgl',true),
            'dasar_nama' => $this->input->post('dasar_nama',true),
            'dasar_nik' => $this->input->post('dasar_nik',true),
            'dasar_nib' => $this->input->post('dasar_nib',true),
            'dasar_alamat' => $this->input->post('dasar_alamat',true),
            'dasar_an' => $this->input->post('dasar_an',true),
            'dasar_kabkota1' => $this->input->post('dasar_kabkota1',true),
            'dasar_kabkota2' => $this->input->post('dasar_kabkota1',true),
            'dasar_noperda' => $this->input->post('dasar_noperda',true),
            'dasar_thnperda' => $this->input->post('dasar_thnperda',true),
            'dasar_noba1' => $this->input->post('dasar_noba1',true),
            'dasar_tglba1' => $this->input->post('dasar_tglba1',true),
            'dasar_noba2' => $this->input->post('dasar_noba2',true),
            'dasar_tglba2' => $this->input->post('dasar_tglba2',true),
            'mohon_jalan' => $this->input->post('mohon_jalan',true),
            'mohon_nomor' => $this->input->post('mohon_nomor',true),
            'mohon_rtrw' => $this->input->post('mohon_rtrw',true),
            'mohon_kdlurah' => $this->input->post('mohon_kdlurah',true),
            'mohon_kdcamat' => $this->input->post('mohon_kdcamat',true),
            'mohon_kdkabkot' => $this->input->post('mohon_kdkabkot',true),
            'mohon_luas_m2' => $this->input->post('mohon_luas_m2',true),
            'mohon_g' => $this->input->post('mohon_g',true),
            'mohon_gq' => $this->input->post('mohon_gq',true),
            'mohon_rcng' => $this->input->post('mohon_rcng',true),
            'mohon_kdkbli' => $this->input->post('mohon_kdkbli',true),
            'mohon_fkws' => $this->input->post('mohon_fkws',true),
            'mohon_kws1' => $this->input->post('mohon_kws1',true),
            'mohon_kws2' => $this->input->post('mohon_kws2',true),
            'mohon_kws3' => $this->input->post('mohon_kws3',true),
            'mohon_kws4' => $this->input->post('mohon_kws4',true),
            'mohon_kws5' => $this->input->post('mohon_kws5',true),
            'luas_kws1m2' => $this->input->post('luas_kws1m2',true) == '' ? '0' : $this->input->post('luas_kws1m2',true),
            'luas_kws1persen' => $this->input->post('luas_kws1persen',true) == '' ? '0' : $this->input->post('luas_kws1persen',true),
            'luas_kws2m2' => $this->input->post('luas_kws2m2',true) == '' ? '0' : $this->input->post('luas_kws2m2',true),
            'luas_kws2persen' => $this->input->post('luas_kws2persen',true) == '' ? '0' : $this->input->post('luas_kws2persen',true),
            'luas_kws3m2' => $this->input->post('luas_kws3m2',true) == '' ? '0' : $this->input->post('luas_kws3m2',true),
            'luas_kws3persen' => $this->input->post('luas_kws3persen',true) == '' ? '0' : $this->input->post('luas_kws3persen',true),
            'luas_kws4m2' => $this->input->post('luas_kws4m2',true) == '' ? '0' : $this->input->post('luas_kws4m2',true),
            'luas_kws4persen' => $this->input->post('luas_kws4persen',true) == '' ? '0' : $this->input->post('luas_kws4persen',true),
            'luas_kws5m2' => $this->input->post('luas_kws5m2',true) == '' ? '0' : $this->input->post('luas_kws5m2',true),
            'luas_kws5persen' => $this->input->post('luas_kws5persen',true) == '' ? '0' : $this->input->post('luas_kws5persen',true),
            'mohon_kesesuaian' => $this->input->post('mohon_kesesuaian',true),
            'longitude' => $this->input->post('longitude',true),
            'latitude' => $this->input->post('latitude',true),
            'simpul_sedia_luasm2' => $this->input->post('simpul_sedia_luasm2',true) == '' ? '0' : $this->input->post('simpul_sedia_luasm2',true),
            'simpul_sedia_luaspersen' => $this->input->post('simpul_sedia_luaspersen',true) == '' ? '0' : $this->input->post('simpul_sedia_luaspersen',true),
            'simpul_total_tdksedia_luasm2' => $this->input->post('simpul_total_tdksedia_luasm2',true) == '' ? '0' : $this->input->post('simpul_total_tdksedia_luasm2',true),
            'simpul_total_tdksedia_luaspersen' => $this->input->post('simpul_total_tdksedia_luaspersen',true) == '' ? '0' : $this->input->post('simpul_total_tdksedia_luaspersen',true),
            'simpul_tdksedia_ilok_luasm2' => $this->input->post('simpul_tdksedia_ilok_luasm2',true) == '' ? '0' : $this->input->post('simpul_tdksedia_ilok_luasm2',true),
            'simpul_tdksedia_ilok_luaspersen' => $this->input->post('simpul_tdksedia_ilok_luaspersen',true) == '' ? '0' : $this->input->post('simpul_tdksedia_ilok_luaspersen',true),
            'simpul_tdksedia_skpl_luasm2' => $this->input->post('simpul_tdksedia_skpl_luasm2',true) == '' ? '0' : $this->input->post('simpul_tdksedia_skpl_luasm2',true),
            'simpul_tdksedia_skpl_luaspersen' => $this->input->post('simpul_tdksedia_skpl_luaspersen',true) == '' ? '0' : $this->input->post('simpul_tdksedia_skpl_luaspersen',true),
            'simpul_tdksedia3_ket' => $this->input->post('simpul_tdksedia3_ket',true),
            'simpul_tdksedia3_luasm2' => $this->input->post('simpul_tdksedia3_luasm2',true) == '' ? '0' : $this->input->post('simpul_tdksedia3_luasm2',true),
            'simpul_tdksedia3_luaspersen' => $this->input->post('simpul_tdksedia3_luaspersen',true) == '' ? '0' : $this->input->post('simpul_tdksedia3_luaspersen',true),
            'simpul_tdksedia4_ket' => $this->input->post('simpul_tdksedia4_ket',true),
            'simpul_tdksedia4_luasm2' => $this->input->post('simpul_tdksedia4_luasm2',true) == '' ? '0' : $this->input->post('simpul_tdksedia4_luasm2',true),
            'simpul_tdksedia4_luaspersen' => $this->input->post('simpul_tdksedia4_luaspersen',true) == '' ? '0' : $this->input->post('simpul_tdksedia4_luaspersen',true),
            'simpul_tdksedia5_ket' => $this->input->post('simpul_tdksedia5_ket',true),
            'simpul_tdksedia5_luasm2' => $this->input->post('simpul_tdksedia5_luasm2',true) == '' ? '0' : $this->input->post('simpul_tdksedia5_luasm2',true),
            'simpul_tdksedia5_luaspersen' => $this->input->post('simpul_tdksedia5_luaspersen',true) == '' ? '0' : $this->input->post('simpul_tdksedia5_luaspersen',true),
            'simpul_total_sediasyarat_luasm2' => $this->input->post('simpul_total_sediasyarat_luasm2',true) == '' ? '0' : $this->input->post('simpul_total_sediasyarat_luasm2',true),
            'simpul_total_sediasyarat_luaspersen' => $this->input->post('simpul_total_sediasyarat_luaspersen',true),
            'simpul_sediasyarat1_luasm2' => $this->input->post('simpul_sediasyarat1_luasm2',true) == '' ? '0' : $this->input->post('simpul_sediasyarat1_luasm2',true),
            'simpul_sediasyarat1_luaspersen' => $this->input->post('simpul_sediasyarat1_luaspersen',true) == '' ? '0' : $this->input->post('simpul_sediasyarat1_luaspersen',true),
            'simpul_sediasyarat2_luasm2' => $this->input->post('simpul_sediasyarat2_luasm2',true) == '' ? '0' : $this->input->post('simpul_sediasyarat2_luasm2',true),
            'simpul_sediasyarat2_luaspersen' => $this->input->post('simpul_sediasyarat2_luaspersen',true) == '' ? '0' : $this->input->post('simpul_sediasyarat2_luaspersen',true),
            'simpul_sediasyarat3_luasm2' => $this->input->post('simpul_sediasyarat3_luasm2',true) == '' ? '0' : $this->input->post('simpul_sediasyarat3_luasm2',true),
            'simpul_sediasyarat3_luaspersen' =>$this->input->post('simpul_sediasyarat3_luaspersen') == '' ? '0' : $this->input->post('simpul_sediasyarat3_luaspersen',true),
            'simpul_sediasyarat4_ket' => $this->input->post('simpul_sediasyarat4_ket',true),
            'simpul_sediasyarat4_luasm2' => $this->input->post('simpul_sediasyarat4_luasm2',true) == '' ? '0' : $this->input->post('simpul_sediasyarat4_luasm2',true),
            'simpul_sediasyarat4_luaspersen' => $this->input->post('simpul_sediasyarat4_luaspersen',true) == '' ? '0' : $this->input->post('simpul_sediasyarat4_luaspersen',true),
            'simpul_sediasyarat5_ket' => $this->input->post('simpul_sediasyarat5_ket',true),
            'simpul_sediasyarat5_luasm2' => $this->input->post('simpul_sediasyarat5_luasm2',true) == '' ? '0' : $this->input->post('simpul_sediasyarat5_luasm2',true),
            'simpul_sediasyarat5_luaspersen' => $this->input->post('simpul_sediasyarat5_luaspersen',true) == '' ? '0' : $this->input->post('simpul_sediasyarat5_luaspersen',true),
            'simpul_ketentuan_gq1' => $this->input->post('simpul_ketentuan_gq1',true),
            'simpul_ketentuan_gq2' => $this->input->post('simpul_ketentuan_gq2',true),
            'simpul_ketentuan_gq3' => $this->input->post('simpul_ketentuan_gq3',true),
            'simpul_ketentuan_gq4' => $this->input->post('simpul_ketentuan_gq4',true),
            'simpul_ketentuan_gq5' => $this->input->post('simpul_ketentuan_gq5',true),
            'simpul_ketentuan_gp1' => $this->input->post('simpul_ketentuan_gp1',true),
            'simpul_ketentuan_gp2' => $this->input->post('simpul_ketentuan_gp2',true),
            'simpul_ketentuan_gp3' => $this->input->post('simpul_ketentuan_gp3',true),
            'simpul_ketentuan_gp4' => $this->input->post('simpul_ketentuan_gp4',true),
            'simpul_ketentuan_gp5' => $this->input->post('simpul_ketentuan_gp5',true),
            'simpul_ketentuan_hat1' => $this->input->post('simpul_ketentuan_hat1',true),
            'simpul_ketentuan_hat2' => $this->input->post('simpul_ketentuan_hat2',true),
            'simpul_ketentuan_hat3' => $this->input->post('simpul_ketentuan_hat3',true),
            'simpul_ketentuan_hat4' => $this->input->post('simpul_ketentuan_hat4',true),
            'simpul_ketentuan_hat5' => $this->input->post('simpul_ketentuan_hat5',true),
            'lokasi_surat' => $this->input->post('lokasi_surat',true),
            'tgl_surat' => $this->input->post('tgl_surat',true),
            'tim_ketua' => $this->input->post('tim_ketua',true),
            'nip_ketua' => $this->input->post('nip_ketua',true),
            'tim_sekretaris' => $this->input->post('tim_sekretaris',true),
            'nip_sekretaris' => $this->input->post('nip_sekretaris',true),
            'tim_member1' => $this->input->post('tim_member1',true),
            'nip_member1' => $this->input->post('nip_member1',true),
            'tim_member2' => $this->input->post('tim_member2',true),
            'nip_member2' => $this->input->post('nip_member2',true),
            'tim_member3' => $this->input->post('tim_member3',true),
            'nip_member3' => $this->input->post('nip_member3',true),
            'tim_member4' => $this->input->post('tim_member4',true),
            'nip_member4' => $this->input->post('nip_member4',true),
            'tim_member5' => $this->input->post('tim_member5',true),
            'nip_member5' => $this->input->post('nip_member5',true),
            'id_tema' => $this->input->post('id_tema',true)

        ];

        $ins = $this->db->insert('t_juknisrisalah', $data_detail);
        $afk_kw = $this->input->post('afk_kw',true);
        $afk_luas = $this->input->post('afk_luas',true);
        $afk_persen = $this->input->post('afk_persen',true);

        $tt_ket = $this->input->post('tt_ket',true);
        $tt_luas = $this->input->post('tt_luas',true);
        $tt_persen = $this->input->post('tt_persen',true);

        $tb_ket = $this->input->post('tb_ket',true);
        $tb_luas = $this->input->post('tb_luas',true);
        $tb_persen = $this->input->post('tb_persen',true);
        
        $milik = $this->input->post('milik',true);
        $manfaat = $this->input->post('manfaat',true);
        $peralihan = $this->input->post('peralihan',true);
        
        $anggota = $this->input->post('anggota',true);
        $nip = $this->input->post('nip',true);

        if ($ins) {
            $last_id = $this->db->insert_id();
            foreach ($afk_kw as $key => $value) {
                if ($value != '') {
                    $afk=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $afk_luas[$key],
                        'persentase' => $afk_persen[$key],
                    ];
                    $this->db->insert('t_juknisrisalah_afk', $afk);
                    
                }
            }


            foreach ($tt_ket as $key => $value) {
                if ($value != '') {
                    $tt=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $tt_luas[$key],
                        'persentase' => $tt_persen[$key],
                    ];
                    $this->db->insert('t_juknisrisalah_tt', $tt);
                }
            }

            foreach ($tb_ket as $key => $value) {
                if ($value != '') {
                    $tb=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $tb_luas[$key],
                        'persentase' => $tb_persen[$key],
                    ];
                    $this->db->insert('t_juknisrisalah_tb', $tb);
                }
            }
            foreach ($milik as $key => $value) {
                if ($value != '') {
                    $milik=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisrisalah_milik', $milik);
                }
            }
            
            
            foreach ($manfaat as $key => $value) {
                if ($value != '') {
                    $manfaat=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisrisalah_manfaat', $manfaat);
                }
            }
            
            foreach ($peralihan as $key => $value) {
                if ($value != '') {
                    $peralihan=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisrisalah_peralihan', $peralihan);
                }

            }

            foreach ($anggota as $key => $value) {
                if ($value != '') {
                    $ts=[
                        'id_juknisptp' => $last_id,
                        'nama' => $value,
                        'nip' => $nip[$key],
                    ];
                    $this->db->insert('t_juknisrisalah_anggota', $ts);
                }
            }
        }
            

        echo json_encode(array("status" => TRUE));
    }

    function update_form() {
        
   
        
        $data_detail = [ 
            'id_ptp' => $this->input->post('xid_ptp',true),
            'nomor' => $this->input->post('xnomor',true),
            'tanggal' => $this->input->post('xtanggal',true),
            'dasar_tgl' => $this->input->post('xdasar_tgl',true),
            'dasar_nama' => $this->input->post('xdasar_nama',true),
            'dasar_nik' => $this->input->post('xdasar_nik',true),
            'dasar_nib' => $this->input->post('xdasar_nib',true),
            'dasar_alamat' => $this->input->post('xdasar_alamat',true),
            'dasar_an' => $this->input->post('xdasar_an',true),
            'dasar_kabkota1' => $this->input->post('xdasar_kabkota1',true),
            'dasar_kabkota2' => $this->input->post('xdasar_kabkota1',true),
            'dasar_noperda' => $this->input->post('xdasar_noperda',true),
            'dasar_thnperda' => $this->input->post('xdasar_thnperda',true),
            'dasar_noba1' => $this->input->post('xdasar_noba1',true),
            'dasar_tglba1' => $this->input->post('xdasar_tglba1',true),
            'dasar_noba2' => $this->input->post('xdasar_noba2',true),
            'dasar_tglba2' => $this->input->post('xdasar_tglba2',true),
            'mohon_jalan' => $this->input->post('xmohon_jalan',true),
            'mohon_nomor' => $this->input->post('xmohon_nomor',true),
            'mohon_rtrw' => $this->input->post('xmohon_rtrw',true),
            'mohon_kdlurah' => $this->input->post('xmohon_kdlurah',true),
            'mohon_kdcamat' => $this->input->post('xmohon_kdcamat',true),
            'mohon_kdkabkot' => $this->input->post('xmohon_kdkabkot',true),
            'mohon_luas_m2' => $this->input->post('xmohon_luas_m2',true),
            'mohon_g' => $this->input->post('xmohon_g',true),
            'mohon_gq' => $this->input->post('xmohon_gq',true),
            'mohon_rcng' => $this->input->post('xmohon_rcng',true),
            'mohon_kdkbli' => $this->input->post('xmohon_kdkbli',true),
            'mohon_fkws' => $this->input->post('xmohon_fkws',true),
            'mohon_kws1' => $this->input->post('xmohon_kws1',true),
            'mohon_kws2' => $this->input->post('xmohon_kws2',true),
            'mohon_kws3' => $this->input->post('xmohon_kws3',true),
            'mohon_kws4' => $this->input->post('xmohon_kws4',true),
            'mohon_kws5' => $this->input->post('xmohon_kws5',true),
            'luas_kws1m2' => $this->input->post('xluas_kws1m2',true) == '' ? '0' : $this->input->post('xluas_kws1m2',true),
            'luas_kws1persen' => $this->input->post('xluas_kws1persen',true) == '' ? '0' : $this->input->post('xluas_kws1persen',true),
            'luas_kws2m2' => $this->input->post('xluas_kws2m2',true) == '' ? '0' : $this->input->post('xluas_kws2m2',true),
            'luas_kws2persen' => $this->input->post('xluas_kws2persen',true) == '' ? '0' : $this->input->post('xluas_kws2persen',true),
            'luas_kws3m2' => $this->input->post('xluas_kws3m2',true) == '' ? '0' : $this->input->post('xluas_kws3m2',true),
            'luas_kws3persen' => $this->input->post('xluas_kws3persen',true) == '' ? '0' : $this->input->post('xluas_kws3persen',true),
            'luas_kws4m2' => $this->input->post('xluas_kws4m2',true) == '' ? '0' : $this->input->post('xluas_kws4m2',true),
            'luas_kws4persen' => $this->input->post('xluas_kws4persen',true) == '' ? '0' : $this->input->post('xluas_kws4persen',true),
            'luas_kws5m2' => $this->input->post('xluas_kws5m2',true) == '' ? '0' : $this->input->post('xluas_kws5m2',true),
            'luas_kws5persen' => $this->input->post('xluas_kws5persen',true) == '' ? '0' : $this->input->post('xluas_kws5persen',true),
            'mohon_kesesuaian' => $this->input->post('xmohon_kesesuaian',true),
            'longitude' => $this->input->post('xlongitude',true),
            'latitude' => $this->input->post('xlatitude',true),
            'simpul_sedia_luasm2' => $this->input->post('xsimpul_sedia_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_sedia_luasm2',true),
            'simpul_sedia_luaspersen' => $this->input->post('xsimpul_sedia_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_sedia_luaspersen',true),
            'simpul_total_tdksedia_luasm2' => $this->input->post('xsimpul_total_tdksedia_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_total_tdksedia_luasm2',true),
            'simpul_total_tdksedia_luaspersen' => $this->input->post('xsimpul_total_tdksedia_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_total_tdksedia_luaspersen',true),
            'simpul_tdksedia_ilok_luasm2' => $this->input->post('xsimpul_tdksedia_ilok_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia_ilok_luasm2',true),
            'simpul_tdksedia_ilok_luaspersen' => $this->input->post('xsimpul_tdksedia_ilok_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia_ilok_luaspersen',true),
            'simpul_tdksedia_skpl_luasm2' => $this->input->post('xsimpul_tdksedia_skpl_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia_skpl_luasm2',true),
            'simpul_tdksedia_skpl_luaspersen' => $this->input->post('xsimpul_tdksedia_skpl_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia_skpl_luaspersen',true),
            'simpul_tdksedia3_ket' => $this->input->post('xsimpul_tdksedia3_ket',true),
            'simpul_tdksedia3_luasm2' => $this->input->post('xsimpul_tdksedia3_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia3_luasm2',true),
            'simpul_tdksedia3_luaspersen' => $this->input->post('xsimpul_tdksedia3_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia3_luaspersen',true),
            'simpul_tdksedia4_ket' => $this->input->post('xsimpul_tdksedia4_ket',true),
            'simpul_tdksedia4_luasm2' => $this->input->post('xsimpul_tdksedia4_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia4_luasm2',true),
            'simpul_tdksedia4_luaspersen' => $this->input->post('xsimpul_tdksedia4_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia4_luaspersen',true),
            'simpul_tdksedia5_ket' => $this->input->post('xsimpul_tdksedia5_ket',true),
            'simpul_tdksedia5_luasm2' => $this->input->post('xsimpul_tdksedia5_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia5_luasm2',true),
            'simpul_tdksedia5_luaspersen' => $this->input->post('xsimpul_tdksedia5_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_tdksedia5_luaspersen',true),
            'simpul_total_sediasyarat_luasm2' => $this->input->post('xsimpul_total_sediasyarat_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_total_sediasyarat_luasm2',true),
            'simpul_total_sediasyarat_luaspersen' => $this->input->post('xsimpul_total_sediasyarat_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_total_sediasyarat_luaspersen',true),
            'simpul_sediasyarat1_luasm2' => $this->input->post('xsimpul_sediasyarat1_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat1_luasm2',true),
            'simpul_sediasyarat1_luaspersen' => $this->input->post('xsimpul_sediasyarat1_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat1_luaspersen',true),
            'simpul_sediasyarat2_luasm2' => $this->input->post('xsimpul_sediasyarat2_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat2_luasm2',true),
            'simpul_sediasyarat2_luaspersen' => $this->input->post('xsimpul_sediasyarat2_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat2_luaspersen',true),
            'simpul_sediasyarat3_luasm2' => $this->input->post('xsimpul_sediasyarat3_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat3_luasm2',true),
            'simpul_sediasyarat3_luaspersen' =>$this->input->post('xsimpul_sediasyarat3_luaspersen') == '' ? '0' : $this->input->post('xsimpul_sediasyarat3_luaspersen',true),
            'simpul_sediasyarat4_ket' => $this->input->post('xsimpul_sediasyarat4_ket',true),
            'simpul_sediasyarat4_luasm2' => $this->input->post('xsimpul_sediasyarat4_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat4_luasm2',true),
            'simpul_sediasyarat4_luaspersen' => $this->input->post('xsimpul_sediasyarat4_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat4_luaspersen',true),
            'simpul_sediasyarat5_ket' => $this->input->post('xsimpul_sediasyarat5_ket',true),
            'simpul_sediasyarat5_luasm2' => $this->input->post('xsimpul_sediasyarat5_luasm2',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat5_luasm2',true),
            'simpul_sediasyarat5_luaspersen' => $this->input->post('xsimpul_sediasyarat5_luaspersen',true) == '' ? '0' : $this->input->post('xsimpul_sediasyarat5_luaspersen',true),
            'simpul_ketentuan_gq1' => $this->input->post('xsimpul_ketentuan_gq1',true),
            'simpul_ketentuan_gq2' => $this->input->post('xsimpul_ketentuan_gq2',true),
            'simpul_ketentuan_gq3' => $this->input->post('xsimpul_ketentuan_gq3',true),
            'simpul_ketentuan_gq4' => $this->input->post('xsimpul_ketentuan_gq4',true),
            'simpul_ketentuan_gq5' => $this->input->post('xsimpul_ketentuan_gq5',true),
            'simpul_ketentuan_gp1' => $this->input->post('xsimpul_ketentuan_gp1',true),
            'simpul_ketentuan_gp2' => $this->input->post('xsimpul_ketentuan_gp2',true),
            'simpul_ketentuan_gp3' => $this->input->post('xsimpul_ketentuan_gp3',true),
            'simpul_ketentuan_gp4' => $this->input->post('xsimpul_ketentuan_gp4',true),
            'simpul_ketentuan_gp5' => $this->input->post('xsimpul_ketentuan_gp5',true),
            'simpul_ketentuan_hat1' => $this->input->post('xsimpul_ketentuan_hat1',true),
            'simpul_ketentuan_hat2' => $this->input->post('xsimpul_ketentuan_hat2',true),
            'simpul_ketentuan_hat3' => $this->input->post('xsimpul_ketentuan_hat3',true),
            'simpul_ketentuan_hat4' => $this->input->post('xsimpul_ketentuan_hat4',true),
            'simpul_ketentuan_hat5' => $this->input->post('xsimpul_ketentuan_hat5',true),
            'lokasi_surat' => $this->input->post('xlokasi_surat',true),
            'tgl_surat' => $this->input->post('xtgl_surat',true),
            'tim_ketua' => $this->input->post('xtim_ketua',true),
            'nip_ketua' => $this->input->post('xnip_ketua',true),
            'tim_sekretaris' => $this->input->post('xtim_sekretaris',true),
            'nip_sekretaris' => $this->input->post('xnip_sekretaris',true),
            'tim_member1' => $this->input->post('xtim_member1',true),
            'nip_member1' => $this->input->post('xnip_member1',true),
            'tim_member2' => $this->input->post('xtim_member2',true),
            'nip_member2' => $this->input->post('xnip_member2',true),
            'tim_member3' => $this->input->post('xtim_member3',true),
            'nip_member3' => $this->input->post('xnip_member3',true),
            'tim_member4' => $this->input->post('xtim_member4',true),
            'nip_member4' => $this->input->post('xnip_member4',true),
            'tim_member5' => $this->input->post('xtim_member5',true),
            'nip_member5' => $this->input->post('xnip_member5',true)
        ];
        // echo "<pre>";
        // print_r ($_POST);
        // echo "</pre>";exit();

        // $this->db->set('updated_by', $this->session->users['id_user'], FALSE);

        $this->db->where('id_risalah', $this->input->post("xid",true));
        $up = $this->db->update('t_juknisrisalah', $data_detail);
        $afk_kw = $this->input->post('xafk_kw',true);
        $afk_luas = $this->input->post('xafk_luas',true);
        $afk_persen = $this->input->post('xafk_persen',true);

        $tt_ket = $this->input->post('xtt_ket',true);
        $tt_luas = $this->input->post('xtt_luas',true);
        $tt_persen = $this->input->post('xtt_persen',true);

        $tb_ket = $this->input->post('xtb_ket',true);
        $tb_luas = $this->input->post('xtb_luas',true);
        $tb_persen = $this->input->post('xtb_persen',true);
        
        $milik = $this->input->post('xmilik',true);
        $manfaat = $this->input->post('xmanfaat',true);
        $peralihan = $this->input->post('xperalihan',true);
        $anggota = $this->input->post('xanggota',true);
        $nip = $this->input->post('xnip',true);
        
        if ($up) {
            $last_id = $this->input->post("xid",true);

            
            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisrisalah_afk');
            foreach ($afk_kw as $key => $value) {
                if ($value != '') {
                    $afk=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $afk_luas[$key],
                        'persentase' => $afk_persen[$key],
                    ];
                    $this->db->insert('t_juknisrisalah_afk', $afk);
                    
                }
            }

            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisrisalah_tt');
            foreach ($tt_ket as $key => $value) {
                if ($value != '') {
                    $tt=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $tt_luas[$key],
                        'persentase' => $tt_persen[$key],
                    ];
                    $this->db->insert('t_juknisrisalah_tt', $tt);
                }
            }

            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisrisalah_tb');
            foreach ($tb_ket as $key => $value) {
                if ($value != '') {
                    $tb=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $tb_luas[$key],
                        'persentase' => $tb_persen[$key],
                    ];
                    $this->db->insert('t_juknisrisalah_tb', $tb);
                }
            }

            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisrisalah_milik');
            foreach ($milik as $key => $value) {
                if ($value != '') {
                    $milik=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisrisalah_milik', $milik);
                }
            }
            
            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisrisalah_manfaat');
            foreach ($manfaat as $key => $value) {
                if ($value != '') {
                    $manfaat=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisrisalah_manfaat', $manfaat);
                }
            }


            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisrisalah_peralihan');
            foreach ($peralihan as $key => $value) {
                if ($value != '') {
                    $peralihan=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisrisalah_peralihan', $peralihan);
                }

            }

            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisrisalah_anggota');
            foreach ($anggota as $key => $value) {
                if ($value != '') {
                    $ts=[
                        'id_juknisptp' => $last_id,
                        'nama' => $value,
                        'nip' => $nip[$key],
                    ];
                    $this->db->insert('t_juknisrisalah_anggota', $ts);
                }
            }
        }
            


        echo json_encode(array("status" => TRUE));
    }


    function ajax_delete($id){
        $this->db->where('id_risalah', $id);
        $this->db->delete('t_juknisrisalah');
    }
    public function getYear($layer=null,$tahun=null)
    {
        // $layer=str_replace('v_d','vm',$layer);
        $layer = 'spatial.v_r_ptp_non_berusaha';
        $this->db->select('tahun_data');
        $this->db->group_by('tahun_data');
        $datas = $this->db->get($layer)->result();
        
        $data = rsort($datas);
        $data=[];
        foreach ($datas as $key => $value) {
            array_push($data,$value->tahun_data);
        }

        $sel = in_array($tahun,$data)? '':'selected';
        // echo $sel;
        $str='<option '.$sel.' value="">Semua Tahun</option>';
        foreach ($data as $key => $value) {
            $text = $value == null ? 'Tidak Ada Tahun' : $value;
            $value = $value == null ? '0' : $value;
            $sel = $value == $tahun ? 'selected' : '';
            $str .= '<option '.$sel.' value="'.$value.'">'.$text.'</option>';
        }
        echo json_encode($str);
        
    }
    public function ssp_paket() {
      
        
    
        
        $table = 't_juknisrisalah';
        $primaryKey = 'id_risalah'; //test        
        $kd_prov=@$this->input->post("kd_prov",true);
        $kd_kabkot=@$this->input->post("kd_kabkot",true);

        $role=$this->session->users['id_user_group_real'];
        $kdpkab=$this->session->users['kd_kabkot'];
        $kdppum=explode('.',$kdpkab)[0];
        // $kd_prov=@$this->input->post("kd_prov",true);
        // $tahun_data=@$this->input->post("tahun_data",true);

        $where="";
        if (!empty($kd_prov)) {
            $where .=" substring(dasar_kabkota1,1,2) = '".$kd_prov."'";
            if (!empty($kd_kabkot)) {
                $where .=" and dasar_kabkota1 = '".$kd_kabkot."'";
            }     
        } 
        // if (!empty($kd_prov)) {
        //     $where .="kdppum = '".$kd_prov."'";
        // } 
        // if ($tahun_data != '') {
        //     $where .="tahun_data = '".$tahun_data."'";
        // } 
        $columns = array(
            array('db' => 'id_risalah', 'dt' => 0),
            array('db' => 'id_ptp', 'dt' => 1),
            array('db' => 'nomor', 'dt' => 2),
            array('db' => 'tanggal', 'dt' => 3),
            array('db' => 'dasar_tgl', 'dt' => 4),
            array('db' => 'dasar_nama', 'dt' => 5),
            array('db' => 'dasar_nik', 'dt' => 6),
            array('db' => 'dasar_nib', 'dt' => 7),
            array('db' => 'dasar_alamat', 'dt' => 8),
            array('db' => 'dasar_an', 'dt' => 9),
            array('db' => 'dasar_kabkota1', 'dt' => 10),
            array('db' => 'dasar_kabkota2', 'dt' => 11),
            array('db' => 'dasar_noperda', 'dt' => 12),
            array('db' => 'dasar_thnperda', 'dt' => 13),
            array('db' => 'dasar_noba1', 'dt' => 14),
            array('db' => 'dasar_tglba1', 'dt' => 15),
            array('db' => 'dasar_noba2', 'dt' => 16),
            array('db' => 'dasar_tglba2', 'dt' => 17),
            array('db' => 'mohon_jalan', 'dt' => 18),
            array('db' => 'mohon_nomor', 'dt' => 19),
            array('db' => 'mohon_rtrw', 'dt' => 20),
            array('db' => 'mohon_kdlurah', 'dt' => 21),
            array('db' => 'mohon_kdcamat', 'dt' => 22),
            array('db' => 'mohon_kdkabkot', 'dt' => 23),
            array('db' => 'mohon_luas_m2', 'dt' => 24),
            array('db' => 'mohon_g', 'dt' => 25),
            array('db' => 'mohon_gq', 'dt' => 26),
            array('db' => 'mohon_rcng', 'dt' => 27),
            array('db' => 'mohon_kdkbli', 'dt' => 28),
            array('db' => 'mohon_fkws', 'dt' => 29),
            array('db' => 'mohon_kws1', 'dt' => 30),
            array('db' => 'mohon_kws2', 'dt' => 31),
            array('db' => 'mohon_kws3', 'dt' => 32),
            array('db' => 'mohon_kws4', 'dt' => 33),
            array('db' => 'mohon_kws5', 'dt' => 34),
            array('db' => 'luas_kws1m2', 'dt' => 35),
            array('db' => 'luas_kws1persen', 'dt' => 36),
            array('db' => 'luas_kws2m2', 'dt' => 37),
            array('db' => 'luas_kws2persen', 'dt' => 38),
            array('db' => 'luas_kws3m2', 'dt' => 39),
            array('db' => 'luas_kws3persen', 'dt' => 40),
            array('db' => 'luas_kws4m2', 'dt' => 41),
            array('db' => 'luas_kws4persen', 'dt' => 42),
            array('db' => 'luas_kws5m2', 'dt' => 43),
            array('db' => 'luas_kws5persen', 'dt' => 44),
            array('db' => 'mohon_kesesuaian', 'dt' => 45),
            array('db' => 'longitude', 'dt' => 46),
            array('db' => 'latitude', 'dt' => 47),
            array('db' => 'simpul_sedia_luasm2', 'dt' => 48),
            array('db' => 'simpul_sedia_luaspersen', 'dt' => 49),
            array('db' => 'simpul_total_tdksedia_luasm2', 'dt' => 50),
            array('db' => 'simpul_total_tdksedia_luaspersen', 'dt' => 51),
            array('db' => 'simpul_tdksedia_ilok_luasm2', 'dt' => 52),
            array('db' => 'simpul_tdksedia_ilok_luaspersen', 'dt' => 53),
            array('db' => 'simpul_tdksedia_skpl_luasm2', 'dt' => 54),
            array('db' => 'simpul_tdksedia_skpl_luaspersen', 'dt' => 55),
            array('db' => 'simpul_tdksedia3_ket', 'dt' => 56),
            array('db' => 'simpul_tdksedia3_luasm2', 'dt' => 57),
            array('db' => 'simpul_tdksedia3_luaspersen', 'dt' => 58),
            array('db' => 'simpul_tdksedia4_ket', 'dt' => 59),
            array('db' => 'simpul_tdksedia4_luasm2', 'dt' => 60),
            array('db' => 'simpul_tdksedia4_luaspersen', 'dt' => 61),
            array('db' => 'simpul_tdksedia5_ket', 'dt' => 62),
            array('db' => 'simpul_tdksedia5_luasm2', 'dt' => 63),
            array('db' => 'simpul_tdksedia5_luaspersen', 'dt' => 64),
            array('db' => 'simpul_total_sediasyarat_luasm2', 'dt' => 65),
            array('db' => 'simpul_total_sediasyarat_luaspersen', 'dt' => 66),
            array('db' => 'simpul_sediasyarat1_luasm2', 'dt' => 67),
            array('db' => 'simpul_sediasyarat1_luaspersen', 'dt' => 68),
            array('db' => 'simpul_sediasyarat2_luasm2', 'dt' => 69),
            array('db' => 'simpul_sediasyarat2_luaspersen', 'dt' => 70),
            array('db' => 'simpul_sediasyarat3_luasm2', 'dt' => 71),
            array('db' => 'simpul_sediasyarat3_luaspersen', 'dt' =>72 ),
            array('db' => 'simpul_sediasyarat4_ket', 'dt' => 73),
            array('db' => 'simpul_sediasyarat4_luasm2', 'dt' => 74),
            array('db' => 'simpul_sediasyarat4_luaspersen', 'dt' => 75),
            array('db' => 'simpul_sediasyarat5_ket', 'dt' => 76),
            array('db' => 'simpul_sediasyarat5_luasm2', 'dt' => 77),
            array('db' => 'simpul_sediasyarat5_luaspersen', 'dt' => 78),
            array('db' => 'simpul_ketentuan_gq1', 'dt' => 79),
            array('db' => 'simpul_ketentuan_gq2', 'dt' => 80),
            array('db' => 'simpul_ketentuan_gq3', 'dt' => 81),
            array('db' => 'simpul_ketentuan_gq4', 'dt' => 82),
            array('db' => 'simpul_ketentuan_gq5', 'dt' => 83),
            array('db' => 'simpul_ketentuan_gp1', 'dt' => 84),
            array('db' => 'simpul_ketentuan_gp2', 'dt' => 85),
            array('db' => 'simpul_ketentuan_gp3', 'dt' => 86),
            array('db' => 'simpul_ketentuan_gp4', 'dt' => 87),
            array('db' => 'simpul_ketentuan_gp5', 'dt' => 88),
            array('db' => 'simpul_ketentuan_hat1', 'dt' => 89),
            array('db' => 'simpul_ketentuan_hat2', 'dt' => 90),
            array('db' => 'simpul_ketentuan_hat3', 'dt' => 91),
            array('db' => 'simpul_ketentuan_hat4', 'dt' => 92),
            array('db' => 'simpul_ketentuan_hat5', 'dt' => 93),
            array('db' => 'lokasi_surat', 'dt' => 94),
            array('db' => 'tgl_surat', 'dt' => 95),
            array('db' => 'tim_ketua', 'dt' => 96),
            array('db' => 'nip_ketua', 'dt' => 97),
            array('db' => 'tim_sekretaris', 'dt' => 98),
            array('db' => 'nip_sekretaris', 'dt' => 99),
            array('db' => 'tim_member1', 'dt' => 100),
            array('db' => 'nip_member1', 'dt' => 101),
            array('db' => 'tim_member2', 'dt' => 102),
            array('db' => 'nip_member2', 'dt' => 103),
            array('db' => 'tim_member3', 'dt' => 104),
            array('db' => 'nip_member3', 'dt' => 105),
            array('db' => 'tim_member4', 'dt' => 106),
            array('db' => 'nip_member4', 'dt' => 107),
            array('db' => 'tim_member5', 'dt' => 108),
            array('db' => 'nip_member5', 'dt' => 109)
        );


        

         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);

        
    }
    function AutoMultiCellWithBorder($w, $h, $text)
    {
        $this->SetX($this->GetX());
        $this->SetY($this->GetY());
        $width = $w - $this->cMargin * 2;
        $text = trim($text);
        $textLength = $this->GetStringWidth($text);

        // Set cell borders
        $this->Cell($width, $h, '', 'LTRB', 0, '', false);
        
        if ($textLength <= $width) {
            $this->MultiCell($width, $h, $text);
        } else {
            $lines = explode("\n", $text);
            foreach ($lines as $line) {
                $line = trim($line);
                while ($this->GetStringWidth($line) > $width) {
                    $line = substr($line, 0, -1);
                }
                $this->MultiCell($width, $h, $line);
            }
        }
    }

    function kabName($id) {
        $this->db->where('kd_kabkot', $id);
        return $this->db->get('aset_r_kabkota')->row_array()['nama_kabkot'];
        
    }
    function kecName($id) {
        $this->db->where('kd_camat', $id);
        return @$this->db->get('aset_r_kecamatan')->row_array()['nama_camat'];
        
    }
    function kelName($id) {
        $this->db->where('kd_lurah', $id);
        return @$this->db->get('aset_r_kelurahan')->row_array()['nama_lurah'];
        
    }


    function export_pdf($id=''){

            $this->db->where('id_risalah', $id);
            $data = $this->db->get('t_juknisrisalah')->row_array();            
            $dasar_kabkota1 = ucfirst(strtolower($this->kabName($data['dasar_kabkota1'])));
            $dasar_kabkota2 = ucfirst(strtolower($this->kabName($data['dasar_kabkota2'])));
            $mohon_kdlurah = ucfirst(strtolower($this->kelName($data['mohon_kdlurah'])));
            $mohon_kdcamat = ucfirst(strtolower($this->kecName($data['mohon_kdcamat'])));
            $lokasi_surat = ucfirst(strtolower($this->kabName($data['lokasi_surat'])));
           
            $this->db->where('id_juknisptp', $id);
            $afk = $this->db->get('t_juknisrisalah_afk')->result();
            $this->db->where('id_juknisptp', $id);
            $tb = $this->db->get('t_juknisrisalah_tb')->result();
            $this->db->where('id_juknisptp', $id);
            $tt = $this->db->get('t_juknisrisalah_tt')->result();
            $this->db->where('id_juknisptp', $id);
            $milik = $this->db->get('t_juknisrisalah_milik')->result();
            $this->db->where('id_juknisptp', $id);
            $manfaat = $this->db->get('t_juknisrisalah_manfaat')->result();
            $this->db->where('id_juknisptp', $id);
            $peralihan = $this->db->get('t_juknisrisalah_peralihan')->result();
            $this->db->where('id_juknisptp', $id);
            $anggota = $this->db->get('t_juknisrisalah_anggota')->result();
            $this->db->where('kode_module', $data['id_tema']);
            $this->db->where('parent', '209');
            $this->db->where('urutan is not null');
            $tema = $this->db->get('aset_module')->row_array();
            
     
            $m2 = utf8_decode(' m²');
            
            
            // $this->db->where('id_tema', $data['id_tema']);
            // $tema = $this->db->get('r_temanalisis')->row_array();



            $pdf = new FPDF('P', 'mm','Letter');
            $pdf->SetMargins(20, 10, 20);
	        $pdf->AddPage();
	        $pdf->SetFont('Arial','',12);
            
			// $pdf->SetTextColor(255,255,255);
            $pdf->SetFillColor(255,255,255);

	        // $pdf->Cell(175,7,' ','LTR',1,'C',true);
	        // $pdf->Cell(175,6,'RISALAH PERTIMBANGAN TEKNIS PERTANAHAN ','LR',1,'C',true);
	        // $pdf->Cell(175,6,'KEGIATAN PENERBITAN PKKPR UNTUK KEGIATAN BERUSAHA/PKKPR  ','LR',1,'C',true);
	        // $pdf->Cell(175,6,'UNTUK KEGIATAN NONBERUSAHA/PKKPR ATAU RKKPR UNTUK KEGIATAN','LR',1,'C',true);
	        // $pdf->Cell(175,6,'YANG BERSIFAT STRATEGIS NASIONAL/PENEGASAN STATUS DAN','LR',1,'C',true);
	        // $pdf->Cell(175,6,'REKOMENDASI PENGUASAAN TANAH TIMBUL/ PENYELENGGARAAN','LR',1,'C',true);
	        // $pdf->Cell(175,6,'KEBIJAKAN PENGGUNAAN DAN PEMANFAATAN TANAH.*','LR',1,'C',true);
	        $pdf->Cell(175,3,'','RLT',1,'C',true);
	        $pdf->Cell(175,15,strtoupper(@$tema['nama_module']),'RL',1,'C',true);
            // $pdf->SetFont('Arial','',9);
	        // $pdf->Cell(175,6,'*) hapus yang tidak perlu','LR',1,'C',true);
	        $pdf->SetFont('Arial','',12);
	        $pdf->Cell(175,6,'NOMOR '.strtoupper($data['nomor']).' TANGGAL '.tanggal_indonesia($data['tanggal']),'LR',1,'C',true);
	        $pdf->Cell(175,5,' ','LBR',1,'C',true);
	        $pdf->Cell(175,5,' ','LT    R',1,'C',true);
            // I
	        $pdf->Cell(175,7,'I. DASAR PENERBITAN RISALAH PERTIMBANGAN TEKNIS PERTANAHAN','LR',1,'L',true);
	        $pdf->SetFont('Arial','',11);
            
                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170,6,'1. Formulir permohonan tanggal '.tanggal_indonesia($data['dasar_tgl']).' yang diajukan oleh pemohon:','R',1,'L',true);
            
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'a. Nama : '.$data['dasar_nama'],'R',1,'L',true);
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'b. Nomor Induk Kependudukan (NIK) : '.$data['dasar_nik'],'R',1,'L',true);
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'c. Nomor Induk Berusaha (NIB)*: '.$data['dasar_nib'],'R',1,'L',true);
                    $pdf->Cell(20,6,'','L',0,'L',true);
                    $pdf->Cell(155,6,'**) untuk pemohon pelaku usaha yang sudah memiliki NIB','R',1,'L',true);
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'d. Alamat : '.$data['dasar_alamat'],'R',1,'L',true);
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'e. Bertindak atas nama : '.$data['dasar_an'],'R',1,'L',true);
                    // $pdf->Cell(5,6,'','L',0,'L',true);
                    // $pdf->Cell(170,6,'2. Peraturan Daerah Kabupaten/Kota '.$data['dasar_kabkota1'].' Nomor '.$data['dasar_noperda'].'. tentang','R',1,'L',true);
                    // $pdf->Cell(10,6,'','L',0,'L',true);
                    // $pdf->Cell(170,6,'Rencana Tata Ruang Wilayah Kabupaten/Kota '.$data['dasar_kabkota2'].' Tahun '.$data['dasar_thnperda'].'','R',1,'L',true);

                    $str = 'Peraturan Daerah Kabupaten/Kota '.$dasar_kabkota1.' Nomor '.$data['dasar_noperda'].'. tentang Rencana Tata Ruang Wilayah Kabupaten/Kota '.$dasar_kabkota2.' Tahun '.$data['dasar_thnperda'];
                    $line = count($this->cutTextIntoArray($str, 80));
                    $height = $line*6;
                    $pdf->Cell(5,$height,'','L',0,'L',true);
                    $pdf->Cell(5,6,'2.','',0,'L',true);
                    $pdf->MultiCell(165 , 6, $str, 'R', 'L',1);

                    $pdf->Cell(5,6,'','L',0,'L',true);
                    $pdf->Cell(170,6,'3. Berita Acara Peninjauan Lapangan Nomor '.$data['dasar_noba1'].' tanggal '.tanggal_indonesia($data['dasar_tglba1']),'R',1,'L',true);
                    // $pdf->Cell(5,6,'','L',0,'L',true);
                    // $pdf->Cell(170,6,'4. Berita Acara Rapat Tim Pertimbangan Teknis Pertanahan Nomor '.$data['dasar_noba2'].' tanggal '.tanggal_indonesia($data['dasar_tglba2']),'R',1,'L',true);
                    $str = 'Berita Acara Rapat Tim Pertimbangan Teknis Pertanahan Nomor '.$data['dasar_noba2'].' tanggal '.tanggal_indonesia($data['dasar_tglba2']);
                    $line = count($this->cutTextIntoArray($str, 70));
                    $height = $line*6;
                    $pdf->Cell(5,$height,'','L',0,'L',true);
                    $pdf->Cell(4,6,'4.','',0,'L',true);
                    $pdf->MultiCell(166 , 6, $str, 'R', 'L',1);
                    // $pdf->Cell(225,7,'',0,1,'L');
            $pdf->Cell(175,7,' ','LR',1,'C',true);

            $pdf->Cell(175,7,'II. KETERANGAN MENGENAI TANAH YANG DIMOHON','LR',1,'L',true);
	        $pdf->SetFont('Arial','',11);
            
                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170,6,'1. Letak tanah yang dimohon:','R',1,'L',true);
            
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'a. Jalan, nomor, RT/RW : '.$data['mohon_jalan'].', ' .$data['mohon_nomor'].', ' .$data['mohon_rtrw'],'R',1,'L',true);
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'b. Desa/Kelurahan : '.$mohon_kdlurah,'R',1,'L',true);
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'c. Kecamatan : '.$mohon_kdcamat,'R',1,'L',true);
                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170, 6, "2. Luas tanah yang dimohon : ".$data['mohon_luas_m2'].$m2." ", 'R', 1, 'L', true);

                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170,6,'3. Penggunaan tanah saat ini : '.$data['mohon_g'].$m2.' ','R',1,'L',true);
                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170,6,'4. Penguasaan tanah saat ini : '.$data['mohon_gq'].$m2.' ','R',1,'L',true);
                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170,6,'5. Rencana penggunaan tanah: '.$data['mohon_rcng'],'R',1,'L',true);
                $pdf->Cell(15,6,'','L',0,'L',true);
                $pdf->Cell(160,6,'Kode dan Nama KBLI ***) : '.$data['mohon_kdkbli'],'R',1,'L',true);
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'***) Klasifikasi Baku Lapangan usaha Indonesia (untuk pemohon pelaku usaha)','R',1,'L',true);
                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170,6,'6. Arahan fungsi kawasan RTR:','R',1,'L',true);
                    // $pdf->Cell(15,6,'','L',0,'L',true);
                    // $pdf->Cell(160,6,'a. Kawasan '.$data['mohon_kws1'].' :  '.$data['luas_kws1m2'].' m2 ('.$data['luas_kws1persen'].'%)','R',1,'L',true);
                    // $pdf->Cell(15,6,'','L',0,'L',true);
                    // $pdf->Cell(160,6,'b. Kawasan '.$data['mohon_kws2'].' : '.$data['luas_kws2m2'].' m2 ('.$data['luas_kws2persen'].'%)','R',1,'L',true);
                    // if($data['mohon_kws3'] !=''){
                    //     $pdf->Cell(15,6,'','L',0,'L',true);
                    //     $pdf->Cell(160,6,'c. Kawasan '.$data['mohon_kws3'].' :  '.$data['luas_kws3m2'].' m2 ('.$data['luas_kws1persen'].'%)','R',1,'L',true);
                    // }
                    // if($data['mohon_kws4'] !=''){
                    //     $pdf->Cell(15,6,'','L',0,'L',true);
                    //     $pdf->Cell(160,6,'d. Kawasan '.$data['mohon_kws4'].' :  '.$data['luas_kws4m2'].' m2 ('.$data['luas_kws1persen'].'%)','R',1,'L',true);
                    // }
                    // if($data['mohon_kws5'] !=''){
                    //     $pdf->Cell(15,6,'','L',0,'L',true);
                    //     $pdf->Cell(160,6,'e. Kawasan '.$data['mohon_kws5'].' :  '.$data['luas_kws5m2'].' m2 ('.$data['luas_kws1persen'].'%)','R',1,'L',true);
                    // }
                    $num = ['a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'];
                    foreach ($afk as $i => $value) {
                        $pdf->Cell(15,6,'','L',0,'L',true);
                        $pdf->Cell(160,6,$num[$i].'. Kawasan '.@$value->kawasan.' :  '.str_replace(',','.',number_format(@$value->luasm2)).' m2 ('.@$value->persen.'%)','R',1,'L',true);
                    }
                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170,6,' 7. Kesesuaian Penggunaan Tanah : '.$data['mohon_kesesuaian'],'R',1,'L',true);
                $pdf->Cell(5,6,'','L',0,'L',true);
                $pdf->Cell(170,6,' 8. Koordinat letak lokasi  : '.$data['latitude'].' '.$data['longitude'],'R',1,'L',true);
            $pdf->Cell(175,5,' ','LR',1,'C',true);

            $pdf->Cell(175,5,' ','LR',1,'C',true);
            $pdf->Cell(175,7,'III. KESIMPULAN','LR',1,'L',true);
            $str = 'Pertimbangan terhadap lokasi yang dimohon untuk kegiatan ....... ditinjau dari aspek Penguasaan, Pemilikan, Penggunaan, dan Pemanfaatan Tanah serta kemampuan tanah: ';
            $line = count($this->cutTextIntoArray($str, 80));
            $height = $line*6;
            $pdf->Cell(10,$height,'','L',0,'L',true);
            $pdf->Cell(5,6,'A.','',0,'L',true);
            $pdf->MultiCell(160 , 6, $str, 'R', 'L',1);

            $pdf->Cell(13,6,'','L',0,'L',true);
            $pdf->Cell(162,6,'1. Tersedia seluas '.$data['simpul_sedia_luasm2'].$m2.' ('.$data['simpul_sedia_luaspersen'].'%)','R',1,'L',true);
            $pdf->Cell(13,6,'','L',0,'L',true);
            $pdf->Cell(162,6,'2. Tidak tersedia seluas '.$data['simpul_total_tdksedia_luasm2'].$m2.'  ('.$data['simpul_total_tdksedia_luaspersen'].'%) dengan alasan sebagai berikut:','R',1,'L',true);
                // if ($data['simpul_tdksedia_ilok_luasm2'] != '') {
                    
                //     $str = 'terdapat Izin Lokasi/KKPR yang masih berlaku seluas '.$data['simpul_tdksedia_ilok_luasm2'].' m2 ('.$data['simpul_tdksedia_ilok_luaspersen'].'%) *apabila pemohon berstatus sewa atau kerjasama dengan pemegang izin lokasi atau kkpr, atau tanah dan kegiatan usahanya akan dialihkan kepada pemohon, maka tergolong tersedia bersyarat.';
                //     $line = count($this->cutTextIntoArray($str, 80));
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'a.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                // if ($data['simpul_tdksedia_skpl_luaspersen'] != '') {
                //     $str = 'terdapat SK Penetapan Lokasi pengadaan tanah untuk kepentingan umum/ proyek strategis nasional seluas '.$data['simpul_tdksedia_skpl_luasm2'].' m2 ('.$data['simpul_tdksedia_skpl_luaspersen'].'%)';
                //     $line = count($this->cutTextIntoArray($str, 80));
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'b.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                // if ($data['simpul_tdksedia3_ket'] != '') {
                //     $str = $data['simpul_tdksedia3_ket'].' '.$data['simpul_tdksedia3_luasm2'].' m2 ('.$data['simpul_tdksedia3_luaspersen'].'%)';
                //     $line = count($this->cutTextIntoArray($str, 80));
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'b.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                // if ($data['simpul_tdksedia4_ket'] != '') {
                //     $str = $data['simpul_tdksedia4_ket'].' '.$data['simpul_tdksedia4_luasm2'].' m2 ('.$data['simpul_tdksedia4_luaspersen'].'%)';
                //     $line = count($this->cutTextIntoArray($str, 80));
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'b.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                // if ($data['simpul_tdksedia5_ket'] != '') {
                //     $str = $data['simpul_tdksedia5_ket'].' '.$data['simpul_tdksedia5_luasm2'].' m2 ('.$data['simpul_tdksedia5_luaspersen'].'%)';
                //     $line = count($this->cutTextIntoArray($str, 80));
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'b.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                    foreach ($tt as $i => $value) {
                            $str = $value->kawasan.' '.$value->luasm2.' m2 ('.$value->persentase.'%)';
                            $line = count($this->cutTextIntoArray($str, 80));
                            $height = $line*6;
                            $pdf->Cell(20,$height,'','L',0,'R',true);
                            $pdf->Cell(5,6,$num[$i].'.','',0,'R',true);
                            $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                        }

            $str = 'Tersedia bersyarat seluas '.$data['simpul_total_sediasyarat_luasm2'].$m2.' ('.$data['simpul_total_sediasyarat_luaspersen'].'%) dengan alasan sebagai berikut:';
            $line = count($this->cutTextIntoArray($str, 80));
            $height = $line*6;
            $pdf->Cell(13,$height,'','L',0,'L',true);
            $pdf->Cell(5,6,'3.','',0,'L',true);
            $pdf->MultiCell(157 , 6, $str, 'R', 'L',1);
                // $str = 'berada di dalam kawasan hutan seluas '.$data['simpul_sediasyarat1_luasm2'].'m2 ('.$data['simpul_sediasyarat1_luaspersen'].'%)';
                // $line = count($this->cutTextIntoArray($str, 80));
                // $height = $line*6;
                // $pdf->Cell(20,$height,'','L',0,'L',true);
                // $pdf->Cell(5,6,'a.','',0,'L',true);
                // $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);

                // $str = 'berada di dalam PIPPIB seluas '.$data['simpul_sediasyarat2_luasm2'].' m2('.$data['simpul_sediasyarat2_luaspersen'].'%)';
                // $line = count($this->cutTextIntoArray($str, 80));
                // $height = $line*6;
                // $pdf->Cell(20,$height,'','L',0,'L',true);
                // $pdf->Cell(5,6,'b.','',0,'L',true);
                // $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                
                // $str = 'berada di dalam lahan sawah dilindungi (LSD)/ LP2B seluas '.$data['simpul_sediasyarat3_luasm2'].'m2 ('.$data['simpul_sediasyarat3_luaspersen'].'%)';
                // $line = count($this->cutTextIntoArray($str, 80));
                // $height = $line*6;
                // $pdf->Cell(20,$height,'','L',0,'L',true);
                // $pdf->Cell(5,6,'c.','',0,'L',true);
                // $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // $abj = ['d','e','f'];
                // $n = 0;
                // if ($data['simpul_sediasyarat4_ket']) {
                  
                //     $str = $data['simpul_sediasyarat4_ket'].' '.$data['simpul_sediasyarat4_luasm2'].' m2 ('.$data['simpul_sediasyarat4_luaspersen'].'%)';
                //     $line = count($this->cutTextIntoArray($str, 80));
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,$abj[$n].'.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                //     $n++;
                // }
                // if ($data['simpul_sediasyarat5_ket']) {
                //     $str = $data['simpul_sediasyarat5_ket'].' '.$data['simpul_sediasyarat5_luasm2'].' m2 ('.$data['simpul_sediasyarat5_luaspersen'].'%)';
                //     $line = count($this->cutTextIntoArray($str, 80));
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,$abj[$n].'.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                //     $n++;
                // }
                 foreach ($tb as $i => $value) {
                            $str = $value->kawasan.' '.$value->luasm2.' m2 ('.$value->persentase.'%)';
                            $line = count($this->cutTextIntoArray($str, 80));
                            $height = $line*6;
                            $pdf->Cell(20,$height,'','L',0,'R',true);
                            $pdf->Cell(5,6,$num[$i].'.','',0,'R',true);
                            $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                        }

               
            $str = 'Ketentuan dan syarat penguasaan dan pemilikan tanah adalah sebagai berikut (mengacu kepada Lampiran 22 sesuai kondisi lokasi): ';
            $line = count($this->cutTextIntoArray($str, 80));
            $height = $line*6;
            $pdf->Cell(10,$height,'','L',0,'L',true);
            $pdf->Cell(5,6,'B.','',0,'L',true);
            $pdf->MultiCell(160 , 6, $str, 'R', 'L',1);
                // $str = 'Penguasaan dan/atau pemilikan tanah harus didasarkan pada alat bukti hak atas tanah berupa bukti tertulis dan/atau bukti penguasaan tanah berupa alas hak dan/atau surat pernyataan penguasaan tanah serta bukti peralihan hak atas tanah';
                // $line = count($this->cutTextIntoArray($str, 70));
                // $height = $line*6;
                // $pdf->Cell(20,$height,'','L',0,'L',true);
                // $pdf->Cell(5,6,'a.','',0,'L',true);
                // $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);

            //     $str = 'Penguasaan dan/atau pemilikan tanah tidak boleh melebihi batas maksimum sesuai dengan ketentuan peraturan perundang - undangan;';
            //     $line = count($this->cutTextIntoArray($str, 70));
            //     $height = $line*6;
            //     $pdf->Cell(20,$height,'','L',0,'L',true);
            //     $pdf->Cell(5,6,'b.','',0,'L',true);
            //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);

            //     $str = 'Penguasaan dan/atau pemilikan tanah yang sudah diperoleh untuk segera didaftarkan hak atas tanahnya;';
            //     $line = count($this->cutTextIntoArray($str, 70));
            //     $height = $line*6;
            //     $pdf->Cell(20,$height,'','L',0,'L',true);
            //     $pdf->Cell(5,6,'c.','',0,'L',true);
            //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);

            //     $str = 'Penguasaan dan/atau pemilikan harus memiliki fungsi sosial;';
            //     $line = count($this->cutTextIntoArray($str, 70));
            //     $height = $line*6;
            //     $pdf->Cell(20,$height,'','L',0,'L',true);
            //     $pdf->Cell(5,6,'d.','',0,'L',true);
            //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
            //     $abj = ['e','f','g','h','i'];
            //     $n = 0;
            //     if($data['simpul_ketentuan_gq1'] != ''){
            //         $str = $data['simpul_ketentuan_gq1'];
            //         $line = count($this->cutTextIntoArray($str, 70));
            //         $height = $line*6;
            //         $pdf->Cell(20,$height,'','L',0,'L',true);
            //         $pdf->Cell(5,6,$abj[$n].'.','',0,'L',true);
            //         $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
            //         $n++;
            //     }
               
            //     if($data['simpul_ketentuan_gq2'] != ''){
            //         $str = $data['simpul_ketentuan_gq2'];
            //         $line = count($this->cutTextIntoArray($str, 70));
            //         $height = $line*6;
            //         $pdf->Cell(20,$height,'','L',0,'L',true);
            //         $pdf->Cell(5,6,$abj[$n].'.','',0,'L',true);
            //         $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
            //         $n++;
            //     }
            //     if($data['simpul_ketentuan_gq3'] != ''){
            //         $str = $data['simpul_ketentuan_gq3'];
            //         $line = count($this->cutTextIntoArray($str, 70));
            //         $height = $line*6;
            //         $pdf->Cell(20,$height,'','L',0,'L',true);
            //         $pdf->Cell(5,6,$abj[$n].'.','',0,'L',true);
            //         $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
            //         $n++;
            //     }
            //     if($data['simpul_ketentuan_gq4'] != ''){
            //         $str = $data['simpul_ketentuan_gq4'];
            //         $line = count($this->cutTextIntoArray($str, 70));
            //         $height = $line*6;
            //         $pdf->Cell(20,$height,'','L',0,'L',true);
            //         $pdf->Cell(5,6,$abj[$n].'.','',0,'L',true);
            //         $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
            //         $n++;
            //     }
            //     if($data['simpul_ketentuan_gq5'] != ''){
            //         $str = $data['simpul_ketentuan_gq5'];
            //         $line = count($this->cutTextIntoArray($str, 70));
            //         $height = $line*6;
            //         $pdf->Cell(20,$height,'','L',0,'L',true);
            //         $pdf->Cell(5,6,$abj[$n].'.','',0,'L',true);
            //         $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
            //         $n++;
            //     }
            foreach ($milik as $i => $value) {
                    $str = $value->keterangan;
                    $line = count($this->cutTextIntoArray($str, 60));
                    $height = $line*6;
                    $pdf->Cell(20,$height,'','L',0,'L',true);
                    $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                    $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                }

            
            $str = 'Ketentuan dan syarat-syarat penggunaan dan pemanfaatan tanah adalah sebagai berikut (mengacu kepada Lampiran I sesuai kondisi lokasi)';
            $line = count($this->cutTextIntoArray($str, 70));
            $height = $line*6;
            $pdf->Cell(10,$height,'','L',0,'L',true);
            $pdf->Cell(5,6,'C.','',0,'L',true);
            $pdf->MultiCell(160 , 6, $str, 'R', 'L',1);
                // if($data['simpul_ketentuan_gq1'] != ''){

                //     $str = $data['simpul_ketentuan_gq1'];
                //     $line = count($this->cutTextIntoArray($str, 70));
                //     $line = $line == 0 ? 1 :$line;
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'a.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                // if($data['simpul_ketentuan_gq2'] != ''){

                //     $str = $data['simpul_ketentuan_gq2'];
                //     $line = count($this->cutTextIntoArray($str, 70));
                //     $line = $line == 0 ? 1 :$line;
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'b.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                // if($data['simpul_ketentuan_gq3'] != ''){

                //     $str = $data['simpul_ketentuan_gq3'];
                //     $line = count($this->cutTextIntoArray($str, 70));
                //     $line = $line == 0 ? 1 :$line;
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'c.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                // if($data['simpul_ketentuan_gq4'] != ''){

                //     $str = $data['simpul_ketentuan_gq4'];
                //     $line = count($this->cutTextIntoArray($str, 70));
                //     $line = $line == 0 ? 1 :$line;
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'d.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                // if($data['simpul_ketentuan_gq5'] != ''){
                    
                //     $str = $data['simpul_ketentuan_gq5'];
                //     $line = count($this->cutTextIntoArray($str, 70));
                //     $line = $line == 0 ? 1 :$line;
                //     $height = $line*6;
                //     $pdf->Cell(20,$height,'','L',0,'L',true);
                //     $pdf->Cell(5,6,'e.','',0,'L',true);
                //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                // }
                foreach ($manfaat as $i => $value) {
                        $str = $value->keterangan;
                        $line = count($this->cutTextIntoArray($str, 60));
                        $height = $line*6;
                        $pdf->Cell(20,$height,'','L',0,'L',true);
                        $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                        $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                    }
            $str = 'Ketentuan perolehan Tanah dan peralihan Hak Atas Tanah (bagi pemohon Pelaku Usaha yang membutuhkan tanah namun belum memiliki/menguasai tanah):';
            $line = count($this->cutTextIntoArray($str, 70));
            $height = $line*6;
            $pdf->Cell(10,$height,'','L',0,'L',true);
            $pdf->Cell(5,6,'D.','',0,'L',true);
            $pdf->MultiCell(160 , 6, $str, 'R', 'L',1);
            //     $str = 'Dapat melakukan perolehan tanah setelah memperoleh Persetujuan KKPR atau Rekomendasi KKPR dalam jangka waktu sesuai masa berlakunya KKPR';
            //     $line = count($this->cutTextIntoArray($str, 70));
            //     $height = $line*6;
            //     $pdf->Cell(20,$height,'','L',0,'L',true);
            //     $pdf->Cell(5,6,'a.','',0,'L',true);
            //     $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
            
            // $pdf->Cell(175,6,'','RL',1,'L',true);
            
	        // $pdf->AddPage();
                // $pdf->Cell(175,1,'','RL',1,'L',true);
                // $str = 'Dapat melakukan perolehan tanah setelah memperoleh Persetujuan KKPR atau Rekomendasi KKPR dalam jangka waktu sesuai masa berlakunya KKPR';
                // $line = count($this->cutTextIntoArray($str, 70));
                // $height = $line*6;
                // $pdf->Cell(20,$height,'','L',0,'L',true);
                // $pdf->Cell(5,6,'a.','',0,'L',true);
                // $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);

                // $str = 'Wajib mendaftarkan tanah yang telah diperoleh pada Kantor Pertanahan setempat paling lama 1 (satu) tahun sejak berakhirnya masa berlaku KKPR;';
                // $line = count($this->cutTextIntoArray($str, 70));
                // $height = $line*6;
                // $pdf->Cell(20,$height,'','L',0,'L',true);
                // $pdf->Cell(5,6,'b.','',0,'L',true);
                // $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                
                // $str = 'Wajib menggunakan dan memanfaatkan tanah yang telah diperoleh sesuai dengan rencana kegiatan berusahanya;';
                // $line = count($this->cutTextIntoArray($str, 70));
                // $height = $line*6;
                // $pdf->Cell(20,$height,'','L',0,'L',true);
                // $pdf->Cell(5,6,'c.','',0,'L',true);
                // $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);

                // $str = 'Selama belum dibebaskan, semua hak atau kepentingan pihak lain yang sudah ada atas tanah pada lokasi yang dimohon tidak berkurang dan tetap diakui haknya, termasuk kewenangan yang menurut hukum dipunyai oleh pemegang Hak Atas Tanah untuk memperoleh tanda bukti hak (sertipikat), dan kewenangan untuk menggunakan dan memanfaatkan tanahnya bagi keperluan pribadi atau usahanya sesuai dengan rencana tata ruang yang berlaku, serta kewenangan untuk mengalihkannya kepada pihak/perseorangan lainnya.';
                // $line = count($this->cutTextIntoArray($str, 70));
                // $height = $line*6;
                // $pdf->Cell(20,$height,'','L',0,'L',true);
                // $pdf->Cell(5,6,'d.','',0,'L',true);
                // $pdf->MultiCell(150 , 6, $str, 'R', 'L',1);
                foreach ($peralihan as $i => $value) {
                        $str = $value->keterangan;
                        $line = count($this->cutTextIntoArray($str, 60));
                        $height = $line*6;
                        $pdf->Cell(20,$height,'','L',0,'L',true);
                        $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                        $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                    }

                $str = 'Pertimbangan Teknis Pertanahan bukan merupakan alas hak atas tanah ataupun izin membuka tanah.';
                $line = count($this->cutTextIntoArray($str, 70));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'E.','',0,'L',true);
                $pdf->MultiCell(160 , 6, $str, 'R', 'L',1);
                
                $str = 'Peta Risalah Pertimbangan Teknis Pertanahan sebagaimana terlampir merupakan bagian yang tidak terpisahkan dari Risalah Pertimbangan Teknis Pertanahan ini.';
                $line = count($this->cutTextIntoArray($str, 70));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'F.','',0,'L',true);
                $pdf->MultiCell(160 , 6, $str, 'R', 'L',1);

                $pdf->Cell(175,6,'','RL',1,'L');
                $pdf->Cell(175,6,$this->camel($lokasi_surat).', '.tanggal_indonesia($data['tgl_surat']),'RL',1,'C',true);
                $pdf->Cell(175,6,'Tim Pertimbangan Teknis Pertanahan,','RL',1,'C');
                $pdf->Cell(175,6,'','RL',1,'L');
                
                $pdf->Cell(30,6,'','L',0,'L');
                $pdf->Cell(145,6,'Ketua, '.$data['tim_ketua'],'R',1,'L');
                $pdf->Cell(30,6,'','L',0,'L');
                $pdf->Cell(90,6,'NIP '.$data['nip_ketua'],'',0,'L');
                $pdf->Cell(55,6,'......................','R',1,'L');

                $pdf->Cell(175,6,'','RL',1,'L');
                $pdf->Cell(30,6,'','L',0,'L');
                $pdf->Cell(145,6,'Sekretaris, '.$data['tim_sekretaris'],'R',1,'L');
                $pdf->Cell(30,6,'','L',0,'L');
                $pdf->Cell(90,6,'NIP '.$data['nip_sekretaris'],'',0,'L');
                $pdf->Cell(55,6,'......................','R',1,'L');
                
                $pdf->Cell(175,6,'','RL',1,'L');
                $pdf->Cell(20,6,'','L',0,'L');
                $pdf->Cell(155,6,'Anggota, ','R',1,'L');
                
                $pdf->Cell(175,6,'','RL',1,'L');
                foreach ($anggota as $i => $value) {
                    $num = $i+1;
                    $pdf->Cell(10,6,'','L',0,'L');
                    $pdf->Cell(5,6,$num.'.','',0,'L');
                    $pdf->Cell(160,6,$value->nama,'R',1,'L');
                    $pdf->Cell(10,6,'','L',0,'L');
                    $pdf->Cell(5,6,'','',0,'L');
                    $pdf->Cell(105,6,'NIP '.$value->nip,'','','L');
                    $pdf->Cell(55,6,'......................','R',0,'L');
                    $pdf->Cell(10,6,'','L',1,'L');
                }
                $pdf->Cell(175,6,'','RBL',1,'L');
                

            $pdf->Output();
    }

    function cutTextIntoArray($text, $maxLength) {
        $segments = array();
        
        while (strlen($text) > $maxLength) {
            // Find the last space within the allowed length
            $lastSpace = strrpos(substr($text, 0, $maxLength), ' ');
    
            if ($lastSpace === false) {
                // No space found within the limit, truncate at the maxLength
                $segment = substr($text, 0, $maxLength);
            } else {
                // Truncate at the last space
                $segment = substr($text, 0, $lastSpace);
            }
    
            $segments[] = $segment;
            $text = substr($text, strlen($segment));
        }
    
        if (!empty($text)) {
            $segments[] = $text; // Add any remaining text
        }
    
        return $segments;
    }
    
    function export_pdff(){
        $pdf = new FPDF('L', 'mm','Letter');
		foreach ($datas as $key => $value) {
	        $pdf->AddPage();
	        $pdf->SetFont('Arial','B',12);
	        $pdf->Cell(190,7,'RISALAH PERTIMBANGAN TEKNIS PERTANAHAN',0,1,'C');
			$pdf->SetTextColor(0,0,0);
	        // foreach ($value as $k => $v) {
		    //     $pdf->Cell(10,7,$k+1,1,0,'C');
		    //     $pdf->Cell(55,7,$v->jenis_prasarana,1,0,'C');
		    //     $pdf->Cell(20,7,$v->keberadaan_ada,1,0,'C');
		    //     $pdf->Cell(22,7,$v->keberadaan_tidak_ada,1,0,'C');
		    //     $pdf->Cell(23,7,$v->keberadaan_kosong,1,0,'C');
		    //     $pdf->Cell(18,7,$v->kondisi_baik,1,0,'C');
		    //     $pdf->Cell(18,7,$v->kondisi_cukup,1,0,'C');
		    //     $pdf->Cell(17,7,$v->kondisi_kurang,1,0,'C');
		    //     $pdf->Cell(17,7,$v->kondisi_kosong,1,0,'C');
		    //     $pdf->Cell(22,7,$v->kepemilikan_individu,1,0,'C');
		    //     $pdf->Cell(22,7,$v->kepemilikan_komunal,1,0,'C');
		    //     $pdf->Cell(21,7,$v->kepemilikan_lainnya,1,1,'C');
	        // }

	        // $pdf->Cell(225,7,'',0,1,'L');
	        
		}
	    
	  
        $pdf->Output();
    }

    function camel($str) {
        $str = strtolower($str);
        $exp = explode(' ',$str);
        $ret='';
        foreach ($exp as $key => $value) {
            $ret .= ucfirst($value).' ';
        }
        return $ret;

    }

    function get_edit($id) {
        $this->db->where('id_juknisptp', $id);
        $afk = $this->db->get('t_juknisrisalah_afk')->result();
        $this->db->where('id_juknisptp', $id);
        $tb = $this->db->get('t_juknisrisalah_tb')->result();
        $this->db->where('id_juknisptp', $id);
        $tt = $this->db->get('t_juknisrisalah_tt')->result();
        $this->db->where('id_juknisptp', $id);
        $milik = $this->db->get('t_juknisrisalah_milik')->result();
        $this->db->where('id_juknisptp', $id);
        $manfaat = $this->db->get('t_juknisrisalah_manfaat')->result();
        $this->db->where('id_juknisptp', $id);
        $peralihan = $this->db->get('t_juknisrisalah_peralihan')->result();
        $this->db->where('id_juknisptp', $id);
        $anggota = $this->db->get('t_juknisrisalah_anggota')->result();
        
        echo json_encode(
                            [
                                'afk' => $afk,
                                'tt' => $tt,
                                'tb' => $tb,
                                'milik' => $milik,
                                'manfaat' => $manfaat,
                                'peralihan' => $peralihan,
                                'anggota' => $anggota,
                            ]
                            );
    }

       function get_tema() 
    {
        $this->db->where('parent', '209');
        $this->db->where('urutan is not null');
        $data = $this->db->get('aset_module')->result();
        echo json_encode($data);
    }
     
    

}