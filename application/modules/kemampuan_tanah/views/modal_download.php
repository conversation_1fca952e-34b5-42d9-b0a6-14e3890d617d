<style>
.control-label{
  font-weight:bold;
}
  </style>

<div class="modal fade" id="modal-download" tabindex="-1" role="dialog">
 
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:125%">
            <div class="modal-header">
                <h4 class="modal-title">Upload File</h4>
                <button type="button" class="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close">
                    <span
                        aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body">
              
            <form class="form-horizontal" id="submit">
              <input type="hidden" id="id_up" name="id_up">
              <input type="hidden" id="utahun_data" name="tahun_data">
              <fieldset class="border rounded-3 p-3">
              <legend class="float-none w-auto px-3">Template Data</legend>
              <div class="form-group">
                <label class="col-md-12 control-label" for="textinput">Jenis</label>  
                <div class="col-md-12">
                <select id="layer" name="layer" required class="bootstrap-select form-control" data-live-search="true">
                      <!-- <option value="#">Pilih</option> -->
                    </select>  
                <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
                </div>
              </div>
              
              
              <div class="form-group">
                  <label class="col-md-12 control-label" for="kd_prov">Provinsi</label>
                  <div class="col-md-12">
                    <select id="kd_prov" required name="kd_prov" class="bootstrap-select form-control" data-live-search="true">
                      <option value="#">Pilih</option>
                    </select>
                  </div>
                </div>
                

                <div class="form-group">
                  <label class="col-md-12 control-label" for="kd_kabkot">Kabupaten/Kota</label>
                  <div class="col-md-12">
                    <select id="kd_kabkot"  required name="kd_kabkot" class="bootstrap-select form-control" data-live-search="true">
                     
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-md-12 control-label" for="kd_kabkot">Tahun Data</label>
                  <div class="col-md-12">
                    <select id="tahun_data"  required name="tahun_data" class="bootstrap-select form-control" data-live-search="true">
                    <!-- <option value="">Tidak Ada Tahun</option> -->
                    <?php
                      $y=intVal(date('Y'));

                      for ($i=$y; $i >= 2000 ; $i--) { 
                        
                        echo '<option value="'.$i.'">'.$i.'</option>';
                      }
                      ?>
                  </select>
                  </div>
                </div>
                <div class="form-group">
                <div class="col-md-12">
                  <button onclick="downloadTemplate()" class="btn btn-outline-primary  " style="float:right"><img style="height:30px" src="<?=base_url().'uploads/icon/logo-shp.png'?>" alt="">Download Template</button>
                </div>
              </div>
                </fieldset>

                <!-- Select Basic -->
            
            <fieldset class="border rounded-3 p-3">
              <legend class="float-none w-auto px-3">Upload File</legend>
                <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
              <div class="form-group">
                <div class="col-md-12">
                <input id="filess" name="filess" type="file" required placeholder="" class="form-control input-md">
                  
                </div>
              </div>
              
              <div class="form-group">
                <div class="col-md-12">
                <button type="submit" 
                    class="btn btn-primary btn-md">Simpan</button>
                </div>
              </div>
              </form>
            </fieldset>
                    

                <br>
              <div class="dt-responsive table-responsive">
                <!-- <table id="table_id2" class="table table-striped table-bordered nowrap"  style="width:100%;">
                    <thead>
                        <tr>
                            <th>File </th>
                            <th>Status</th>
                            <th>Luas</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table> -->
            </div>
                </div>
            <div class="modal-footer">
                <button type="button"
                    class="btn btn-default waves-effect "
                    data-bs-dismiss="modal">Close</button>
             
            </div>

        </div>
    </div>
</div>