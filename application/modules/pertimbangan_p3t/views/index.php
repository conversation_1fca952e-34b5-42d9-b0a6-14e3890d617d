<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>

 <div class="page-body">
    <!-- Server Side Processing table start -->
    <div class="card">
        <div class="card-header">
            <h5><?=$title;?></h5>
        </div>
        <div class="card-block">
        <div class="row">    
                <div class="col-sm-6">
                    <button type="button" style="float:left!important" class=" btn btn-primary waves-effect waves-light add" onclick="dtUpload();">Tambah PTP IPPT Provinsi

                </div>
                <div class="col-sm-6 d-flex align-items-end justify-content-end" style="">
                    <!-- <button style="margin-left:3px;border-radius:20%;color:blue;border-color:blue;" onclick="downloadCaraUpload()" title="Petunjuk Upload" class=""><i class="fa-regular fa-2x fa-file-pdf"></i></button> -->
                    <!-- <button style="margin-left:3px;border-radius:20%;color:red;border-color:red" title="Dokumen " class=""onclick="viewDokumen()"><i class="fa-regular fa-2x fa-file-pdf"></i></button> -->
                </div>
                <div class="col-md-4" style="margin-top:20px;display:inline">
                        <label style="font-size:15pt;">Provinsi</label>
                        <select id="ikd_prov"onchange="filterChange('prov')" name="ikd_prov" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                </div>
                <div class="col-md-4" style="margin-top:20px">
                        <label style="font-size:15pt">Kabupaten/Kota</label>
                        <select id="ikd_kabkot" onchange="filterChange('kabkot')" name="ikd_kabkot" class="bootstrap-select form-control" data-live-search="true">
                        <!-- <option value="#">Pilih</option> -->
                        </select>
                </div>
                
                
            </div>
            <br/>
            <div class="dt-responsive table-responsive">
                <table id="dt-server-processing" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Provinsi</th>
                            <th>Kab/Kota</th>
                            <th>Luas(Ha)</th>
                            <th>Tahun</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <!-- Server Side Processing table end -->
</div>   

<input type="hidden" name="alias_menu" id="alias_menu" value="<?php echo $menu?>">
<?php echo $jv_script; ?>
<?php //echo $modal_tambah; ?>
<?php echo $modal_download; ?>
<?php echo $modal_edit; ?>
<?php echo $modal_history; ?>


