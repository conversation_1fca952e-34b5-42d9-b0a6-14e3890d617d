<script>
    var xhrdata = null;
    var table = null;
    function clear_input() {
        $("#formData :input").val("");
    }

    function listing() {
        table = $('#table_id').DataTable({
            "draw": 0,
            "columnDefs": [{"orderable": true, "targets": [0, 1,2,3,4]}],
            //"columns": [
            //{ "name": "kd_prov_irmsv3" },
            //{ "name": "kd_prov_rkakl" },
            //{ "name": "nama_dapil" },
            //{ "name": "kd_dapil" }
            //],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>/reff_ruas/ssp"},
            "aoColumnDefs": [
                {
                    "aTargets": [4],
                    "mRender": function (data, type, row) {

                        var id = row[4];

                        console.log(id);
                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-warning btn-xs'>",
                            "<i class='fa fa-search'>",
                            "</i>",
                            "</button>\n\
                        "].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fast backward ui icon'></i>",
                    "last": "<i class='fast forward ui icon'></i>",
                    "next": "<i class='step forward ui icon'></i>",
                    "previous": "<i class='step backward ui icon'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();

            console.log(xhrdata);
        });
        //});


    }

    function dtEditRow(id) {
//   / alert(id);

        console.log(xhrdata);

        var data_selected = xhrdata.data.filter(x => x[4] == id)[0];  //46

        var waydata = {
            id_ruas:data_selected[0],
            no_ruas: data_selected[1],
            linkname: data_selected[2],
            sk_tahun: data_selected[3],
            id_ruas: data_selected[4]
            

        }

        way.set('formData', waydata);

        $('#modalTitle').text('Detail Data');
        $('#modeform').val('edit');
        $("input").prop('disabled', true);
        $('#modal-tambah').modal('show');
    }



    $(document).ready(function () {
        listing();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        //bind_modul();
        //bind_level();
        initCombobox('kode_module', 30);
        initCombobox('id_user_group', 27);
        $(".si-close").click(function(){
         $("input").prop('disabled', false);
        })

    });



</script>