<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Maintenance extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();
        $this->load->helper('dtssp'); //datatable server side processing helper
    }

    public function index() {
        header("Access-Control-Allow-Origin: *");
        $data = array();
        /*         * keterangan parameter
          $this->template->load('default_layout', 'contents' , 'index', $data);
          1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
          2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
          3.index = nama view yang akan di load
         * */
        $title = "Perbaikan";
        $js_file = $this->load->view('reff_ruas/js_file', '', true);
        $modal_filter = $this->load->view('reff_ruas/modal_filter', '', true);
        $modal_tambah = $this->load->view('reff_ruas/modal_tambah', '', true);
        $data = array("modal_filter" => $modal_filter,
            "modal_tambah" => $modal_tambah,
            "title" => $title,
        );
        $this->template->set('title', $title);
        $this->template->set('jv_script', $js_file);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }
    public function ssp() {
        $table = 'v_ruas';
        $primaryKey = 'id_ruas';
        $columns = array(
            array('db' => 'id_ruas', 'dt' => 0),
            array('db' => 'no_ruas', 'dt' => 1),
            array('db' => 'linkname', 'dt' => 2),
//            array('db' => 'shape', 'dt' => 2),
            array('db' => 'sk_tahun', 'dt' => 3),
            array('db' => 'id_ruas', 'dt' => 4)

        );

        datatable_ssp($table, $primaryKey, $columns);
    }

  


    function get_response() {

        $url = '************:12890/testdata/listuser'; //url di set global

        $ch = curl_init($url);

        $header = [];
        $header[] = 'Content-type: application/json';
        $header[] = 'client-id:webgis'; //client-id di set global / di constructor
        $header[] = 'client-pass:webgisindonesia'; //password di set global / di constructor
        $header[] = "Cache-Control: no-cache";
        $header[] = "accept-encoding:*";
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);

        if (!$result) {
            //bisa ditampilkan errornya ketika development, sebaiknya ketika production di "die"
            die("Koneksi Gagal");
        } else {
            return $result;
            //print_r($result);
        }
    }
}
