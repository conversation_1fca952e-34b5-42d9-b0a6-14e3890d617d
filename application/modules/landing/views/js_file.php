<script type="text/javascript" src="<?php echo base_url();?>assets/semantic/js/jquery-2.1.4.min.js"></script>
<script type="text/javascript" src="<? echo base_url();?>assets/js/sweetalert11.js"></script>
<!-- jquery slimscroll js -->
<script type="text/javascript"
    src="<? echo base_url();?>assets/themes/adminity/bower_components/jquery-slimscroll/jquery.slimscroll.js"></script>

<script>
function viewImage(v) {

    $('#imgView').attr('src', v);
    $('#images').modal('show');
}

function ssologout() {
    //alert("force_logout");
    var url =
        "https://logindev.atrbpn.go.id/auth/realms/internal-realm/protocol/openid-connect/logout?redirect_uri=http://************:4980/atrpgt";
    window.open(url, "_self")
}

var getUrlParameter = function getUrlParameter(sParam) {
    var sPageURL = window.location.search.substring(1),
        sURLVariables = sPageURL.split('&'),
        sParameterName,
        i;

    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');

        if (sParameterName[0] === sParam) {
            return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
        }
    }
    return false;
};



$(document).ready(function() {
    var code = getUrlParameter('code');
    if (code) {
        var data_post = {
            "code": code,
            "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
        };
        jQuery.ajax({
            contentType: 'application/x-www-form-urlencoded',
            dataType: "json",
            type: "POST",
            data: data_post,
            url: "<?php echo base_url() . "login/sso_login"; ?>",
            success: function(response) {
                if (response.status == "sukses") {
                    var role = [5];

                    Swal.fire({
                        icon: 'success',
                        title: 'Login SSO Berhasil!',
                        html: 'Silahkan Tunggu.',
                        timerProgressBar: true,
                        didOpen: () => {
                            Swal.showLoading()
                        }

                    })

                    window.location.href = "<?php echo base_url();?>dashboard4";

                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: response.msg,
                    })


                    // $("#pesan").html(
                    //     "<i class='fa fa-warning'></i> password atau username salah, login gagal <a class='alert-link' href='javascript:void(0)''></a>!"
                    // )

                    // $("#alert_login").css({
                    //     display: "block"
                    // });
                    setTimeout(ssologout, 3000);
                }

            },
            error: function(xhr, ajaxOptions, thrownError) {
                // alert(xhr.status);
                // alert(thrownError);
            }
        });

    }

    $('.carousel-item-img').slimscroll({
        color: 'rgba(0,0,0,0.5)',
        size: '0px',
        height: '473px',
        alwaysVisible: true
    });
});
</script>