<?=$head?>
<style>
h3 {
    color: white;
}
</style>
<div class="container">
    <div class="row align-items-center">
        <div class="col-12">
            <div class="row justify-content-center">
                <div data-aos="fade-up" class=" text-center hero-text">
                    <h3>Struktur Organisasi</h3>
                    <!-- <img style="width:100%" title="Klik Untuk Memperbesar" onclick="view()" src="<?=base_url()?>assets/landingpage/assets/img/<?=$data['struktur']?>" alt=""> -->
                    <img style="width:100%;border-radius:10px"
                        src="<?=base_url()?>assets/landingpage/assets/img/<?=$data['struktur']?>" alt="">

                </div>
            </div>
        </div>
    </div>
</div>
<?=$foot?>

<style>
/* .modal-body{
      width:100%;
      height:100%
      overflow:auto;
  }

  .modal-body::-webkit-scrollbar {
      width: 10px;
  }
  
  .modal-body::-webkit-scrollbar-track {
      box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.3);
  }
  
  .modal-body::-webkit-scrollbar-thumb {
      background-color: darkgrey;
      outline: 0px solid slategrey;
  } */

.my-custom-scrollbar {
    position: relative;

    overflow: auto;
}
</style>
<div id="images" class="modal" tabindex="-1">
    <div class="modal-dialog modal-xl" style="scrollbar-width: none !important;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body my-custom-scrollbar my-custom-scrollbar-primary" id="modal-body"
                style="scrollbar-width: none !important;">
                <div class="item-img" style="border-radius:10px">

                    <img id="imgView" src="<?=base_url()?>assets/landingpage/assets/img/<?=$data['struktur']?>" alt="">
                </div>
            </div>
            <!-- <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div> -->
        </div>
    </div>
</div>


<script>
$(document).ready(function() {

    var myCustomScrollbar = document.querySelector('.my-custom-scrollbar');
    var ps = new PerfectScrollbar(myCustomScrollbar);

    var scrollbarY = myCustomScrollbar.querySelector('.ps__rail-y');

    myCustomScrollbar.onscroll = function() {
        alert('ok');
        scrollbarY.style.cssText =
            `top: ${this.scrollTop}px!important; height: 400px; right: ${-this.scrollLeft}px`;
    }
});

function view() {
    $('#images').modal('show');
}
</script>