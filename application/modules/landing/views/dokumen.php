<?=$head?>
<div class="container">
        <div class="row align-items-center">
          <div class="col-12">
            <div class="row justify-content-center">
              <div data-aos="fade-up" data-aos-delay="" class="col-md-10 text-center hero-text" >
              <style>/* Add this to import Font-Awesome */
               
                @import  url(//netdna.bootstrapcdn.com/font-awesome/3.2.1/css/font-awesome.css);

                /* Add this to important Google Font (Oswald) */
                /* @import  url(https://fonts.googleapis.com/css?family=Oswald:400,300,700); */

                @import url('https://fonts.cdnfonts.com/css/poppins');
                
                /* Main accordion css */
                #accordion {
                    /* max-width: 1000px; You can change this */
                    margin: auto; You can remove this if you do not need it to align in the center of your element
                    /* display: block;  */
                }

                /* This controls the text inside the div, you can play with this by adding background-color, border, etc. */
                #accordion div {
                    padding: 5px 10px;
                    display: inline-block;
                    overflow: auto;
                }

                /* This controls the header */
                #accordion h4 {
                    display: block;
                    text-decoration: none;
                    outline: none;
                    cursor: pointer;
                    padding: 8px 11px;
                    font-family: 'Poppins', sans-serif;
                    letter-spacing: 1px;
                    font-weight: 700;
                    margin: 5px;
                }

                #accordion div p {
                margin-top: 0;
                }

                /* This CSS controls the Font Awesome Icon, You can float: left as well and add margin-right: 5px; */
                .icon-expand,.icon-collapse {
                    float: right;
                    margin-top: 5px;
                }

                /* USE THE BELOW CSS FOR BLUE */
                .blue h4 {
                    background: linear-gradient(to right,#215e99,#30a8c4); /* You can change this to whatever background color you want for the header */
                    color: #fff; /* You can change this to whatever font color you want for the header */
                    border-radius: 5px;
                    text-align:left;
                }
                 .acor{
                        width:100%;
                        height:100%
                        overflow:auto;
                    }
                .acor{
                    scrollbar-width: none;
                }
                .acor::-webkit-scrollbar {
                    width: 3px;
                }
                
                .acor::-webkit-scrollbar-track {
                    box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.3);
                }
                
                .acor::-webkit-scrollbar-thumb {
                    background-color: darkgrey;
                    outline: 0px solid slategrey;
                } 
                
            </style>

                <h3 style="color:white"><center>Download Dokumen</center></h3>
                <div style="padding:5px;background-color:white;border-radius:0.375rem;">
                    <div style="height:80vh;border-radius:0.375rem;" class="overflow-auto acor ">
                        <div class="blue card " style="" id="accordion">
                            <?php foreach ($data as $key => $value) { ?>
                                <h4><?=$key?></h4>
                                <div style="color:black">
                                    <table class="table">
                                        <thead>
                                            <?php foreach ($value as $k => $v) {
                                                $judul = strtoupper(str_replace('_',' ',$v->judul_dok));
                                                $ext = pathinfo($v->path, PATHINFO_EXTENSION);
                                                $path = $ext != 'pdf' ? $v->path :base_url().$v->path;

                                                ?>
                            
                                                <tr >
                                                    <th scope="col" style='text-align:left'><span class="align-middle" style="color:black"><?=$judul?></span></th>
                                                    <th scope="col"> <button type="button" class="" style="border-color: #30a8c4;backgroud-color:white;border-radius:20%" onclick="lihat('<?=$path?>')"><i class="bi bi-download"></i></button></th>
                                                </tr>
                                            <?php }?>
                                        </thead>
                                    </table>
                                </div>
                            <?php }?>
                        </div>
                    </div>
                </div>
            </div>
          </div>
        </div>
      </div>


<div id="pdf" class="modal" tabindex="-1" style="height:100%;margin-top:30px;">
    <div class="modal-dialog modal-xl" style="height:90%;margin-top:20hv">
        <div class="modal-content" style="height:100%">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="modal-body" >
                <iframe src="" id="pdfframe" frameborder="0" style="width:100%;height:100%"></iframe>
            </div>
            <!-- <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div> -->
        </div>
    </div>
</div>   
<?=$foot?>

<!-- jquery slimscroll js -->
<script type="text/javascript" src="<? echo base_url();?>assets/themes/adminity/bower_components/jquery-slimscroll/jquery.slimscroll.js"></script>
	
<script>
$(function() {
    $( document ).ready(function() {
        // $('#accordion').slimscroll({
        //         color: 'rgba(0,0,0,0.5)',
        //         size: '0px',
        //         height: '473px',
        //         alwaysVisible: true
        //     });
        
    });
    $("#accordion").accordion({
        // You can change this to auto or fill for more info view https://api.jqueryui.com/accordion/#option-heightStyle
        heightStyle: "content",
        // Set this to true if you want the active accordion to remain open
        collapsible: true,
        // SSetting active to false will collapse all panels. This requires the collapsible option to be true.
        active: false
    });
    //initialize accordion            
    $("#accordion").accordion();
    //set accordion header options
    $("#accordion").accordion("option", "icons", {
        // You can change this to differnt Font Awesome class  
        'header': 'icon-expand',
        // You can change this to differnt Font Awesome class
        'activeHeader': 'icon-collapse'
    });
});

function lihat(v) {
    // alert(v);exit();
    var ext = v.substr( (v.lastIndexOf('.') +1) );
    if(ext == 'pdf'){
        $('#pdfframe').attr('src',v);
        $('#pdf').modal('show');
        $( "div" ).removeClass( "modal-backdrop" )
    }else{
        var v = btoa(v);
        window.location.href = "<?php echo base_url().'landing/downloadDokumen/'?>" +v;
    }

}


</script>

<script src="<?=base_url()?>assets/js/jqueryUi.min.js" ></script>

<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js" integrity="sha512-57oZ/vW8ANMjR/KQ6Be9v/+/h6bq9/l3f0Oc7vn6qMqyhvPd1cvKBRWWpzu0QoneImqr2SkmO4MSqU+RpHom3Q==" crossorigin="anonymous" referrerpolicy="no-referrer"></script> -->


