            </div>
        </div>
      </div>
    </div>
  </section><!-- End Hero -->

<main id="main">

  <!-- ======= Home Section ======= -->


</main><!-- End #main -->


    <div style="background-image: linear-gradient(to right, #223e70 , #4bacbd);" style="margin-top:30px">
      <div class="row align-items-center" style="text-align:center">
      <p class="copyright" style="color:white;margin-top:10px">&copy; Copyright @<?=date('Y')?>. <PERSON><PERSON> (BPN). Jalan H. Agus <PERSON> 58, Menteng Jakarta Pusat 10350 Telephon : (021) 3195574</p>
      </div>
    </div>
</footer> 

<a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

<!-- Vendor JS Files -->
<script src="<?=base_url()?>assets/landingpage/assets/vendor/aos/aos.js"></script>
<script src="<?=base_url()?>assets/landingpage/assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="<?=base_url()?>assets/landingpage/assets/vendor/swiper/swiper-bundle.min.js"></script>
<script src="<?=base_url()?>assets/landingpage/assets/vendor/php-email-form/validate.js"></script>

<!-- Template Main JS File -->
<script src="<?=base_url()?>assets/landingpage/assets/js/main.js"></script>
<script src="<?=base_url()?>assets/landingpage/assets/js/popper.min.js"></script>
<script src="<?=base_url()?>assets/landingpage/assets/js/anime.min.js"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/animejs@3.1.0/lib/anime.min.js"></script> -->
<!-- <script src="<?=base_url()?>assets/landingpage/glightbox-master/demo/js/valde.min.js"></script>
<script src="<?=base_url()?>assets/landingpage/glightbox-master/dist/js/glightbox.js"></script>
<script src="<?=base_url()?>assets/landingpage/glightbox-master/demo/js/site.js"></script> -->
<script>
            var lightbox = GLightbox();
            lightbox.on('open', (target) => {
                console.log('lightbox opened');
            });
            var lightboxDescription = GLightbox({
                selector: '.glightbox2'
            });
            var lightboxVideo = GLightbox({
                selector: '.glightbox3'
            });
            lightboxVideo.on('slide_changed', ({ prev, current }) => {
                console.log('Prev slide', prev);
                console.log('Current slide', current);

                const { slideIndex, slideNode, slideConfig, player } = current;

                if (player) {
                    if (!player.ready) {
                        // If player is not ready
                        player.on('ready', (event) => {
                            // Do something when video is ready
                        });
                    }

                    player.on('play', (event) => {
                        console.log('Started play');
                    });

                    player.on('volumechange', (event) => {
                        console.log('Volume change');
                    });

                    player.on('ended', (event) => {
                        console.log('Video ended');
                    });
                }
            });

            var lightboxInlineIframe = GLightbox({
                selector: '.glightbox4'
            });

            /* var exampleApi = GLightbox({ selector: null });
            exampleApi.insertSlide({
                href: 'https://picsum.photos/1200/800',
            });
            exampleApi.insertSlide({
                width: '500px',
                content: '<p>Example</p>'
            });
            exampleApi.insertSlide({
                href: 'https://www.youtube.com/watch?v=WzqrwPhXmew',
            });
            exampleApi.insertSlide({
                width: '200vw',
                content: document.getElementById('inline-example')
            });
            exampleApi.open(); */
        </script>
</body>

</html>