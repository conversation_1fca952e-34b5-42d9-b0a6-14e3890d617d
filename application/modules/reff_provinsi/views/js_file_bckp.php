<script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/wayjs/way.js"></script>
<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var xhrdata = null;
    var table = null;
    function clear_input() {
        $("#formData :input").val("");
    }

    function listing() {
        table = $('#dt-server-processing').DataTable({
            "draw": 0,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>reff_provinsi/ssp_paket","data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
                // d.thang = $('#fthang').val();
                
            }},
            "columnDefs": [
                {
                    "aTargets": [0],
                    "mRender": function (data, type, full) {
                        return full[0];
                    }
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
                },
                {
                    "aTargets": [2],
                    "mRender": function (data, type, row) {
                        var id = row[0];
                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "Edit",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "Hapus",
                            "</button>"//,
                            //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                // "paginate": {
                //     "first": "<i class='fast backward ui icon'></i>",
                //     "last": "<i class='fast forward ui icon'></i>",
                //     "next": "<i class='step forward ui icon'></i>",
                //     "previous": "<i class='step backward ui icon'></i>"
                // },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }
    
    function bind_combo_induk(thang, selval){
        var data = get_induk(thang);
        var objthang = JSON.parse(data);
        $("#kdinduk").empty();
        $('#kdinduk').append("<option value='' >" + "--Pilih--" + "</option>");
        for(var i=0; i<= objthang.length-1; i++){
            $("#kdinduk").append("<option value="+objthang[i].kdsatker+">"+objthang[i].nmsatker+"</option>");
        }
        if (selval){
            $("#kdinduk").val(selval);
        }
    }
    
    function get_induk(thang){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('reff_provinsi/get_induk/') ?>" + thang,
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }

    function dtEditRow(id) {
        $("#frm-tambah :input").val("");
        $("#Kode_provinsi").prop("disabled",true);
        var data_selected = xhrdata.data.filter(x => x[1] === id)[0];
        // console.log(data_selected)
        // return;
        // updateComboboxAndSelectedWoThang("thang", 21, $("#fthang").val());
        
        var waydata = {
            // thang: $("#fthang").val(),
            //ibukota: data_selected[2] === null ? '' : data_selected[2],
            kd_prov: data_selected[0] === null ? '' : data_selected[0],
            nama_prov: data_selected[1] === null ? '' : data_selected[1],
            // //kd_prov_bps: data_selected[3] === null ? '' : data_selected[3],
            // kd_prov_irmsv3: data_selected[2] === null ? '' : data_selected[2],
            // kd_prov_rkakl: data_selected[3] === null ? '' : data_selected[3],
            // kd_prov_rams: data_selected[4] === null ? '' : data_selected[4]
            //kdinduk: data_selected[5] === null ? '' : data_selected[5]
            //kd_prov_krisna: data_selected[5] === null ? '' : data_selected[5],
            //kd_prov_sipro: data_selected[8] === null ? '' : data_selected[8],  
        };
        // way.set('formData', waydata);
        // bind_combo_induk($("#fthang").val(), data_selected[5] === null ? '' : data_selected[5]);
        // $("#id").val(data_selected[6]);
        
        //$("input").prop('disabled', false);






	$("#hid").show("slow");
	$("#hida").show("slow");
        $('#modalTitle').text('Edit Data');
        $('#modeform').val('edit');
        $("#thang").val($("#fthang").val());
        // $("#nama_prov").val(data_selected[1]);
        $('#modal-tambah').modal('show');


        var formData = new FormData($('#frm-tambah')[0]);
        $.each(waydata, function( index, value ) {
            formData.append(index, value);
        });

    //     var data = new FormData(), fields = $("#frm-tambah").serializeArray();

    // $.each( fields, function( i, field ) {


    //    // console.log(field.name+'-'+field.value);
    // });


    }


    function dtTambahRow() {
        $('#frm-tambah')[0].reset();
        $("#frm-tambah :input").val("");
        $("#kdinduk").val("");
        $("#Kode_provinsi").prop("disabled",false);
        //$("input").prop('disabled', false);
	$("#hid").show("slow");
	$("#hida").show("slow");
        var thangchoose = $("#fthang").val();
        //$("#thang").val(thangchoose);
        // updateComboboxAndSelectedWoThang("thang", 21, thangchoose);
        // bind_combo_induk(thangchoose);
        $('#modalTitle').text('Tambah ');
        $('#modeform').val('tambah');
        $('#modal-tambah').modal('show');
    }
    function dtdetail(id){
        //console.log(xhrdata);

        var data_selected = xhrdata.data.filter(x => x[9] === id)[0];  //46

        var waydata = {
            thang: $("#fthang").val(),
            ibukota: data_selected[2],
            kd_prov:data_selected[0],
            kd_prov_bps: data_selected[3],
            kd_prov_irmsv3: data_selected[4],
            kd_prov_krisna:data_selected[5],
            kd_prov_rams: data_selected[6],
            kd_prov_rkakl: data_selected[7],
            kd_prov_sipro: data_selected[8],
            nama_prov: data_selected[1]

        }

        way.set('formData', waydata);
        $('#modalTitle').text('Detail');
//        $('#modeform').val('edit');
        $("input").prop('disabled', true);
	$("#hid").hide("slow");
	$("#hida").hide("slow");
        $('#modal-tambah').modal('show');
    }

    function dtDeleteRow(id) {
        url = "<?php echo base_url();?>reff_provinsi/deleteform";
        //console.log(id);
        var r = confirm("Data yang anda pilih akan kami hapus!, apakah anda yakin mau melanjutkan");
        if (r === true) {
            $.post(url, {id: id,"<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"}).done(function (data) {
	    $("#alert-content").empty();
            $("#alert-content").append("<h4 style='color:green;'><i class='fa fa-check'></i> Berhasil Menghapus Data<h4>");
            $("#alert_information").css({display: "block"});
            setTimeout(close_alert, 3000);
            table.ajax.reload();
            })

        }


    }


    function simpanForm() {
        var mode = $('#modeform').val();
        //var serializeData = way.get('formData');
        
        if($( "#frm-tambah" ).valid() === true){
            var serializeData = {
                "tahun" : 2021,
                "kd_prov": $("#Kode_provinsi").val(),
                // "kd_prov_bps": $("#Kode_provinsi").val(),
                // "kd_prov_irmsv3": $("#kd_prov_irmsv3").val(),
                //"ibukota" : $("#ibukota").val(),
                //"kd_prov_krisna": $("#kd_prov_krisna").val(),
                // "kd_prov_rams": $("#kd_prov_rams").val(),
                // "kd_prov_rkakl": $("#kd_prov_rkakl").val(),
                //"kd_prov_sipro": $("#kd_prov_sipro").val(),
                "nama_prov": $("#nama_prov").val(),
                "id": $("#id").val(),
                // "kd_satker_balai": $("#kdinduk").val()
            };

            //console.log(serializeData);
            if (mode === 'tambah') {
                url = "<?php echo base_url(); ?>reff_provinsi/addform";
            } else if (mode === 'edit') {
                url = "<?php echo base_url(); ?>reff_provinsi/editform";
            }

            var params = {"formData": serializeData,"<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};       
            $.post(url, params).done(function (data) {
                $("#alert-content").empty("");
                $("#alert-content").append(data);
                $("#alert_information").css({display: "block"});
                setTimeout(close_alert, 3000);
                table.ajax.reload();
            });
        } else {
            $("label.error").css("color","red");
        }
    }

    function close_alert() {
        $("#alert_information").css({display: "none"});
    }
    
    function bind_combo_thang(selval){
        var data = get_thang();
        var objthang = JSON.parse(data);
        for(var i=0; i<= objthang.length-1; i++){
            $("#fthang").append("<option value="+objthang[i].thang+">"+objthang[i].uraian+"</option>");
        }
        if (selval){
            $("#fthang").val(selval);
        }
    }
    
    function get_thang(){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('reff_provinsi/get_thang') ?>",
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }

    $(document).ready(function () {
        // bind_combo_thang(thangs);
        listing();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");

        $(".si-close").click(function(){
            $("input").prop('disabled', false);
        });
        
        $('#fthang').change( function() {
            table.draw();
        });
        
        
        
    });

</script>