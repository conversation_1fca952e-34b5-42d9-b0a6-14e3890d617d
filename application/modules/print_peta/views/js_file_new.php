<!-- <script src="https://unpkg.com/leaflet@1.6.0/dist/leaflet.js" integrity="sha512-gZwIG9x3wUXg2hdXF6+rVkLF/0Vi9U8D2Ntg4Ga5I5BZpVkVxlJWbSQtXPSiUTtC0TjtGOmxa1AJPuV0CPthew==" crossorigin=""></script> -->
<!-- <script src="<? //echo base_url();?>assets/js/leaflet-panel-layers-master/src/leaflet-panel-layers.js"></script> -->
<!-- <script src="<? //echo base_url();?>assets/js/leaflet-panel-layers-master/src/leflet-panel.js"></script> -->
<!-- <script src="https://code.jquery.com/jquery-3.4.1.min.js" integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous"></script> -->
<script src="<? echo base_url();?>assets/js/pgt-maps/leaflet-src.js"></script>
<script src="<? echo base_url();?>assets/js/pgt-maps/L.Control.Layers.Tree.js"></script>
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script> -->

<script src="<? echo base_url();?>assets/js/L.TileLayer.BetterWMS.js"></script>

<!-- <script src="<? //echo base_url();?>assets/peta/libs/leaflet-betterwms/leaflet-betterwms_wgi.js"></script> -->
<!-- <script src="<? //echo base_url();?>assets/peta/libs/leaflet-sidebar-wgi/leaflet-sidebar2.js"></script> -->

<style>
    .checkPeta{
        background-color: transparent;
    }
</style>


<!-- <script src="<?php //echo base_url(); ?>assets/js/app.js"></script> -->

<!-- GEOJSON DATA -->
<!-- <script src="data/bar.js"></script>
<script src="data/drinking_water.js"></script> -->

<script>
var baseURL = "<?php echo base_url(); ?>";
//load maps	
var map = L.map('map', {
        zoom: 5,
        center: L.latLng([-1.736, 119.246]),
        attributionControl: false
        // maxBounds: L.latLngBounds([[42.41281,12.28821],[42.5589,12.63805]]).pad(0.5)
    }),
    osmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');

var thnData = '1=1';








// var topPane = map.createPane('leaflet-top-pane', map.getPanes().mapPane);
// var bottomPane = map.createPane('leaflet-bottom-pane', map.getPanes().tilePane);

// var ptp_p3t = L.Geoserver.wms("http://************:4980/geoserver/pgt/wms", {
//   layers: "pgt:ptp_p3t"
// });
// var prov = L.control();
// prov.onAdd = function(map) {
//     var div = L.DomUtil.create('div', 'prov');
//     div.innerHTML = '<select onchange="prov_filter(this)"><option value="">Provinsi</option></select>';
//     div.firstChild.onmousedown = div.firstChild.ondblclick = L.DomEvent.disableClickPropagation(div);
//     return div;
// };
// prov.addTo(map);

// var kabkot = L.control();
// kabkot.onAdd = function(map) {
//     var div = L.DomUtil.create('div', 'kabkot');
//     div.innerHTML = '<select onchange="kabkot_filter(this)"><option value="">Kab./Kota</option></select>';
//     div.firstChild.onmousedown = div.firstChild.ondblclick = L.DomEvent.disableClickPropagation(div);
//     return div;
// };
// kabkot.addTo(map);



var mapSelectTop = L.control();
mapSelectTop.onAdd = function(map) {
    var div = L.DomUtil.create('div', 'tahun');
    div.innerHTML =
        '<select id="provSelect" style="width:200px;border-radius:5px;padding-left:10px" onchange="provKabFilter(this)" class="bootstrap-select form-control"><option id="kabSelect"  value=""> Semua Provinsi</option></select>'+
        '&nbsp;<select id="kabSelect" style="width:200px;border-radius:5px;padding-left:10px" class="bootstrap-select form-control" data-live-search="true" onchange="provKabFilter(this)"><option value=""> Semua Kabupaten/Kota</option></select>'+
        '&nbsp;<select id="thnSelect" style="width:170px;border-radius:5px;padding-left:10px" class="bootstrap-select form-control" data-live-search="true" onchange="provKabFilter(this)"></select>';
    //  '<select id="provSelect"  onchange="prov_filter(this)"><option id="kabSelect"  value="">Provinsi</option></select>&nbsp;<select id="kabSelect" class="bootstrap-select" data-live-search="true" onchange="kabkot_filter(this)"><option value="">Kabupaten/Kota</option></select>&nbsp;<select id="thnSelect" class="bootstrap-select" data-live-search="true" onchange="thn_filter(this)"></select>';
    div.firstChild.onmousedown = div.firstChild.ondblclick = L.DomEvent.disableClickPropagation(div);
    return div;
};
mapSelectTop.addTo(map);





// var info = L.control({position: 'topleft'});
// info.onAdd = function (map) {
//     var div = L.DomUtil.create('div', 'informasi'); // create a div with a class "info"
// 	div.innerHTML = '<table border="1"><tr><td>xxx</td><td>xx</td></tr></table>';
// 	div.firstChild.onmousedown = div.firstChild.ondblclick =  L.DomEvent.disableClickPropagation(div);
//     return div;
// };

// info.addTo(map);

// function getComboA(el) {
//   var value = el.value;  

// //   console.log(value);
//   thn_filter(value);
// }

function setFilter() {
    var prov = $('#provSelect').val();
    var kab = $('#kabSelect').val();
    var thn = $('#thnSelect').val();
    if (prov != '') {
        thnData = 'kdppum= ' + prov;
    } else {
        thnData = '1=1';

    }
    if (prov != '' && kab != '') {
        thnData += ' and kdpkab = ' + kab
    }
    if (prov != '' && thn != null && thn != '') {
        thnData += ' and tahun_data= ' + thn;
    }else if(prov == '' && thn != null && thn != ''){
        thnData = ' tahun_data= ' + thn;
    }
}
function provKabFilter(el) {
    console.log('change')
    var prov = $('#provSelect').val();
    var kab = $('#kabSelect').val();
    var thn = $('#thnSelect').val();
    if (prov != '') {
        thnData = 'kdppum= ' + prov;
    } else {
        thnData = '1=1';

    }
    if (prov != '' && kab != '') {
        thnData += ' and kdpkab = ' + kab
    }
    if (prov != '' && thn != null && thn != '') {
        thnData += ' and tahun_data= ' + thn;
    }else if(prov == '' && thn != null && thn != ''){
        thnData = ' tahun_data= ' + thn;
    }
    // removeLayer()
    if (map.hasLayer(tanahnegara_prov_tnbh)) {
        map.removeLayer(tanahnegara_prov_tnbh);
        tnbh(thnData);
        tanahnegara_prov_tnbh.addTo(map);
    };


    if (map.hasLayer(tanahnegara_prov_tnbk)) {
        map.removeLayer(tanahnegara_prov_tnbk);
        tnbk(thnData);
        tanahnegara_prov_tnbk.addTo(map);
    };


    if (map.hasLayer(tanahnegara_prov_tntk)) {
        map.removeLayer(tanahnegara_prov_tntk);
        tntk(thnData);
        tanahnegara_prov_tntk.addTo(map);
    };
    if (map.hasLayer(npgt_prov)) {
        map.removeLayer(npgt_prov);
        npgtProv(thnData);
        npgt_prov.addTo(map);
    };

    if (map.hasLayer(npgt_kabkota)) {
        map.removeLayer(npgt_kabkota);
        kabkotA(thnData);
        npgt_kabkota.addTo(map);
    };
    // if (map.hasLayer(npgt_kabkot_a)) {
    //     map.removeLayer(npgt_kabkot_a);
    //     kabkotA(thnData);
    //     npgt_kabkot_a.addTo(map);
    // };

    // if (map.hasLayer(npgt_kabkot_g)) {
    //     map.removeLayer(npgt_kabkot_g);
    //     kabkotG(thnData);
    //     npgt_kabkot_g.addTo(map);
    // };

    // if (map.hasLayer(npgt_kabkot_gq)) {
    //     map.removeLayer(npgt_kabkot_gq);
    //     kabkotGQ(thnData);
    //     npgt_kabkot_gq.addTo(map);
    // };
    // if (map.hasLayer(npgt_kabkot_h)) {
    //     map.removeLayer(npgt_kabkot_h);
    //     kabkotH(thnData);
    //     npgt_kabkot_h.addTo(map);
    // };

    // if (map.hasLayer(npgt_kabkot_k)) {
    //     map.removeLayer(npgt_kabkot_k);
    //     kabkotK(thnData);
    //     npgt_kabkot_k.addTo(map);
    // };
    // if (map.hasLayer(npgt_kabkot_n)) {
    //     map.removeLayer(npgt_kabkot_n);
    //     kabkotN(thnData);
    //     npgt_kabkot_n.addTo(map);
    // };

    // if (map.hasLayer(npgt_kabkot_o)) {
    //     map.removeLayer(npgt_kabkot_o);
    //     kabkotO(thnData);
    //     npgt_kabkot_o.addTo(map);
    // };

    // if (map.hasLayer(npgt_kabkot_q)) {
    //     map.removeLayer(npgt_kabkot_q);
    //     kabkotQ(thnData);
    //     npgt_kabkot_q.addTo(map);
    // };

    // if (map.hasLayer(npgt_kabkot_v)) {
    //     map.removeLayer(npgt_kabkot_v);
    //     kabkotV(thnData);
    //     npgt_kabkot_v.addTo(map);
    // };

    // if (map.hasLayer(npgt_kabkot_w)) {
    //     map.removeLayer(npgt_kabkot_w);
    //     kabkotW(thnData);
    //     npgt_kabkot_w.addTo(map);
    // };

    if (map.hasLayer(npgt_kec_a)) {
        map.removeLayer(npgt_kec_a);
        kecA(thnData);
        npgt_kec_a.addTo(map);
    };

    if (map.hasLayer(npgt_kec_h)) {
        map.removeLayer(npgt_kec_h);
        kecH(thnData);
        npgt_kec_h.addTo(map);
    };

    if (map.hasLayer(npgt_kec_k)) {
        map.removeLayer(npgt_kec_k);
        kecK(thnData);
        npgt_kec_k.addTo(map);
    };
    if (map.hasLayer(npgt_kec_n)) {
        map.removeLayer(npgt_kec_n);
        kecN(thnData);
        npgt_kec_n.addTo(map);
    };

    if (map.hasLayer(npgt_kec_o)) {
        map.removeLayer(npgt_kec_o);
        kecO(thnData);
        npgt_kec_o.addTo(map);
    };

    if (map.hasLayer(npgt_kec_q)) {
        map.removeLayer(npgt_kec_q);
        kecQ(thnData);
        npgt_kec_q.addTo(map);
    };

    if (map.hasLayer(npgt_kec_v)) {
        map.removeLayer(npgt_kec_v);
        kecV(thnData);
        npgt_kec_v.addTo(map);
    };

    if (map.hasLayer(npgt_kec_w)) {
        map.removeLayer(npgt_kec_w);
        kecW(thnData);
        npgt_kec_w.addTo(map);
    };
    if (map.hasLayer(npgt_perkebunan)) {
        map.removeLayer(npgt_perkebunan);
        npgtKebun(thnData);
        npgt_perkebunan.addTo(map);
    };

    if (map.hasLayer(mppt_prov)) {
        map.removeLayer(mppt_prov);
        mppt(thnData);
        mppt_prov.addTo(map);
    };
    if (map.hasLayer(kemampuan_tanah)) {
        map.removeLayer(kemampuan_tanah);
        kemampuanTanah(thnData);
        kemampuan_tanah.addTo(map);
    };

    if (map.hasLayer(lahanbakusawah_prov)) {
        map.removeLayer(lahanbakusawah_prov);
        lahanBaku(thnData);
        lahanbakusawah_prov.addTo(map);
    };

    if (map.hasLayer(ptp_p3t)) {
        map.removeLayer(ptp_p3t);
        ptpP3t(thnData);
        ptp_p3t.addTo(map);
    };

    if (map.hasLayer(ptpil_prov)) {
        map.removeLayer(ptpil_prov);
        ptpilProv(thnData);
        ptpil_prov.addTo(map);
    };

    if (map.hasLayer(ptpil_prov_tk)) {
        map.removeLayer(ptpil_prov_tk);
        ptpilProvPOI(thnData);
        ptpil_prov_tk.addTo(map);
    };

    if (map.hasLayer(ptp_kppr_berusaha)) {
        map.removeLayer(ptp_kppr_berusaha);
        ptpBerusaha(thnData);
        ptp_kppr_berusaha.addTo(map);
    };
    if (map.hasLayer(ptp_kppr_stranas)) {
        map.removeLayer(ptp_kppr_stranas);
        ptpStranas(thnData);
        ptp_kppr_stranas.addTo(map);
    };
    if (map.hasLayer(ptp_kppr_non_berusaha)) {
        map.removeLayer(ptp_kppr_non_berusaha);
        ptpNonBerusaha(thnData);
        ptp_kppr_non_berusaha.addTo(map);
    };
    if (map.hasLayer(ptp_pk_p3t)) {
        map.removeLayer(ptp_pk_p3t);
        ptpPkP3t(thnData);
        ptp_pk_p3t.addTo(map);
    };
    if (map.hasLayer(ptp_tnh_timbul)) {
        map.removeLayer(ptp_tnh_timbul);
        ptpTnhTimbul(thnData);
        ptp_tnh_timbul.addTo(map);
    };
    if (map.hasLayer(wp3wt_ppk_polygon)) {
        map.removeLayer(wp3wt_ppk_polygon);
        Wp3wtPpkPolygon(thnData);
        wp3wt_ppk_polygon.addTo(map);
    };
    if (map.hasLayer(wp3wt_ppk_poi)) {
        map.removeLayer(wp3wt_ppk_poi);
        Wp3wtPpkPOI(thnData);
        wp3wt_ppk_poi.addTo(map);
    };
    if (map.hasLayer(wp3wt_ppkt)) {
        map.removeLayer(wp3wt_ppkt);
        wp3wtPpkt(thnData);
        wp3wt_ppkt.addTo(map);
    };
    if (map.hasLayer(wp3wt_perbatasan)) {
        map.removeLayer(wp3wt_perbatasan);
        wp3wtPerbatasan(thnData);
        wp3wt_perbatasan.addTo(map);
        console.log('filter ')
    };
    if (map.hasLayer(wp3wt_pesisir)) {
        map.removeLayer(wp3wt_pesisir);
        wp3wtPesisir(thnData);
        wp3wt_pesisir.addTo(map);
    };

    if (map.hasLayer(wp3wt_pulau)) {
        map.removeLayer(wp3wt_pulau);
        wp3wtPulau(thnData);
        wp3wt_pulau.addTo(map);
    };

    if (map.hasLayer(wp3wt_tertentu)) {
        map.removeLayer(wp3wt_tertentu);
        wp3wtTertentu(thnData);
        wp3wt_tertentu.addTo(map);
    };

    if (map.hasLayer(wp3wt_timbul)) {
        map.removeLayer(wp3wt_timbul);
        wp3wtTimbul(thnData);
        wp3wt_timbul.addTo(map);
    };
    if (map.hasLayer(rtrw)) {
        map.removeLayer(rtrw);
        pgtlRtrw(thnData);
        rtrw.addTo(map);
    };
    
    if (map.hasLayer(bts_desa)) {
        map.removeLayer(bts_desa);
        pgtlDesa(thnData);
        bts_desa.addTo(map);
    };

    if (map.hasLayer(bts_kecamatan)) {
        map.removeLayer(bts_kecamatan);
        pgtlDesa(thnData);
        bts_kecamatan.addTo(map);
    };


}
var url = 'http://************:4980/geoserver/pgt/wms';

Wp3wtPpkPolygon()
Wp3wtPpkPOI()
wp3wtPpkt()
wp3wtPerbatasan()
wp3wtPesisir()
wp3wtPulau()
wp3wtTertentu()
wp3wtTimbul()

pgtlKecamatan()
pgtlDesa()
pgtlRtrw()

tnbh();
tnbk();
tntk();

npgtProv();

kabkotA();
// kabkotG();
// kabkotGQ();
// kabkotH();
// kabkotK();
// kabkotN();
// kabkotO();
// kabkotQ();
// kabkotV();
// kabkotW();

kecA();
kecH();
kecK();
kecN();
kecO();
kecQ();
kecV();
kecW();

npgtKebun()

mppt();

kemampuanTanah();

lahanBaku()

ptpP3t()
ptpilProv()
ptpilProvPOI()
ptpBerusaha()
ptpNonBerusaha()
ptpPkP3t()
ptpTnhTimbul()
ptpStranas()

//NPGT Provinsi
// var npgt_prov = L.tileLayer.betterWms(url, {
//     layers: 'pgt:npgt_prov',
//     transparent: true,
//     cql_filter: thnData,
//     format: 'image/png8',
//     title: 'NPGT Provinsi'
// });

// cql_filter: thnData,

//NPGT Kabupaten/Kota


function npgtProvCall(v) {
    // removeLayer()
    map.removeLayer(npgt_prov);

    if(v == 1){
        if($('#npgtProvCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#npgtProvCheck').prop('checked', false); 

            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#npgtProvCheck').prop('checked', true); 
        }
    }else{
        if($('#npgtProvCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#npgtProvCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_prov')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        npgtProv()
    });
}

function npgtProv() {
    map.closePopup()
    npgt_prov = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_prov',
        styles: 'pgt:npgt_prov',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "gname100",
                "alias": "Penggunaan Lama"
            },
            // {
            //     "name": "qname100",
            //     "alias": "Penggunaan Akhir"
            // },
            {
                "name": "gq_name",
                "alias": "Perubahan"
            },
            {
                "name": "wname",
                "alias": "RTRW"
            },
            {
                "name": "nname",
                "alias": "Kesesuaian"
            },
            {
                "name": "oname",
                "alias": "Gambaran Umum"
            },
            {
                "name": "vname",
                "alias": "Ketersediaan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "kaw_hutan",
                "alias": "Kawasan"
            }
        ],
        title: 'NPGT Provinsi'
    });
    npgt_prov.addTo(map);

}

function kabkotACall(v) {
    // removeLayer()
    map.removeLayer(npgt_kabkota);

    if(v == 1){
        if($('#kabkotACheck').is(':checked') )
        {
            $('#kabkotACheck').prop('checked', false); 
            // $('.checkPeta').prop('checked', false);
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kabkotACheck').prop('checked', true); 
        }
    }else{
        if($('#kabkotACheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kabkotACheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kabkota')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kabkotA()
    });
}
function kabkotA() {
    map.closePopup()
    npgt_kabkota = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kabkota',
        // styles: 'pgt:npgt_kabkota',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wname",
                "alias": "RTRW"
            },
            {
                "name": "oname",
                "alias": "Gambara Uumum"
            },
            {
                "name": "gqname",
                "alias": "Perubahan"
            },
            {
                "name": "vname",
                "alias": "Ketersediaan"
            },
            {
                "name": "nname",
                "alias": "Kesesuaian"
            },
            {
                "name": "qname",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "keterangan",
                "alias": "Keterangan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "gname10",
                "alias": "Penggunaan Lama"
            }
        ],
        title: 'NPGT Kabupaten/Kota'

    });
    npgt_kabkota.addTo(map)
}

// function kabkotG() {
//     npgt_kabkot_g = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_g',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "gname100",
//                 "alias": "G"
//             },
//             {
//                 "name": "keterangan",
//                 "alias": "Keterangan"
//             },
//             {
//                 "name": "luas_ha",
//                 "alias": "Luasan(ha)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota Penguasaan (G)'
//     });
// }

// function kabkotGQ() {
//     npgt_kabkot_gq = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_gq',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "gqname",
//                 "alias": "Status"
//             },
//             {
//                 "name": "perubahanf",
//                 "alias": "GQ"
//             },
//             {
//                 "name": "luas_ha",
//                 "alias": "Luasan(ha)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota Perubahan Penggunaan(GQ)'
//     });
// }

// function kabkotH() {
//     npgt_kabkot_h = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_h_polygon',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "hname",
//                 "alias": "H"
//             },
//             {
//                 "name": "luas_ha",
//                 "alias": "Luasan(ha)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota Sungai (H)'
//     });
// }

// var npgt_kabkot_h2 = L.tileLayer.betterWms(url, {
//     layers: 'pgt:npgt_kabkot_h_polyline',
//     transparent: true,
//     format: 'image/png8',
//     outFields: [{
//             "name": "wadmpr",
//             "alias": "Provinsi"
//         },
//         {
//             "name": "wadmkk",
//             "alias": "Kabupaten"
//         },
//         {
//             "name": "hname",
//             "alias": "H"
//         },
//         {
//             "name": "panjang_m",
//             "alias": "Panjang(Km)"
//         },
//         {
//             "name": "tahun_data",
//             "alias": "Tahun"
//         }
//     ],
//     title: 'NPGT Kabupaten/Kota Sungai (H)'
// });

// // var npgt_kabkot_k = L.tileLayer.betterWms(url, {
// //     layers: 'pgt:npgt_kabkot_k',
// //     transparent: true,
// //     format: 'image/png8'
// // });

// function kabkotK() {
//     npgt_kabkot_k = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_k_polyline ',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "ksname",
//                 "alias": "K"
//             },
//             {
//                 "name": "panjang_m",
//                 "alias": "Panjang(Km)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota Jalan (K)'
//     });
// }
// // cql_filter: thnData,
// function kabkotN() {
//     npgt_kabkot_n = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_n',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "nname",
//                 "alias": "N"
//             },
//             {
//                 "name": "kesesuaian",
//                 "alias": "Status"
//             },
//             {
//                 "name": "luas_ha",
//                 "alias": "Luasan(ha)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota Kesesuaian (N)'
//     });
// }

// function kabkotO() {

//     npgt_kabkot_o = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_o',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "oname",
//                 "alias": "O"
//             },
//             {
//                 "name": "kesesuaian",
//                 "alias": "Status"
//             },
//             {
//                 "name": "luas_ha",
//                 "alias": "Luasan(ha)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota Gambaran Umum Penguasaan (O)'
//     });
// }

// function kabkotQ(params) {

//     npgt_kabkot_q = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_q',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "qname25",
//                 "alias": "Q"
//             },
//             {
//                 "name": "keterangan",
//                 "alias": "Keterangan"
//             },
//             {
//                 "name": "luas_ha",
//                 "alias": "Luasan(ha)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota Penggunaan Terakhir (Q)'
//     });
// }

// function kabkotV() {
//     npgt_kabkot_v = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_v',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "vname",
//                 "alias": "V"
//             },
//             {
//                 "name": "sedia_tnah",
//                 "alias": "Keterangan"
//             },
//             {
//                 "name": "luas_ha",
//                 "alias": "Luasan(ha)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota Ketersediaan (V)'
//     });
// }

// function kabkotW(params) {
//     npgt_kabkot_w = L.tileLayer.betterWms(url, {
//         layers: 'pgt:npgt_kabkot_w',
//         transparent: true,
//         format: 'image/png8',
//         cql_filter: thnData,
//         outFields: [{
//                 "name": "wadmpr",
//                 "alias": "Provinsi"
//             },
//             {
//                 "name": "wadmkk",
//                 "alias": "Kabupaten"
//             },
//             {
//                 "name": "wname",
//                 "alias": "W"
//             },
//             {
//                 "name": "arahan",
//                 "alias": "Keterangan"
//             },
//             {
//                 "name": "luas_ha",
//                 "alias": "Luasan(ha)"
//             },
//             {
//                 "name": "tahun_data",
//                 "alias": "Tahun"
//             }
//         ],
//         title: 'NPGT Kabupaten/Kota RTRW (W)'
//     });
// }




//NPGT Kecamatan
function kecACall(v) {
    // removeLayer()
    map.removeLayer(npgt_kec_a);

    if(v == 1){
        if($('#kecACheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecACheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecACheck').prop('checked', true); 
        }
    }else{
        if($('#kecACheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecACheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec_a')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecA()
    });
}

function kecA(params) {
    map.closePopup()

    npgt_kec_a = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kec_a',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan Administrasi (A)'
    });
    npgt_kec_a.addTo(map)
}

function kecHCall(v) {
    // removeLayer()
    map.removeLayer(npgt_kec_h);

    if(v == 1){
        if($('#kecHCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecHCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecHCheck').prop('checked', true); 
        }
    }else{
        if($('#kecHCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecHCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec_h_polygon')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecH()
    });
}
function kecH(params) {
    map.closePopup()

    npgt_kec_h = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kec_h_polyline',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "hname",
                "alias": "Sungai"
            },
            {
                "name": "panjang_m",
                "alias": "Panjang(Km)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan Sungai (H)'
    });
    npgt_kec_h.addTo(map)

}

function kecKCall(v) {
    // removeLayer()
    map.removeLayer(npgt_kec_k);

    if(v == 1){
        if($('#kecKCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecKCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecKCheck').prop('checked', true); 
        }
    }else{
        if($('#kecKCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecKCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec_k_polyline')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecK()
    });
}
function kecK(params) {
    map.closePopup()

    npgt_kec_k = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kec_k_polyline',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "ksname",
                "alias": "Jalan"
            },
            {
                "name": "panjang_m",
                "alias": "Panjang(Km)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan Jalan (K)'
    });
    npgt_kec_k.addTo(map)

}

function kecNCall(v) {
    // removeLayer()
    map.removeLayer(npgt_kec_n);

    if(v == 1){
        if($('#kecNCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecNCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecNCheck').prop('checked', true); 
        }
    }else{
        if($('#kecNCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecNCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec_n')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecN()
    });
}
function kecN(params) {
    map.closePopup()
            $('#kecNCheck').prop('checked', true); 

    npgt_kec_n = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kec_n',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "nname",
                "alias": "Kesesuaian"
            },
            // {
            //     "name": "kesesuaian",
            //     "alias": "Status"
            // },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan Kesesuaian (N)'
    });
    npgt_kec_n.addTo(map)

}

function kecOCall(v) {
    map.removeLayer(npgt_kec_h);
    // removeLayer()
    if(v == 1){
        if($('#kecOCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecOCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecOCheck').prop('checked', true); 
        }
    }else{
        if($('#kecOCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecOCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec_o')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecO()
    });
}
function kecO(params) {
    map.closePopup()

    npgt_kec_o = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kec_o',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "oname",
                "alias": "Gambaran Umum"
            },
            {
                "name": "kesesuaian",
                "alias": "Status"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan Gambaran Umum Penguasaan (O)'
    });
    npgt_kec_o.addTo(map)

}
function kecQCall(v) {
    map.removeLayer(npgt_kec_q);
    // removeLayer()
    if(v == 1){
        if($('#kecQCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecQCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecQCheck').prop('checked', true); 
        }
    }else{
        if($('#kecQCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecQCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec_q')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecQ()
    });
}
function kecQ(params) {
    map.closePopup()

    npgt_kec_q = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kec_q',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "qname25",
                "alias": "Q"
            },
            {
                "name": "keterangan",
                "alias": "Keterangan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan Penggunaan Terakhir (Q)'
    });
    npgt_kec_q.addTo(map)

}

function kecVCall(v) {
    map.removeLayer(npgt_kec_v);
    // removeLayer()
    if(v == 1){
        if($('#kecVCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecVCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecVCheck').prop('checked', true); 
        }
    }else{
        if($('#kecVCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecVCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec_v')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecV()
    });
    // kecV()
}
function kecV(params) {
    map.closePopup()

    npgt_kec_v = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kec_v',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "vname",
                "alias": "Ketersediaan"
            },
            // {
            //     "name": "sedia_tnah",
            //     "alias": "Keterangan"
            // },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan Ketersediaan (V)'
    });
    npgt_kec_v.addTo(map)

}

function kecWCall(v) {
    map.removeLayer(npgt_kec_w);
    // removeLayer()
    if(v == 1){
        if($('#kecWCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecWCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecWCheck').prop('checked', true); 
        }
    }else{
        if($('#kecWCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kecWCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec_w')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecW()
    });
}
function kecW(params) {
    map.closePopup()

    npgt_kec_w = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_kec_w',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wname",
                "alias": "RTRW"
            },
            {
                "name": "arahan",
                "alias": "Arahan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan RTRW (W)'
    });
    npgt_kec_w.addTo(map)

}
// var npgt_kec = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:temp_npgt_kecamatan',
// 	transparent: true,
// 	format: 'image/png8'
// });

function npgtKebunCall(v) {
    // removeLayer()
    map.removeLayer(npgt_perkebunan);

    if(v == 1){
        if($('#npgtKebunCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#npgtKebunCheck').prop('checked', false); 

            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#npgtKebunCheck').prop('checked', true); 
        }
    }else{
        if($('#npgtKebunCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#npgtKebunCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_perkebunan')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        npgtKebun()
    });
}
function npgtKebun() {
    map.closePopup()
    npgt_perkebunan = L.tileLayer.betterWms(url, {
        layers: 'pgt:npgt_perkebunan',
        styles: 'pgt:npgt_perkebunan',
        transparent: true,
        format: 'image/png8',
        // bgcolor: '#FAFA7D'
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "q",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "w",
                "alias": "RTRW"
            },
            // {
            //     "name": "hgu",
            //     "alias": "No. HGU"
            // },
            {
                "name": "hgu_reclas",
                "alias": "HGU"
            },
            {
                "name": "tipe",
                "alias": "Tipe"
            },
            {
                "name": "pemilik",
                "alias": "Pemilik"
            },
            {
                "name": "sk",
                "alias": "SK"
            },
            {
                "name": "verifikasi",
                "alias": "Verifikasi"
            },
            // {
            //     "name": "komoditas",
            //     "alias": "Komoditas"
            // },
            {
                "name": "hat_reclas",
                "alias": "HAT"
            },
            {
                "name": "lp2b_recla",
                "alias": "LP2B"
            },
            {
                "name": "ppb_reclas",
                "alias": "PPB"
            },
            {
                "name": "kh_reclass",
                "alias": "Kawasan"
            },
            {
                "name": "kssn",
                "alias": "KSSN"
            },
            {
                "name": "ktsdn",
                "alias": "KTSDN"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }

        ],
        title: 'NPGT Perkebunan'
    });
    npgt_perkebunan.addTo(map)
}

function pgtlRtrwCall(v) {
    // removeLayer()
    map.removeLayer(rtrw);

    if(v == 1){
        if($('#pgtlRtrwCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlRtrwCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlRtrwCheck').prop('checked', true); 
        }
    }else{
        if($('#pgtlRtrwCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlRtrwCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_pgtl_rtrw')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        pgtlRtrw()
    });
}
function pgtlRtrw(params) {

    map.closePopup()
    rtrw = L.tileLayer.betterWms(url, {
        layers: 'pgt:rtrw',
        styles: 'pgt:rtrw',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "no_perda",
                "alias": "No. Perda"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "jnsrpr",
                "alias": "Rencana Pola Ruang"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'RTRW'

    });
    rtrw.addTo(map)
}






// var npgt_industri = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:temp_npgt_industri',
// 	transparent: true,
// 	format: 'image/png8'
// });

// var npgt_rumah= L.tileLayer.betterWms(url, {
// 	layers: 'pgt:temp_npgt_perumahan',
// 	transparent: true,
// 	format: 'image/png8'
// });

// var tanahnegara_prov_tnbh = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:tanahnegara_prov_tnbh',
// 	transparent: true,
// 	format: 'image/png8'
// });

function tnbkCall(v) {
    // removeLayer()
    map.removeLayer(tanahnegara_prov_tnbk);
    if(v == 1){
        if($('#tnbkCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#tnbkCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#tnbkCheck').prop('checked', true); 
        }
    }else{
        if($('#tnbkCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#tnbkCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_tnbk')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        tnbk()
    });
}

function tnbk() {

    map.closePopup()
    tanahnegara_prov_tnbk = L.tileLayer.betterWms(url, {
        layers: 'pgt:tanahnegara_prov_tnbk',
        styles: 'pgt:tanahnegara_prov_tnbk',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "arahan",
                "alias": "Arahan"
            },
            {
                "name": "riwayat",
                "alias": "Riwayat"
            },
            {
                "name": "oname19",
                "alias": "Gambaran Umum"
            },
            {
                "name": "qname19",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "pmanfaatan",
                "alias": "Pemanfaatan"
            },
            {
                "name": "wname19",
                "alias": "RTRW"
            },
            {
                "name": "kegiatan",
                "alias": "Kegiatan"
            },
            {
                "name": "aprogram",
                "alias": "Program"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Tanah Negara Bekas Kawasan'


    });
    tanahnegara_prov_tnbk.addTo(map)
}

// var tanahnegara_prov_tntk  = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:tanahnegara_prov_tntk',
// 	transparent: true,
// 	format: 'image/png8'
// });

function lahanBakuCall(v) {
    // removeLayer()
    map.removeLayer(lahanbakusawah_prov);
    if(v == 1){
        if($('#lahanBakuCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#lahanBakuCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#lahanBakuCheck').prop('checked', true); 
        }
    }else{
        if($('#lahanBakuCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#lahanBakuCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_lahanbakusawah')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        lahanBaku()
    });
}

function lahanBaku() {

    map.closePopup()
    lahanbakusawah_prov = L.tileLayer.betterWms(url, {
        layers: 'pgt:lahanbakusawah_prov',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Lahan Baku Sawah'


    });
    lahanbakusawah_prov.addTo(map)
}


function mpptCall(v) {
    // removeLayer()
    map.removeLayer(mppt_prov);
    if(v == 1){
        if($('#mpptCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#mpptCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#mpptCheck').prop('checked', true); 
        }
    }else{
        if($('#mpptCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#mpptCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_mppt')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        mppt()
    });
}

function mppt() {
    map.closePopup()
    mppt_prov = L.tileLayer.betterWms(url, {
        layers: 'pgt:mppt_prov',
        styles: 'pgt:mppt_prov',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "qname100",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "gname100",
                "alias": "Penggunaan Lama"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Monitoring PPT'

    });
    mppt_prov.addTo(map)
}


function ptpP3tCall(v) {
    // removeLayer()
    map.removeLayer(ptp_p3t);
    if(v == 1){
        if($('#ptpP3tCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpP3tCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpP3tCheck').prop('checked', true); 
        }
    }else{
        if($('#ptpP3tCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpP3tCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_p3t')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpP3t()
    });
}
function ptpP3t(params) {
    map.closePopup()
    ptp_p3t = L.tileLayer.betterWms(url, {
        layers: 'pgt:ptp_p3t',
        // styles: 'pgt:ptp_p3t',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "alamat_prs",
                "alias": "Alamat"
            },
            {
                "name": "peruntukan",
                "alias": "Peruntukan"
            },
            {
                "name": "no_berkas",
                "alias": "No. Berkas"
            },
            {
                "name": "tg_risalah",
                "alias": "Tgl. Risalah"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "no_ilok",
                "alias": "No. Ilok"
            },
            {
                "name": "tgl_ilok",
                "alias": "Tgl. Ilok"
            },
            {
                "name": "posisi",
                "alias": "Posisi"
            },
            {
                "name": "info",
                "alias": "Info"
            },
            {
                "name": "klas",
                "alias": "Kelas"
            }
        ],
        title: 'IP4T'

    });
    ptp_p3t.addTo(map)
}


function ptpilProvCall(v) {
    // removeLayer()
    map.removeLayer(ptpil_prov);
    if(v == 1){
        if($('#ptpilProvCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvCheck').prop('checked', true); 
        }
    }else{
        if($('#ptpilProvCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptpil_prov')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpilProv()
    });
}
function ptpilProv(params) {

    map.closePopup()
    ptpil_prov = L.tileLayer.betterWms(url, {
        layers: 'pgt:ptpil_prov',
        styles: 'pgt:ptpil_prov',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "alamat_prs",
                "alias": "Alamat Perusahaan"
            },
            {
                "name": "peruntukan",
                "alias": "Peruntukan"
            },
            // {
            //     "name": "alamat_ptk",
            //     "alias": "Alamat PTK"
            // },
            {
                "name": "no_berkas",
                "alias": "No. Berkas"
            },
            {
                "name": "tg_risalah",
                "alias": "Tgl. Risalah"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            // {
            //     "name": "luas_ptp",
            //     "alias": "Luas PTP"
            // },
            {
                "name": "no_ilok",
                "alias": "No. Ilok"
            },
            {
                "name": "tgl_ilok",
                "alias": "Tgl. Ilok"
            },
            // {
            //     "name": "luas_ilok",
            //     "alias": "Luas Ilok"
            // },
            {
                "name": "posisi",
                "alias": "Posisi"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "info",
                "alias": "Info"
            },
            {
                "name": "klas",
                "alias": "Kelas"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan Izin Lokasi Provinsi- AOI'

    });
    ptpil_prov.addTo(map)
}


function ptpilProvPoiCall(v) {
    // removeLayer()
    map.removeLayer(ptpil_prov_tk);
    if(v == 1){
        if($('#ptpilProvPoiCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvPoiCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvPoiCheck').prop('checked', true); 
        }
    }else{
        if($('#ptpilProvPoiCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvPoiCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptpil_prov_tk')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpilProvPOI()
    });
}
function ptpilProvPOI(params) {

    map.closePopup()
    ptpil_prov_tk = L.tileLayer.betterWms(url, {
        layers: 'pgt:ptpil_prov_tk',
        styles: 'pgt:ptpil_prov_tk',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "alamat_prs",
                "alias": "Alamat"
            },
            {
                "name": "peruntukan",
                "alias": "Peruntukan"
            },
            {
                "name": "alamat_ptk",
                "alias": "Alamat PTK"
            },
            {
                "name": "no_berkas",
                "alias": "No. Berkas"
            },
            {
                "name": "tg_risalah",
                "alias": "Tgl. Risalah"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "luas_ptp",
                "alias": "Luas PTP"
            },
            {
                "name": "no_ilok",
                "alias": "No. Ilok"
            },
            {
                "name": "tgl_ilok",
                "alias": "Tgl. Ilok"
            },
            {
                "name": "luas_ilok",
                "alias": "Luas Ilok"
            },
            {
                "name": "posisi",
                "alias": "Posisi"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "info",
                "alias": "Info"
            },
            {
                "name": "klas",
                "alias": "Kelas"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "luas",
                "alias": "Luasan(ha)"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan Izin Lokasi Provinsi - POI'

    });
    ptpil_prov_tk.addTo(map)
}


function ptpBerusahaCall(v) {
    // removeLayer()
    map.removeLayer(ptp_kppr_berusaha);
    if(v == 1){
        if($('#ptpBerusahaCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpBerusahaCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpBerusahaCheck').prop('checked', true); 
        }
    }else{
        if($('#ptpBerusahaCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpBerusahaCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_berusaha')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpBerusaha()
    });
}
function ptpBerusaha() {

    map.closePopup()
    ptp_kppr_berusaha = L.tileLayer.betterWms(url, {
        layers: 'pgt:ptp_kppr_berusaha',
        styles: 'pgt:ptp_kppr_berusaha',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "lokasi",
                "alias": "Lokasi"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "nama_perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "alamat_perusahaan",
                "alias": "Alamat"
            },
            {
                "name": "rencana_kegiatan",
                "alias": "Rencana"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "jns_ptp",
                "alias": "Jenis"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            // {
            //     "name": "luas_m2",
            //     "alias": "Luasan(m2)"
            // },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "kbli",
                "alias": "KBLI"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan KKPR Berusaha'


    });
    ptp_kppr_berusaha.addTo(map)
}


function ptpNonBerusahaCall(v) {
    // removeLayer()
    map.removeLayer(ptp_kppr_non_berusaha);
    if(v == 1){
        if($('#ptpNonBerusahaCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpNonBerusahaCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpNonBerusahaCheck').prop('checked', true); 
        }
    }else{
        if($('#ptpNonBerusahaCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpNonBerusahaCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_non_berusaha')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpNonBerusaha()
    });
}
function ptpNonBerusaha() {

    map.closePopup()
    ptp_kppr_non_berusaha = L.tileLayer.betterWms(url, {
        layers: 'pgt:ptp_kppr_non_berusaha',
        styles: 'pgt:ptp_kppr_non_berusaha',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "lokasi",
                "alias": "Lokasi"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "nama_perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "alamat_perusahaan",
                "alias": "Alamat"
            },
            {
                "name": "rencana_kegiatan",
                "alias": "Rencana"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "jns_ptp",
                "alias": "Jenis"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            // {
            //     "name": "luas_m2",
            //     "alias": "Luasan(m2)"
            // },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "kbli",
                "alias": "KBLI"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan KKPR Non Berusaha'


    });
    ptp_kppr_non_berusaha.addTo(map)
}


function ptpPkP3tCall(v) {
    // removeLayer()
    map.removeLayer(ptp_pk_p3t);
    if(v == 1){
        if($('#ptpPkP3tCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpPkP3tCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpPkP3tCheck').prop('checked', true); 
        }
    }else{
        if($('#ptpPkP3tCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpPkP3tCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_stranas')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpPkP3t()
    });
}
function ptpPkP3t() {

    map.closePopup()
    ptp_pk_p3t = L.tileLayer.betterWms(url, {
        layers: 'pgt:ptp_pk_p3t',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "lokasi",
                "alias": "Lokasi"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "nama_perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "alamat_perusahaan",
                "alias": "Alamat"
            },
            {
                "name": "rencana_kegiatan",
                "alias": "Rencana"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "jns_ptp",
                "alias": "Jenis"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "kbli",
                "alias": "KBLI"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan PK P3T'

    });
    ptp_pk_p3t.addTo(map)
}


function ptpStranasCall(v) {
    // removeLayer()
    map.removeLayer(ptp_kppr_stranas);
    if(v == 1){
        if($('#ptpStranasCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpStranasCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpStranasCheck').prop('checked', true); 
        }
    }else{
        if($('#ptpStranasCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpStranasCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_stranas')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpStranas()
    });
}
function ptpStranas() {

    map.closePopup()
    ptp_kppr_stranas = L.tileLayer.betterWms(url, {
        layers: 'pgt:ptp_kppr_stranas',
        styles: 'pgt:ptp_kppr_stranas',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "land_use",
                "alias": "Penggunaan"
            },
            {
                "name": "tata_ruang",
                "alias": "RTRW"
            },
            {
                "name": "kesesuain",
                "alias": "Kesesuaian"
            },
            {
                "name": "ketersedia",
                "alias": "Ketersediaan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "jns_ptp",
                "alias": "Jenis"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "renc_kegiatan",
                "alias": "Rencana"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan KKPR Stranas'
    });
    ptp_kppr_stranas.addTo(map)
}


function ptpTnhTimbulCall(v) {
    // removeLayer()
    map.removeLayer(ptp_tnh_timbul);
    if(v == 1){
        if($('#ptpTnhTimbulCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpTnhTimbulCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpTnhTimbulCheck').prop('checked', true); 
        }
    }else{
        if($('#ptpTnhTimbulCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#ptpTnhTimbulCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_tnh_timbul')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpTnhTimbul()
    });
}
function ptpTnhTimbul() {

    map.closePopup()
    ptp_tnh_timbul = L.tileLayer.betterWms(url, {
        layers: 'pgt:ptp_tnh_timbul',
        styles: 'pgt:ptp_tnh_timbul',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "kd_bidang",
                "alias": "KD Bidang"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "pemilik",
                "alias": "Pemilik"
            },
            {
                "name": "sk",
                "alias": "SK"
            },
            {
                "name": "tanggal_sk",
                "alias": "Tgl. SK"
            },
            {
                "name": "kriteria",
                "alias": "Kriteria"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "kelayakan",
                "alias": "Kelayakan"
            },
            {
                "name": "aprogram",
                "alias": "Program"
            },
            {
                "name": "kluster",
                "alias": "Kluster"
            },
            {
                "name": "penggarap",
                "alias": "Penggarap"
            },
            {
                "name": "jml_kk",
                "alias": "Jml. KK"
            },
            {
                "name": "pokgar",
                "alias": "Garapan"
            },
            {
                "name": "lokasi",
                "alias": "Lokasi"
            },
            {
                "name": "bdn_hukum",
                "alias": "Bdn. Hukum"
            },
            {
                "name": "rncana_keg",
                "alias": "Rencana"
            },
            {
                "name": "nomor_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "jenis_ptp",
                "alias": "Jenis PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "hasil_ptp",
                "alias": "Hasil"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan Tanah Timbul'
    });
    ptp_tnh_timbul.addTo(map)
}

// var wp3wt_pfn  = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:wp3wt_pfn ',
// 	transparent: true,
// 	format: 'image/png8'
// });

// var wp3wt_pfn  = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:wp3wt_pfn ',
// 	transparent: true,
// 	format: 'image/png8'
// });


// var wp3wt_ptn  = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:wp3wt_ptn ',
// 	transparent: true,
// 	format: 'image/png8'
// });

// var wp3wt_pmn  = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:wp3wt_pmn ',
// 	transparent: true,
// 	format: 'image/png8'
// });

// var wp3wt_psn  = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:wp3wt_psn ',
// 	transparent: true,
// 	format: 'image/png8'
// });

// var wp3wt_ppk_point  = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:wp3wt_ppk_point',
// 	transparent: true,
// 	format: 'image/png8'
// });

function Wp3wtPpkPolygonCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_ppk_polygon);
    if(v == 1){
        if($('#Wp3wtPpkPolygonCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#Wp3wtPpkPolygonCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#Wp3wtPpkPolygonCheck').prop('checked', true); 
        }
    }else{
        if($('#Wp3wtPpkPolygonCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#Wp3wtPpkPolygonCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_ppk_polygon');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_ppk_polygon')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        Wp3wtPpkPolygon()
    });
}
function Wp3wtPpkPolygon() {

    map.closePopup()
    wp3wt_ppk_polygon = L.tileLayer.betterWms(url, {
        layers: 'pgt:wp3wt_ppk_polygon',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "desa",
                "alias": "Desa"
            },
            {
                "name": "tutupan_la",
                "alias": "Keterangan"
            },
            {
                "name": "objek",
                "alias": "Objek"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "nama_pulau",
                "alias": "Pulau"
            },
            {
                "name": "pemilikan",
                "alias": "Pemilikan"
            },
            {
                "name": "kawasan_hu",
                "alias": "Kawasan"
            }
        ],
        title: 'WP3WT Sebaran Pulau Pulau Kecil '
    });
    wp3wt_ppk_polygon.addTo(map)
}

function wp3wtPpkPoiCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_ppk_poi);
    if(v == 1){
        if($('#wp3wtPpkPoiCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpkPoiCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpkPoiCheck').prop('checked', true); 
        }
    }else{
        if($('#wp3wtPpkPoiCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpkPoiCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_ppk_point');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_ppk_point')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        Wp3wtPpkPOI()
    });
}
function Wp3wtPpkPOI() {

    map.closePopup()
    wp3wt_ppk_poi = L.tileLayer.betterWms(url, {
        layers: 'pgt:wp3wt_ppk_point',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "desa",
                "alias": "Desa"
            },
            {
                "name": "tutupan_la",
                "alias": "Keterangan"
            },
            {
                "name": "objek",
                "alias": "Objek"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "verifikasi",
                "alias": "Verifikasi"
            },
            {
                "name": "nama_pulau",
                "alias": "Pulau"
            }
        ],
        title: 'WP3WT Sebaran Pulau Pulau Kecil (POI) '
    });
    wp3wt_ppk_poi.addTo(map)
}

function wp3wtPpktCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_ppkt);
    if(v == 1){
        if($('#wp3wtPpktCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpktCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpktCheck').prop('checked', true); 
        }
    }else{
        if($('#wp3wtPpktCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpktCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_ppkt');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_ppkt')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtPpkt()
    });
}
function wp3wtPpkt() {

    map.closePopup()
    wp3wt_ppkt = L.tileLayer.betterWms(url, {
        layers: 'pgt:wp3wt_ppkt',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "tutupan_la",
                "alias": "Lahan"
            },
            {
                "name": "objek",
                "alias": "Objek"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Sebaran Pulau Pulau Kecil Terluar'

    });
    wp3wt_ppkt.addTo(map)
}


function wp3wtPerbatasanCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_perbatasan);
    if(v == 1){
        if($('#wp3wtPerbatasanCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPerbatasanCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPerbatasanCheck').prop('checked', true); 
        }
    }else{
        if($('#wp3wtPerbatasanCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPerbatasanCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_penataan_perbatasan');
     var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_penataan_perbatasan')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtPerbatasan()
    });
}
function wp3wtPerbatasan() {
    // console.log('thnData')
    // console.log(thnData)
    map.closePopup()
    wp3wt_perbatasan = L.tileLayer.betterWms(url, {
        layers: 'pgt:wp3wt_penataan_perbatasan',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        // bgcolor: 'FAFA7D',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_m2",
                "alias": "Luasan(m2)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Perbatasan'

    });
    wp3wt_perbatasan.addTo(map)
}

function wp3wtPesisirCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_pesisir);
    if(v == 1){
        if($('#wp3wtPesisirCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPesisirCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPesisirCheck').prop('checked', true); 
        }
    }else{
        if($('#wp3wtPesisirCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPesisirCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_penataan_pesisir')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtPesisir()
    });
}
function wp3wtPesisir() {

    map.closePopup()
    wp3wt_pesisir = L.tileLayer.betterWms(url, {
        layers: 'pgt:wp3wt_penataan_pesisir',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_m2",
                "alias": "Luasan(m2)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Pesisir'

    });
    wp3wt_pesisir.addTo(map)
}


function wp3wtPulauCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_pulau);
    if(v == 1){
        if($('#wp3wtPulauCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPulauCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPulauCheck').prop('checked', true); 
        }
    }else{
        if($('#wp3wtPulauCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPulauCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_penataan_pulau_kecil');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_penataan_pulau_kecil')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtPulau()
    });
}
function wp3wtPulau() {

    map.closePopup()
    wp3wt_pulau = L.tileLayer.betterWms(url, {
        layers: 'pgt:wp3wt_penataan_pulau_kecil',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_m2",
                "alias": "Luasan(m2)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Pulau Pulau Kecil'

    });
    wp3wt_pulau.addTo(map)
}

function wp3wtTertentuCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_tertentu);
    if(v == 1){
        if($('#wp3wtTertentuCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTertentuCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTertentuCheck').prop('checked', true); 
        }
    }else{
        if($('#wp3wtTertentuCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTertentuCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_penataan_wilayah_tertentu');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_penataan_wilayah_tertentu')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtTertentu()
    });
}
function wp3wtTertentu() {
    // console.log(tnhData)
    map.closePopup()
    wp3wt_tertentu = L.tileLayer.betterWms(url, {
        layers: 'pgt:wp3wt_penataan_wilayah_tertentu',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_m2",
                "alias": "Luasan(m2)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Wilayah Tertentu'

    });
    wp3wt_tertentu.addTo(map)
}


function wp3wtTimbulCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_timbul);
    if(v == 1){
        if($('#wp3wtTimbulCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTimbulCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTimbulCheck').prop('checked', true); 
        }
    }else{
        if($('#wp3wtTimbulCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTimbulCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    
    // getYear('');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_tnh_timbul')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtTimbul()
    });
}
function wp3wtTimbul() {
    // console.log()
    map.closePopup()
    wp3wt_timbul = L.tileLayer.betterWms(url, {
        layers: 'pgt:wp3wt_tnh_timbul',
        styles: 'pgt:wp3wt_tnh_timbul',
        transparent: true,
        cql_filter: thnData,
        format: 'image/png8',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Tanah Timbul'

    });
    wp3wt_timbul.addTo(map)
}
// var lahanbakusawah_prov = L.tileLayer.betterWms(url, {
//     layers: 'pgt:lahanbakusawah_prov',
//     styles: 'pgt:lahanbakusawah_prov',
//     transparent: true,
//     format: 'image/png8',
//     outFields: [{
//             "name": "wadmpr",
//             "alias": "Provinsi"
//         },
//         {
//             "name": "wadmkk",
//             "alias": "Kabupaten"
//         },
//         {
//             "name": "wadmkc",
//             "alias": "Kecamatan"
//         },
//         {
//             "name": "wadmkd",
//             "alias": "Kelurahan"
//         },
//         // {"name": "tipadm", "alias": "Tipadm"},
//         {
//             "name": "luas_ha",
//             "alias": "Luasan(ha)"
//         },
//         {
//             "name": "tahun_data",
//             "alias": "Tahun"
//         }
//     ],
//     title: 'Lahan Baku Sawah'
// });

function pgtlDesaCall(v) {
    // removeLayer()
    map.removeLayer(bts_desa);
    if(v == 1){
        if($('#pgtlDesaCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlDesaCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlDesaCheck').prop('checked', true); 
        }
    }else{
        if($('#pgtlDesaCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlDesaCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_pgtl_desa')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        pgtlDesa()
    });
    // pgtlDesa()
}
function pgtlDesa() {
    
    map.closePopup()
    bts_desa = L.tileLayer.betterWms(url, {
        layers: 'pgt:batas_administrasi_desa',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            // {"name": "tipadm", "alias": "Tipadm"},
            {
                "name": "luaswh",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Wilayah Administrasi Desa'
    });
    bts_desa.addTo(map)
}

function pgtlKecamatanCall(v) {
    // removeLayer()
    map.removeLayer(bts_kecamatan);
    if(v == 1){
        if($('#pgtlKecamatanCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlKecamatanCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlKecamatanCheck').prop('checked', true); 
        }
    }else{
        if($('#pgtlKecamatanCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlKecamatanCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_pgtl_kecamatan')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        pgtlKecamatan()
    });
    // pgtlKecamatan()
}
function pgtlKecamatan() {
    
    map.closePopup()
    bts_kecamatan = L.tileLayer.betterWms(url, {
        layers: 'pgt:batas_administrasi_kecamatan',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            // {"name": "tipadm", "alias": "Tipadm"},
            {
                "name": "luaswh",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Wilayah Administrasi Kecamatan'
    });
    bts_kecamatan.addTo(map)
}


function kemampuanTanahCall(v) {
    // removeLayer()
    map.removeLayer(kemampuan_tanah);
    if(v == 1){
        if($('#kemampuanTanahCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kemampuanTanahCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#kemampuanTanahCheck').prop('checked', true); 
        }
    }else{
        if($('#kemampuanTanahCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#kemampuanTanahCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_kemampuan_tanah')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kemampuanTanah()
    });
}
function kemampuanTanah() {

    map.closePopup()
    kemampuan_tanah = L.tileLayer.betterWms(url, {
        layers: 'pgt:kemampuan_tanah',
        styles: 'pgt:kemampuan_tanah',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "b_name",
                "alias": "Lereng"
            },
            {
                "name": "u_name",
                "alias": "Kedalaman Efektif"
            },
            {
                "name": "x_name",
                "alias": "Tekstur"
            },
            {
                "name": "d_name",
                "alias": "Drainase"
            },
            {
                "name": "e_name",
                "alias": "Erosi"
            },
            {
                "name": "l_name",
                "alias": "Faktor Pembatas"
            },
            // {"name": "tipadm", "alias": "Tipadm"},
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Kemampuan Tanah'
    });
    kemampuan_tanah.addTo(map)
}


// var map = L.map('map').setView([39.707045, -104.940817], 12);
//     var thnData = '1=1';
//     var topPane = map.createPane('leaflet-top-pane', map.getPanes().mapPane);
//     var bottomPane = map.createPane('leaflet-bottom-pane', map.getPanes().tilePane);
//     //load maps
//     //load google basemap
//     load_schools();

//     L.tileLayer('http://{s}.tile.osm.org/{z}/{x}/{y}.png', {
//         attribution: '&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors'
//     }).addTo(map);




function tnbhCall(v) {
    // removeLayer()
    map.removeLayer(tanahnegara_prov_tnbh);
    if(v == 1){
        if($('#tnbhCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#tnbhCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#tnbhCheck').prop('checked', true); 
        }
    }else{
        if($('#tnbhCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#tnbhCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_tnbh')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        tnbh()
    });
    // tnbh()
}

function tnbh() {
    map.closePopup()

    tanahnegara_prov_tnbh = L.tileLayer.betterWms(url, {
        layers: 'pgt:tanahnegara_prov_tnbh',
        styles: 'pgt:tanahnegara_prov_tnbh',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        zIndex: 600,
        opacity: 1,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "arahan",
                "alias": "Arahan"
            },
            {
                "name": "riwayat",
                "alias": "Riwayat"
            },
            {
                "name": "oname19",
                "alias": "Gambaran Umum"
            },
            {
                "name": "qname19",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "pmanfaatan",
                "alias": "Pemanfaatan"
            },
            {
                "name": "wname19",
                "alias": "RTRW"
            },
            {
                "name": "kegiatan",
                "alias": "Kegiatan"
            },
            {
                "name": "aprogram",
                "alias": "Program"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Tanah Negara Bekas Hak'
    });
    tanahnegara_prov_tnbh.addTo(map)


    //  tanahnegara_prov_tnbh.infoSetting = {
    // 	infoFields: ['tahun_data', 'qname100', 'luas_ha'],
    // 	infoLabels: ['Tahun', 'Penggunaan', 'Luas'],
    // 	headerFields: '',
    // 	headerPrefix: '',
    // 	headerSuffix: '',
    // 	callbackFields: [],
    //     headerLabel: 'Tanah Negara Bekas Hak',
    // 	cbInformasi: function (data, infoContent, latlng, feat) {
    //     //    sidebar.close();
    // 		// (tanahnegara_prov_tnbh.infoSetting.headerLabel)
    // 		// $("#informasi div").empty();





    // 		//return list;

    // 		// $('#infoobjek').append('<h5><b>TNBH<b></h5>');
    // 		//$("#infoobjek").html("");

    // 		if(tanahnegara_prov_tnbh) {
    // 			var list = document.getElementById('infoobjek');	
    // 		    list.innerHTML = infoContent;
    // 			$('#infoobjek').html('<hr style="border-width: 4px;">');

    // 		} else {
    // 			$("#infoobjek").append(infoContent);
    // 			$('#infoobjek').append('<hr style="border-width: 4px;">');

    // 		}

    // 		// sidebar.open("informasi");




    // 		// console.log('3--showInfo ' + infoContent);
    // 		//$('#infoobjek').append('<h5><b>TNBH<b></h5>');

    // 		//$("#infoobjek").innerHTML(infoContent);
    // 		//$('#infoobjek').append('<hr style="border-width: 4px;">');




    // 	}
    // };
};


function tntkCall(v) {
    // removeLayer()
    map.removeLayer(tanahnegara_prov_tntk);
    if(v == 1){
        if($('#tntkCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#tntkCheck').prop('checked', false); 
            return false 
        }else
        {
            // $('.checkPeta').prop('checked', false);
            $('#tntkCheck').prop('checked', true); 
        }
    }else{
        if($('#tntkCheck').is(':checked') )
        {
            // $('.checkPeta').prop('checked', false);
            $('#tntkCheck').prop('checked', true); 
            // return false 
        }else{
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_tntk')?>";
    $.get( url, function( data ) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        tntk()
    });
    // tntk()
}

function tntk() {
    map.closePopup()
    tanahnegara_prov_tntk = L.tileLayer.betterWms(url, {
        layers: 'pgt:tanahnegara_prov_tntk',
        styles: 'pgt:tanahnegara_prov_tntk',
        transparent: true,
        format: 'image/png8',
        cql_filter: thnData,
        zIndex: 600,
        opacity: 1,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "arahan",
                "alias": "Arahan"
            },
            {
                "name": "riwayat",
                "alias": "Riwayat"
            },
            {
                "name": "oname",
                "alias": "Gambaran Umum"
            },
            {
                "name": "qname",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "pmanfaatan",
                "alias": "Pemanfaatan"
            },
            {
                "name": "wname",
                "alias": "RTRW"
            },
            {
                "name": "kegiatan",
                "alias": "Kegiatan"
            },
            {
                "name": "aprogram",
                "alias": "Program"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "pengelolah",
                "alias": "Pengolahan"
            },
            {
                "name": "program",
                "alias": "Program"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Tanah Kritis'
    });

    tanahnegara_prov_tntk.addTo(map)
    // tanahnegara_prov_tntk.infoSetting = {
    // 	infoFields: ['tahun_data', 'qname100', 'luas_ha'],
    // 	infoLabels: ['Tahun', 'Penggunaan', 'Luas'],
    // 	headerFields: '',
    // 	headerPrefix: '',
    // 	headerSuffix: '',
    // 	callbackFields: ['objectid'],
    //     headerLabel: 'Tanah Negara Tanah Kritis',
    // 	cbInformasi: function (data, infoContent, latlng, feat) {
    //     //    sidebar.close();
    // 		// (tanahnegara_prov_tnbh.infoSetting.headerLabel)
    // 		// $("#informasi div").empty();

    // 	//	console.log(feat);


    // 		// var list = document.getElementById('infoobjek');	
    // 		// list.innerHTML = '';


    // 		//return list;

    // 		// $('#infoobjek').append('<h5><b>TNTK<b></h5>');


    // 		if(tanahnegara_prov_tntk) {
    // 			var list = document.getElementById('infoobjek');	
    // 		    list.innerHTML = infoContent;
    // 			$('#infoobjek').html('<hr style="border-width: 4px;">');

    // 		} else {
    // 			$("#infoobjek").append(infoContent);
    // 			$('#infoobjek').append('<hr style="border-width: 4px;">');

    // 		}




    // 		// console.log('3--showInfo ' + infoContent);
    // 		//$('#infoobjek').append('<h5><b>TNBH<b></h5>');

    // 		//$("#infoobjek").innerHTML(infoContent);
    // 		//$('#infoobjek').append('<hr style="border-width: 4px;">');




    // 	}
    // };
};

function thn_filter(el) {

    // console.log(val);

    //sql_value = document.getElementById('tahun').value;
    thnData = 'tahun_data= ' + el.value;
    // console.log(thnData);

    //Remove old schools to prevent "a new point is added but the old one is not removed"
    //check if map has schools layer
    if (map.hasLayer(tanahnegara_prov_tnbh)) {
        map.removeLayer(tanahnegara_prov_tnbh);
        //if so, remove previously added schools layer before loading schools with new cql filter
    };

    if (map.hasLayer(tanahnegara_prov_tntk)) {
        map.removeLayer(tanahnegara_prov_tntk);

        //if so, remove previously added schools layer before loading schools with new cql filter
    };

    tnbh(thnData);
    tntk(thnData);
    tanahnegara_prov_tnbh.addTo(map);
    tanahnegara_prov_tntk.addTo(map);
}


// var ptp_p3t  = L.Geoserver.wfs("http://************:4980/geoserver/pgt/wfs", {
// 	layers: "pgt:ptp_p3t",
// 	style: {
//     color: "black",
//     fillOpacity: "0",
//     opacity: "0.5",
// 	},
// 	onEachFeature: function (feature, layer) {

// 		var popup = 'Provinsi: ' + feature.properties.wadmpr + '<br>' +
//                        'Kabupaten: ' + feature.properties.wadmkk +  '<br>' +
//                        'Tahun Data: ' + feature.properties.tahun_data +  '<br>' +
// 					   'Luas(Ha):' + feature.properties.luas_ha +  '<br>' +
// 					   'Penggunaan: ' + feature.properties.qname50 +  '<br>' +
// 					   'No PTP:' + feature.properties.no_ptp
//         layer.bindPopup(popup);

// 	}
// });



// var wp3wt_pfn  = L.Geoserver.wfs("http://************:4980/geoserver/pgt/wfs", {
// 	layers: "pgt:wp3wt_pfn",
// 	style: {
//     color: "black",
//     fillOpacity: "0",
//     opacity: "0.5",
// 	},
// 	onEachFeature: function (feature, layer) {

// 		var popup2 = 'Provinsi: ' + feature.properties.wadmpr + '<br>' +
//                        'Kabupaten: ' + feature.properties.wadmkk +  '<br>' +
//                        'Tahun Data: ' + feature.properties.tahun_data +  '<br>' +
// 					   'Luas(Ha):' + feature.properties.luas_ha +  '<br>' +
// 					   'Pemanfaatan: ' + feature.properties.pfnobjname
//         layer.bindPopup(popup2);

// 	}
// });



// var npgt_prov  = L.Geoserver.wfs("http://************:4980/geoserver/pgt/wfs", {
// 	layers: "pgt:npgt_prov",
// 	style: {
//     color: "blue",
//     fillOpacity: "0",
//     opacity: "0.5",
// 	},
// 	onEachFeature: function (feature, layer) {
// 		var popupTxt = 'Provinsi: ' + feature.properties.wadmpr + '<br>' +
//                        'Kabupaten: ' + feature.properties.wadmkk +  '<br>' +
// 					   'Penggunaan Terakhir: ' + feature.properties.qname100 + '<br>' +
//                        'Perubahan: ' + feature.properties.gq_name +  '<br>' +
// 					   'RTRW: ' + feature.properties.wname +  '<br>' +
// 					   'Kesesuaian: ' + feature.properties.nname +  '<br>' +
// 					   'Penguasaan: ' + feature.properties.oname +  '<br>' +
// 					   'Ketersediaan: ' + feature.properties.vname +  '<br>' +
//                        'Tahun Data: ' + feature.properties.tahun_data
//         layer.bindPopup(popupTxt);

// 	}
// });

// map.fitBounds(ptp_p3t.getBounds());


map.addLayer(osmLayer);

var baseTree = [{
    label: 'OpenStreeMap',
    layer: osmLayer
}];
var role = "<?=$this->session->users['id_user_group']?>"

if (role == 1) {
    
    var overlaysTree = [{
            label: 'Neraca Penatagunaan Tanah Regional',
            selectAllCheckbox: false,
            children: [{
                    label: 'NPGT Provinsi',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>'+
                        '<input class="checkPeta" style="boder:none" onclick="npgtProvCall(0)" type="checkbox" id="npgtProvCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="npgtProvCall(1)">NPGT Provinsi</button></div>',
                        // label: 'NPGT Provinsi',
                        // layer: npgt_prov
                    }, ]
                },
                {
                    label: 'NPGT Kabupaten/Kota',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kabkotACall(0)" id="kabkotACheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kabkotACall(1)">Kabupaten/Kota </button></div>',
                        
                        // label: 'NPGT Kabupaten/Kota',
                        // layer: npgt_kabkota
                    }, ]
                },
                
                {
                    label: 'NPGT Kecamatan',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecACall(0)" id="kecACheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecACall(1)">Administrasi(A) </button></div>',
                        
                            // label: 'Administrasi(A)',
                            // layer: npgt_kec_a
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecHCall(0)" id="kecHCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecHCall(1)">Sungai(H) </button></div>',
                        // label: 'Sungai(H)',
                        //     layer: npgt_kec_h
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecKCall(0)" id="kecKCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecKCall(1)">Jalan(K) </button></div>',
                        // label: 'Jalan(K)',
                        //     layer: npgt_kec_k
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecNCall(0)" id="kecNCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecNCall(1)">Kesesuaian(N) </button></div>',
                        // label: 'Kesesuaian(N)',
                        //     layer: npgt_kec_n
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecOCall(0)" id="kecOCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecOCall(1)">Penggunaan Tanah Terakir(O) </button></div>',
                            // label: 'Gambaran Umum Penguasaan Tanah(O)',
                            // layer: npgt_kec_o
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecQCall(0)" id="kecQCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecQCall(1)">Penggunaan Tanah Terakhir(Q) </button></div>',
                        // label: 'Penggunaan Tanah Terakhir(Q)',
                        //     layer: npgt_kec_q
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecVCall(0)" id="kecVCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecVCall(1)">Ketersediaan(V) </button></div>',
                        // label: 'Ketersediaan(V)',
                        //     layer: npgt_kec_v
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecWCall(0)" id="kecWCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecWCall(1)">RTRW(W) </button></div>',
                            // label: 'RTRW(W)',
                            // layer: npgt_kec_w
                        }
                    ]
                },
            ],
        },
        {
            label: 'Neraca Penatagunaan Tanah Sektoral',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="npgtKebunCall(0)" id="npgtKebunCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="npgtKebunCall(1)">Npgt Perkebunan </button></div>',
                    //         label: 'NPGT Perkebunan',
                    // layer: npgt_perkebunan
                }
                // {
                //     label: 'NPGT Industri',
                //     layer: null
                // }, //npgt_industri},
                // {
                //     label: 'NPGT Perumahan',
                //     layer: null
                // }, //npgt_perumahan}
            ]
        },
        {
            label: 'Pertimbangan Teknis Pertanahan',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpilProvCall(0)" id="ptpilProvCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpilProvCall(1)">PTP Izin Lokasi Provinsi-AOI </button></div>',
                    // label: 'PTP Izin Lokasi Provinsi-AOI',
                    // layer: ptpil_prov
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpilProvPoiCall(0)" id="ptpilProvPoiCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpilProvPoiCall(1)">PTP Izin Lokasi Provinsi-POI </button></div>',
                    // label: 'PTP Izin Lokasi Provinsi-POI',
                    // layer: ptpil_prov_tk
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpP3tCall(0)" id="ptpP3tCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpP3tCall(1)">PTP IPPT Provinsi </button></div>',
                    // label: 'PTP IPPT Provinsi',
                    // layer: ptp_p3t
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpPkP3tCall(0)" id="ptpPkP3tCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpPkP3tCall(1)">PTP P Kebijakan P3T </button></div>',
                    // label: 'PTP P Kebijakan P3T',
                    // layer: ptp_pk_p3t
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpBerusahaCall(0)" id="ptpBerusahaCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpBerusahaCall(1)">PTP PKKPR Kegiatan Berusaha </button></div>',
                    // label: 'PTP PKKPR Kegiatan Berusaha',
                    // layer: ptp_kppr_berusaha
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpNonBerusahaCall(0)" id="ptpNonBerusahaCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpNonBerusahaCall(1)">PTP PKKPR Kegiatan Non Berusaha </button></div>',
                    // label: 'PTP PKKPR Kegiatan Non Berusaha',
                    // layer: ptp_kppr_non_berusaha
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpStranasCall(0)" id="ptpStranasCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpStranasCall(1)">PTP P/RKKPR Kegiatan Stranas </button></div>',
                    // label: 'PTP P/RKKPR Kegiatan Stranas',
                    // layer: ptp_kppr_stranas
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpTnhTimbulCall(0)" id="ptpTnhTimbulCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpTnhTimbulCall(1)">PTP PSRP Tanah Timbul </button></div>',
                    // label: 'PTP PSRP Tanah Timbul',
                    // layer: ptp_tnh_timbul
                },
            ]
        },
        {
            label: 'Penataan WP3WT',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPerbatasanCall(0)" id="wp3wtPerbatasanCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPerbatasanCall(1)">Penataan Perbatasan </button></div>',
                    // label: 'Penataan Perbatasan',
                    // layer: wp3wt_perbatasan
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPulauCall(0)" id="wp3wtPulauCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPulauCall(1)">Penataan Pulau Pulau Kecil </button></div>',
                    // label: 'Penataan Pulau Pulau Kecil',
                    // layer: wp3wt_pulau
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtTertentuCall(0)" id="wp3wtTertentuCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtTertentuCall(1)">Penataan Wilayah Tertentu </button></div>',
                    // label: 'Penataan Wilayah Tertentu',
                    // layer: wp3wt_tertentu
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPesisirCall(0)" id="wp3wtPesisirCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPesisirCall(1)">Penataan Pesisir </button></div>',
                    // label: 'Penataan Pesisir',
                    // layer: wp3wt_pesisir
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtTimbulCall(0)" id="wp3wtTimbulCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtTimbulCall(1)">Tanah Timbul </button></div>',
                    // label: 'Tanah Timbul',
                    // layer: wp3wt_timbul
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPpkPoiCall(0)" id="wp3wtPpkPoiCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPpkPoiCall(1)">Sebaran Pulau Pulau Kecil (POI) </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil (POI)',
                    // layer: wp3wt_ppk_poi
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="Wp3wtPpkPolygonCall(0)" id="Wp3wtPpkPolygonCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="Wp3wtPpkPolygonCall(1)">Sebaran Pulau Pulau Kecil </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil',
                    // layer: wp3wt_ppk_polygon
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPpktCall(0)" id="wp3wtPpktCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPpktCall(1)">Sebaran Pulau Pulau Kecil Terluar </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil Terluar',
                    // layer: wp3wt_ppkt
                }


            ]
        },
        {
            label: 'Tanah Negara Bekas Hak/Bekas Kawasan/Tanah Kritis',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="tnbhCall(0)" id="tnbhCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="tnbhCall(1)">Tanah Negara Bekas Hak </button></div>',
                    // label: 'Tanah Negara Bekas Hak',
                    // layer: tanahnegara_prov_tnbh
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="tnbkCall(0)" id="tnbkCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="tnbkCall(1)">Tanah Negara Bekas Kawasan </button></div>',
                    // label: 'Tanah Negara Bekas Kawasan',
                    // layer: tanahnegara_prov_tnbk
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="tntkCall(0)" id="tntkCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="tntkCall(1)">Tanah Negara Tanah Kritis </button></div>',
                    // label: 'Tanah Kritis',
                    // layer: tanahnegara_prov_tntk
                },
            ]
        },
        {
            label: 'Monitoring PPT',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="mpptCall(0)" id="mpptCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="mpptCall(1)">Monitoring PPT </button></div>',
                //     label: 'Monitoring PPT',
                // layer: mppt_prov
            }, ]
        },
        {
            label: 'Kemampuan Tanah',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kemampuanTanahCall(0)" id="kemampuanTanahCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kemampuanTanahCall(1)">Kemampuan Tanah </button></div>',
                //     label: 'Kemampuan Tanah',
                // layer: kemampuan_tanah
            }, ]
        },
        {
            label: 'Penatagunaan Tanah Lainnya',
            selectAllCheckbox: false,
            children: [{
                     label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="pgtlDesaCall(0)" id="pgtlDesaCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="pgtlDesaCall(1)">Wilayah Administrasi Desa </button></div>',
                    //     label: 'Wilayah Administrasi Desa',
                    // layer: bts_desa
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="pgtlKecamatanCall(0)" id="pgtlKecamatanCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="pgtlKecamatanCall(1)">Wilayah Administrasi Kecamatan </button></div>',
                    //     label: 'Wilayah Administrasi Kecamatan',
                    // layer: bts_kecamatan
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="pgtlRtrwCall(0)" id="pgtlRtrwCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="pgtlRtrwCall(1)">RTRW </button></div>',
                    //     label: 'RTRW',
                    // layer: rtrw
                }
            ]
        },
        {
            label: 'Lahan Baku Sawah',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="lahanBakuCall(0)" id="lahanBakuCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="lahanBakuCall(1)">Lahan Baku Sawah </button></div>',
                //     label: 'Lahan Baku Sawah',
                // layer: lahanbakusawah_prov
            }, ]
        },
    ]   
}
if (role == 2) {
    
    
    var overlaysTree = [
        {
            label: 'Pertimbangan Teknis Pertanahan',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpilProvCall(0)" id="ptpilProvCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpilProvCall(1)">PTP Izin Lokasi Provinsi-AOI </button></div>',
                    // label: 'PTP Izin Lokasi Provinsi-AOI',
                    // layer: ptpil_prov
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpilProvPoiCall(0)" id="ptpilProvPoiCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpilProvPoiCall(1)">PTP Izin Lokasi Provinsi-POI </button></div>',
                    // label: 'PTP Izin Lokasi Provinsi-POI',
                    // layer: ptpil_prov_tk
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpP3tCall(0)" id="ptpP3tCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpP3tCall(1)">PTP IPPT Provinsi </button></div>',
                    // label: 'PTP IPPT Provinsi',
                    // layer: ptp_p3t
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpPkP3tCall(0)" id="ptpPkP3tCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpPkP3tCall(1)">PTP P Kebijakan P3T </button></div>',
                    // label: 'PTP P Kebijakan P3T',
                    // layer: ptp_pk_p3t
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpBerusahaCall(0)" id="ptpBerusahaCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpBerusahaCall(1)">PTP PKKPR Kegiatan Berusaha </button></div>',
                    // label: 'PTP PKKPR Kegiatan Berusaha',
                    // layer: ptp_kppr_berusaha
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpNonBerusahaCall(0)" id="ptpNonBerusahaCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpNonBerusahaCall(1)">PTP PKKPR Kegiatan Non Berusaha </button></div>',
                    // label: 'PTP PKKPR Kegiatan Non Berusaha',
                    // layer: ptp_kppr_non_berusaha
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpStranasCall(0)" id="ptpStranasCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpStranasCall(1)">PTP P/RKKPR Kegiatan Stranas </button></div>',
                    // label: 'PTP P/RKKPR Kegiatan Stranas',
                    // layer: ptp_kppr_stranas
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="ptpTnhTimbulCall(0)" id="ptpTnhTimbulCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="ptpTnhTimbulCall(1)">PTP PSRP Tanah Timbul </button></div>',
                    // label: 'PTP PSRP Tanah Timbul',
                    // layer: ptp_tnh_timbul
                },
            ]
        },
       {
            label: 'Tanah Negara Bekas Hak/Bekas Kawasan/Tanah Kritis',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="tnbhCall(0)" id="tnbhCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="tnbhCall(1)">Tanah Negara Bekas Hak </button></div>',
                    // label: 'Tanah Negara Bekas Hak',
                    // layer: tanahnegara_prov_tnbh
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="tnbkCall(0)" id="tnbkCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="tnbkCall(1)">Tanah Negara Bekas Kawasan </button></div>',
                    // label: 'Tanah Negara Bekas Kawasan',
                    // layer: tanahnegara_prov_tnbk
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="tntkCall(0)" id="tntkCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="tntkCall(1)">Tanah Negara Tanah Kritis </button></div>',
                    // label: 'Tanah Kritis',
                    // layer: tanahnegara_prov_tntk
                },
            ]
        },
        {
            label: 'Monitoring PPT',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="mpptCall(0)" id="mpptCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="mpptCall(1)">MOnitoring PPT </button></div>',
                //     label: 'Monitoring PPT',
                // layer: mppt_prov
            }, ]
        },
        {
            label: 'Kemampuan Tanah',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kemampuanTanahCall(0)" id="kemampuanTanahCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kemampuanTanahCall(1)">Kemampuan Tanah </button></div>',
                //     label: 'Kemampuan Tanah',
                // layer: kemampuan_tanah
            }, ]
        },
        {
            label: 'Penatagunaan Tanah Lainnya',
            selectAllCheckbox: false,
            children: [{
                     label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="pgtlDesaCall(0)" id="pgtlDesaCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="pgtlDesaCall(1)">Wilayah Administrasi Desa </button></div>',
                    //     label: 'Wilayah Administrasi Desa',
                    // layer: bts_desa
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="pgtlKecamatanCall(0)" id="pgtlKecamatanCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="pgtlKecamatanCall(1)">Wilayah Administrasi Kecamatan </button></div>',
                    //     label: 'Wilayah Administrasi Kecamatan',
                    // layer: bts_kecamatan
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="pgtlRtrwCall(0)" id="pgtlRtrwCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="pgtlRtrwCall(1)">RTRW </button></div>',
                    //     label: 'RTRW',
                    // layer: rtrw
                }
            ]
        }
    ]   
}

if (role == 3) {
    
    var overlaysTree = [{
            label: 'Neraca Penatagunaan Tanah Regional',
            selectAllCheckbox: false,
            children: [{
                    label: 'NPGT Provinsi',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>'+
                        '<input class="checkPeta" style="boder:none" onclick="npgtProvCall(0)" type="checkbox" id="npgtProvCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="npgtProvCall(1)">NPGT Provinsi</button></div>',
                        // label: 'NPGT Provinsi',
                        // layer: npgt_prov
                    }, ]
                },
                {
                    label: 'NPGT Kabupaten/Kota',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kabkotACall(0)" id="kabkotACheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kabkotACall(1)">Kabupaten/Kota </button></div>',
                        
                        // label: 'NPGT Kabupaten/Kota',
                        // layer: npgt_kabkota
                    }, ]
                },
                
                {
                    label: 'NPGT Kecamatan',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecACall(0)" id="kecACheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecACall(1)">Administrasi(A) </button></div>',
                        
                            // label: 'Administrasi(A)',
                            // layer: npgt_kec_a
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecHCall(0)" id="kecHCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecHCall(1)">Sungai(H) </button></div>',
                        // label: 'Sungai(H)',
                        //     layer: npgt_kec_h
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecKCall(0)" id="kecKCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecKCall(1)">Jalan(K) </button></div>',
                        // label: 'Jalan(K)',
                        //     layer: npgt_kec_k
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecNCall(0)" id="kecNCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecNCall(1)">Kesesuaian(N) </button></div>',
                        // label: 'Kesesuaian(N)',
                        //     layer: npgt_kec_n
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecOCall(0)" id="kecOCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecOCall(1)">Penggunaan Tanah Terakir(O) </button></div>',
                            // label: 'Gambaran Umum Penguasaan Tanah(O)',
                            // layer: npgt_kec_o
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecQCall(0)" id="kecQCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecQCall(1)">Penggunaan Tanah Terakhir(Q) </button></div>',
                        // label: 'Penggunaan Tanah Terakhir(Q)',
                        //     layer: npgt_kec_q
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecVCall(0)" id="kecVCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecVCall(1)">Ketersediaan(V) </button></div>',
                        // label: 'Ketersediaan(V)',
                        //     layer: npgt_kec_v
                        },
                        {
                            label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="kecWCall(0)" id="kecWCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="kecWCall(1)">RTRW(W) </button></div>',
                            // label: 'RTRW(W)',
                            // layer: npgt_kec_w
                        }
                    ]
                },
            ],
        },
        {
            label: 'Neraca Penatagunaan Tanah Sektoral',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="npgtKebunCall(0)" id="npgtKebunCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="npgtKebunCall(1)">Npgt Perkebunan </button></div>',
                    //         label: 'NPGT Perkebunan',
                    // layer: npgt_perkebunan
                }
                // {
                //     label: 'NPGT Industri',
                //     layer: null
                // }, //npgt_industri},
                // {
                //     label: 'NPGT Perumahan',
                //     layer: null
                // }, //npgt_perumahan}
            ]
        },
        {
            label: 'Lahan Baku Sawah',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="lahanBakuCall(0)" id="lahanBakuCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="lahanBakuCall(1)">Lahan Baku Sawah </button></div>',
                //     label: 'Lahan Baku Sawah',
                // layer: lahanbakusawah_prov
            }, ]
        },
    ]   
}

if (role == 4) {
    
    var overlaysTree = [
        
        {
            label: 'Penataan WP3WT',
            selectAllCheckbox: false,
            children: [{
                label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPerbatasanCall(0)" id="wp3wtPerbatasanCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPerbatasanCall(1)">Penataan Perbatasan </button></div>',
                    // label: 'Penataan Perbatasan',
                    // layer: wp3wt_perbatasan
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPulauCall(0)" id="wp3wtPulauCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPulauCall(1)">Penataan Pulau Pulau Kecil </button></div>',
                    // label: 'Penataan Pulau Pulau Kecil',
                    // layer: wp3wt_pulau
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtTertentuCall(0)" id="wp3wtTertentuCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtTertentuCall(1)">Penataan Wilayah Tertentu </button></div>',
                    // label: 'Penataan Wilayah Tertentu',
                    // layer: wp3wt_tertentu
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPesisirCall(0)" id="wp3wtPesisirCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPesisirCall(1)">Penataan Pesisir </button></div>',
                    // label: 'Penataan Pesisir',
                    // layer: wp3wt_pesisir
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtTimbulCall(0)" id="wp3wtTimbulCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtTimbulCall(1)">Tanah Timbul </button></div>',
                    // label: 'Tanah Timbul',
                    // layer: wp3wt_timbul
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPpkPoiCall(0)" id="wp3wtPpkPoiCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPpkPoiCall(1)">Sebaran Pulau Pulau Kecil (POI) </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil (POI)',
                    // layer: wp3wt_ppk_poi
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="Wp3wtPpkPolygonCall(0)" id="Wp3wtPpkPolygonCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="Wp3wtPpkPolygonCall(1)">Sebaran Pulau Pulau Kecil </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil',
                    // layer: wp3wt_ppk_polygon
                },
                {
                    label: '<div>'+
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPpktCall(0)" id="wp3wtPpktCheck">'+
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPpktCall(1)">Sebaran Pulau Pulau Kecil Terluar </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil Terluar',
                    // layer: wp3wt_ppkt
                }


            ]
        }
    ]   
}


var lay = L.control.layers.tree(baseTree, overlaysTree, {
    namedToggle: true,
    selectorBack: false,
    closedSymbol: '&#8862; &#x1f5c0;',
    openedSymbol: '&#8863; &#x1f5c1;',
    // collapseAll: 'Collapse all',
    // expandAll: 'Expand all',
    collapsed: false,
});

lay.addTo(map).collapseTree().expandSelected().collapseTree(true);
// L.DomEvent.on(L.DomUtil.get('onlysel'), 'click', function() {
//     lay.collapseTree(true).expandSelected(true);
// });




// console.log(lay.getOverlays())

// var baseLayers = [
// 	{
// 		name: "Open Street Map",
// 		layer: osmLayer
// 	}
// 	// {
// 	// 	name: "Hiking",
// 	// 	layer: L.tileLayer("https://toolserver.org/tiles/hikebike/{z}/{x}/{y}.png")
// 	// },
// 	// {
// 	// 	name: "Aerial",
// 	// 	layer: L.tileLayer('https://otile{s}.mqcdn.com/tiles/1.0.0/{type}/{z}/{x}/{y}.{ext}', {
// 	// 		type: 'sat',
// 	// 		ext: 'jpg',
// 	// 		attribution: 'Tiles Courtesy of <a href="https://www.mapquest.com/">MapQuest</a> &mdash; Portions Courtesy NASA/JPL-Caltech and U.S. Depart. of Agriculture, Farm Service Agency',
// 	// 		subdomains: '1234'
// 	// 	})
// 	// }
// 	// {
// 	// 	group: "Road Layers",
// 	// 	collapsed: true,
// 	// 	layers: [
// 	// 		{
// 	// 			name: "Open Cycle Map",
// 	// 			layer: L.tileLayer('https://{s}.tile.opencyclemap.org/cycle/{z}/{x}/{y}.png')
// 	// 		},
// 	// 		{
// 	// 			name: "Transports",
// 	// 			layer: L.tileLayer('https://{s}.tile2.opencyclemap.org/transport/{z}/{x}/{y}.png')
// 	// 		}
// 	// 	]
// 	// }
// ];

// var overLayers = [
// 	{
// 		group: "Neraca Penatagunaan Tanah Regional",
// 		collapsed:false,
// 		layers: [
// 			{
// 				active: false,
// 				name: "NPGT Provinsi",
// 				// icon: '',
// 				layer: npgt_prov
// 			}
// 		]
// 	},
// 	{
// 		group: "Lahan Baku Sawah",
// 		layers: [
// 			{
// 				active: false,
// 				name: "Lahan Baku Sawah",
// 				// icon: '',
// 				layer: lahanbakusawah_prov
// 			}
// 		]
// 	},
// 	{
// 		group: "Monitoring PPT",
// 		layers: [
// 			{
// 				active: false,
// 				name: "Monitoring PPT",
// 				// icon: '',
// 				layer: mppt_prov
// 			}
// 		]
// 	},
// 	{
// 		group: "Tanah Negara Bekas Hak",
// 		layers: [
// 			{
// 				active: false,
// 				name: "Tanah Negara Bekas Hak",
// 				// icon: '',
// 				layer: tanahnegara_prov_tnbh
// 			},
// 			{
// 				active: false,
// 				name: "Tanah Negara Bekas Kawasan",
// 				// icon: '',
// 				layer: tanahnegara_prov_tnbk
// 			},
// 			{
// 				active: false,
// 				name: "Tanah Negara Tanah Kritis",
// 				// icon: '',
// 				layer: tanahnegara_prov_tntk
// 			}
// 		]
// 	},
// 	{
// 		group: "Penataan WP3WT",
// 		layers: [
// 			{
// 				active: false,
// 				name: "Penggunaan Tanah (PTN)",
// 				layer: wp3wt_ptn
// 			},
// 			{
// 				active: false
// 				name: "Pemanfaatan Tanah (PFN)",
// 				layer: wp3wt_pfn
// 			},
// 			{
// 				active: false,
// 				name: "Penguasaan Tanah (PSN)",
// 				layer: wp3wt_psn
// 			},
// 			{
// 				active: false,
// 				name: "Pemilikan Tanah (PMN)",
// 				layer: wp3wt_pmn
// 			},
// 			{
// 				active: false,
// 				name: "Pulau-Pulau Kecil",
// 				layer: wp3wt_ppk_polygon
// 			},
// 			{
// 				active: false,
// 				name: "Pulau-Pulau Kecil (POI)",
// 				layer: wp3wt_ppk_point
// 			},
// 			{
// 				active: false,
// 				name: "Pulau-Pulau Kecil Terluar",
// 				layer: wp3wt_ppkt
// 			},


// 			// {
// 			// 	active: true,
// 			// 	name: "Streams",
// 			// 	layer: {
// 			// 		type: "tileLayer.wms",
// 			// 		args: ["https://siat.regione.umbria.it/arcgis/services/public/DBT_04_Idrografia/MapServer/WMSServer", {
// 			// 				layers: '6',
// 			// 				format: 'image/png8',
// 			// 				transparent: true,
// 			// 			}
// 			// 		]
// 			// 	}
// 			// }
// 		]
// 	},
// 	{
// 		group: "Pertimbangan Teknis Pertanahan",
// 		layers: [
// 			{
// 				active: false,
// 				name: "PTP-IL Provinsi",
// 				layer: ptpil_prov
// 			},
// 			{
// 				active: false,
// 				name: "PTP-IL Provinsi (POI)",
// 				layer: ptpil_prov_tk
// 			},
// 			{
// 				active: false,
// 				name: "PTP P3T",
// 				layer: ptp_p3t
// 			}

// 			// {
// 			// 	active: true,
// 			// 	name: "Streams",
// 			// 	layer: {
// 			// 		type: "tileLayer.wms",
// 			// 		args: ["https://siat.regione.umbria.it/arcgis/services/public/DBT_04_Idrografia/MapServer/WMSServer", {
// 			// 				layers: '6',
// 			// 				format: 'image/png8',
// 			// 				transparent: true,
// 			// 			}
// 			// 		]
// 			// 	}
// 			// }
// 		]
// 	}
// ];
map.on('popupopen', function(e) {
    var px = map.project(e.target._popup
        ._latlng); // find the pixel location on the map where the popup anchor is
    px.y -= e.target._popup._container.clientHeight /
        2; // find the height of the popup container, divide by 2, subtract from the Y axis of marker location
    map.panTo(map.unproject(px), {
        animate: true
    }); // pan to new center
});


// var panelLayers = new L.Control.PanelLayers(baseLayers, overLayers, {
// 	// compact: true,
// 	// collapsed: false,
// 	collapsibleGroups: true
// });

// map.addControl(panelLayers);

var cek = 0;
// var sidebar = L.control.sidebar('sidebar-panel').addTo(map);
$(document).ready(function() {
    // var tnhData='';
    // alert('masuk')
    // console.log('masuk')

    // for (var name in overlaysTree) {
    // map.removeLayer(overlaysTree[name]);
    // }
    // $('.bootstrap-select').slimscroll({
    //     color: 'rgba(0,0,0,0.5)',
    //     size: '0px',
    //     height: '473px',
    //     alwaysVisible: true
    // });
    initComboboxPeta('provSelect', 19)
    var str = '<option value="">Pilih Tahun</option>'
    // var year = '<?=date('Y')?>'

    // for (let i = year; i >= 1990; i--) {
    //     str += '<option value="' + i + '">' + i + '</option>';
    //     // console.log(i)
    // }
    $('#thnSelect').html(str)
    $("#provSelect").change(function() {
        refreshSelectbootPeta('kabSelect', 20, 'kd_prov', this.value);
        // removeSelectpicker('kabSelect')

    });
    // setTimeout(removeLayer, 5000)
    removeLayer()
    // console.log('cek'+cek)
});

function initComboboxPeta(divname, refindex, jns, $rev = 1) {
    var url = null;
    if ($rev === 1) {
        url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex;
    } else {
        url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex + "/" + 0;
    }
    wgiAjaxCache(url, function(ajaxdata) {
        //console.log(ajaxdata);
        jdata = JSON.parse(ajaxdata);
        $('#' + divname).empty();
        $('#' + divname).append(new Option(" Semua Provinsi", ""));
        $.each(jdata, function(i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });
        // $('#' + divname).selectpicker('refresh')

    });


}

function refreshSelectbootPeta(divname, refindex, refresh_field, refresh_value, $rev = 1) {
    var url = null;
    if ($rev === 1) {
        url = WGI_APP_BASE_URL + "lookup/refreshlookVchar/" + refindex + "/" + refresh_field + "/" + refresh_value;
    } else {
        url = WGI_APP_BASE_URL + "lookup/refreshlookVchar/" + refindex + "/" + refresh_field + "/" + refresh_value +
            "/" + 0;
    }
    wgiAjaxCache(url, function(ajaxdata) {
        jdata = JSON.parse(ajaxdata);

        // console.log(jdata);

        $('#' + divname).empty();
        $('#' + divname).append(new Option(" Semua Kabupaten/Kota", ''));
        $.each(jdata, function(i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        // $('#' + divname).selectpicker('refresh');
        // $('#' + divname).trigger('change');
    });

    
    
}

function removeLayer(v) {
    
        if (map.hasLayer(tanahnegara_prov_tnbh)) {
            map.removeLayer(tanahnegara_prov_tnbh);
        };


        if (map.hasLayer(tanahnegara_prov_tnbk)) {
            map.removeLayer(tanahnegara_prov_tnbk);
        };


        if (map.hasLayer(tanahnegara_prov_tntk)) {
            map.removeLayer(tanahnegara_prov_tntk);
        };
        if (map.hasLayer(npgt_prov)) {
            map.removeLayer(npgt_prov);
        };

        if (map.hasLayer(npgt_kabkota)) {
            map.removeLayer(npgt_kabkota);
        };
    
        if (map.hasLayer(npgt_kec_a)) {
            map.removeLayer(npgt_kec_a);
        };

        if (map.hasLayer(npgt_kec_h)) {
            map.removeLayer(npgt_kec_h);
        };

        if (map.hasLayer(npgt_kec_k)) {
            map.removeLayer(npgt_kec_k);
        };
        if (map.hasLayer(npgt_kec_n)) {
            map.removeLayer(npgt_kec_n);
        };

        if (map.hasLayer(npgt_kec_o)) {
            map.removeLayer(npgt_kec_o);
        };

        if (map.hasLayer(npgt_kec_q)) {
            map.removeLayer(npgt_kec_q);
        };

        if (map.hasLayer(npgt_kec_v)) {
            map.removeLayer(npgt_kec_v);
        };

        if (map.hasLayer(npgt_kec_w)) {
            map.removeLayer(npgt_kec_w);
        };
        if (map.hasLayer(npgt_perkebunan)) {
            map.removeLayer(npgt_perkebunan);
        };

        if (map.hasLayer(mppt_prov)) {
            map.removeLayer(mppt_prov);
        };
        if (map.hasLayer(kemampuan_tanah)) {
            map.removeLayer(kemampuan_tanah);
        };

        if (map.hasLayer(lahanbakusawah_prov)) {
            map.removeLayer(lahanbakusawah_prov);
        };

        if (map.hasLayer(ptp_p3t)) {
            map.removeLayer(ptp_p3t);
        };

        if (map.hasLayer(ptpil_prov)) {
            map.removeLayer(ptpil_prov);
        };

        if (map.hasLayer(ptpil_prov_tk)) {
            map.removeLayer(ptpil_prov_tk);
        };

        if (map.hasLayer(ptp_kppr_berusaha)) {
            map.removeLayer(ptp_kppr_berusaha);
        };
        if (map.hasLayer(ptp_kppr_stranas)) {
            map.removeLayer(ptp_kppr_stranas);
        };
        if (map.hasLayer(ptp_kppr_non_berusaha)) {
            map.removeLayer(ptp_kppr_non_berusaha);
        };
        if (map.hasLayer(ptp_pk_p3t)) {
            map.removeLayer(ptp_pk_p3t);
        };
        if (map.hasLayer(ptp_tnh_timbul)) {
            map.removeLayer(ptp_tnh_timbul);
        };
        if (map.hasLayer(wp3wt_ppk_polygon)) {
            map.removeLayer(wp3wt_ppk_polygon);
        };
        if (map.hasLayer(wp3wt_ppk_poi)) {
            map.removeLayer(wp3wt_ppk_poi);
        };
        if (map.hasLayer(wp3wt_ppkt)) {
            map.removeLayer(wp3wt_ppkt);
        };
        if (map.hasLayer(wp3wt_perbatasan)) {
            map.removeLayer(wp3wt_perbatasan);
        };
        if (map.hasLayer(wp3wt_pesisir)) {
            map.removeLayer(wp3wt_pesisir);
        };

        if (map.hasLayer(wp3wt_pulau)) {
            map.removeLayer(wp3wt_pulau);
        };

        if (map.hasLayer(wp3wt_tertentu)) {
            map.removeLayer(wp3wt_tertentu);
        };

        if (map.hasLayer(wp3wt_timbul)) {
            map.removeLayer(wp3wt_timbul);
        };
        if (map.hasLayer(rtrw)) {
            map.removeLayer(rtrw);
        };
        if (map.hasLayer(bts_desa)) {
            map.removeLayer(bts_desa);
        };
        if (map.hasLayer(bts_kecamatan)) {
            map.removeLayer(bts_kecamatan);
        };
    }
    var active='';
    function getYear(layer) {
        if (active!= layer) {
            var url = "<?php echo base_url('lookup/getYear/')?>"+layer;
            $.get( url, function( data ) {
                data = JSON.parse(data)
                $('#thnSelect').html(data.year)

            });
        }
    }
</script>