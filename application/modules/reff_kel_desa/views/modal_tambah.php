<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!-- Slide Right Modal -->
<div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-slideright">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 id="modalTitle" class="block-title">Tambah <?php echo $title; ?></h3>
                </div>
                <div class="block-content">
                    <!-- form start -->
                    <input type="hidden" id="modeform">
                    <form  id="frm-tambah" role="form" way-data="formData">
                        <div class="box-body">
                            <!-- <div class="form-group">
                                <label for="exampleInputPassword1"><PERSON><PERSON> Anggaran</label>
                                <select class="form-control" id="thang" name="thang" onchange="changeThang(this);"></select>
                            </div> -->
                            <div class="form-group">
                                <label for="exampleInputPassword1">Provinsi</label>
                                <select class="form-control" id="kd_prov" name="kd_prov" onchange="changeProv(this);"></select>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">Nama Kota/Kabupaten</label>
                                <select class="form-control" id="kd_kabkot" name="kd_kabkot" onchange="changeKab(this);"></select>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">Nama Kecamatan</label>
                                <select class="form-control" id="kd_kecamatan" name="kd_kecamatan" onchange="changeKec(this);"></select>
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">Nama Kelurahan</label>
                                <input type="text" class="form-control" id="kelurahan" name="kelurahan" placeholder="" maxlength="255">
                            </div>
                            <!-- <div class="form-group">
                                <label for="exampleInputPassword1">Kode Kabupaten/kota BPIW</label>
                                <div class="row form-inline input-group" style="padding-left: 18px; padding-right: 18px;">
                                    <span class="input-group-addon" id="kd_kab_kota_bpiw" name="kd_kab_kota_bpiw"></span>
                                    <input type="text" class="form-control" id="kd_kab_kota_bpiw2" name="kd_kab_kota_bpiw2" maxlength="2" placeholder="">
                                </div>
                            </div> -->
                            <!-- <div class="form-group">
                                <label for="exampleInputPassword1">Kode Kabupaten/kota IRMSv3</label>
                                <div class="row form-inline input-group" style="padding-left: 18px; padding-right: 18px;">
                                    <span class="input-group-addon" id="kd_kab_irmsv3" name="kd_kab_irmsv3"></span>
                                    <input type="text" class="form-control" id="kd_kab_irmsv32" name="kd_kab_irmsv32" maxlength="2" placeholder="">
                                </div>                              
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">Kode Kabupaten/kota RAMS</label>
                                <div class="row form-inline input-group" style="padding-left: 18px; padding-right: 18px;">
                                    <span class="input-group-addon" id="kd_kab_rams" name="kd_kab_rams"></span>
                                    <input type="text" class="form-control" id="kd_kab_rams2" name="kd_kab_rams2" maxlength="2" placeholder="">
                                </div>                               
                            </div> -->
                            <div class="form-group">
                                <label for="exampleInputPassword1">Kode Kecamatan</label>
                                <div class="row form-inline input-group" style="padding-left: 18px; padding-right: 18px;">
                                    <span class="input-group-addon" id="kd_kel" name="kd_kel"></span>
                                    <input type="text" class="form-control" id="kd_kel2" name="kd_kel2" maxlength="3" placeholder="">
                                </div>
                            </div>
                            <!-- <div class="form-group">
                                <label for="exampleInputPassword1">Kode Kabupaten/kota RKAKL</label>
                                <div class="row form-inline input-group" style="padding-left: 18px; padding-right: 18px;">
                                    <span class="input-group-addon" id="kd_kab_rkakl" name="kd_kab_rkakl"></span>
                                    <input type="text" class="form-control" id="kd_kab_rkakl2" name="kd_kab_rkakl2" maxlength="2" placeholder="">
                                </div>
                            </div> -->
                            <input type="hidden" id="id" name="id"/>
                        </div>
                        <!-- /.box-body -->
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Batal</button>
                <button onclick="javascript:simpanForm();" class="btn btn-sm btn-primary" type="button" data-bs-dismiss="modal"><i class="fa fa-check"></i>Simpan</button>
            </div>
        </div>
    </div>
</div>
<!-- END Slide Right Modal -->

<!-- Slide Right Modal -->
<!-- <div class="modal fade" id="modal-tambah" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-slideright modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-bs-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title" id="modalTitle">Tambah <?php //echo $title; ?></h3>
                </div>
                <div class="block-content">
                     form start 
                    <input type="hidden" id="modeform"/>
            		<form role="form" id="frm-tambah" class="js-validation-material form-horizontal push-10-t">
            		  <div class="box-body">
                        <div class="col-sm-12">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="form-material">
                                            <input class="form-control" type="text" id="id_kabkot" name="id_kabkot" placeholder="ID Kapupaten/Kota">
                                            <label for="id_kabkot"></label>
                                        </div>
                                    </div>
                                </div>
                            
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="form-material">
                                            <input class="form-control" type="text" id="kabkota" name="kabkota" placeholder="Kabupaten/Kota">
                                            <label for="kabkota"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-12">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group ">
                                        <div class="form-material">
                                            <label for="kd_prov">Pilih Provinsi</label>
                                            <select class="form-control" id="kd_prov" name="kd_prov">
                                               
                                            </select>
                                            <label for="kd_prov"></label>
                                        </div>
                                    </div>
                                </div>
                               
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <div class="form-material">
                                            <input class="form-control" type="text" id="kd_kab_kota_bpiw" name="kd_kab_kota_bpiw" placeholder="Kabupaten/Kota BPIW">
                                            <label for="kd_kab_kota_bpiw"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                         <div class="form-material">
                                            <input class="form-control" type="text" id="kd_kab_irmsv3" name="kd_kab_irmsv3" placeholder="Kode Kabupaten/Kota IRMS V3">
                                            <label for="kd_kab_irmsv3"></label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-sm-6">
                                         <div class="form-group">
                                         <div class="form-material">
                                            <input class="form-control" type="text" id="kd_kab_rams" name="kd_kab_rams" placeholder="Kode Kabupaten/Kota RAMS">
                                            <label for="kd_kab_rams"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                         
                            <div class="col-sm-6">
                                <div class="form-group">
                                 <div class="form-material">
                                    <input class="form-control" type="text" id="kd_kab_bps" name="kd_kab_bps" placeholder="Kode Kabupaten/Kota BPS">
                                    <label for="kd_kab_bps"></label>
                                </div>
                            </div>
                        </div>
                        
                            <div class="col-sm-6">
                                 <div class="form-group">
                                 <div class="form-material">
                                    <input class="form-control" type="text" id="kd_kab_rkakl" name="kd_kab_rkakl" placeholder="Kode Kabupaten/Kota RKAKL">
                                    <label for="kd_kab_rkakl"></label>
                                </div>
                            </div>
                        </div>
                       
         
            		  </div>
            		   /.box-body 
            		</form>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-sm btn-default" type="button" data-bs-dismiss="modal">Close</button>
                <button onclick="simpanForm()" class="btn btn-sm btn-primary" type="button><i class="fa fa-check"></i> Ok</button>
            </div>
        </div>
    </div>
</div>-->
<!-- END Slide Right Modal -->
