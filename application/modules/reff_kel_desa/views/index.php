<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<!--style for modal wizard-->
<style>
/*    table{
        clear: both;
        table-layout: fixed;
        word-wrap:break-word;
    }*/
    .background-solid{
        background-color:#F9F9F9;
        opacity : '';
    }
    .invisible{
        display:none;
    }
</style>
<!--<div class="content bg-gray-lighter">
        <div class="row items-push">
                <div class="col-sm-7">
                        <h1 class="page-heading">
                                        &nbsp;
                        </h1>
                </div>
                <div class="col-sm-5 text-right hidden-xs">
                        <ol class="breadcrumb push-10-t">
                                        <li><?php echo $title; ?></li>
                                        <li><a class="link-effect" href="">Listing</a></li>
                        </ol>
                </div>
        </div>
</div>-->
<!--<h1 class="page-heading" style="margin-left: 20px; margin-bottom: 10px; margin-top: 10px;"> <?php echo $title; ?> </h1>-->
<div class="block">
    <div class="block-header">
        <div class="row form-inline">
            <div class="col-md-12">
                <!-- <div class="form-group">
                    <label for="thang">Filter Tahun Anggaran :</label>
                    <select class="form-control" id="fthang" name="fthang" size="1"></select>
                </div> -->
                <button type="button" class="btn btn-success" onclick="javascript:tambah()" style="margin-left:20px;">
                    <i class="fa fa-plus"> </i>&nbsp; Tambah <?php echo $title; ?>
                </button>
            </div>
        </div>
        <div id="success" class="row" style="display: none;">
            <div class="col-sm-12">
                <div class="alert alert-success alert-dismissible fade in">
                    <a href="#" class="close" data-bs-dismiss="alert" aria-label="close">&times;</a>
                    <strong>Informasi: </strong><span id="success-text"></span>
                </div>
            </div>
        </div>
<!--        <button class="btn btn-minw btn-success" onclick="tambah()"><i class="fa fa-plus" aria-hidden="true"></i>&nbsp;Tambah Kabupaten/Kota</button>-->
    </div>
    <div class="block-content" style="overflow-x: auto;">
       <div class="col-sm-12">	
            <div id="alert_information" style="display:none; text-align: center;" class="alert alert-success alert-dismissable">
                <button type="button" class="close" data-bs-dismiss="alert" aria-hidden="true">×</button>
                <h3 class="font-w300 push-15"><i class="fa fa-info-circle"></i> <b>Informasi</b></h3>
                <div id="alert-content" style="font-weight: bold;"></div>
            </div>
        </div>
        <!--
        $columns = array(
    array('db' => 'kd_prov', 'dt' => 0),
    array('db' => 'kd_kab_kota_bpiw','dt'=> 1),
    array('db' => 'kab_kota','dt' =>2),
    array('db' => 'kd_kab_irmsv3', 'dt'=>3),
    array('db' => 'kd_kab_rams','dt' => 4),
    array('db' => 'kd_kab_bps', 'dt' => 5),
    array('db' => 'id_kabkot','dt' => 6),
);
        -->
        <table id="table_id" class="display">
            <thead>
                <tr>
                <th><div style="width:180px;">Provinsi</div></th>
                    <th><div style="width:180px;">Nama Kota</div></th>
                    <th><div style="width:180px;">Nama Kecamatan</div></th>
                    <th><div style="width:180px;">Nama Kelurahan</div></th>
                    <th><div style="width:180px;">Kode Kelurahan</div></th>
                    <th><div>Action</div></th>
                </tr>
            </thead>
        </table>

    </div>
</div>
<?php //echo $modal_filter; ?>
<?php echo $modal_tambah; ?>
<?php // echo $modal_edit; ?>
<?php
//echo $modal_view; ?>