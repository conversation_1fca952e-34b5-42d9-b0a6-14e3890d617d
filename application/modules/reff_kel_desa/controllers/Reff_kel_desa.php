<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Reff_kel_desa extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();
        $this->load->helper('dtssp'); //datatable server side processing helper
        
        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index() {
        header("Access-Control-Allow-Origin: *");
        /*         * keterangan parameter
          $this->template->load('default_layout', 'contents' , 'index', $data);
          1.default_layout = nama template utama untuk merender menu header dan footer wajib diisi
          2.contents = nama variabel  yang akan di passing kedalam template wajib diisi dan dibuat filenya nama file view akan diambil variabel content
          3.index = nama view yang akan di load
         * */
        $title = "Kelurahan";
        $js_file = $this->load->view('reff_kel_desa/js_file', '', true);
        $modal_filter = $this->load->view('reff_kel_desa/modal_filter', '', true);
        $modal_tambah = $this->load->view('reff_kel_desa/modal_tambah', '', true);
        $modal_edit = $this->load->view('reff_kel_desa/modal_edit', '', true);
        $modal_view = $this->load->view('reff_kel_desa/modal_view', '', true);
        $data = array("modal_filter" => $modal_filter,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "modal_view" => $modal_view,
            "title" => $title
        );
        $this->template->set('title', $title);
        $this->template->set('jv_script', $js_file);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }

    public function ssp() {
        $thang = $this->input->post('thang', TRUE); 
        if (strlen($thang) > 0){
            $whe = "thang = $thang";
        } else {
            $whe = "thang is null";
        }
        $whe="tahun =2021";
        $table = 'view_kelurahan';
        $primaryKey = 'kd_camat';
        $columns = array(
            array('db' => 'nama_prov', 'dt' => 0),
            array('db' => 'nama_kabkot', 'dt' => 1),
            array('db' => 'nama_camat', 'dt' => 2),
            array('db' => 'nama_lurah', 'dt' => 3),
            array('db' => 'kd_lurah', 'dt' => 4),
            array('db' => 'tahun', 'dt' => 5),
            array('db' => 'kd_prov', 'dt' => 6),
            array('db' => 'kd_kabkot', 'dt' => 7),
            array('db' => 'kd_camat', 'dt' => 8),
            array('db' => 'id', 'dt' => 9)
            
        );

        datatable_ssp($table, $primaryKey, $columns, $whe);
    }

    public function addform() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);
        $id=$param['kd_lurah'];
        $th=$param['tahun'];
        $data = [
            "tahun" => $param['tahun'],
            // "kd_prov" => (string)$param['kd_prov'],
            // "kd_kab_kota_bpiw" => (string)$param['kd_kab_kota_bpiw'],
            "kd_lurah" => $param['kd_lurah'],
            "kd_camat" => $param['kd_kec'],
            // "kd_kab_irmsv3" => (string)$param['kd_kab_irmsv3'],
            // "kd_kab_rams" => (string)$param['kd_kab_rams'],
            // "kd_kab_bps" => (string)$param['kd_kab_bps'],
            // "kd_kab_rkakl" => (string)$param['kd_kab_rkakl'],
            "nama_lurah" => (string)$param['nama_lurah']
        ];
      
        $cek = $this->db->query("select * from aset_r_kelurahan where kd_lurah='$id' and tahun=$th");
        if ($cek->num_rows() > 0) {
            echo "<h4 style='color:orange;'><i class='fa fa-times'></i> ID Kecamatan Tersebut Sudah Ada, Harap Ganti..<h4>";
        } else {
            $res = $this->db->insert('aset_r_kelurahan', $data);
            if (!$res) {
                echo "Gagal";
            } else {
                echo "<h4 style='color:green;'><i class='fa fa-check'></i> Berhasil Menambah Data<h4>";
            }
        }
    }

    public function editform() {
        $param = $this->input->post('formData', TRUE);
        $this->wgisitia->handle_removed($param);

        
        $datas = [
            "tahun" => date('Y'),
            // "kd_prov" => (string)$param['kd_prov'],
            // "kd_kab_kota_bpiw" => (string)$param['kd_kab_kota_bpiw'],
            "kd_lurah" => $param['kd_lurah'],
            "kd_camat" => $param['kd_kec'],
            // "kd_kab_irmsv3" => (string)$param['kd_kab_irmsv3'],
            // "kd_kab_rams" => (string)$param['kd_kab_rams'],
            // "kd_kab_bps" => (string)$param['kd_kab_bps'],
            // "kd_kab_rkakl" => (string)$param['kd_kab_rkakl'],
            "nama_lurah" => (string)$param['nama_lurah']
        ];
        $data = [
            "tahun" => date('Y'),
            "id" => (string) $param['id']
        ];        
        
        //print_r($data);
        $this->db->where($data);
        $res = $this->db->update('aset_r_kelurahan', $datas);

        if (!$res) {
            echo "Gagal";
        } else {
            echo "<h4 style='color:green;'><i class='fa fa-check'></i> Berhasil Merubah Data<h4>";
        }
    }

    public function deleteform() {
        $id = $this->input->post('id', TRUE);
        $thang = $this->input->post('thang');
        $data = [
            "tahun" => $thang,
            "kd_lurah" => $id
        ];
        
        $this->db->where($data);
        $res = $this->db->delete('aset_r_kelurahan');

        if (!$res) {
            // if query returns null
            $msg = $this->db->_error_message();
            echo '{"status":"error", "msg":"' . $msg . '"}';
        } else {
            echo '{"status":"sukses"}';
        }
    }

//    public function listing() {
//        $url = "http://localhost:12892/referensi/kabkot/listall";
//        echo $this->get_data_module($url);
//    }
//
//    //kode satker RKAKL
//    public function kabkota_by_kd_satker($id) {
//        $url = "http://localhost:12892/prakonreg/reg_satker/satkerkabkot/" . $id;
//        echo $this->get_data_module($url);
//    }
//
//    public function datalist() {
//        //die();
//        $link = "http://localhost:12892/referensi/kabkot/jumlahquerylist";
//        $jumlahraw = $this->get_data_module($link);
//        $jumlahdata = json_decode($jumlahraw);
//        $jumlah = $jumlahdata->jumlah;
//
//        $columns = [
//            0 => 'kd_prov',
//            1 => 'id_kabkot',
//            2 => 'kd_kab_kota_bpiw',
//            3 => 'kab_kota',
//            4 => 'kd_kab_irmsv3',
//            5 => 'kd_kab_rams',
//            6 => 'kd_kab_bps',
//            7 => 'kd_kab_rkakl',
//        ];
//
//        $limit = $this->input->post('length');
//        $start = $this->input->post('start');
//        $totalData = $jumlah;
//        $totalFiltered = $totalData;
//
//        if (isset($_POST['order'])) {
//            $order = $columns[$this->input->post('order')[0]['column']];
//            $dir = $this->input->post('order')[0]['dir'];
//        } else {
//            $order = 'id_kabkot';
//            $dir = 'desc';
//        }
//
//        if (!empty($this->input->post('search')['value'])) {
//            if (empty($limit)) {
//                $limit = $jumlah;
//            }
//
//            if (empty($start)) {
//                $start = '0';
//            }
//            $search = $this->input->post('search')['value'];
//            $kolom = 'kab_kota';
//
//            $linksearchdatakabkota = "http://localhost:12892/referensi/kabkot/searchquerylist";
//            $datasearchkirimkabkota = [
//                "limit" => $limit,
//                "start" => $start,
//                "order" => $order,
//                "dir" => $dir,
//                "search" => $search,
//                "kolom" => $kolom
//            ];
//            $datasearchraw = $this->insert_moduleduo($linksearchdatakabkota, $datasearchkirimkabkota);
//            //print_r($datasearchraw);
//            $datasearchkabkotaraw = json_decode($datasearchraw);
//            $kabkota = $datasearchkabkotaraw->data;
//            $totalFiltered = $datasearchkabkotaraw->jumlahlimit;
//        } else {
//
//            if (empty($limit)) {
//                $limit = $jumlah;
//            }
//
//            if (empty($start)) {
//                $start = '0';
//            }
//
//            $linkdatakabkota = "http://localhost:12892/referensi/kabkot/querylist";
//            $datakirimkabkota = [
//                "limit" => $limit,
//                "start" => $start,
//                "order" => $order,
//                "dir" => $dir
//            ];
//
//            $datakabkotaraw = $this->insert_moduleduo($linkdatakabkota, $datakirimkabkota);
//            //print_r($datakabkotaraw);
//            $datakabkota = json_decode($datakabkotaraw);
//            //print_r($datakabkota);
//            $kabkota = $datakabkota->data;
//        }
//
//        /**
//          { "data": "kd_prov" },
//          { "data": "id_kabkot" },
//          { "data": "kd_kab_kota_bpiw" },
//          { "data": "kab_kota" },
//          { "data": "kd_kab_irmsv3" },
//          { "data": "kd_kab_rams" },
//          { "data": "kd_kab_bps" },
//          { "data": "kd_kab_rkakl" },
//         * */
//        //print_r($kabkota); die();
//        foreach ($kabkota as $p) {
//
//            $row[] = [
//                "kd_prov" => $p->kd_prov,
//                "id_kabkot" => $p->id_kabkot,
//                "kd_kab_kota_bpiw" => $p->kd_kab_kota_bpiw,
//                "kab_kota" => $p->kab_kota,
//                "kd_kab_irmsv3" => $p->kd_kab_irmsv3,
//                "kd_kab_rams" => $p->kd_kab_rams,
//                "kd_kab_bps" => $p->kd_kab_bps,
//                "kd_kab_rkakl" => $p->kd_kab_rkakl
//            ];
//        }
//
//        $output = array(
//            "draw" => intval($this->input->post('draw')),
//            "recordsTotal" => intval($totalData),
//            "recordsFiltered" => intval($totalFiltered),
//            "data" => $row
//        );
//        $this->output->set_content_type('application/json')->set_output(json_encode($output, JSON_PRETTY_PRINT));
//    }
//
//    public function simpan() {
//
//        /**
//          { "data": "kd_prov" },
//          { "data": "id_kabkot" },
//          { "data": "kd_kab_kota_bpiw" },
//          { "data": "kab_kota" },
//          { "data": "kd_kab_irmsv3" },
//          { "data": "kd_kab_rams" },
//          { "data": "kd_kab_bps" },
//          { "data": "kd_kab_rkakl" },
//         * */
//        $url = "http://localhost:12892/referensi/kabkot/add";
//
//        $kd_prov = $this->input->post("kd_prov");
//        $id_kabkot = $this->input->post("id_kabkot");
//        $kd_kab_kota_bpiw = $this->input->post("kd_kab_kota_bpiw");
//        $kab_kota = $this->input->post("kab_kota");
//        $kd_kab_irmsv3 = $this->input->post("kd_kab_irmsv3");
//        $kd_kab_rams = $this->input->post("kd_kab_rams");
//        $kd_kab_bps = $this->input->post("kd_kab_bps");
//        $kd_kab_rkakl = $this->input->post("kd_kab_rkakl");
//
//        $data_raw = array(
//            "kd_prov" => $kd_prov,
//            "id_kabkot" => $id_kabkot,
//            "kd_kab_kota_bpiw" => $kd_kab_kota_bpiw,
//            "kab_kota" => $kab_kota,
//            "kd_kab_irmsv3" => $kd_kab_irmsv3,
//            "kd_kab_rams" => $kd_kab_rams,
//            "kd_kab_bps" => $kd_kab_bps,
//            "kd_kab_rkakl" => $kd_kab_rkakl
//        );
//        //echo "<pre>";
//        //print_r($data_raw);
//        //echo "</pre>";
//        echo $this->insert_module($url, $data_raw);
//    }
//
//    public function update() {
//
//        /**
//          { "data": "kd_prov" },
//          { "data": "id_kabkot" },
//          { "data": "kd_kab_kota_bpiw" },
//          { "data": "kab_kota" },
//          { "data": "kd_kab_irmsv3" },
//          { "data": "kd_kab_rams" },
//          { "data": "kd_kab_bps" },
//          { "data": "kd_kab_rkakl" },
//         * */
//        $url = "http://localhost:12892/referensi/kabkot/update";
//
//        $kd_prov = $this->input->post("kd_prov");
//        $id_kabkot = $this->input->post("id_kabkot");
//        $kd_kab_kota_bpiw = $this->input->post("kd_kab_kota_bpiw");
//        $kab_kota = $this->input->post("kab_kota");
//        $kd_kab_irmsv3 = $this->input->post("kd_kab_irmsv3");
//        $kd_kab_rams = $this->input->post("kd_kab_rams");
//        $kd_kab_bps = $this->input->post("kd_kab_bps");
//        $kd_kab_rkakl = $this->input->post("kd_kab_rkakl");
//        //echo $kd_prov;
//
//        $data_raw = array(
//            "kd_prov" => $kd_prov,
//            "id_kabkot" => $id_kabkot,
//            "kd_kab_kota_bpiw" => $kd_kab_kota_bpiw,
//            "kab_kota" => $kab_kota,
//            "kd_kab_irmsv3" => $kd_kab_irmsv3,
//            "kd_kab_rams" => $kd_kab_rams,
//            "kd_kab_bps" => $kd_kab_bps,
//            "kd_kab_rkakl" => $kd_kab_rkakl
//        );
//        //echo "<pre>";
//        //print_r($data_raw);
//        //echo "</pre>";
//        echo $this->update_module($url, $data_raw);
//    }
    
    public function get_prov($prov, $thang) {
       $str_sql = "select *
                   from aset_r_provinsi
                   where kd_prov= '$prov' and tahun = $thang";
        // $where = "kd_prov = '11' and tahun = 2021";
        // $str_sql = $this->db->where($where)->get("aset_r_provinsi");
        $query = $this->db->query($str_sql);
        // print_r($query);
        // die();
        $data = json_decode(json_encode($query->result()), true);
        if (count($data) > 0) {
            echo json_encode($data[0]);
        }
    }
    
}
