<script>
// $(function() {
//  // Handler for .ready() called.
//  $("#syslogin").hide(); 
// });
let rightCode = '';
let valiIpt = document.querySelector('#valiIpt');
$(document).ready(function() {
    
    $("#valicode").text(getImgValiCode());
    $("#toggle").click(function() {
        $("#valicode").text(getImgValiCode());
    });
    console.log(rightCode)
});

function getImgValiCode() {
    let showNum = [];
    let canvasWinth = 150;
    let canvasHeight = 50;
    let canvas = document.getElementById('valicode');
    let context = canvas.getContext('2d');
    canvas.width = canvasWinth;
    canvas.height = canvasHeight;
    let sDeg = "";
    let sCode =
        'A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,0,1,2,3,4,5,6,7,8,9,!,@,#,$,%,^,&,*,(,)';
    let saCode = sCode.split(',');
    let saCodeLen = saCode.length;
    for (let i = 0; i <= 5; i++) {
        let sIndex = Math.floor(Math.random() * saCodeLen);
        let sDeg = (Math.random() * 30 * Math.PI) / 180;
        console.log(sIndex)
        let cTxt = saCode[sIndex];
        showNum[i] = cTxt;
        let x = 10 + i * 20;
        let y = 20 + Math.random() * 8;
        context.font = 'bold 23px 微软雅黑';
        context.translate(x, y);
        context.rotate(sDeg);

        context.fillStyle = randomColor();
        context.fillText(cTxt, 0, 0);

        context.rotate(-sDeg);
        context.translate(-x, -y);
    }
    // for (let i = 0; i <= 5; i++) {
    //     context.strokeStyle = randomColor();
    //     context.beginPath();
    //     context.moveTo(
    //         Math.random() * canvasWinth,
    //         Math.random() * canvasHeight
    //     );
    //     context.lineTo(
    //         Math.random() * canvasWinth,
    //         Math.random() * canvasHeight
    //     );
    //     context.stroke();
    // }
    for (let i = 0; i < 30; i++) {
        context.strokeStyle = randomColor();
        context.beginPath();
        let x = Math.random() * canvasWinth;
        let y = Math.random() * canvasHeight;
        context.moveTo(x, y);
        context.lineTo(x + 1, y + 1);
        context.stroke();
    }
    rightCode = showNum.join('');
    console.log('rightCode :'+rightCode)
}

function randomColor() {
    let r = Math.floor(Math.random() * 256);
    let g = Math.floor(Math.random() * 256);
    let b = Math.floor(Math.random() * 256);
    return 'rgb(' + r + ',' + g + ',' + b + ')';
}

   
function close_alert() {
    $("#alert_login").css({
        display: "none"
    });
}

function doesConnectionExist() {
    var xhr = new XMLHttpRequest();
    var file = "https://www.kirupa.com/blank.png";
    var randomNum = Math.round(Math.random() * 10000);

    xhr.open('HEAD', file + "?rand=" + randomNum, true);
    xhr.send();

    xhr.addEventListener("readystatechange", processRequest, false);

    function processRequest(e) {
        if (xhr.readyState == 4) {
            if (xhr.status >= 200 && xhr.status < 304) {
                alert("connection exists!");
            } else {
                alert("connection doesn't exist!");
            }
        }
    }
}

function refreshCaptcha() {
    event.preventDefault()
    $.get("<?php echo base_url(); ?>login/refreshCaptcha/", function(data) {
        data = JSON.parse(data)
        $('#idCaptcha').html(data.image);
        // console.log(data.image)
    });
}

function login(modesys) {
    // alert('inside login ->'+modesys)
    event.preventDefault()

    // alert(check_connection());   
    //  if(check_connection()=='conn_active'){
    var email = $("#email").val();
    var password = $("#password").val();
    var OptCaptcha = $('#captcha').val();
    // var captcha = "<?=$this->session->userdata('captcha')?>"

    
    if (OptCaptcha != rightCode) {
        Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: 'Maaf Captcha Salah!!',
        })
        return false
    }
    $("#pesan").empty();
    var data_post = {
        "email": email,
        "password": password,
        // "captcha": captcha,
        "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"
    };

    if (modesys == 'iam') {
        // console.log(data_post);
        jQuery.ajax({
            contentType: 'application/x-www-form-urlencoded',
            dataType: "json",
            type: "POST",
            data: data_post,
            url: "<?php echo base_url() . "login/create_login"; ?>",
            success: function(response) {
                if (response.status == "sukses") {
                    var role = [];
                    $.each(response.data, function(index, value) {
                        role.push(value.id_user_group);
                    });
                    // Swal.fire(
                    //             'Sukses!',
                    //             'Login Berhasil!',
                    //             'success'
                    //         )
                    // swal({
                    //     title: 'Login Berhasil!!',
                    //     allowEscapeKey: false,
                    //     allowOutsideClick: false,
                    //     timer: 2000,
                    //     onOpen: () => {
                    //     swal.showLoading();
                    //     }
                    // })

                    Swal.fire({
                        icon: 'success',
                        title: 'Login Berhasil!',
                        html: 'Silahkan Tunggu.',
                        timerProgressBar: true,
                        didOpen: () => {
                            Swal.showLoading()
                        }

                    })
                    if (role[0] == 3) {
                        window.location.href = "<?php echo base_url() . "dashboard4"; ?>";
                    } else {
                        window.location.href = "<?php echo base_url() . "dashboard4"; ?>";
                    }

                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: response.msg,
                    })
                    $("#pesan").html(
                        "<i class='fa fa-warning'></i> password atau username salah, login gagal <a class='alert-link' href='javascript:void(0)''></a>!"
                    )
                        
                    $("#alert_login").css({
                        display: "block"
                    });
                    setTimeout(close_alert, 3000);
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.status);
                alert(thrownError);
            }
        });
    } else if (modesys == 'sso') {
        doSysLogin('thesso');
        // jQuery.ajax({
        //     contentType: 'application/x-www-form-urlencoded',
        //     dataType: "json",
        //     type: "POST",
        //     data: data_post,
        //     url: "<?php //echo base_url() . "login/sso_login"; ?>",
        //     success: function(response) {
        //         //console.log(response);
        //         if (!response.error) {
        //             $("#thesso").hide();
        //             $('#kantor-field').show();
        //             $.each(response.kantor, function(i, item) {
        //                 $("#kantor").append('<option value="' + item.kantorid + '">' + item
        //                     .kantorname +
        //                     '</option>');
        //             });

        //             doSysLogin('thesso');
        //         } else {
        //             Swal.fire({
        //                 icon: 'error',
        //                 title: 'Oops...',
        //                 text: response.error_description,
        //             })
        //             $("#pesan").html(
        //                 "<i class='fa fa-warning'></i> password atau username salah, login gagal <a class='alert-link' href='javascript:void(0)''></a>!"
        //             )

        //             $("#alert_login").css({
        //                 display: "block"
        //             });
        //             setTimeout(close_alert, 3000);
        //         }
        //     },
        //     error: function(xhr, ajaxOptions, thrownError) {
        //         // alert(xhr.status);
        //         // alert(thrownError);
        //     }
        // });

    } else if (modesys == 'thesso') {
        // jQuery.ajax({
        //     contentType: 'application/x-www-form-urlencoded',
        //     dataType: "json",
        //     type: "POST",
        //     data: {
        //         "kantorid": $('#kantor').val(),
        //         "<?php //echo $this->security->get_csrf_token_name(); ?>": "<?php //echo $this->security->get_csrf_hash(); ?>"
        //     },
        //     url: "<?php //echo base_url() . "login/kantor_login"; ?>",
        //     success: function(response) {
        //         if (response.status == "sukses") {
        //             var role = [5];

        //             Swal.fire({
        //                 icon: 'success',
        //                 title: 'Login Berhasil!',
        //                 html: 'Silahkan Tunggu.',
        //                 timerProgressBar: true,
        //                 didOpen: () => {
        //                     Swal.showLoading()
        //                 }

        //             })
        //             // if (role[0] == 3) {
        //             window.location.href = "<?php //echo base_url() . "dashboard4"; ?>";
        //             // } else {
        //             //     window.location.href = "<?php //echo base_url() . "dashboard4"; ?>";
        //             // }

        //         } else {
        //             Swal.fire({
        //                 icon: 'error',
        //                 title: 'Oops...',
        //                 text: response.msg,
        //             })
        //             $("#pesan").html(
        //                 "<i class='fa fa-warning'></i> password atau username salah, login gagal <a class='alert-link' href='javascript:void(0)''></a>!"
        //             )

        //             $("#alert_login").css({
        //                 display: "block"
        //             });
        //             setTimeout(close_alert, 3000);
        //         }
        //     },
        //     error: function(xhr, ajaxOptions, thrownError) {
        //         alert(xhr.status);
        //         alert(thrownError);
        //     }
        // });

    }
    //  }else{
    //      alert("Koneksi Internet anda bermasalah pastikan koneksi anda stabil dan silhkan ulangi kembali login anda")
    //  }
}

function check_connection() {
    var status = "";
    var url = "<?php echo base_url('/login/check_connection') ?>";
    $.ajax({
        //type: "GET",
        url: url,
        async: false,
        success: function(msg) {
            status = "conn_active";
        },
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            if (textStatus == 'timeout') {
                status = "conn_error";
            }
        }
    });

    return status;
    //alert(status);
}


$(".login100-form-btn").click(function() {
    // e.preventDefault();
    var modesys;
    if ($(".login100-form-btn").hasClass("sso")) {
        // alert('has sso');
        modesys = 'sso';
    } else if ($(".login100-form-btn").hasClass("iam")) {
        // alert('has iam');
        modesys = 'iam';
    } else {
        modesys = 'thesso';
    }
    login(modesys);
    return false;
});

var yuhu = $('#password') || $('#email');
yuhu.keypress(function(e) {
    if (e.which == 13) {
        login();
        return false;
    }
});




const doSysLogin = (mode) => {
    // alert(mode);
    $("#syslogin").fadeIn("slow", function() {
        // Animation complete.
    });
    $("#option-login").hide();
    if (mode == 'sso') {
        // $('.username').text('Username');
        // $('.password').text('Nomor Induk Pegawai');
        // $('.nosso').hide();
        $('#syslogin').hide();

   
        // $('.back-login').text('Login SSO');
        // $('.login100-form-btn').removeClass('iam');
        // $('.login100-form-btn').addClass('sso');
        // window.location.href="https://logindev.atrbpn.go.id/auth/realms/internal-realm/protocol/openid-connect/auth?client_id=dotnet-web&redirect_uri=http://103.6.53.254:4980/atrpgt&response_type=code&scope=openid";
        window.location.href="https://logininternal.atrbpn.go.id/auth/realms/internal/protocol/openid-connect/auth?client_id=dotnet-web&redirect_uri=https://signata.atrbpn.go.id&response_type=code&scope=openid";
    } else if (mode == 'iam') {
        $('.username').text('Username');
        $('.password').text('Password');
        $('.back-login').text('Login IIAM');
        $('.login100-form-btn').removeClass('sso');
        $('.login100-form-btn').addClass('iam');
    } else {
        $('.login100-form-btn').removeClass('sso');
        $('.login100-form-btn').removeClass('iam');
        $('.login100-form-btn').addClass('thesso');
    }
}
</script>