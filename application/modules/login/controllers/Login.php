<?php

defined('BASEPATH') OR exit('No direct script access allowed');

require_once(APPPATH . "vendor/autoload.php");

use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;


class Login extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url','captcha');
        $this->load->library('session');
        $this->load->library(array('form_validation', 'Recaptcha'));
        // if (!empty($this->session->has_userdata('users'))) {
        //     //echo 1111; die();
        //     echo "<script>
        //         history.go(-1);
        //         </script>";
        // }
    }



    function check_connection() {
        echo json_encode(array("con_status" => "active"));
    }

    public function index() {
        header("Access-Control-Allow-Origin: *");
        $data=[];

        $captcha = $this->createCaptcha();
        $data = ['captcha' => ($captcha !== false) ? $captcha['image'] : ''];
        // $this->session->set_userdata is now handled in createCaptcha()

        // $data = array('captcha' => $this->recaptcha->getWidget(),
        //                'script_captcha' => $this->recaptcha->getScriptTag()
        //              );

        $this->template->set('title', 'LOGIN');
        $js_file = $this->load->view('login/js_file', '', true); //asign view into variabel
        $this->template->set('jv_script', $js_file); //javascript file
        $this->template->load('guard_layout', 'contents', 'index', $data);
    }

    public function createCaptcha()
    {
        $vals = [
            // 'word' -> nantinya akan digunakan sebagai random teks yang akan keluar di captchanya
            'word'          => substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 8),
            'img_path'      => './uploads/captcha/',
            'img_url'       => base_url('./uploads/captcha/'),
            'img_width'     => 150,
            'img_height'    => 30,
            'expiration'    => 7200,
            'word_length'   => 8,
            'font_size'     => 16,
            'img_id'        => 'Imageid',
            'pool'          => '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'colors'        => [
                    'background'=> [255, 255, 255],
                    'border'    => [255, 255, 255],
                    'text'      => [0, 0, 0],
                    'grid'      => [255, 40, 40]
            ]
        ];
        
        $captcha = create_captcha($vals);
        
        if ($captcha !== false) {
            $this->session->set_userdata('captcha', $captcha['word']);
            return $captcha;
        }
        return false;
    }

    public function refreshCaptcha()
    {
        $vals = [
            // 'word' -> nantinya akan digunakan sebagai random teks yang akan keluar di captchanya
            'word'          => substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 8),
            'img_path'      => './uploads/captcha/',
            'img_url'       => base_url('./uploads/captcha/'),
            'img_width'     => 150,
            'img_height'    => 30,
            'expiration'    => 7200,
            'word_length'   => 8,
            'font_size'     => 16,
            'img_id'        => 'Imageid',
            'pool'          => '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'colors'        => [
                    'background'=> [255, 255, 255],
                    'border'    => [255, 255, 255],
                    'text'      => [0, 0, 0],
                    'grid'      => [255, 40, 40]
            ]
        ];
        
        $captcha = create_captcha($vals);
        $this->session->set_userdata('captcha', $captcha['word']);
        echo json_encode(['image' => $captcha['image']]);
    }

    public function create_login_js() {
        $this->load->database();

        $email = $this->input->post("email");
        $password = $this->input->post("password");
        //echo $email."xxx";
        $url = WGI_NODE_API_URL."loginsippa/asdas/login";
        // $url = WGI_PY_API_URL."usr/login";

        $data_raw = array(
            "email" => $email,
            "password" => $password,
        );

        $datamentah = $this->post_data($url, $data_raw);
        // echo $datamentah;
        // die();

        $data = json_decode($datamentah);
        $child = "";
        if (isset($data->sukses)) {
            $users = json_decode(json_encode($data->data), true);

//              echo "<pre>";
//            print_r($users);
//              echo "</pre>";
            // die();
            // $this->session->set_userdata('temp_users',$users);
            //$this->session->set_userdata('temp_users',$users);

            if ($users[0]["id_sub_user_group"] != 0) {

//                echo '1';

                $parent = $users[0]["id_sub_user_group"];
                $child = $users[0]["id_user_group"];

                $query3 = $this->db->get_where('v_user_satker', array('id_user_group' => $users[0]["id_user_group"]));
            } else {

//                 echo '2';
                $parent = $users[0]["id_user_group"];

                $query3 = $this->db->get_where('v_user_satker', array('kode_satker' => $users[0]["kode_satker"]));
            }

            // echo $this->db->last_query();

            $nmsatker = $query3->row_array();



            $new_users = array(
                "id_user" => $users[0]["id_user"],
                "id_user_group" => $parent, //role
                "id_user_group_real" => $users[0]["id_user_group"], //id_user_group
                "id_sub_user_group" => $child,
                "id_sub_sub_user_group" => $nmsatker["id_sub_sub_user_group"],
                "kode_satker" => $users[0]["kode_satker"],
                "kd_prov" => $users[0]["kd_prov"],
                "kdlokasi" => $query3->row()->kdlokasi,
                "kd_prov_irms" => $query3->row()->kd_prov_irmsv3,
                "kd_dapil" => $users[0]["kd_dapil"],
                "kd_fraksi" => $users[0]["kd_fraksi"],
                "username" => $users[0]["username"],
                "email" => $users[0]["email"],
                "firstname" => $users[0]["firstname"],
                "roledesc" => $users[0]["roledesc"],
                "role" => $users[0]["role"],
                "lastname" => $users[0]["lastname"],
                "nmsatker" => $nmsatker['nmsatker'],
                "kddekon" => $query3->row()->kddekon,
                "" => $query3->row()->kdkabkota,
                "kdkdkabkotainduk" => $query3->row()->kdinduk,
                "nama_user_group" => $query3->row()->nama
            );
            //print_r($new_users);
            //print_r($query3);
            //die();

            //print_r($query3);
            $this->session->set_userdata('users', $new_users);

            $this->session->set_userdata('token', $data->token);


            $konfig = json_decode(json_encode($data->config), true);
            $this->session->set_userdata('konfig_tahun_ang', $konfig[0]['tahun_ang']);
            $this->session->set_userdata('konfig_tahapan', $konfig[0]['tahap']);
            $this->session->set_userdata('konfig_status_sipro', $konfig[0]['status_sipro']);
            $this->session->set_userdata('konfig_status_krisna', $konfig[0]['status_krisna']);
            $this->session->set_userdata('konfig_revisi_dipa', $konfig[0]['rev_dipa']);


            switch ($konfig[0]['tahap']) {
                case 'PAGU_ANGGARAN': $kdtahapan = 'PA';
                    break;
                case 'PAGU_ALOKASI_ANGGARAN': $kdtahapan = 'PAA';
                    break;
                case 'PAGU_INDIKATIF': $kdtahapan = 'PI';
                    break;
                case 'DIPA': $kdtahapan = 'DA';
                    break;
                case 'PRA_KONREG': $kdtahapan = 'KRG';
                    break;
                case 'KONREG': $kdtahapan = 'KRG';
                    break;
                case 'REVISI': $kdtahapan = 'RD';
                    break;    
            }

            if($users[0]["id_user_group"] === 99){
                $kdtahapan = 'X';
            } else {
                $kdtahapan = $kdtahapan;
            }
            $this->session->set_userdata('konfig_kd_tahapan', $kdtahapan);


            echo json_encode(array("status" => "sukses","kdtahapan"=>$kdtahapan,
                "data" => $users
                )
            );
        } elseif (isset($data->error)) {
            echo json_encode(array("status" => "gagal login"));
        }
    }


    private function md5pwd($password) {
        $pwd = md5($password);
        $hashpwd =  $pwd; 

        return $hashpwd;
    }

    private function get_config() {
        $sql = "select nilai from aset_config";
        $qres= $this->db->query($sql)->result_array();

        return  $qres;
    }

    private function get_user($username) {
       $sql = "select 
       cast(a.id_user as integer) as id_user, 
       cast(a.id_user_group as integer) as id_user_group, 
       cast(a.kd_bujt as integer) as kd_bujt, 
       a.kd_prov,
       a.kd_kabkot,
       cast(a.id_sub_user_group as integer) as id_sub_user_group, 
       a.username, 
       a.email, 
       a.firstname, 
       a.lastname, 
       a.password,
       b.nama as roledesc, 
       b.alias as role,
       a.nama,
       a.userlogin
        from aset_users a
        left join aset_user_group b on (a.id_user_group = b.id_user_group and a.id_sub_user_group = b.id_sub_user_group)
        where userlogin = ?";
        $qres= $this->db->query($sql, array($username))->result_array();

        return $qres;
    }


    private function get_kantor($username) {
        $sql = "SELECT kd_kab, namakantah 
                FROM v_rmapkantah
                WHERE kantahid = ?
               ";
         $qres= $this->db->query($sql, array($username))->result_array();
 
         return $qres;
     }


    private function cek_user($nip) {
        $sql = "SELECT kd_kab, namakantah 
                FROM v_rmapkantah
                WHERE kantahid = ?
            ";
        $qres= $this->db->query($sql, array($username))->result_array();

        return $qres;
    }

    private function get_token($u,$n,$i) {

        $now = time();
        $payload = array(
            "id" => $u,
            "nama" => $n,
            "id_user_group" => $i,
            "time" => time() + WGI_JWT_EXP_DELTA_SECONDS
        );

        $jwt = JWT::encode($payload, WGI_JWT_KEY, WGI_JWT_ALG);
       // $decoded = JWT::decode($jwt, WGI_JWT_KEY, array(WGI_JWT_ALG));

        return $jwt;

    }


    public function create_login() {

      $this->load->database();
      $username = $this->input->post("email", TRUE);
      $password = $this->input->post("password", TRUE);
      $captcha_post = $this->input->post("captcha", TRUE);
      $captcha    = $this->session->userdata('captcha');
    //   echo $captcha.'.'.$captcha_post;
    //   if ($captcha_post && ($captcha_post == $captcha)){
    //   }else{
    //     echo json_encode(['msg' => 'Captcha Salah','status'=>'gagal']);
    //     exit();
    //   }
    //  // $url = WGI_APP_BASE_URL."login/get_token";
    //     $recaptcha = $this->input->post("captcha");
    //     $response = $this->recaptcha->verifyResponse($recaptcha);
    //     //  if (!isset($response['success']) || $response['success'] <> true) {
    //     //    echo json_encode(array("status" => "captcha_error"));
    //     //    return;
    //     // } else {
        // $password_hash=password_hash($password, PASSWORD_DEFAULT);
        // echo $password_hash;
        // die();
        $ruser = $this->get_user($username);
        $jml_user = count($ruser);
        if($jml_user > 0 ){
            // echo $ruser[0]['password'];    

            if (password_verify($password,$ruser[0]['password'])) {

                // echo 'masuk 1';    

                $rconfig = $this->get_config(); 
                $arc=array();
                foreach ($rconfig as $row3) {
                    $dt3[] = $row3['nilai'];      
                    $arc = $dt3;      
                }
        
                $rc = array(
                    "tahun" => $arc[0]
                    // "semester" => $arc[1]
                );
                
                $rd=array();
                foreach ($ruser as $row)
                {
                    $dt[] = $row['id_user'];
                    $dt[] = $row['username'];
                    $dt[] = $row['id_user_group'];
        
                    $rd = $dt;
                }
        
                $rs = array();
                foreach ($ruser as $row2) {
                    $dt2[0]['id_user'] = (int) $row2['id_user'];
                    $dt2[0]['id_user_group'] = (int) $row2['id_user_group'];
                    $dt2[0]['kd_bujt'] = (int) $row2['kd_bujt'];
                    $dt2[0]['kd_prov'] = $row2['kd_prov'];
                    $dt2[0]['id_sub_user_group'] = (int) $row2['id_sub_user_group'];
                    $dt2[0]['username'] = $row2['username'];
                    $dt2[0]['email'] =  $row2['email'];
                    $dt2[0]['firstname'] = '';
                    $dt2[0]['lastname'] = '';
                    $dt2[0]['roledesc'] = $row2['roledesc'];
                    $dt2[0]['role'] = $row2['role'];
        
                    $rs = $dt2;
        
        
                }
        
                $token = $this->get_token($rd[0],$rd[1],$rd[2]);

     
                $json_data = array(
                         "sukses" => "sukses",
                         "data" => $rs,
                         "token" => $token,
                         "config" => $rc
                );
        
                $data = json_decode(json_encode($json_data));
               // $child = "";
                if (isset($data->sukses)) {
                    $users = json_decode(json_encode($data->data), true);
                   $konfig = json_decode(json_encode($data->config), true);
                    $query3 = $this->db->get_where('v_users', array('id_user' => $users[0]["id_user"]));
                  
                    $nmbujt = $query3->row_array();

                  //  print_r( $nmbujt); 
                    $new_users = array(
                        "id_user" => (int)$users[0]["id_user"],
                        "id_user_group" =>  (int) $users[0]["id_user_group"], //role
                        "id_user_group_real" => (int) $users[0]["id_user_group"], //id_user_group
                        "id_sub_user_group" =>  (int) $users[0]["id_sub_user_group"],
                       // "id_sub_sub_user_group" => $nmsatker["id_sub_sub_user_group"],
                        "kd_bujt" => (int) $users[0]["kd_bujt"],
                        "kd_prov" => $users[0]["kd_prov"],
                        "nama" => $nmbujt["nama"],
                      //  "kd_camat" => $users[0]["kd_camat"],
                        //"kd_lurah" => $users[0]["kd_lurah"],
                        "username" => $users[0]["username"],
                        "email" => $users[0]["email"],
                        "firstname" => $users[0]["firstname"],
                        "roledesc" => $users[0]["roledesc"],
                        "role" => $users[0]["role"],
                        "lastname" => $users[0]["lastname"],
                        // "nm_bujt" => $users['nama_bujt'],
                        "kd_kabkot" => substr($query3->row()->kd_kabkot,0,2).'.'.substr($query3->row()->kd_kabkot,2,2),
                        "nama_user_group" => $query3->row()->nama,
                        "userlogin" => $nmbujt['userlogin'],
                        "mode" => 'internal'
                    );

                    // print_r($new_users);
        
                    $this->session->set_userdata('users', $new_users);
                    $this->session->set_userdata('token', $data->token);
                    $this->session->set_userdata( 'disclaimer',['disclaimer' => 'on']);
        
        
        
                   $konfig = json_decode(json_encode($data->config), true);
                    $this->session->set_userdata('konfig_tahun_ang', $konfig['tahun']);
                    // $this->session->set_userdata('konfig_tahapan', $konfig['semester']);
        
                    echo json_encode(array(
                        "status" => "sukses",
                        "data" => $users
                        )
                    );
                } elseif (isset($data->error)) {
                    echo json_encode(array("status" => "gagal login"));
                }
            } else {
                echo json_encode(array("status" => "hash gagal","msg" => "Password Salah"));
            }

        }else{
            echo json_encode(array("status" => "Username tidak terdaftar","msg" => "Username tidak terdaftar"));
        }
        // print_r($ruser);
        // die();
    //}
    
    }

    public function logout() {
        //echo "logout"; die();
        // $this->session->unset_userdata('konfig_tahun_ang');
        // $this->session->unset_userdata('konfig_tahapan');
        // $this->session->unset_userdata('konfig_status_sipro');
        // $this->session->unset_userdata('konfig_status_krisna');
        $this->session->unset_userdata('users');
        $this->session->unset_userdata('token');
        $this->session->sess_destroy();
        redirect('login', 'refresh');
        
    }

    public function base64url_decode(string $base64Url): string
    {
        return base64_decode(strtr($base64Url, '-_', '+/'));
    }

    public function decodeJWT($jwt, $section = 0) {

        $parts = explode(".", $jwt);
        return json_decode($this->base64url_decode($parts[$section]));
    }

    public function is_valid(string $jwt): bool
    {
        $token = explode('.', $jwt); // explode token based on JWT breaks
        if (!isset($token[1]) && !isset($token[2])) {
            return false; // fails if the header and payload is not set
        }
        $headers = base64_decode($token[0]); // decode header, create variable
        $payload = base64_decode($token[1]); // decode payload, create variable
        $clientSignature = $token[2]; // create variable for signature

        if (!json_decode($payload)) {
            return false; // fails if payload does not decode
        }

        if ((json_decode($payload)->exp - time()) < 0) {
            return false; // fails if expiration is greater than 0, setup for 1 minute
        }

        if (isset(json_decode($payload)->iss)) {
            if (json_decode($headers)->iss != json_decode($payload)->iss) {
                return false; // fails if issuers are not the same
            }
        } else {
            return false; // fails if issuer is not set 
        }

        if (isset(json_decode($payload)->aud)) {
            if (json_decode($headers)->aud != json_decode($payload)->aud) {
                return false; // fails if audiences are not the same
            }
        } else {
            return false; // fails if audience is not set
        }

        $base64_header = $this->encode($headers);
        $base64_payload = $this->encode($payload);

        $signature = hash_hmac('SHA256', $base64_header . "." . $base64_payload, $this->secret, true);
        $base64_signature = $this->encode($signature);

        return ($base64_signature === $clientSignature);
    }


    // public function getIdTokenKey($keys, $header) {
    //     foreach ($keys as $key) {
    //         if ($key['kty'] == 'RSA') {
    //             if (!isset($header['kid'])) {
    //                 return $key;
    //             }
    //         }
    //     }   
        
    //     throw new Exception("key not found");
    // }

    public function sso_login() {

        //development
        // $urlTarget = "https://logindev.atrbpn.go.id/auth/realms/internal-realm/protocol/openid-connect/token";
        
        //production
        $urlTarget = "https://logininternal.atrbpn.go.id/auth/realms/internal/protocol/openid-connect/token";

        $authcode = $this->input->post('code', true);
        
        // echo $authcode;
        // die();
        
        //dev
        // $vars = "client_id=dotnet-web&client_secret=2b0106ef-a5e5-4c62-bab6-abab773cc7e6&grant_type=authorization_code&code=$authcode&redirect_uri=".SSO_APP_BASE_URL;
        
        // prod
        $vars = "client_id=dotnet-web&client_secret=5f291f75-ee35-41d8-84df-a41b64ee89cf&grant_type=authorization_code&code=$authcode&redirect_uri=".SSO_APP_BASE_URL;

        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $urlTarget);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $headers = [
            'Accept: */*',
            'Cache-Control: no-cache',
            'Content-Type: application/x-www-form-urlencoded; charset=utf-8',
            'User-Agent: Mozilla/5.0 (X11; Ubuntu; Linux i686; rv:28.0) Gecko/20100101 Firefox/28.0'
        ];

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $server_output = curl_exec($ch);

        curl_close ($ch);

        $arrayJSON = json_decode($server_output, true);

        $header = $this->decodeJWT($arrayJSON['access_token'], 0);
        $payload = $this->decodeJWT($arrayJSON['access_token'], 1);
        $signature = $this->decodeJWT($arrayJSON['access_token'], 2);
        $arrBody = json_decode(json_encode($payload), true);
        $profile = $arrBody["atrbpn-profile"];

        // print_r($profile); die();

        $this->kantor_login($profile);
    
    }

    function kantor_login($profile) {

        $this->load->database();
        // $username = $profile["kantorid"];

        $username = $profile["nip"]; 

        // $ruser = $this->get_kantor($username);
        $ruser = $this->get_user($username);
        $jml_user = count($ruser);
        if($jml_user > 0 ){
            // echo $ruser[0]['password'];    
                $rconfig = $this->get_config(); 
                $arc=array();
                foreach ($rconfig as $row3) {
                    $dt3[] = $row3['nilai'];      
                    $arc = $dt3;      
                }

                $rc = array(
                    "tahun" => $arc[0]
                );

                
                $rd=array();
                foreach ($ruser as $row)
                {
                    $dt[] = $row['id_user'];
                    $dt[] = $row['username'];
                    $dt[] = $row['id_user_group'];
        
                    $rd = $dt;
                }
        
                $rs = array();
                foreach ($ruser as $row2) {
                    $dt2[0]['id_user'] = (int) $row2['id_user'];
                    $dt2[0]['id_user_group'] = (int) $row2['id_user_group'];
                    $dt2[0]['kd_bujt'] = (int) $row2['kd_bujt'];
                    $dt2[0]['kd_prov'] = $row2['kd_prov'];
                    $dt2[0]['kd_kabkot'] = substr($row2['kd_kabkot'],0,2).'.'.substr($row2['kd_kabkot'],2,2);
                    $dt2[0]['id_sub_user_group'] = (int) $row2['id_sub_user_group'];
                    $dt2[0]['username'] = $row2['username'];
                    $dt2[0]['userlogin'] = $row2['userlogin'];
                    $dt2[0]['email'] =  $row2['email'];
                    $dt2[0]['firstname'] = '';
                    $dt2[0]['lastname'] = '';
                    $dt2[0]['roledesc'] = $row2['roledesc'];
                    $dt2[0]['role'] = $row2['role'];
                    $dt2[0]['nama'] = $row2['nama'];
        
                    $rs = $dt2;
        
        
                }
        
                $token = $this->get_token($rd[0],$rd[1],$rd[2]);

        
                $json_data = array(
                         "sukses" => "sukses",
                         "data" => $rs,
                         "token" => $token,
                         "config" => $rc
                );
        
                $data = json_decode(json_encode($json_data));
                if (isset($data->sukses)) {
                    $users = json_decode(json_encode($data->data), true);
                    $konfig = json_decode(json_encode($data->config), true);
                  
                    $new_users = array(
                        // "id_user" => 10,
                        // "id_user_group" =>  7, //role
                        // "id_user_group_real" => 7, //id_user_group
                        // "id_sub_user_group" =>  5,
                        // "kd_bujt" => $username,
                        // "kd_prov" => '',
                        // "username" => $ruser[0]['namakantah'],
                        // "email" => '',
                        // "firstname" => '',
                        // "roledesc" => 'Kantor BPN',
                        // "role" => 5,
                        // "lastname" => '',
                        // "kd_kabkot" => $ruser[0]['kd_kab'],
                        // "nama_user_group" => 'Kantor BPN',
                        // "userlogin" => 'Kantor',
                        // "namapegawai" => $profile["namapegawai"],
                        // "nip" => $profile["nip"],
                        // "mode" => 'sso'
                        "id_user" => (int)$users[0]["id_user"],
                        "id_user_group" =>  (int) $users[0]["id_user_group"], //role
                        "id_user_group_real" => (int) $users[0]["id_user_group"], //id_user_group
                        "id_sub_user_group" =>  (int) $users[0]["id_sub_user_group"],
                       // "id_sub_sub_user_group" => $nmsatker["id_sub_sub_user_group"],
                        "kd_bujt" => (int) $users[0]["kd_bujt"],
                        // "kd_kabkot" => (int) $users[0]["kd_kabkot"],
                        "kd_prov" => $users[0]["kd_prov"],
                        "nama" => $profile["namapegawai"],
                      //  "kd_camat" => $users[0]["kd_camat"],
                        //"kd_lurah" => $users[0]["kd_lurah"],
                        "username" => $users[0]["username"],
                        "email" => $users[0]["email"],
                        "firstname" => $users[0]["firstname"],
                        "roledesc" => $users[0]["roledesc"],
                        "role" => $users[0]["role"],
                        "lastname" => $users[0]["lastname"],
                        // "nm_bujt" => $users['nama_bujt'],
                        "kd_kabkot" =>  $users[0]["kd_kabkot"],
                        // "nama_user_group" => $query3->row()->nama,
                        "userlogin" => $users[0]['userlogin'],
                        "mode" => 'sso'
                    );
        
                    $this->session->set_userdata('users', $new_users);
                    $this->session->set_userdata('token', $data->token);
                    $this->session->set_userdata( 'disclaimer',['disclaimer' => 'on']);
        
        
        
                    $konfig = json_decode(json_encode($data->config), true);
                    $this->session->set_userdata('konfig_tahun_ang', date('Y'));

        
                    echo json_encode(array(
                        "status" => "sukses",
                        "data" => $users
                        )
                    );
                } elseif (isset($data->error)) {
                    echo json_encode(array("status" => "gagal login"));
                }


        }else{
            echo json_encode(array("status" => "Username tidak terdaftar","msg" => "Username tidak terdaftar"));
        }

    }
}