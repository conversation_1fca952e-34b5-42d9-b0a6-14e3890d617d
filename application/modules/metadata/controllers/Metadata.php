<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Metadata extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "Metadata";
        $js_file = $this->load->view('metadata/js_file', '', true);
        $kontak = $this->db->get('landing_page')->row_array();
        $modal_tambah = $this->load->view('metadata/modal_tambah', '', true);
        $modal_edit = $this->load->view('metadata/modal_edit', '', true);
        
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "jv_script" => $js_file,
            "data" => $kontak
        );
        
        $this->load->view('index', $data);
    }
    function is_base64_encoded($string) {
        return base64_encode(base64_decode($string, true)) === $string;
}
    public function save_form() {
        
        if (is_array($_FILES)) {
            $userfile = $_FILES['filess'];
            if(!empty($userfile['name'])) {
                $allowed_mime_types = array(
                        'application/xml',		
                        'text/xml',		
                        'application/atom+xml',	
                        'text/plain'	
                    );

                    $filesizeLimit = 5000;
                    $sourcePath = $userfile['tmp_name'];
                    $format = strtolower(pathinfo($userfile['name'], PATHINFO_EXTENSION));
                    $other_mime = mime_content_type($sourcePath);
        
                    if ($format == 'py' || $format == 'php' || $format == 'php4' || $format == 'php5') {
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 2

                        ]);
                        return; // Stop execution if it's a file
                    }

                    $fileContent = file_get_contents($sourcePath, false, null, 0, 500); // Read first 500 bytes
                    $pythonPatterns = ['import', 'from', 'def', 'print'];
                       
                    foreach ($pythonPatterns as $pattern) {
                        if (strpos($fileContent, $pattern) !== false) {
                            echo json_encode([
                                'status' => false,
                                'msg' => 'Sorry, you are not allowed to upload this file type!!',
                                'sts' => 2
                            ]);
                            exit(); // Stop execution if Python code is detected
                        }
                    }

                    if ($this->is_base64_encoded($fileContent)) {
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type!!',
                            'sts' => 3
                        ]);
                        exit(); // Stop execution if Python code is detected
                    } 
                       
                    $putFile = false;
                    if (in_array($other_mime, $allowed_mime_types)) {
                        if(($userfile['size'] > $filesizeLimit) || ($userfile['size'] < $filesizeLimit)) {
                            $putFile = true;
                        } else {
                            $putFile = false;
                        }
                    }

                    if($putFile !== false) {
            
                        $id_tematik = $this->input->post('id_tematik', TRUE);
                        $nama_tematik = @$this->input->post('nama_tematik', TRUE);
                        $nama_tematik_snake = strtolower(str_replace(' ','_',@$this->input->post('nama_tematik', TRUE)));
                        $kdppum = $this->input->post('kdppum', TRUE);
                        $wadmpr = $this->input->post('wadmpr', TRUE);
                        $nama_dir = FCPATH . 'uploads/metadata/'.$nama_tematik_snake;
                        $nama_file = $kdppum.date('Ymdhis').'_'.$_FILES['filess']['name'];
                        
                        if (is_dir($nama_dir)) {

                        } else {
                            $oldmask = umask(0);
                            mkdir($nama_dir, 0777, true);
                            umask($oldmask);
                        }
                            $sourcePath = $_FILES['filess']['tmp_name'];
                            $up = move_uploaded_file($sourcePath, $nama_dir.'/'.$nama_file);
                            $data = [ 
                                "id_tematik" => $id_tematik,
                                "url" => $nama_file, 
                                "path" => 'uploads/metadata/'.$nama_tematik_snake.'/', 
                                "kdppum" => $kdppum, 
                                "nama_tematik" => $nama_tematik, 
                                "wadmpr" => $wadmpr, 
                            ];
                            
                            $this->db->insert('metadata',$data);
                            echo json_encode(array("status" => TRUE));

                    }else{
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 4
                        ]);
                    }
            }
        }

    }


    function update_form() {
        

        
        $id_tematik = $this->input->post('xid_tematik', TRUE);
        $id = $this->input->post('xid', TRUE);

        if (is_array($_FILES)) {
            $userfile = $_FILES['xfiless'];
            if(!empty($userfile['name'])) {
                $allowed_mime_types = array(
                        'application/xml',		
                        'text/xml',		
                        'application/atom+xml',	
                        'text/plain'	
                    );

                    $filesizeLimit = 5000;
                    $sourcePath = $userfile['tmp_name'];
                    $format = strtolower(pathinfo($userfile['name'], PATHINFO_EXTENSION));
                    $other_mime = mime_content_type($sourcePath);
        
                    if ($format == 'py' || $format == 'php' || $format == 'php4' || $format == 'php5') {
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 2

                        ]);
                        return; // Stop execution if it's a file
                    }

                    $fileContent = file_get_contents($sourcePath, false, null, 0, 500); // Read first 500 bytes
                    $pythonPatterns = ['import', 'from', 'def', 'print'];
                       
                    foreach ($pythonPatterns as $pattern) {
                        if (strpos($fileContent, $pattern) !== false) {
                            echo json_encode([
                                'status' => false,
                                'msg' => 'Sorry, you are not allowed to upload this file type!!',
                                'sts' => 2
                            ]);
                            exit(); // Stop execution if Python code is detected
                        }
                    }

                    if ($this->is_base64_encoded($fileContent)) {
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type!!',
                            'sts' => 3
                        ]);
                        exit(); // Stop execution if Python code is detected
                    } 
                       
                    $putFile = false;
                    if (in_array($other_mime, $allowed_mime_types)) {
                        if(($userfile['size'] > $filesizeLimit) || ($userfile['size'] < $filesizeLimit)) {
                            $putFile = true;
                        } else {
                            $putFile = false;
                        }
                    }

                    if($putFile !== false) {
            
                        $id_tematik = $this->input->post('xid_tematik', TRUE);
                        $nama_tematik = @$this->input->post('xnama_tematik', TRUE);
                        $nama_tematik_snake = strtolower(str_replace(' ','_',@$this->input->post('xnama_tematik', TRUE)));
                        $kdppum = $this->input->post('xkdppum', TRUE);
                        $wadmpr = $this->input->post('xwadmpr', TRUE);
                        $nama_dir = FCPATH . 'uploads/metadata/'.$nama_tematik_snake;
                        $nama_file = $kdppum.date('Ymdhis').'_'.$_FILES['xfiless']['name'];
                        
                        if (is_dir($nama_dir)) {

                        } else {
                            $oldmask = umask(0);
                            mkdir($nama_dir, 0777, true);
                            umask($oldmask);
                        }
                            $sourcePath = $_FILES['xfiless']['tmp_name'];
                            $up = move_uploaded_file($sourcePath, $nama_dir.'/'.$nama_file);
                            $data = [ 
                                "id_tematik" => $id_tematik,
                                "url" => $nama_file, 
                                "path" => 'uploads/metadata/'.$nama_tematik_snake.'/', 
                                "kdppum" => $kdppum, 
                                "nama_tematik" => $nama_tematik, 
                                "wadmpr" => $wadmpr, 
                            ];
                            $this->db->where('id', $id);
                            $this->db->update('metadata',$data);
                            echo json_encode(array("status" => TRUE));

                    }else{
                        echo json_encode([
                            'status' => false,
                            'msg' => 'Sorry, you are not allowed to upload this file type !!',
                            'sts' => 4
                        ]);
                    }
            }else{
                $id_tematik = $this->input->post('xid_tematik', TRUE);
                $nama_tematik = @$this->input->post('xnama_tematik', TRUE);
                $nama_tematik_snake = strtolower(str_replace(' ','_',@$this->input->post('xnama_tematik', TRUE)));
                $kdppum = $this->input->post('xkdppum', TRUE);
                $wadmpr = $this->input->post('xwadmpr', TRUE);
                $data = [ 
                            "id_tematik" => $id_tematik,
                            "kdppum" => $kdppum, 
                            "nama_tematik" => $nama_tematik, 
                            "wadmpr" => $wadmpr, 
                        ];
                        
                $this->db->where('id', $id);
                $this->db->update('metadata',$data);
                echo json_encode(array("status" => TRUE));
                        
            }
        }
            
    }

    
    public function ajax_delete($id) {

        $this->M_model->delete_by_id('metadata','id', $id);
        echo json_encode(array("status" => TRUE));
    }

    public function ssp_paket() {
      
        $table = 'metadata';
        $primaryKey = 'id'; //test        
         
        $columns = array(
            array('db' => 'id', 'dt' => 0),
            array('db' => 'url', 'dt' => 1),
            array('db' => 'path', 'dt' => 2),
            array('db' => 'id_tematik', 'dt' => 3),
            array('db' => 'nama_tematik', 'dt' => 4),
            array('db' => 'kdppum', 'dt' => 5),
            array('db' => 'wadmpr', 'dt' => 6),
        );
       

        datatable_ssp($table, $primaryKey, $columns);

        
    }

    function get_tema() {
        $this->db->select('id_layer,nama_layer,kd_module');
        $this->db->group_by('id_layer,nama_layer,kd_module');
        $this->db->order_by('nama_layer');
        $data = $this->db->get('v_tematik')->result();


        $group = $this->session->userdata('users')['id_user_group'];
        $this->db->select('kode_module');
        $this->db->where('id_user_group', $group);
        $group_module = $this->db->get('aset_group_modules')->result();
        $groups=[];
        $menu=[];
        foreach ($group_module as $key => $value) {
            array_push($groups,$value->kode_module);          
        }
        foreach ($data as $k => $v) {
            if (in_array($v->kd_module,$groups)) {
                
                array_push($menu,$v);          
            }
            
        }
        echo json_encode($menu);
    }


}