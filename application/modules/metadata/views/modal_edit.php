<div class="modal fade" id="modal-edit" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Edit Metadata</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" id="frm-edit">
                    <fieldset>
                        <input type="hidden" id="xwadmpr" name="xwadmpr">
                        <input type="hidden" id="xnama_tematik" name="xnama_tematik">
                        <!-- Form Name -->
                        <!-- <legend>Financial Close</legend> -->
                        <input id="xid" name="xid" type="hidden">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="xid_tematik">Layer Tematik</label>
                            <div class="col-md-12">
                                <select id="xid_tematik" onchange="xtematikChange($(this).val())" name="xid_tematik"
                                    required class="bootstrap-select form-control" data-live-search="true">
                                    <!-- <option value="#">Pilih</option> -->
                                </select>
                                <!-- <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none"> -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="xkdppum">Provinsi</label>
                            <div class="col-md-12">
                                <select id="xkdppum" name="xkdppum" onchange="xkdppumChange($(this).val())" required
                                    class="bootstrap-select form-control" data-live-search="true">
                                    <!-- <option value="#">Pilih</option> -->
                                </select>
                                <!-- <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none"> -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="xurl">URL</label>
                            <div class="col-md-12">
                                <input id="xfiless" name="xfiless" type="file" required placeholder=""
                                    class="form-control input-md">
                            </div>
                        </div>

                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default waves-effect " data-bs-dismiss="modal">Close</button>
                <button type="button" onclick="updateForm()"
                    class="btn btn-primary waves-effect waves-light ">Simpan</button>
            </div>
        </div>
    </div>
</div>