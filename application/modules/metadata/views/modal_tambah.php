<div class="modal fade" id="modal-tambah" role="dialog" tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Tambah Metadata</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"></span>
                </button>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" id="frm-tambah">
                    <fieldset>
                        <input type="hidden" id="wadmpr" name="wadmpr">
                        <input type="hidden" id="nama_tematik" name="nama_tematik">
                        <!-- Text input-->
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="id_tematik">Layer Tematik</label>
                            <div class="col-md-12">
                                <select id="id_tematik" onchange="tematikChange($(this).val())" name="id_tematik"
                                    required class="bootstrap-select form-control" data-live-search="true">
                                    <!-- <option value="#">Pilih</option> -->
                                </select>
                                <!-- <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none"> -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="kdppum">Provinsi</label>
                            <div class="col-md-12">
                                <select id="kdppum" name="kdppum" onchange="kdppumChange($(this).val())" required
                                    class="bootstrap-select form-control" data-live-search="true">
                                    <!-- <option value="#">Pilih</option> -->
                                </select>
                                <!-- <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none"> -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="url">URL</label>
                            <div class="col-md-12">
                                <input id="filess" name="filess" type="file" required placeholder=""
                                    class="form-control input-md">
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default waves-effect " data-bs-dismiss="modal">Close</button>
                <button type="button" onclick="simpanForm()"
                    class="btn btn-primary waves-effect waves-light ">Simpan</button>
            </div>
        </div>
    </div>
</div>