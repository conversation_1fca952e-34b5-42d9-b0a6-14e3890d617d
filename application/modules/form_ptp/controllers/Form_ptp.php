<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Form_ptp extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
		$this->load->library('pdf');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $satker = $this->session->users['kd_bujt'];
        $title = "Form PTP";

        $js_file = $this->load->view('form_ptp/js_file', '', true);
        $modal_tambah = $this->load->view('form_ptp/modal_tambah', '', true);
        $modal_edit = $this->load->view('form_ptp/modal_edit', '', true);
        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "title" => $title,
            "jv_script" => $js_file
        );
        
        $this->load->view('index', $data);
    }
    public function getKab($kdProv,$tahun,$layer=null)
    {
        $layer='spatial.v_d_ptp_non_berusaha'; 
        $this->db->where('kdppum', $kdProv);
        $this->db->where('tahun_data', $tahun);
        $data = $this->db->get($layer.'_drill')->result();
            
        $str = '';
        $no = 0;
        foreach ($data as $key => $value) {
            if($no < 4){
                $str .= '<td>- '.$value->wadmkk.'<td>';
            }else{
                $str .= '<td>- '.$value->wadmkk.'<td></tr><tr><td style="width:100px"></td>';
                $no=-1;
            }
            $no++;
        }
        $str = substr($str, 0, -2);
        $html = '<table cellpadding="5" cellspacing="0" border="0" style="width:100%;padding-left:50px;table-layout: fixed;">
        <tr><td style="width:100px">List Kabupaten:</td>'.$str.'</tr></table>';
            
        echo json_encode(['html'=>$html]);
    }

    public function save_form() {

 

        $data_detail = [ 
            'alamat_kantah' => $this->input->post('alamat_kantah',true),
            'kdkabkot_kantah' => $this->input->post('kdkabkot_kantah',true),
            'kdprov_kantah' => $this->input->post('kdprov_kantah',true),
            'keg' => $this->input->post('keg',true),
            'nomor' => $this->input->post('nomor',true),
            'tgl' => $this->input->post('tgl',true),
            'dasar_tgl' => $this->input->post('dasar_tgl',true),
            'dasar_nama' => $this->input->post('dasar_nama',true),
            'dasar_nik' => $this->input->post('dasar_nik',true),
            'dasar_nib' => $this->input->post('dasar_nib',true),
            'dasar_alamat' => $this->input->post('dasar_alamat',true),
            'dasar_an' => $this->input->post('dasar_an',true),
            'mohon_jalan' => $this->input->post('mohon_jalan',true),
            'mohon_nomor' => $this->input->post('mohon_nomor',true),
            'mohon_rtrw' => $this->input->post('mohon_rtrw',true),
            'mohon_kdlurah' => $this->input->post('mohon_kdlurah',true),
            'mohon_kdcamat' => $this->input->post('mohon_kdcamat',true),
            'mohon_kdkabkot' => $this->input->post('mohon_kdkabkot',true),
            'mohon_luas_m2' => $this->input->post('mohon_luas_m2',true),
            'mohon_g' => $this->input->post('mohon_g',true),
            'mohon_gq' => $this->input->post('mohon_gq',true),
            'mohon_rcnkeg' => $this->input->post('mohon_rcnkeg',true),
            'mohon_kdkbli' => $this->input->post('mohon_kdkbli',true),
            'mohon_fkws' => $this->input->post('mohon_fkws',true),
            'mohon_kws1' => $this->input->post('mohon_kws1',true),
            'mohon_kws2' => $this->input->post('mohon_kws2',true),
            'mohon_kws3' => $this->input->post('mohon_kws3',true),
            'mohon_kws4' => $this->input->post('mohon_kws4',true),
            'mohon_kws5' => $this->input->post('mohon_kws5',true),
            'luas_kws1m2' => $this->input->post('luas_kws1m2',true) == '' ? '0' : $this->input->post('luas_kws1m2',true),
            'luas_kws1persen' => $this->input->post('luas_kws1persen',true) == '' ? '0' : $this->input->post('luas_kws1persen',true),
            'luas_kws2m2' => $this->input->post('luas_kws2m2',true) == '' ? '0' : $this->input->post('luas_kws2m2',true),
            'luas_kws2persen' => $this->input->post('luas_kws2persen',true) == '' ? '0' : $this->input->post('luas_kws2persen',true),
            'luas_kws3m2' => $this->input->post('luas_kws3m2',true) == '' ? '0' : $this->input->post('luas_kws3m2',true),
            'luas_kws3persen' => $this->input->post('luas_kws3persen',true) == '' ? '0' : $this->input->post('luas_kws3persen',true),
            'luas_kws4m2' => $this->input->post('luas_kws4m2',true) == '' ? '0' : $this->input->post('luas_kws4m2',true),
            'luas_kws4persen' => $this->input->post('luas_kws4persen',true) == '' ? '0' : $this->input->post('luas_kws4persen',true),
            'luas_kws5m2' => $this->input->post('luas_kws5m2',true) == '' ? '0' : $this->input->post('luas_kws5m2',true),
            'luas_kws5persen' => $this->input->post('luas_kws5persen',true) == '' ? '0' : $this->input->post('luas_kws5persen',true),
            'keterangan' => $this->input->post('keterangan',true),
            'terbit_keg' => $this->input->post('terbit_keg',true),
            'terbit_sesuai_luasm2' => $this->input->post('terbit_sesuai_luasm2',true) == '' ? '0' : $this->input->post('terbit_sesuai_luasm2',true),
            'terbit_sesuai_luaspersen' => $this->input->post('terbit_sesuai_luaspersen',true) == '' ? '0' : $this->input->post('terbit_sesuai_luaspersen',true),
            'terbit_total_tdksesuai_luasm2' => $this->input->post('terbit_total_tdksesuai_luasm2',true) == '' ? '0' : $this->input->post('terbit_total_tdksesuai_luasm2',true),
            'terbit_total_tdksesuai_luaspersen' => $this->input->post('terbit_total_tdksesuai_luaspersen',true) == '' ? '0' : $this->input->post('terbit_total_tdksesuai_luaspersen',true),
            'terbit_tdksesuai_ilok_luasm2' => $this->input->post('terbit_tdksesuai_ilok_luasm2',true) == '' ? '0' : $this->input->post('terbit_tdksesuai_ilok_luasm2',true),
            'terbit_tdksesuai_ilok_luaspersen' => $this->input->post('terbit_tdksesuai_ilok_luaspersen',true) == '' ? '0' : $this->input->post('terbit_tdksesuai_ilok_luaspersen',true),
            'terbit_tdksesuai_skpl_luasm2' => $this->input->post('terbit_tdksesuai_skpl_luasm2',true) == '' ? '0' : $this->input->post('terbit_tdksesuai_skpl_luasm2',true),
            'terbit_tdksesuai_skpl_luaspersen' => $this->input->post('terbit_tdksesuai_skpl_luaspersen',true) == '' ? '0' : $this->input->post('terbit_tdksesuai_skpl_luaspersen',true),
            'terbit_tdksesuai3_luasm2' => $this->input->post('terbit_tdksesuai3_luasm2',true) == '' ? '0' : $this->input->post('terbit_tdksesuai3_luasm2',true),
            'terbit_tdksesuai3_luaspersen' => $this->input->post('terbit_tdksesuai3_luaspersen',true) == '' ? '0' : $this->input->post('terbit_tdksesuai3_luaspersen',true),
            'terbit_tdksesuai3_ket' => $this->input->post('terbit_tdksesuai3_ket',true),
            'terbit_tdksesuai4_luasm2' => $this->input->post('terbit_tdksesuai4_luasm2',true) == '' ? '0' : $this->input->post('terbit_tdksesuai4_luasm2',true),
            'terbit_tdksesuai4_luaspersen' => $this->input->post('terbit_tdksesuai4_luaspersen',true) == '' ? '0' : $this->input->post('terbit_tdksesuai4_luaspersen',true),
            'terbit_tdksesuai4_ket' => $this->input->post('terbit_tdksesuai4_ket',true),
            'terbit_tdksesuai5_luasm2' => $this->input->post('terbit_tdksesuai5_luasm2',true) == '' ? '0' : $this->input->post('terbit_tdksesuai5_luasm2',true),
            'terbit_tdksesuai5_luaspersen' => $this->input->post('terbit_tdksesuai5_luaspersen',true) == '' ? '0' : $this->input->post('terbit_tdksesuai5_luaspersen',true),
            'terbit_tdksesuai5_ket' => $this->input->post('terbit_tdksesuai5_ket',true),
            'terbit_total_sesuaisyarat_luasm2' => $this->input->post('terbit_total_sesuaisyarat_luasm2',true) == '' ? '0' : $this->input->post('terbit_total_sesuaisyarat_luasm2',true),
            'terbit_total_sesuaisyarat_luaspersen' => $this->input->post('terbit_total_sesuaisyarat_luaspersen',true) == '' ? '0' : $this->input->post('terbit_total_sesuaisyarat_luaspersen',true),
            'terbit_sesuaisyarat1_luasm2' => $this->input->post('terbit_sesuaisyarat1_luasm2',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat1_luasm2',true),
            'terbit_sesuaisyarat1_luaspersen' => $this->input->post('terbit_sesuaisyarat1_luaspersen',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat1_luaspersen',true),
            'terbit_sesuaisyarat1_ket' => $this->input->post('terbit_sesuaisyarat1_ket',true),
            'terbit_sesuaisyarat2_luasm2' => $this->input->post('terbit_sesuaisyarat2_luasm2',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat2_luasm2',true),
            'terbit_sesuaisyarat2_luaspersen' => $this->input->post('terbit_sesuaisyarat2_luaspersen',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat2_luaspersen',true),
            'terbit_sesuaisyarat2_ket' => $this->input->post('terbit_sesuaisyarat2_ket',true),
            'terbit_sesuaisyarat3_luasm2' => $this->input->post('terbit_sesuaisyarat3_luasm2',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat3_luasm2',true),
            'terbit_sesuaisyarat3_luaspersen' => $this->input->post('terbit_sesuaisyarat3_luaspersen',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat3_luaspersen',true),
            'terbit_sesuaisyarat3_ket' => $this->input->post('terbit_sesuaisyarat3_ket',true),
            'terbit_sesuaisyarat4_luasm2' => $this->input->post('terbit_sesuaisyarat4_luasm2',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat4_luasm2',true),
            'terbit_sesuaisyarat4_luaspersen' => $this->input->post('terbit_sesuaisyarat4_luaspersen',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat4_luaspersen',true),
            'terbit_sesuaisyarat4_ket' => $this->input->post('terbit_sesuaisyarat4_ket',true),
            'terbit_sesuaisyarat5_luasm2' => $this->input->post('terbit_sesuaisyarat5_luasm2',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat5_luasm2',true),
            'terbit_sesuaisyarat5_luaspersen' => $this->input->post('terbit_sesuaisyarat5_luaspersen',true) == '' ? '0' : $this->input->post('terbit_sesuaisyarat5_luaspersen',true),
            'terbit_sesuaisyarat5_ket' => $this->input->post('terbit_sesuaisyarat5_ket',true),
            'terbit_ketentuan_gq1' => $this->input->post('terbit_ketentuan_gq1',true),
            'terbit_ketentuan_gq2' => $this->input->post('terbit_ketentuan_gq2',true),
            'terbit_ketentuan_gq3' => $this->input->post('terbit_ketentuan_gq3',true),
            'terbit_ketentuan_gq4' => $this->input->post('terbit_ketentuan_gq4',true),
            'terbit_ketentuan_gq5' => $this->input->post('terbit_ketentuan_gq5',true),
            'terbit_ketentuan_gp1' => $this->input->post('terbit_ketentuan_gp1',true),
            'terbit_ketentuan_gp2' => $this->input->post('terbit_ketentuan_gp2',true),
            'terbit_ketentuan_gp3' => $this->input->post('terbit_ketentuan_gp3',true),
            'terbit_ketentuan_gp4' => $this->input->post('terbit_ketentuan_gp4',true),
            'terbit_ketentuan_gp5' => $this->input->post('terbit_ketentuan_gp5',true),
            'terbit_ketentuan_hat1' => $this->input->post('terbit_ketentuan_hat1',true),
            'terbit_ketentuan_hat2' => $this->input->post('terbit_ketentuan_hat2',true),
            'terbit_ketentuan_hat3' => $this->input->post('terbit_ketentuan_hat3',true),
            'terbit_ketentuan_hat4' => $this->input->post('terbit_ketentuan_hat4',true),
            'terbit_ketentuan_hat5' => $this->input->post('terbit_ketentuan_hat5',true),
            'lokasi_surat' => $this->input->post('lokasi_surat',true),
            'tgl_surat' => $this->input->post('tgl_surat',true),
            'nm_kakantah' => $this->input->post('nm_kakantah',true),
            'nip_kakantah' => $this->input->post('nip_kakantah',true),
            'id_tema' => $this->input->post('id_tema',true)
        ];

        $afk_kw = $this->input->post('afk_kw',true);
        $afk_luas = $this->input->post('afk_luas',true);
        $afk_persen = $this->input->post('afk_persen',true);

        $ts_ket = $this->input->post('ts_ket',true);
        $ts_luas = $this->input->post('ts_luas',true);
        $ts_persen = $this->input->post('ts_persen',true);

        $sb_ket = $this->input->post('sb_ket',true);
        $sb_luas = $this->input->post('sb_luas',true);
        $sb_persen = $this->input->post('sb_persen',true);
        
        $milik = $this->input->post('milik',true);
        $manfaat = $this->input->post('manfaat',true);
        $peralihan = $this->input->post('peralihan',true);
        
        $ins = $this->db->insert('t_juknisptp', $data_detail);
        if ($ins) {
            $last_id = $this->db->insert_id();
            foreach ($afk_kw as $key => $value) {
                if ($value != '') {
                    $afk=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $afk_luas[$key],
                        'persentase' => $afk_persen[$key],
                    ];
                    $this->db->insert('t_juknisptp_afk', $afk);
                    
                }
            }


            foreach ($ts_ket as $key => $value) {
                if ($value != '') {
                    $ts=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $ts_luas[$key],
                        'persentase' => $ts_persen[$key],
                    ];
                    $this->db->insert('t_juknisptp_ts', $ts);
                }
            }

            foreach ($sb_ket as $key => $value) {
                if ($value != '') {
                    $sb=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $sb_luas[$key],
                        'persentase' => $sb_persen[$key],
                    ];
                    $this->db->insert('t_juknisptp_sb', $sb);
                }
            }
            foreach ($milik as $key => $value) {
                if ($value != '') {
                    $milik=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisptp_milik', $milik);
                }
            }
            
            
            foreach ($manfaat as $key => $value) {
                if ($value != '') {
                    $manfaat=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisptp_manfaat', $manfaat);
                }
            }
            
            foreach ($peralihan as $key => $value) {
                if ($value != '') {
                    $peralihan=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisptp_peralihan', $peralihan);
                }

            }
        }
        

        echo json_encode(array("status" => TRUE));
    }

    function update_form() {
       
        
 
    
        
        $data_detail = [ 
            'alamat_kantah' => $this->input->post('xalamat_kantah',true),
            'kdkabkot_kantah' => $this->input->post('xkdkabkot_kantah',true),
            'kdprov_kantah' => $this->input->post('xkdprov_kantah',true),
            'keg' => $this->input->post('xkeg',true),
            'nomor' => $this->input->post('xnomor',true),
            'tgl' => $this->input->post('xtgl',true),
            'dasar_tgl' => $this->input->post('xdasar_tgl',true),
            'dasar_nama' => $this->input->post('xdasar_nama',true),
            'dasar_nik' => $this->input->post('xdasar_nik',true),
            'dasar_nib' => $this->input->post('xdasar_nib',true),
            'dasar_alamat' => $this->input->post('xdasar_alamat',true),
            'dasar_an' => $this->input->post('xdasar_an',true),
            'mohon_jalan' => $this->input->post('xmohon_jalan',true),
            'mohon_nomor' => $this->input->post('xmohon_nomor',true),
            'mohon_rtrw' => $this->input->post('xmohon_rtrw',true),
            'mohon_kdlurah' => $this->input->post('xmohon_kdlurah',true),
            'mohon_kdcamat' => $this->input->post('xmohon_kdcamat',true),
            'mohon_kdkabkot' => $this->input->post('xmohon_kdkabkot',true),
            'mohon_luas_m2' => $this->input->post('xmohon_luas_m2',true),
            'mohon_g' => $this->input->post('xmohon_g',true),
            'mohon_gq' => $this->input->post('xmohon_gq',true),
            'mohon_rcnkeg' => $this->input->post('xmohon_rcnkeg',true),
            'mohon_kdkbli' => $this->input->post('xmohon_kdkbli',true),
            'mohon_fkws' => $this->input->post('xmohon_fkws',true),
            'mohon_kws1' => $this->input->post('xmohon_kws1',true),
            'mohon_kws2' => $this->input->post('xmohon_kws2',true),
            'mohon_kws3' => $this->input->post('xmohon_kws3',true),
            'mohon_kws4' => $this->input->post('xmohon_kws4',true),
            'mohon_kws5' => $this->input->post('xmohon_kws5',true),
            'luas_kws1m2' => $this->input->post('xluas_kws1m2',true) == '' ? '0' : $this->input->post('xluas_kws1m2',true),
            'luas_kws1persen' => $this->input->post('xluas_kws1persen',true) == '' ? '0' : $this->input->post('xluas_kws1persen',true),
            'luas_kws2m2' => $this->input->post('xluas_kws2m2',true) == '' ? '0' : $this->input->post('xluas_kws2m2',true),
            'luas_kws2persen' => $this->input->post('xluas_kws2persen',true) == '' ? '0' : $this->input->post('xluas_kws2persen',true),
            'luas_kws3m2' => $this->input->post('xluas_kws3m2',true) == '' ? '0' : $this->input->post('xluas_kws3m2',true),
            'luas_kws3persen' => $this->input->post('xluas_kws3persen',true) == '' ? '0' : $this->input->post('xluas_kws3persen',true),
            'luas_kws4m2' => $this->input->post('xluas_kws4m2',true) == '' ? '0' : $this->input->post('xluas_kws4m2',true),
            'luas_kws4persen' => $this->input->post('xluas_kws4persen',true) == '' ? '0' : $this->input->post('xluas_kws4persen',true),
            'luas_kws5m2' => $this->input->post('xluas_kws5m2',true) == '' ? '0' : $this->input->post('xluas_kws5m2',true),
            'luas_kws5persen' => $this->input->post('xluas_kws5persen',true) == '' ? '0' : $this->input->post('xluas_kws5persen',true),
            'keterangan' => $this->input->post('xketerangan',true),
            'terbit_keg' => $this->input->post('xterbit_keg',true),
            'terbit_sesuai_luasm2' => $this->input->post('xterbit_sesuai_luasm2',true) == '' ? '0' : $this->input->post('xterbit_sesuai_luasm2',true),
            'terbit_sesuai_luaspersen' => $this->input->post('xterbit_sesuai_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_sesuai_luaspersen',true),
            'terbit_total_tdksesuai_luasm2' => $this->input->post('xterbit_total_tdksesuai_luasm2',true) == '' ? '0' : $this->input->post('xterbit_total_tdksesuai_luasm2',true),
            'terbit_total_tdksesuai_luaspersen' => $this->input->post('xterbit_total_tdksesuai_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_total_tdksesuai_luaspersen',true),
            'terbit_tdksesuai_ilok_luasm2' => $this->input->post('xterbit_tdksesuai_ilok_luasm2',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai_ilok_luasm2',true),
            'terbit_tdksesuai_ilok_luaspersen' => $this->input->post('xterbit_tdksesuai_ilok_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai_ilok_luaspersen',true),
            'terbit_tdksesuai_skpl_luasm2' => $this->input->post('xterbit_tdksesuai_skpl_luasm2',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai_skpl_luasm2',true),
            'terbit_tdksesuai_skpl_luaspersen' => $this->input->post('xterbit_tdksesuai_skpl_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai_skpl_luaspersen',true),
            'terbit_tdksesuai3_luasm2' => $this->input->post('xterbit_tdksesuai3_luasm2',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai3_luasm2',true),
            'terbit_tdksesuai3_luaspersen' => $this->input->post('xterbit_tdksesuai3_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai3_luaspersen',true),
            'terbit_tdksesuai3_ket' => $this->input->post('xterbit_tdksesuai3_ket',true),
            'terbit_tdksesuai4_luasm2' => $this->input->post('xterbit_tdksesuai4_luasm2',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai4_luasm2',true),
            'terbit_tdksesuai4_luaspersen' => $this->input->post('xterbit_tdksesuai4_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai4_luaspersen',true),
            'terbit_tdksesuai4_ket' => $this->input->post('xterbit_tdksesuai4_ket',true),
            'terbit_tdksesuai5_luasm2' => $this->input->post('xterbit_tdksesuai5_luasm2',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai5_luasm2',true),
            'terbit_tdksesuai5_luaspersen' => $this->input->post('xterbit_tdksesuai5_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_tdksesuai5_luaspersen',true),
            'terbit_tdksesuai5_ket' => $this->input->post('xterbit_tdksesuai5_ket',true),
            'terbit_total_sesuaisyarat_luasm2' => $this->input->post('xterbit_total_sesuaisyarat_luasm2',true) == '' ? '0' : $this->input->post('xterbit_total_sesuaisyarat_luasm2',true),
            'terbit_total_sesuaisyarat_luaspersen' => $this->input->post('xterbit_total_sesuaisyarat_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_total_sesuaisyarat_luaspersen',true),
            'terbit_sesuaisyarat1_luasm2' => $this->input->post('xterbit_sesuaisyarat1_luasm2',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat1_luasm2',true),
            'terbit_sesuaisyarat1_luaspersen' => $this->input->post('xterbit_sesuaisyarat1_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat1_luaspersen',true),
            'terbit_sesuaisyarat1_ket' => $this->input->post('xterbit_sesuaisyarat1_ket',true),
            'terbit_sesuaisyarat2_luasm2' => $this->input->post('xterbit_sesuaisyarat2_luasm2',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat2_luasm2',true),
            'terbit_sesuaisyarat2_luaspersen' => $this->input->post('xterbit_sesuaisyarat2_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat2_luaspersen',true),
            'terbit_sesuaisyarat2_ket' => $this->input->post('xterbit_sesuaisyarat2_ket',true),
            'terbit_sesuaisyarat3_luasm2' => $this->input->post('xterbit_sesuaisyarat3_luasm2',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat3_luasm2',true),
            'terbit_sesuaisyarat3_luaspersen' => $this->input->post('xterbit_sesuaisyarat3_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat3_luaspersen',true),
            'terbit_sesuaisyarat3_ket' => $this->input->post('xterbit_sesuaisyarat3_ket',true),
            'terbit_sesuaisyarat4_luasm2' => $this->input->post('xterbit_sesuaisyarat4_luasm2',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat4_luasm2',true),
            'terbit_sesuaisyarat4_luaspersen' => $this->input->post('xterbit_sesuaisyarat4_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat4_luaspersen',true),
            'terbit_sesuaisyarat4_ket' => $this->input->post('xterbit_sesuaisyarat4_ket',true),
            'terbit_sesuaisyarat5_luasm2' => $this->input->post('xterbit_sesuaisyarat5_luasm2',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat5_luasm2',true),
            'terbit_sesuaisyarat5_luaspersen' => $this->input->post('xterbit_sesuaisyarat5_luaspersen',true) == '' ? '0' : $this->input->post('xterbit_sesuaisyarat5_luaspersen',true),
            'terbit_sesuaisyarat5_ket' => $this->input->post('xterbit_sesuaisyarat5_ket',true),
            'terbit_ketentuan_gq1' => $this->input->post('xterbit_ketentuan_gq1',true),
            'terbit_ketentuan_gq2' => $this->input->post('xterbit_ketentuan_gq2',true),
            'terbit_ketentuan_gq3' => $this->input->post('xterbit_ketentuan_gq3',true),
            'terbit_ketentuan_gq4' => $this->input->post('xterbit_ketentuan_gq4',true),
            'terbit_ketentuan_gq5' => $this->input->post('xterbit_ketentuan_gq5',true),
            'terbit_ketentuan_gp1' => $this->input->post('xterbit_ketentuan_gp1',true),
            'terbit_ketentuan_gp2' => $this->input->post('xterbit_ketentuan_gp2',true),
            'terbit_ketentuan_gp3' => $this->input->post('xterbit_ketentuan_gp3',true),
            'terbit_ketentuan_gp4' => $this->input->post('xterbit_ketentuan_gp4',true),
            'terbit_ketentuan_gp5' => $this->input->post('xterbit_ketentuan_gp5',true),
            'terbit_ketentuan_hat1' => $this->input->post('xterbit_ketentuan_hat1',true),
            'terbit_ketentuan_hat2' => $this->input->post('xterbit_ketentuan_hat2',true),
            'terbit_ketentuan_hat3' => $this->input->post('xterbit_ketentuan_hat3',true),
            'terbit_ketentuan_hat4' => $this->input->post('xterbit_ketentuan_hat4',true),
            'terbit_ketentuan_hat5' => $this->input->post('xterbit_ketentuan_hat5',true),
            'lokasi_surat' => $this->input->post('xlokasi_surat',true),
            'tgl_surat' => $this->input->post('xtgl_surat',true),
            'nm_kakantah' => $this->input->post('xnm_kakantah',true),
            'nip_kakantah' => $this->input->post('xnip_kakantah',true),
            'id_tema' => $this->input->post('xid_tema',true),
        ];

        

        // $this->db->set('updated_by', $this->session->users['id_user'], FALSE);

        $this->db->where('id_ptp', $this->input->post("xid",true));
        $up = $this->db->update('t_juknisptp', $data_detail);

        $afk_kw = $this->input->post('xafk_kw',true);
        $afk_luas = $this->input->post('xafk_luas',true);
        $afk_persen = $this->input->post('xafk_persen',true);

        $ts_ket = $this->input->post('xts_ket',true);
        $ts_luas = $this->input->post('xts_luas',true);
        $ts_persen = $this->input->post('xts_persen',true);

        $sb_ket = $this->input->post('xsb_ket',true);
        $sb_luas = $this->input->post('xsb_luas',true);
        $sb_persen = $this->input->post('xsb_persen',true);
        
        $milik = $this->input->post('xmilik',true);
        $manfaat = $this->input->post('xmanfaat',true);
        $peralihan = $this->input->post('xperalihan',true);
        
        if ($up) {
            $last_id = $this->input->post("xid",true);

            
            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisptp_afk');
            foreach ($afk_kw as $key => $value) {
                if ($value != '') {
                    $afk=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $afk_luas[$key],
                        'persentase' => $afk_persen[$key],
                    ];
                    $this->db->insert('t_juknisptp_afk', $afk);
                    
                }
            }

            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisptp_ts');
            foreach ($ts_ket as $key => $value) {
                if ($value != '') {
                    $ts=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $ts_luas[$key],
                        'persentase' => $ts_persen[$key],
                    ];
                    $this->db->insert('t_juknisptp_ts', $ts);
                }
            }

            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisptp_sb');
            foreach ($sb_ket as $key => $value) {
                if ($value != '') {
                    $sb=[
                        'id_juknisptp' => $last_id,
                        'kawasan' => $value,
                        'luasm2' => $sb_luas[$key],
                        'persentase' => $sb_persen[$key],
                    ];
                    $this->db->insert('t_juknisptp_sb', $sb);
                }
            }

            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisptp_milik');
            foreach ($milik as $key => $value) {
                if ($value != '') {
                    $milik=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisptp_milik', $milik);
                }
            }
            
            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisptp_manfaat');
            foreach ($manfaat as $key => $value) {
                if ($value != '') {
                    $manfaat=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisptp_manfaat', $manfaat);
                }
            }


            $this->db->where('id_juknisptp', $last_id);
            $this->db->delete('t_juknisptp_peralihan');
            foreach ($peralihan as $key => $value) {
                if ($value != '') {
                    $peralihan=[
                        'id_juknisptp' => $last_id,
                        'keterangan' => $value,
                    ];
                    $this->db->insert('t_juknisptp_peralihan', $peralihan);
                }

            }
        }


        echo json_encode(array("status" => TRUE));
    }
    public function getYear($layer=null,$tahun=null)
    {
        // $layer=str_replace('v_d','vm',$layer);
        $layer = 'spatial.v_r_ptp_non_berusaha';
        $this->db->select('tahun_data');
        $this->db->group_by('tahun_data');
        $datas = $this->db->get($layer)->result();
        
        $data = rsort($datas);
        $data=[];
        foreach ($datas as $key => $value) {
            array_push($data,$value->tahun_data);
        }

        $sel = in_array($tahun,$data)? '':'selected';
        // echo $sel;
        $str='<option '.$sel.' value="">Semua Tahun</option>';
        foreach ($data as $key => $value) {
            $text = $value == null ? 'Tidak Ada Tahun' : $value;
            $value = $value == null ? '0' : $value;
            $sel = $value == $tahun ? 'selected' : '';
            $str .= '<option '.$sel.' value="'.$value.'">'.$text.'</option>';
        }
        echo json_encode($str);
        
    }
    public function ssp_paket() {
      
        
    
        
        $table = 't_juknisptp';
        $primaryKey = 'id_ptp'; //test        
        $kd_prov=@$this->input->post("kd_prov",true);
        $kd_kabkot=@$this->input->post("kd_kabkot",true);

        $role=$this->session->users['id_user_group_real'];
        $kdpkab=$this->session->users['kd_kabkot'];
        $kdppum=explode('.',$kdpkab)[0];
        // $kd_prov=@$this->input->post("kd_prov",true);
        // $tahun_data=@$this->input->post("tahun_data",true);

        $where="";
        if (!empty($kd_prov)) {
            $where .=" kdprov_kantah = '".$kd_prov."'";
            if (!empty($kd_kabkot)) {
                $where .=" and kdkabkot_kantah = '".$kd_kabkot."'";
            }     
        }
        // } 
        $columns = array(
            array('db' => 'id_ptp' , 'dt' =>0),
            array('db' => 'alamat_kantah' , 'dt' =>1),
            array('db' => 'kdkabkot_kantah' , 'dt' =>2),
            array('db' => 'kdprov_kantah' , 'dt' =>3),
            array('db' => 'keg' , 'dt' =>4),
            array('db' => 'nomor' , 'dt' =>5),
            array('db' => 'tgl' , 'dt' =>6),
            array('db' => 'dasar_tgl' , 'dt' =>7),
            array('db' => 'dasar_nama' , 'dt' =>8),
            array('db' => 'dasar_nik' , 'dt' =>9),
            array('db' => 'dasar_nib' , 'dt' =>10),
            array('db' => 'dasar_alamat' , 'dt' =>11),
            array('db' => 'dasar_an' , 'dt' =>12),
            array('db' => 'mohon_jalan' , 'dt' =>13),
            array('db' => 'mohon_nomor' , 'dt' =>14),
            array('db' => 'mohon_rtrw' , 'dt' =>15),
            array('db' => 'mohon_kdlurah' , 'dt' =>16),
            array('db' => 'mohon_kdcamat' , 'dt' =>17),
            array('db' => 'mohon_kdkabkot' , 'dt' =>18),
            array('db' => 'mohon_luas_m2' , 'dt' =>19),
            array('db' => 'mohon_g' , 'dt' =>20),
            array('db' => 'mohon_gq' , 'dt' =>21),
            array('db' => 'mohon_rcnkeg' , 'dt' =>22),
            array('db' => 'mohon_kdkbli' , 'dt' =>23),
            array('db' => 'mohon_fkws' , 'dt' =>24),
            array('db' => 'mohon_kws1' , 'dt' =>25),
            array('db' => 'mohon_kws2' , 'dt' =>26),
            array('db' => 'mohon_kws3' , 'dt' =>27),
            array('db' => 'mohon_kws4' , 'dt' =>28),
            array('db' => 'mohon_kws5' , 'dt' =>29),
            array('db' => 'luas_kws1m2' , 'dt' =>30),
            array('db' => 'luas_kws1persen' , 'dt' =>31),
            array('db' => 'luas_kws2m2' , 'dt' =>32),
            array('db' => 'luas_kws2persen' , 'dt' =>33),
            array('db' => 'luas_kws3m2' , 'dt' =>34),
            array('db' => 'luas_kws3persen' , 'dt' =>35),
            array('db' => 'luas_kws4m2' , 'dt' =>36),
            array('db' => 'luas_kws4persen' , 'dt' =>37),
            array('db' => 'luas_kws5m2' , 'dt' =>38),
            array('db' => 'luas_kws5persen' , 'dt' =>39),
            array('db' => 'keterangan' , 'dt' =>40),
            array('db' => 'terbit_keg' , 'dt' =>41),
            array('db' => 'terbit_sesuai_luasm2' , 'dt' =>42),
            array('db' => 'terbit_sesuai_luaspersen' , 'dt' =>43),
            array('db' => 'terbit_total_tdksesuai_luasm2' , 'dt' =>44),
            array('db' => 'terbit_total_tdksesuai_luaspersen' , 'dt' =>45),
            array('db' => 'terbit_tdksesuai_ilok_luasm2' , 'dt' =>46),
            array('db' => 'terbit_tdksesuai_ilok_luaspersen' , 'dt' =>47),
            array('db' => 'terbit_tdksesuai_skpl_luasm2' , 'dt' =>48),
            array('db' => 'terbit_tdksesuai_skpl_luaspersen' , 'dt' =>49),
            array('db' => 'terbit_tdksesuai3_luasm2' , 'dt' =>50),
            array('db' => 'terbit_tdksesuai3_luaspersen' , 'dt' =>51),
            array('db' => 'terbit_tdksesuai3_ket' , 'dt' =>52),
            array('db' => 'terbit_tdksesuai4_luasm2' , 'dt' =>53),
            array('db' => 'terbit_tdksesuai4_luaspersen' , 'dt' =>54),
            array('db' => 'terbit_tdksesuai4_ket' , 'dt' =>55),
            array('db' => 'terbit_tdksesuai5_luasm2' , 'dt' =>56),
            array('db' => 'terbit_tdksesuai5_luaspersen' , 'dt' =>57),
            array('db' => 'terbit_tdksesuai5_ket' , 'dt' =>58),
            array('db' => 'terbit_total_sesuaisyarat_luasm2' , 'dt' =>59),
            array('db' => 'terbit_total_sesuaisyarat_luaspersen' , 'dt' =>60),
            array('db' => 'terbit_sesuaisyarat1_luasm2' , 'dt' =>61),
            array('db' => 'terbit_sesuaisyarat1_luaspersen' , 'dt' =>62),
            array('db' => 'terbit_sesuaisyarat1_ket' , 'dt' =>63),
            array('db' => 'terbit_sesuaisyarat2_luasm2' , 'dt' =>64),
            array('db' => 'terbit_sesuaisyarat2_luaspersen' , 'dt' =>65),
            array('db' => 'terbit_sesuaisyarat2_ket' , 'dt' =>66),
            array('db' => 'terbit_sesuaisyarat3_luasm2' , 'dt' =>67),
            array('db' => 'terbit_sesuaisyarat3_luaspersen' , 'dt' =>68),
            array('db' => 'terbit_sesuaisyarat3_ket' , 'dt' =>69),
            array('db' => 'terbit_sesuaisyarat4_luasm2' , 'dt' =>70),
            array('db' => 'terbit_sesuaisyarat4_luaspersen' , 'dt' =>71),
            array('db' => 'terbit_sesuaisyarat4_ket' , 'dt' =>72),
            array('db' => 'terbit_sesuaisyarat5_luasm2' , 'dt' =>73),
            array('db' => 'terbit_sesuaisyarat5_luaspersen' , 'dt' =>74),
            array('db' => 'terbit_sesuaisyarat5_ket' , 'dt' =>75),
            array('db' => 'terbit_ketentuan_gq1' , 'dt' =>76),
            array('db' => 'terbit_ketentuan_gq2' , 'dt' =>77),
            array('db' => 'terbit_ketentuan_gq3' , 'dt' =>78),
            array('db' => 'terbit_ketentuan_gq4' , 'dt' =>79),
            array('db' => 'terbit_ketentuan_gq5' , 'dt' =>80),
            array('db' => 'terbit_ketentuan_gp1' , 'dt' =>81),
            array('db' => 'terbit_ketentuan_gp2' , 'dt' =>82),
            array('db' => 'terbit_ketentuan_gp3' , 'dt' =>83),
            array('db' => 'terbit_ketentuan_gp4' , 'dt' =>84),
            array('db' => 'terbit_ketentuan_gp5' , 'dt' =>85),
            array('db' => 'terbit_ketentuan_hat1' , 'dt' =>86),
            array('db' => 'terbit_ketentuan_hat2' , 'dt' =>87),
            array('db' => 'terbit_ketentuan_hat3' , 'dt' =>88),
            array('db' => 'terbit_ketentuan_hat4' , 'dt' =>89),
            array('db' => 'terbit_ketentuan_hat5' , 'dt' =>90),
            array('db' => 'lokasi_surat' , 'dt' =>91),
            array('db' => 'tgl_surat' , 'dt' =>92),
            array('db' => 'nm_kakantah' , 'dt' =>93),
            array('db' => 'nip_kakantah' , 'dt' =>94),
            array('db' => 'id_tema' , 'dt' =>95),
        );
        

         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);

        
    }

    function ajax_delete($id){
        $this->db->where('id_ptp', $id);
        $this->db->delete('t_juknisptp');
    }
    function AutoMultiCellWithBorder($w, $h, $text)
    {
        $this->SetX($this->GetX());
        $this->SetY($this->GetY());
        $width = $w - $this->cMargin * 2;
        $text = trim($text);
        $textLength = $this->GetStringWidth($text);

        // Set cell borders
        $this->Cell($width, $h, '', 'LTRB', 0, '', false);
        
        if ($textLength <= $width) {
            $this->MultiCell($width, $h, $text);
        } else {
            $lines = explode("\n", $text);
            foreach ($lines as $line) {
                $line = trim($line);
                while ($this->GetStringWidth($line) > $width) {
                    $line = substr($line, 0, -1);
                }
                $this->MultiCell($width, $h, $line);
            }
        }
    }

    function provName($id) {
        $this->db->where('kd_prov', $id);
        return $this->db->get('aset_r_provinsi')->row_array()['nama_prov'];
        
    }
    function kabName($id) {
        $this->db->where('kd_kabkot', $id);
        return $this->db->get('aset_r_kabkota')->row_array()['nama_kabkot'];
        
    }
    function kecName($id) {
        $this->db->where('kd_camat', $id);
        return $this->db->get('aset_r_kecamatan')->row_array()['nama_camat'];
        
    }
    function kelName($id) {
        $this->db->where('kd_lurah', $id);
        return $this->db->get('aset_r_kelurahan')->row_array()['nama_lurah'];
        
    }

    function export_pdf($id=''){

            $this->db->where('id_ptp', $id);
            $data = $this->db->get('t_juknisptp')->row_array();
            
    
            
            $this->db->where('id_juknisptp', $data['id_ptp']);
            $afk = $this->db->get('t_juknisptp_afk')->result();
            $this->db->where('id_juknisptp', $data['id_ptp']);
            $sb = $this->db->get('t_juknisptp_sb')->result();
            $this->db->where('id_juknisptp', $data['id_ptp']);
            $ts = $this->db->get('t_juknisptp_ts')->result();
            $this->db->where('id_juknisptp', $data['id_ptp']);
            $milik = $this->db->get('t_juknisptp_milik')->result();
            $this->db->where('id_juknisptp', $data['id_ptp']);
            $manfaat = $this->db->get('t_juknisptp_manfaat')->result();
            $this->db->where('id_juknisptp', $data['id_ptp']);
            $peralihan = $this->db->get('t_juknisptp_peralihan')->result();
            $this->db->where('kode_module', $data['id_tema']);
            $this->db->where('parent', '209');
            $this->db->where('urutan is not null');
            $tema = $this->db->get('aset_module')->row_array();
            

            

            

            $kdprov_kantah = strtoupper($this->provName($data['kdprov_kantah']));
            $kdkabkot_kantah = str_replace('KAB.','KABUPATEN',strtoupper($this->kabName($data['kdkabkot_kantah'])));
            $mohon_kdkabkot = ucfirst(strtolower($this->kabName($data['mohon_kdkabkot'])));
            $mohon_kdlurah = ucfirst(strtolower($this->kelName($data['mohon_kdlurah'])));
            $mohon_kdcamat = ucfirst(strtolower($this->kecName($data['mohon_kdcamat'])));
            $lokasi_surat = ucfirst(strtolower($this->kabName($data['lokasi_surat'])));
           
            $m2 = utf8_decode(' m²');

            $pdf = new FPDF('P', 'mm','Letter');
            $pdf->SetMargins(20, 10, 20);
	        $pdf->AddPage();
	        $pdf->SetFont('Arial','B',12);
            $url_logo = 'assets/themes/adminity/images/logo_bpn.png';
			// $pdf->SetTextColor(255,255,255);
            $pdf->SetFillColor(255,255,255);
            
	        $pdf->Cell(175,7,' ','LTR',1,'C',true);
            
	        $pdf->Cell(30,6,'','L',0,'C',true);
	        $pdf->Cell(125,6,'KEMENTERIAN AGRARIA DAN TATA RUANG/','',0,'C',true);
	        $pdf->Cell(20,6,'','R',1,'C',true);
	        $pdf->Cell(30,6,'','L',0,'C',true);
	        $pdf->Cell(125,6,'BADAN PERTANAHAN NASIONAL','',0,'C',true);
	        $pdf->Cell(20,6,'','R',1,'C',true);
	        $pdf->Cell(30,6,'','L',0,'C',true);
	        $pdf->Cell(125,6,'KANTOR PERTANAHAN '.$kdkabkot_kantah,'',0,'C',true);
	        $pdf->Cell(20,6,'','R',1,'C',true);
	        $pdf->Cell(30,6,'','L',0,'C',true);
	        $pdf->Cell(125,6,'PROVINSI '.$kdprov_kantah,'',0,'C',true);
	        $pdf->Cell(20,6,'','R',1,'C',true);
	        $pdf->SetFont('Arial','',10);
	        $pdf->Cell(30,6,'','L',0,'C',true);
	        $pdf->Cell(125,6,'Alamat : Jalan Pahlawan no 99 Kartoharjo Madiun','',0,'C',true);
	        $pdf->Cell(20,6,'','R',1,'C',true);
	        $pdf->SetFont('Arial','',11);
	        $pdf->Cell(175,3,'','RLT',1,'C',true);
	        $pdf->Cell(175,15,strtoupper(@$tema['nama_module']),'RL',1,'C',true);
	        // $pdf->Cell(175,5,'KEGIATAN PENERBITAN PKKPR UNTUK KEGIATAN BERUSAHA/PKKPR UNTUK KEGIATAN ','RL',1,'C',true);
	        // $pdf->Cell(175,5,'NONBERUSAHA/PKKPR ATAU RKKPR UNTUK KEGIATAN YANG BERSIFAT STRATEGIS','RL',1,'C',true);
	        // $pdf->Cell(175,5,'NASIONAL/PENEGASAN STATUS DAN REKOMENDASI PENGUASAAN TANAH ','RL',1,'C',true);
	        // $pdf->Cell(175,5,'TIMBUL/PENYELENGGARAAN KEBIJAKAN PENGGUNAAN DAN PEMANFAATAN TANAH*)','RL',1,'C',true);
	        // $pdf->SetFont('Arial','',10);
	        // $pdf->Cell(175,5,'*) hapus yang tidak perlu.','RL',1,'C',true);
	        $pdf->Cell(175,2,'','RBL',1,'C',true);
	        $pdf->Cell(175,5,'','RTL',1,'C',true);
            $pdf->Image($url_logo,22, 15, 25, 0);
            
            $str = 'Berdasarkan Risalah Pertimbangan Teknis Pertimbangan Teknis Pertanahan untuk kegiatan '.$data['keg'].' Nomor '.$data['nomor'].' tanggal '.tanggal_indonesia($data['tgl']).' beserta lampiran, bahwa:';
            $pdf->MultiCell(175 , 6, $str, 'LR', 'L',1);
            $pdf->Cell(10,6,'A. ','L',0,'R',true);
            $pdf->Cell(165,6,'DASAR PENERBITAN ','R',1,'L',true);
            $pdf->Cell(10,6,'','L',0,'L',true);
            $pdf->Cell(165,6,'Permohonan tanggal '.tanggal_indonesia($data['dasar_tgl']).' yang diajukan oleh: ','R',1,'L',true);
            
            $str = $data['dasar_nama'];
            $line = count($this->cutTextIntoArray($str, 60));
            $height = $line*6;
            $pdf->Cell(10,$height,'','L',0,'L',true);
            $pdf->Cell(5,6,'1.','',0,'L',true);
            $pdf->Cell(60,6,'Nama','',0,'L',true);
            $pdf->Cell(2,6,':','',0,'L',true);
            $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
            
            $str = $data['dasar_nik'];
            $line = count($this->cutTextIntoArray($str, 60));
            $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'2.','',0,'L',true);
                $pdf->Cell(60,6,'Nomor Induk Kependudukan (NIK)','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                
                $str =  $data['dasar_nib'];
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'3.','',0,'L',true);
                $pdf->Cell(60,6,'Nomor Induk Berusaha (NIB)**','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                
                $pdf->Cell(15,6,'','L',0,'L',true);
                $pdf->Cell(160,6,'**) untuk pemohon pelaku usaha yang sudah memiliki NIB','R',1,'L',true);
                
                $str =  $data['dasar_alamat'];
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'4.','',0,'L',true);
                $pdf->Cell(60,6,'Alamat','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                
                $str =  $data['dasar_an'];
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'5.','',0,'L',true);
                $pdf->Cell(60,6,'Bertindak atas nama','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                
            $pdf->Cell(10,6,'B. ','L',0,'R',true);
            $pdf->Cell(165,6,'KETERANGAN MENGENAI TANAH YANG DIMOHON','R',1,'L',true);
                
                $str =  '';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,6,'','L',0,'L',true);
                $pdf->Cell(5,6,'1.','',0,'L',true);
                $pdf->Cell(60,6,'Letak tanah yang dimohon','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                
                    $str =  $data['mohon_jalan'].', '. $data['mohon_nomor'].' RT/RW '. $data['mohon_rtrw'];
                    $line = count($this->cutTextIntoArray($str, 60));
                    $height = $line*6;
                    $pdf->Cell(15,$height,'','L',0,'L',true);
                    $pdf->Cell(5,6,'a.','',0,'L',true);
                    $pdf->Cell(55,6,'Jalan, nomor, RT/RW','',0,'L',true);
                    $pdf->Cell(2,6,':','',0,'L',true);
                    $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                    
                    $str = $mohon_kdlurah;
                    $line = count($this->cutTextIntoArray($str, 60));
                    $height = $line*6;
                    $pdf->Cell(15,$height,'','L',0,'L',true);
                    $pdf->Cell(5,6,'b.','',0,'L',true);
                    $pdf->Cell(55,6,'Desa/Kelurahan','',0,'L',true);
                    $pdf->Cell(2,6,':','',0,'L',true);
                    $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                    
                    $str = $mohon_kdcamat.', '.$mohon_kdkabkot;
                    $line = count($this->cutTextIntoArray($str, 60));
                    $height = $line*6;
                    $pdf->Cell(15,$height,'','L',0,'L',true);
                    $pdf->Cell(5,6,'c.','',0,'L',true);
                    $pdf->Cell(55,6,'Kecamatan, Kab/Kota','',0,'L',true);
                    $pdf->Cell(2,6,':','',0,'L',true);
                    $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                    
                    
                $pdf->Cell(10,6,'','L',0,'L',true);
                $pdf->Cell(5,6,'2.','',0,'L',true);
                $pdf->Cell(60,6,'Luas tanah yang dimohon','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->Cell(98,6,str_replace(',','.',number_format($data['mohon_luas_m2'])).' '.$m2,'R',1,'L',true);

                $str = 'Sawah dan ladang';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'3.','',0,'L',true);
                $pdf->Cell(60,6,'Penggunaan tanah saat ini','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
               
                $str = 'Sawah dan ladang';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'4.','',0,'L',true);
                $pdf->Cell(60,6,'Penguasaan tanah saat ini ','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);
                
                $str = 'Gedung';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'5.','',0,'L',true);
                $pdf->Cell(80,6,'Rencana Kegiatan/penggunaan dan pemanfaatan tanah ','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(78 , 6,$str, 'R', 'L',1);

                $str = 'KBLI';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,6,'','L',0,'L',true);
                $pdf->Cell(5,6,'6.','',0,'L',true);
                $pdf->Cell(60,6,'KBLI ','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);

                $str = $data['mohon_fkws'];
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'7.','',0,'L',true);
                $pdf->Cell(60,6,'Arahan fungsi kawasan ','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);

                $num = ['a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'];
                // for ($i=1; $i < 6; $i++) { 
                    // $pdf->Cell(15,6,'','L',0,'L',true);
                    // $pdf->Cell(160,6,$num[$i].'. Kawasan '.@$data['mohon_kws'.$i].' :  '.@$data['luas_kws'.$i.'m2'].' m2 ('.@$data['luas_kws'.$i.'persen'].'%)','R',1,'L',true);
                // }
                    
                foreach ($afk as $i => $value) {
                        $pdf->Cell(15,6,'','L',0,'L',true);
                        $pdf->Cell(160,6,$num[$i].'. Kawasan '.@$value->kawasan.' :  '.str_replace(',','.',number_format(@$value->luasm2)).$m2. ' ('.@$value->persen.'%)','R',1,'L',true);
                }
                $str = $data['keterangan'];
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,6,'','L',0,'L',true);
                $pdf->Cell(5,6,'6.','',0,'L',true);
                $pdf->Cell(60,6,'Keterangan lain yang dianggap perlu ','',0,'L',true);
                $pdf->Cell(2,6,':','',0,'L',true);
                $pdf->MultiCell(98 , 6,$str, 'R', 'L',1);

            $pdf->Cell(10,6,'C. ','L',0,'R',true);
            $pdf->Cell(165,6,'PENERBITAN','R',1,'L',true);
                
                $str = 'Permohonan Pertimbangan Teknis Pertanahan untuk kegiatan '.$data['terbit_keg'].',    berdasarkan hasil analisis P4T, kemampuan tanah (dan aspek fisik serta aspek kebencanaan) dapat disimpulkan sebagai berikut';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'1.','',0,'L',true);
                $pdf->MultiCell(160 , 6,$str, 'R', 'L',1);
        
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'a. Sesuai seluruhnya atau sebagian seluas '.@$data['terbit_sesuai_luasm2'].$m2.'   ('.@$data['terbit_sesuai_luaspersen'].'%)','R',1,'L',true);
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'b. Tidak Sesuai seluas '.@$data['terbit_total_tdksesuai_luasm2'].$m2.'  ('.@$data['terbit_total_tdksesuai_luaspersen'].'%) dengan alasan :','R',1,'L',true);
                       

                        // $str = $data['terbit_tdksesuai_ilok_luasm2'].' m2 ('.$data['terbit_tdksesuai_ilok_luaspersen'].'%) : terdapat Izin Lokasi/KKPR yang masih berlaku';
                        // $line = count($this->cutTextIntoArray($str, 80));
                        // $height = $line*6;
                        // $pdf->Cell(20,$height,'','L',0,'R',true);
                        // $pdf->Cell(5,6,'1.','',0,'R',true);
                        // $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                        
                        // $str = $data['terbit_tdksesuai_skpl_luasm2'].' m2 ('.$data['terbit_tdksesuai_skpl_luaspersen'].'%) : terdapat SK Penetapan Lokasi pengadaan tanah untuk kepentingan umum/ Proyek strategis nasional';
                        // $line = count($this->cutTextIntoArray($str, 80));
                        // $height = $line*6;
                        // $pdf->Cell(20,$height,'','L',0,'R',true);
                        // $pdf->Cell(5,6,'2.','',0,'R',true);
                        // $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);

                        // for ($i=3; $i < 6; $i++) { 
                        //     if($data['terbit_tdksesuai'.$i.'_ket'] != ''){

                        //         $str = $data['terbit_tdksesuai'.$i.'_luasm2'].' m2 ('.$data['terbit_tdksesuai'.$i.'_luaspersen'].'%) : '.$data['terbit_tdksesuai'.$i.'_ket'];
                        //         $line = count($this->cutTextIntoArray($str, 80));
                        //         $height = $line*6;
                        //         $pdf->Cell(20,$height,'','L',0,'R',true);
                        //         $pdf->Cell(5,6,$i.'.','',0,'R',true);
                        //         $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                        //     }
                        // }

                        foreach ($ts as $i => $value) {
                            $i = $i+1;
                            $str = $value->luasm2.$m2.'  ('.$value->persentase.'%) : '.$value->kawasan;
                            $line = count($this->cutTextIntoArray($str, 80));
                            $height = $line*6;
                            $pdf->Cell(20,$height,'','L',0,'R',true);
                            $pdf->Cell(5,6,$i.'.','',0,'R',true);
                            $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                        }

                        
                    $pdf->Cell(15,6,'','L',0,'L',true);
                    $pdf->Cell(160,6,'c. Sesuai bersyarat seluas '.@$data['terbit_total_sesuaisyarat_luasm2'].$m2.'  ('.@$data['terbit_total_sesuaisyarat_luaspersen'].'%) dengan syarat-syarat sebagai berikut :','R',1,'L',true);
                        
                        // for ($i=1; $i < 6; $i++) { 
                        //     if($data['terbit_sesuaisyarat'.$i.'_ket'] != ''){

                        //         $str = $data['terbit_sesuaisyarat'.$i.'_luasm2'].' m2 ('.$data['terbit_sesuaisyarat'.$i.'_luaspersen'].'%) : '.$data['terbit_sesuaisyarat'.$i.'_ket'];
                        //         $line = count($this->cutTextIntoArray($str, 80));
                        //         $height = $line*6;
                        //         $pdf->Cell(20,$height,'','L',0,'R',true);
                        //         $pdf->Cell(5,6,$i.'.','',0,'R',true);
                        //         $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                        //     }
                        // }
                        foreach ($sb as $i => $value) {
                            $i = $i+1;
                            $str = $value->luasm2.$m2.'  ('.$value->persentase.'%) : '.$value->kawasan;
                            $line = count($this->cutTextIntoArray($str, 80));
                            $height = $line*6;
                            $pdf->Cell(20,$height,'','L',0,'R',true);
                            $pdf->Cell(5,6,$i.'.','',0,'R',true);
                            $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                        }
                        

                $str = 'Ketentuan dan syarat penguasaan dan pemilikan tanah adalah sebagai berikut: (didasarkan pada Lampiran 22 sesuai kondisi lokasi)';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'2.','',0,'L',true);
                $pdf->MultiCell(160 , 6,$str, 'R', 'L',1);
                // for ($i=1; $i < 6; $i++) { 
                //     if($data['terbit_ketentuan_gq'.$i] != ''){
                //         $str = $data['terbit_ketentuan_gq'.$i].$i;
                //         $line = count($this->cutTextIntoArray($str, 60));
                //         $height = $line*6;
                //         $pdf->Cell(20,$height,'','L',0,'L',true);
                //         $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                //         $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                //     }
                // }
                foreach ($milik as $i => $value) {
                    $str = $value->keterangan;
                    $line = count($this->cutTextIntoArray($str, 60));
                    $height = $line*6;
                    $pdf->Cell(20,$height,'','L',0,'L',true);
                    $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                    $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                }

                $str = 'Ketentuan dan syarat-syarat penggunaan dan pemanfaatan tanah adalah sebagai berikut (didasarkan pada Lampiran I sesuai kondisi lokasi)';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'3.','',0,'L',true);
                $pdf->MultiCell(160 , 6,$str, 'R', 'L',1);
                    // for ($i=1; $i < 6; $i++) { 
                    //     if($data['terbit_ketentuan_gp'.$i] != ''){
                    //         $str = $data['terbit_ketentuan_gp'.$i].$i;
                    //         $line = count($this->cutTextIntoArray($str, 60));
                    //         $height = $line*6;
                    //         $pdf->Cell(20,$height,'','L',0,'L',true);
                    //         $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                    //         $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                    //     }
                    // }
                    foreach ($manfaat as $i => $value) {
                        $str = $value->keterangan;
                        $line = count($this->cutTextIntoArray($str, 60));
                        $height = $line*6;
                        $pdf->Cell(20,$height,'','L',0,'L',true);
                        $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                        $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                    }
                    
                $str = 'Ketentuan perolehan Tanah dan peralihan Hak Atas Tanah (bagi pemohon Pelaku Usaha yang membutuhkan tanah namun belum memiliki/menguasai tanah)';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'4.','',0,'L',true);
                $pdf->MultiCell(160 , 6,$str, 'R', 'L',1);
                
                    // for ($i=1; $i < 6; $i++) { 
                    //     if($data['terbit_ketentuan_hat'.$i] != ''){
                    //         $str = $data['terbit_ketentuan_hat'.$i].$i;
                    //         $line = count($this->cutTextIntoArray($str, 60));
                    //         $height = $line*6;
                    //         $pdf->Cell(20,$height,'','L',0,'L',true);
                    //         $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                    //         $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                    //     }
                    // }
                    foreach ($peralihan as $i => $value) {
                        $str = $value->keterangan;
                        $line = count($this->cutTextIntoArray($str, 60));
                        $height = $line*6;
                        $pdf->Cell(20,$height,'','L',0,'L',true);
                        $pdf->Cell(5,6,$num[$i].'.','',0,'L',true);
                        $pdf->MultiCell(150 , 6,$str, 'R', 'L',1);
                    }
                    

                $str = 'Pertimbangan Teknis Pertanahan bukan merupakan alas hak atas tanah ataupun izin membuka tanah.';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'5.','',0,'L',true);
                $pdf->MultiCell(160 , 6,$str, 'R', 'L',1);
               
                $str = 'Peta Pertimbangan Teknis Pertanahan sebagaimana terlampir merupakan bagian yang tidak terpisahkan dari Pertimbangan Teknis Pertanahan ini.';
                $line = count($this->cutTextIntoArray($str, 60));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'6.','',0,'L',true);
                $pdf->MultiCell(160 , 6,$str, 'R', 'L',1);
                
                $str = 'Keterangan lebih rinci mengenai ketentuan dan syarat-syarat penggunaan tanah, letak dan luas tanah yang sesuai, tidak sesuai dan/atau sesuai bersyarat dapat dilihat pada Peta Pertimbangan Teknis Pertanahan sebagaimana terlampir, yang merupakan bagian yang tidak terpisahkan dari Pertimbangan Teknis Pertanahan ini.';
                $line = count($this->cutTextIntoArray($str, 80));
                $height = $line*6;
                $pdf->Cell(10,$height,'','L',0,'L',true);
                $pdf->Cell(5,6,'7.','',0,'L',true);
                $pdf->MultiCell(160 , 6,$str, 'R', 'L',1);
                
            $str = 'Penerima Pertimbangan teknis Pertanahan ini telah membaca dan memahami serta akan mematuhi syarat dan ketentuan yang ditetapkan dalam pertimbangan teknis pertanahan ini';
            $line = count($this->cutTextIntoArray($str, 80));
            $height = $line*6;
            $pdf->Cell(5,$height,'','L',0,'L',true);
            $pdf->Cell(5,6,'C.','',0,'L',true);
            $pdf->MultiCell(165 , 6,$str, 'R', 'L',1);
            
            $pdf->Cell(175,20,'','RL',1,'L',true);
            
            $pdf->Cell(80,6,'','L',0,'L',true);
            $pdf->Cell(80,6,$this->camel($lokasi_surat).',Tanggal '.tanggal_indonesia($data['tgl_surat']).'','',0,'C',true);
            $pdf->Cell(15,6,'','R',1,'L',true);
            $pdf->Cell(80,6,'','L',0,'L',true);
            $pdf->Cell(80,6,'Kepala Kantor Pertanahan','',0,'C',true);
            $pdf->Cell(15,6,'','R',1,'L',true);
            
            $pdf->Cell(175,20,'','RL',1,'L',true);
            
            $pdf->Cell(80,6,'','L',0,'L',true);
            $pdf->Cell(80,6,$data['nm_kakantah'],'',0,'C',true);
            $pdf->Cell(15,6,'','R',1,'L',true);
            
            $pdf->Cell(80,6,'','L',0,'L',true);
            $pdf->Cell(80,6,'NIP. '.$data['nip_kakantah'],'',0,'C',true);
            $pdf->Cell(15,6,'','R',1,'L',true);
            
            // $pdf->Cell(5,10,'','L',0,'L',true);
            // $pdf->Cell(165,10,'','B',0,'L',true);
            // $pdf->Cell(5,10,'','R',1,'L',true);
            
            
            // $pdf->SetFont('Arial','',8);
            $pdf->Cell(175,10,'','BLR',1,'L',true);
            // $pdf->Cell(175,10,'','BLRT',1,'L',true);
            
            $pdf->Output();
    }

   

    function cutTextIntoArray($text, $maxLength) {
        $segments = array();
        
        while (strlen($text) > $maxLength) {
            // Find the last space within the allowed length
            $lastSpace = strrpos(substr($text, 0, $maxLength), ' ');
    
            if ($lastSpace === false) {
                // No space found within the limit, truncate at the maxLength
                $segment = substr($text, 0, $maxLength);
            } else {
                // Truncate at the last space
                $segment = substr($text, 0, $lastSpace);
            }
    
            $segments[] = $segment;
            $text = substr($text, strlen($segment));
        }
    
        if (!empty($text)) {
            $segments[] = $text; // Add any remaining text
        }
    
        return $segments;
    }
    
    function export_pdff(){
        $pdf = new FPDF('L', 'mm','Letter');
		foreach ($datas as $key => $value) {
	        $pdf->AddPage();
	        $pdf->SetFont('Arial','B',12);
	        $pdf->Cell(190,7,'RISALAH PERTIMBANGAN TEKNIS PERTANAHAN',0,1,'C');
			$pdf->SetTextColor(0,0,0);
	        // foreach ($value as $k => $v) {
		    //     $pdf->Cell(10,7,$k+1,1,0,'C');
		    //     $pdf->Cell(55,7,$v->jenis_prasarana,1,0,'C');
		    //     $pdf->Cell(20,7,$v->keberadaan_ada,1,0,'C');
		    //     $pdf->Cell(22,7,$v->keberadaan_tidak_ada,1,0,'C');
		    //     $pdf->Cell(23,7,$v->keberadaan_kosong,1,0,'C');
		    //     $pdf->Cell(18,7,$v->kondisi_baik,1,0,'C');
		    //     $pdf->Cell(18,7,$v->kondisi_cukup,1,0,'C');
		    //     $pdf->Cell(17,7,$v->kondisi_kurang,1,0,'C');
		    //     $pdf->Cell(17,7,$v->kondisi_kosong,1,0,'C');
		    //     $pdf->Cell(22,7,$v->kepemilikan_individu,1,0,'C');
		    //     $pdf->Cell(22,7,$v->kepemilikan_komunal,1,0,'C');
		    //     $pdf->Cell(21,7,$v->kepemilikan_lainnya,1,1,'C');
	        // }

	        // $pdf->Cell(225,7,'',0,1,'L');
	        
		}
	    
	  
        $pdf->Output();
    }
    

    function camel($str) {
        $str = strtolower($str);
        $exp = explode(' ',$str);
        $ret='';
        foreach ($exp as $key => $value) {
            $ret .= ucfirst($value).' ';
        }
        return $ret;

    }

    function get_edit($id) {
        $this->db->where('id_juknisptp', $id);
        $afk = $this->db->get('t_juknisptp_afk')->result();
        $this->db->where('id_juknisptp', $id);
        $sb = $this->db->get('t_juknisptp_sb')->result();
        $this->db->where('id_juknisptp', $id);
        $ts = $this->db->get('t_juknisptp_ts')->result();
        $this->db->where('id_juknisptp', $id);
        $milik = $this->db->get('t_juknisptp_milik')->result();
        $this->db->where('id_juknisptp', $id);
        $manfaat = $this->db->get('t_juknisptp_manfaat')->result();
        $this->db->where('id_juknisptp', $id);
        $peralihan = $this->db->get('t_juknisptp_peralihan')->result();
        
        echo json_encode(
                            [
                                'afk' => $afk,
                                'ts' => $ts,
                                'sb' => $sb,
                                'milik' => $milik,
                                'manfaat' => $manfaat,
                                'peralihan' => $peralihan,
                            ]
                            );
    }

    function get_tema() 
    {
        $this->db->where('parent', '209');
        $this->db->where('urutan is not null');
        $data = $this->db->get('aset_module')->result();
        echo json_encode($data);
    }
}