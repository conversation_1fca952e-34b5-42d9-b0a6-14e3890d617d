<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
	<!-- <link rel="stylesheet" type="text/css" href="<? echo base_url();?>assets/themes/adminity/bower_components/bootstrap/dist/css/bootstrap.min.css"> -->

<!-- <link rel="stylesheet" href="https://unpkg.com/leaflet@1.6.0/dist/leaflet.css" integrity="sha512-xwE/Az9zrjBIphAcBb3F6JVqxf46+CDLwfLMHloNu6KEQCAWi6HcDUbeOfBIptF7tcCzusKFjFw2yuvEpDL9wQ==" crossorigin=""/> -->
<!-- <link rel="stylesheet" href="<? //echo base_url();?>assets/js/leaflet-panel-layers-master/src/leaflet-panel-layers.css" /> -->
<link rel="stylesheet" href="<? echo base_url();?>assets/js/pgt-maps/leaflet.css" />
<link rel="stylesheet" href="<? echo base_url();?>assets/js/pgt-maps/L.Control.Layers.Tree.css" />

<!-- <link rel="stylesheet" href="<?// echo base_url();?>assets/js/leaflet-panel-layers-master/examples/style.css" /> -->
<!-- <link href="http://maxcdn.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css" rel="stylesheet"> -->
<!-- <link rel="stylesheet" href="<? echo base_url();?>assets/peta/libs/leaflet-sidebar2/css/leaflet-sidebar.css" /> -->
<!-- <link rel="stylesheet" href="<? //echo base_url();?>assets/peta/css/leaflet/leaflet-sidebar.css" /> -->
<link rel="stylesheet" href="<?=base_url()?>assets/peta/cropper/cropper.min.css">
<link href="<?=base_url()?>assets/peta/cropper/select2.min.css" rel="stylesheet" />

<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/leaflet-draw/css/leaflet.css" />
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/geoman/leaflet-geoman.css" /> 
<link href='<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.css' rel='stylesheet' />
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/leaflet-draw/css/leaflet.draw.css" />
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/leaflet-draw/css/L.Control.ZoomBox.min.css">
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/geocoder/control.geocoder.css" />
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/leafletbigimage/Leaflet.BigImage.min.css" />
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/leaflet-easyprint/leaflet-easyprint.min.css" />


<style>



#map{
    /* position: absolute; */
    top: 0;
    left: 0;
    width: 100%;
    /* height: 900px; */
    min-height: 600px;
}

.leaflet-grab {
   cursor: auto;
}
.leaflet-dragging .leaflet-grab{
   cursor: move;
}

.leaflet-control-layers-separator{
    display:block!important
}

.opt_select{
  color:black!important;
}
</style>

<!-- <h3><a href="../"><big>◄</big> Leaflet Panel Layers</a></h3>
<h4> Groups Example: multiple groups of layers</h4>
<br /> -->
	<!-- <div id="sidebar-panel" class="sidebar collapsed"> -->
        <!-- Nav tabs -->
        <!-- <div class="sidebar-tabs"> -->
            <!-- <ul role="tablist"> -->
                <!-- <li><a href="#dashboard" role="tab"><i class="fa fa-bars"></i></a></li> -->
                <!-- <li><a href="#informasi" role="tab"><i class="fa fa-user"></i></a></li> -->
                <!-- <li class="disabled"><a href="#messages" role="tab"><i class="fa fa-envelope"></i></a></li>
                <li><a href="https://github.com/Turbo87/sidebar-v2" role="tab" target="_blank"><i class="fa fa-github"></i></a></li> -->
            <!-- </ul> -->

            <!-- <ul role="tablist">
                <li><a href="#settings" role="tab"><i class="fa fa-gear"></i></a></li>
            </ul> -->
        <!-- </div> -->

        <!-- Tab panes -->
        <!-- <div class="sidebar-content"> -->
			<!-- <div class="sidebar-pane" id="dashboard"> -->
                <!-- <h1 class="sidebar-header">Dashboard<span class="sidebar-close"><i class="fa fa-caret-left"></i></span></h1> -->
            <!-- </div> -->
            <!-- <div class="sidebar-pane" id="informasi"> -->
                <!-- <h1 class="sidebar-header"> -->
                    <!-- Informasi -->
                    <!-- <span class="sidebar-close"><i class="fa fa-caret-left"></i></span> -->
                <!-- </h1> -->

                <!-- <p id="infoobjek"></p> -->
            <!-- </div> -->

            <!-- <div class="sidebar-pane" id="messages">
                <h1 class="sidebar-header">Messages<span class="sidebar-close"><i class="fa fa-caret-left"></i></span></h1>
            </div>

            <div class="sidebar-pane" id="settings">
                <h1 class="sidebar-header">Settings<span class="sidebar-close"><i class="fa fa-caret-left"></i></span></h1>
            </div> -->
        <!-- </div> -->
    <!-- </div> -->
    <div>

      
      <!-- <div id="mymap" style="width: 100%; height: 400px;"></div> -->
      <div id="map">
    </div>
  <!-- <iframe src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d2027928.7027891946!2d113.58324044999999!3d-6.914709!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sid!2sid!4v1697119110698!5m2!1sid!2sid" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe> -->
  <!-- <button onclick="captures()">Tombol</button> -->
  </div>
  
    <div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="imageModalLabel">Captured Image Preview</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          
          <div class="modal-body">
            <form id="uploadForm" class="form-horizontal" >
            <fieldset class="border rounded-3 p-3">
           <legend class="float-none w-auto px-3">Peta</legend>
           <img id="capturedImage" src="" alt="Captured Image" class="img-fluid">
           <button type="button" style="float:right" class="btn btn-primary" id="cropButton">Crop</button>
          
          </fieldset>
            <fieldset class="border rounded-3 p-3">
           <legend class="float-none w-auto px-3">Isian</legend>
          
            <div class="form-group">
              <label class="col-md-12 control-label" for="id_ptp">Nama Pemohon</label>
              <div class="col-md-12">
                <select id="id_ptp" name="id_ptp"  class="bootstrap-select form-control" data-live-search="true">
                  <!-- <option value="#">Pilih</option> -->
                </select>
              </div>
            </div>
          
            <div class="form-group">
              <label class="col-md-12 control-label" for="legenda">Legenda</label>  
              <div class="col-md-12">
              <select class="js-example-basic-multiple select2" id="legenda" name="legenda[]" multiple="multiple">
                
              </select>  
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-12 control-label" for="ditinjau">Ditinjau Oleh</label>
              <div class="col-md-12">
              <input id="ditinjau" name="ditinjau"   type="text" placeholder="" class="form-control input-md numberonly" >

              </div>
            </div>
            <div class="form-group">
              <label class="col-md-12 control-label" for="tanggal">Tanggal</label>
              <div class="col-md-12">
              <input id="tanggal" name="tanggal"   type="date" placeholder="" class="form-control input-md numberonly" >

              </div>
            </div>
            <div class="form-group">
              <label class="col-md-12 control-label" for="digambar">Digambar Oleh</label>
              <div class="col-md-12">
              <input id="digambar" name="digambar"   type="text" placeholder="" class="form-control input-md numberonly" >

              </div>
            </div>
            <div class="form-group">
              <label class="col-md-12 control-label" for="diperiksa">Diperiksa Oleh</label>
              <div class="col-md-12">
              <input id="diperiksa" name="diperiksa"   type="text" placeholder="" class="form-control input-md numberonly" >

              </div>
            </div>
          </fieldset>

          <fieldset class="border rounded-3 p-3">
           <legend class="float-none w-auto px-3">Sumber Peta</legend>
        
            <div class="form-group div_sumber_peta">
              <label class="col-md-10 control-label" for="sumber_peta">Sumber Peta</label>
              <div class="row">

                <div class="col-md-10">
                  <input id="sumber_peta" name="sumber_peta[]"   type="text" placeholder="" class="form-control input-md numberonly" >
                </div>
                <button type="button" onclick="addSumber()" class="col-md-2 btn btn-sm btn-primary">Tambah</button>
              </div>
            </div>
          </fieldset>
          <fieldset class="border rounded-3 p-3">
           <legend class="float-none w-auto px-3"></legend>
        
            <div class="form-group">
              <label class="col-md-10 control-label" for="tanggal_surat">Tanggal Surat</label>
                <div class="col-md-10">
                  <input id="tanggal_surat" name="tanggal_surat"   type="date" placeholder="" class="form-control input-md numberonly" >
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-10 control-label" for="kepala_nama">Nama Kepala</label>
                <div class="col-md-10">
                  <input id="kepala_nama" name="kepala_nama"   type="text" placeholder="" class="form-control input-md numberonly" >
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-10 control-label" for="kepala_nip">NIP Kepala</label>
                <div class="col-md-10">
                  <input id="kepala_nip" name="kepala_nip"   type="text" placeholder="" class="form-control input-md numberonly" >
              </div>
            </div>
          </fieldset>
        </div>

          
          <div class="modal-footer">
            <button type="button"  class="btn btn-success" onclick="sub()" id="">Submit</button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" id="saveImageButton">Save Image</button>
          </div>
        </form>
        </div>
      </div>
    </div>
    <div id="a"> <h1>ini tes</h1></div>
    <!-- <button id="capture-button" class="btn btn-primary">Capture Map</button> -->

	<!-- <?php echo $js_wms; ?> -->
	<?php echo $jv_script; ?>
	<?php echo $print_peta; ?>
	<?php echo $easy_print; ?>
<!-- <div id="copy"><a href="https://opengeo.tech/">Labs</a> &bull; <a rel="author" href="https://opengeo.tech/stefano-cudini/">Stefano Cudini</a></div> -->

<!-- <a href="https://github.com/stefanocudini/leaflet-panel-layers"><img id="ribbon" src="https://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png" alt="Fork me on GitHub"></a> -->
<script>
  
  $(document).ready(function() {
    
    initComboboxPeta('id_ptp', 26)
    // initComboboxPeta('legenda', 27)
    $.get("<?= base_url('peta/getLegenda')?>", function(data) {
        data.forEach(function (item) {
          $("#legenda").append('<option value="'+item.id+'" data-image="<?php echo base_url('uploads/legenda/')?>'+item.img+'">'+item.text+'</option>');
        });

        // Trigger an event to refresh the Select2 dropdown
        $("#legenda").trigger("change");
      }, "json");
      $(".select2").select2({
        templateResult: formatState,
        templateSelection: formatState


        
    });

    $('#uploadForm').submit(function (e) {
      e.preventDefault();

      var formData = new FormData(this);
          // Get the cropped canvas as a Blob
          cropper.getCroppedCanvas().toBlob(function (blob) {
              // Create FormData object
              // var formData = new FormData();

              // Append the cropped image Blob to the FormData
              formData.append('croppedImage', blob, 'cropped_image.png');

              formData.append("<?php echo $this->security->get_csrf_token_name(); ?>", "<?php echo $this->security->get_csrf_hash(); ?>");
            
              
              $.ajax({
                  url: '<?=base_url()?>'+'peta/uploadPeta',
                  type: "post",
                  data: formData,
                  processData: false,
                  contentType: false,
                  cache: false,
                  async: false,
                  success: function (data) {
                      console.log(data)
                      var urlToOpen = "<?php echo base_url(); ?>peta/export_pdf_peta/"+data;
                      window.open(urlToOpen, '_blank');
                      
                  },error: function (jqXHR, exception) {
                      
                  }
              });

          });
      });

      


  });


  
// Custom template function
function formatState (opt) {
    if (!opt.id) {
        return opt.text.toUpperCase();
    } 

    var optimage = $(opt.element).attr('data-image'); 
    if(!optimage){
       return opt.text.toUpperCase();
    } else {                    
        var $opt = $(
           '<span ><img src="' + optimage + '" width="60px" /> <span class"opt_select" style="color:black!important">' + opt.text.toUpperCase() + '</span></span>'
        );
        return $opt;
    }
};

function sub() {
  $('#uploadForm').submit();
}

var no_sumber = 1
function addSumber() {
  
  var str = '<div id="sumber'+no_sumber+'" class="row"><div  class="col-md-10">'+
    '<input id="sumber_peta'+no_sumber+'" name="sumber_peta[]"   type="text" placeholder="" class="form-control input-md numberonly" >'+
  '</div>'+
  '<button type="button" onclick="delSumber(\'sumber'+no_sumber+'\')" class="col-md-2 btn btn-sm btn-danger">Hapus</button></div>'
  $('.div_sumber_peta').append(str)
}

function delSumber(v) {
  $('#'+v).remove()
}
    </script>
