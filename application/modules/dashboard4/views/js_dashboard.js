$(document).ready(function() {
    // var dt_fclose = JSON.parse($("#chart_fclose").val());
    // var dt_fclose_ruas = JSON.parse($("#chart_fclose_ruas").val());

    // // console.log(dt_aset_jembatan);

    // Highcharts.chart("container", {
    //     chart: {
    //         type: "column",
    //     },
    //     title: {
    //         text: "Financial Close",
    //     },
    //     xAxis: {
    //         type: "category",
    //     },

    //     legend: {
    //         enabled: false,
    //     },

    //     plotOptions: {
    //         series: {
    //             borderWidth: 0,
    //             dataLabels: {
    //                 enabled: true,
    //             },
    //         },
    //     },

    //     series: [{
    //         name: "Ruas",
    //         colorByPoint: true,
    //         data: dt_fclose,
    //     }, ],
    //     drilldown: {
    //         series: dt_fclose_ruas,
    //     },
    // });
});