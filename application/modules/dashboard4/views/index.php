<!-- <script src="https://code.highcharts.com/highcharts.js"></script> -->
<!-- <script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://code.highcharts.com/modules/export-data.js"></script>
<script src="https://code.highcharts.com/modules/accessibility.js"></script> -->
<!-- <script src="https://code.highcharts.com/modules/data.js"></script> -->
<!-- <script src="https://github.highcharts.com/modules/drilldown.js"></script> -->
<!-- <script src="https://code.highcharts.com/modules/drilldown.js"></script> -->
<style>
#container {
    height: 400px;
}

.highcharts-figure,
.highcharts-data-table table {
    min-width: 310px;
    max-width: 800px;
    margin: 1em auto;
}

.highcharts-data-table table {
    font-family: Verdana, sans-serif;
    border-collapse: collapse;
    border: 1px solid #ebebeb;
    margin: 10px auto;
    text-align: center;
    width: 100%;
    max-width: 500px;
}

.highcharts-data-table caption {
    padding: 1em 0;
    font-size: 1.2em;
    color: #555;
}

.highcharts-data-table th {
    font-weight: 600;
    padding: 0.5em;
}

.highcharts-data-table td,
.highcharts-data-table th,
.highcharts-data-table caption {
    padding: 0.5em;
}

.highcharts-data-table thead tr,
.highcharts-data-table tr:nth-child(even) {
    background: #f8f8f8;
}

.highcharts-data-table tr:hover {
    background: #f1f7ff;
}

#divWp3wtPxn>.bootstrap-select {
    width: 100px !important
}

/* #divWp3wtLayer > .bootstrap-select{
width: 230px !important;
/* float:left !important; */
/* }  */
#divWp3wtYear>.bootstrap-select {
    width: 180px !important
}


#divWp3wtProv>.bootstrap-select {
    width: 190px !important
}

#divWp3wtKab>.bootstrap-select {
    width: 180px !important
}

#divWp3wtKec>.bootstrap-select {
    width: 180px !important
}

#divPgtlYear>.bootstrap-select {
    width: 170px !important
}


#divPgtlKec>.bootstrap-select {
    width: 170px !important
}

#divPgtlKab>.bootstrap-select {
    width: 170px !important
}


#divPgtlProv>.bootstrap-select {
    width: 170px !important
}

/* loader */


.dropdown-item {
    /* white-space: pre-wrap; */
}
</style>
<?php 
	$role=$this->session->users['id_user_group_real'];
	$csshide=($role==7)?'display:none;':'display:block;';
?>
<div class="page-wrapper">
    <textarea id="chart_fclose" style="display:none"><?php //echo $financial_close;  ?></textarea>
    <textarea id="chart_fclose_ruas" style="display:none"><?php //echo $financial_close_ruas;  ?></textarea>
    <!-- <div class="card">
	<div class="row">
		<div class="col-md-12" id="divKosong" style="float:right">
			<select name=""   class="bootstrap-select w-700">
				<option value="">kosong</option>
			</select>
		</div>
	</div>
</div> -->

    <div class="page-body">
        <div class="row">

            <div class="card" id="divnprov" style="background-color: white:20x; <?php echo $csshide;?>">
                <h2 style="margin-top:20px">
                    <center> Neraca Penatagunaan Tanah Provinsi</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">
                    <div class="col-md-12" style="">
                        <div style="align-text:right">
                            <!-- <span class="float-right">Tahun Data</span> -->
                            <select style="align-selt:center;width:700px !important;float:right!important"
                                name="provSelectYear" id="ProvSelectYear" onchange="npgtProv()"
                                class="bootstrap-select">
                                <option value="">Tahun Pengerjaan</option>
                                <?php echo $tahunNpgtProv?>
                            </select>
                        </div>
                        <!-- <select  style="align-selt:center;width:300px;float:right!important;margin-right:20px!important" name="provSelectProv" id="provSelectProv" onchange="npgtProv()" class="bootstrap-select" data-live-search="true"> -->
                        <!-- </select>	 -->
                    </div>
                    <div class="col-md-12" style="">
                        <figure class="highcharts-figure">
                            <div id="npgtProv" style="">
                            </div>
                        </figure>
                    </div>
                </div>

            </div>

            <br>
            <div class="card" id="divnhprov" style="background-color: white:20x; <?php echo $csshide;?>">
                <h2 style="margin-top:20px">
                    <center> Hasil Analisis Neraca Penatagunaan Tanah Provinsi</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">
                    <div class="col-md-12">
                        <!-- <select style="align-selt:center;width:300px;float:right!important" name="provSelectYearS" id="provSelectYearS" onchange="npgtProvChangeTahun()" class="bootstrap-select"> -->

                        <select style="align-selt:center;float:right!important" name="provSelectYearDual"
                            id="provSelectYearDual" onchange="npgtProvDual()" class="bootstrap-select">
                            <!-- <option value="">Tahun Pengerjaan</option>
					<?php //echo $tahunNpgtProv?>	 -->
                            <option value="">Tahun Pengerjaan</option>

                        </select>

                        <!-- <select style="align-selt:center;width:300px;float:right!important" name="provSelectLayer" id="provSelectLayer" onchange="npgtProvDual()" class="bootstrap-select">
				<option valnullue="">Pilih Layer</option>
				<option value="spatial.v_d_np_sesuai">Kesesuaian Penggunaan Tanah Terhadap RTRW (N)</option>
				<option value="spatial.v_d_np_tersedia">Ketersediaan Tanah (V)</option>
			</select>  -->
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="provSelectProvDual" id="provSelectProvDual" onchange="npgtProvDual()"
                            class="bootstrap-select" data-live-search="true">
                        </select>
                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="npgtProvStack"></div>

                        </figure>
                    </div>

                </div>

            </div>

            <br>


            <!-- <div style="background-color: white;:20x" >
				<h2 style="margin-top:20px"><center> Neraca Penatagunaan Tanah Provinsi</center></h2>

					<div class="row">
						<div class="col-md-12">
						<select style="align-selt:center;width:300px;float:right!important" name="provSelectYear" id="provSelectTahun" onchange="npgtProvChangeTahun()" class="bootstrap-select">
						</select>
						</div>
						<div class="col-md-12">
							<figure class="highcharts-figure">
								<div id="npgtProv"></div>
							</figure>
						</div>
					</div>
					
				</div> -->
            <!-- =========================================================================== -->

            <div class="card" id="divnkab" style="background-color: white;:20x">
                <h2 style="margin-top:20px">
                    <center> Neraca Penatagunaan Tanah Kabupaten/Kota</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">

                    <div class="col-md-12" style="padding:10px">

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kabkotSelect" id="kabkotSelectTahun" onchange="npgtKabkotChange()"
                            class="bootstrap-select">
                            <option value="">Tahun Pengerjaan</option>
                        </select>

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kabkotSelectKab" id="kabkotSelectKab" onchange="npgtKabkotChange()"
                            class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                        <div class="noprov" style="float:right!important;">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important;<?php echo $csshide;?>"
                                name="kabkotSelectProv" id="kabkotSelectProv"
                                onchange="npgtKabkotChangeProv($(this).val())" class="bootstrap-select"
                                data-live-search="true">
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>
                        <!-- <select style="align-selt:center;width:300px;float:right!important" name="kabkotSelectYear" id="kabkotSelect" onchange="npgtKabkotChange()" class="bootstrap-select">
				<option value="null">Pilih Layer</option>;
				<?php //foreach ($npgtKabkot as $key => $value) {
					//if($value->v_dasboard!=''){
						// echo '<option value="kab=spatial.v_d_npgt_kabkot_a.kec=spatial.v_d_npgt_kec_a">'.$value->nama_layer.'</option>';
					//	echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
						
						// echo $value->v_dasboard.'/';
					//}
				//}?>	

			</select> -->

                        &nbsp;


                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="npgtKabkota"></div>
                        </figure>
                    </div>
                </div>

            </div>

            <div class="card" id="divnkab" style="background-color: white;:20x;display:none">
                <h2 style="margin-top:20px">
                    <center>Hasil Analisis Neraca Penatagunaan Tanah Kabupaten/Kota</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">
                    <div class="col-md-12" style="padding:10px">

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kabkotSelect" id="kabSelectYearDual" onchange="npgtKabDual()"
                            class="bootstrap-select">
                            <option value="">Tahun Pengerjaan</option>
                        </select>
                        <!-- <select style="align-selt:center;width:300px;float:right!important" name="kabkotSelectYearS" id="kabSelect" onchange="npgtKabDual()" class="bootstrap-select">
			<option valnullue="">Pilih Layer</option>
			<option value="spatial.v_d_nkab_sesuai">Kesesuaian Penggunaan Tanah Terhadap RTRW (N)</option>
			<option value="spatial.v_d_nkab_tersedia">Ketersediaan Tanah (V)</option>
			</select> -->

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kabSelectProvDual" id="kabSelectProvDual" onchange="npgtKabDual()"
                            class="bootstrap-select">
                            <option value="">Pilih Provinsi</option>
                        </select>
                        &nbsp;


                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="npgtKabkotaStack"></div>
                        </figure>
                    </div>


                </div>

            </div>
            <!-- =========================================================================== -->
            <div class="card" id="divnkec" style="background-color: white;:20x">
                <h2 style="margin-top:20px">
                    <center> Neraca Penatagunaan Tanah Kecamatan</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">
                    <div class="col-md-12">
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kecSelectYear" id="kecSelectYear" onchange="npgtKecChange()" class="bootstrap-select">
                            <option value="">Tahun Pengerjaan</option>
                        </select>
                        <!-- <select style="align-selt:center;width:300px;float:right!important" name="kecSelect" id="kecSelect" onchange="npgtKecChange()" class="bootstrap-select">
				<option value="null">Pilih Layer</option>;
				<?php //foreach ($npgtKec as $key => $value) {
					//if($value->v_dasboard!=''){
						// echo '<option value="kab=spatial.v_d_npgt_kabkot_a.kec=spatial.v_d_npgt_kec_a">'.$value->nama_layer.'</option>';
						//echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
						
						// echo $value->v_dasboard.'/';
					//}
				//}?>	

			</select> -->

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kecSelectKec" id="kecSelectKec" onchange="npgtKecChange()" class="bootstrap-select"
                            data-live-search="true">
                            <option value="">Pilih Kecamatan</option>
                        </select>
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kecSelectKab" id="kecSelectKab" onchange="npgtKec()" class="bootstrap-select"
                            data-live-search="true">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                        <div class="noprov" style="float:right!important;">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                name="kecSelectProv" id="kecSelectProv" onchange="npgtKecProv()"
                                class="bootstrap-select" data-live-search="true">
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>

                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="npgtKec"></div>
                        </figure>
                    </div>
                </div>

            </div>

            <div class="card" id="divnkec" style="background-color: white;:20x;display:none">
                <h2 style="margin-top:20px">
                    <center> Hasil Analisis Penatagunaan Tanah Kecamatan</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">
                    <div class="col-md-12" style="padding:10px">

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kecSelectYearDual" id="kecSelectYearDual" onchange="npgtKecDual()"
                            class="bootstrap-select">
                            <option value="">Tahun Pengerjaan</option>
                        </select>

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kecSelectKabDual" id="kecSelectKabDual" onchange="npgtKecDual()"
                            class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="kecSelectProvDual" id="kecSelectProvDual" class="bootstrap-select"
                            data-live-search="true">
                            <option value="">Pilih Provinsi</option>
                        </select>
                        &nbsp;


                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="npgtKecStack"></div>
                        </figure>
                    </div>

                </div>

            </div>

            <!-- =========================================================================== -->

            <!-- <div style="background-color: white;:20x" >
			<h2 style="margin-top:20px"><center> Neraca Penatagunaan Tanah Perkebunan</center></h2>
				<div class="row">
					<div class="col-md-12">
						<select style="align-selt:center;width:300px;float:right!important" name="kebunSelectYear" id="kebunSelectTahun" onchange="npgtKebunChangeTahun()" class="bootstrap-select">
						</select>
					</div>
					<div class="col-md-12">
						<figure class="highcharts-figure">
							<div id="npgtKebun"></div>
						</figure>
					</div>
				</div>
				
			</div> -->
            <!-- =========================================================================== -->



            <div class="card" id="divnkeb" style="background-color: white;:20x;display:none">
                <h2 style="margin-top:20px">
                    <center> Neraca Penatagunaan Tanah Perkebunan </center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">
                    <div class="col-md-12" style="padding:10px">

                        &nbsp;
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="perkebunanSelectTahun" id="perkebunanSelectTahun" onchange="npgtPerkebunan()"
                            class="bootstrap-select">
                            <option value="">Tahun Pengerjaan</option>

                        </select>

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            onchange="npgtPerkebunanKab()" name="kebunSelectKab" id="kebunSelectKab"
                            class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                        <div class="noprov" style="float:right!important;">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="npgtPerkebunanKab($(this).val())" name="kebunSelectProv" id="kebunSelectProv"
                                class="bootstrap-select" data-live-search="true">
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="npgtPerkebunan"></div>
                        </figure>
                    </div>
                </div>

            </div>



            <div class="card" id="divnhkeb" style="background-color: white;:20x">
                <h2 style="margin-top:20px">
                    <center> Hasil Analisis Neraca Penatagunaan Tanah Perkebunan</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">
                    <div class="col-md-12" style="padding:10px">

                        &nbsp;
                        <select style="align-selt:center;width:300px;margin-right:20px!important"
                            onchange="npgtPerkebunanDual()" name="kebunSelectJnsDual" id="kebunSelectJnsDual"
                            class="bootstrap-select" data-live-search="true">
                            <option value="kosong">PIlih Jenis</option>
                            <option value="spatial.v_d_anls_kebun_kssn">Kesesuaian</option>
                            <option value="spatial.v_d_anls_kebun_ktsdn">Ketersediaan</option>
                            <option value="spatial.v_d_anls_kebun_ocr">Rekomendasi</option>
                        </select>
                        <select style="align-selt:center;width:300px;margin-right:20px!important"
                            name="perkebunanSelectDual" id="perkebunanSelectTahunDual" onchange="npgtPerkebunanDual()"
                            class="bootstrap-select">
                            <!-- <option value="">Pilih tahun</option> -->
                            <?php //echo $tahunNpgtPerkebunan?>
                            <option value="">Tahun Pengerjaan</option>

                        </select>

                        <select style="align-selt:center;width:300px;margin-right:20px!important"
                            onchange="npgtPerkebunanDual()" name="kebunSelectKabDual" id="kebunSelectKabDual"
                            class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                        <div class="noprov" style="float:right!important;">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="npgtPerkebunanDualKab()" name="kebunSelectProvDual" id="kebunSelectProvDual"
                                class="bootstrap-select" data-live-search="true">
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="npgtPerkebunanStack"></div>
                        </figure>
                    </div>
                </div>

            </div>

            <div class="card" id="divptp" style="background-color: white:20x;display:none">
                <h2 style="margin-top:20px">
                    <center>Standart Biaya per Layanan PTP</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">

                    <div class="col-md-12">
                        <select style="align-selt:center;width:500px;float:right!important" name="ptpSelectOld"
                            id="ptpSelectOld" onchange="ptpChangeMonitoring($(this).val())" class="bootstrap-select">
                            <option value="">Pilih Layer</option>;
                            <?php foreach ($ptp as $key => $value) {
									if($value->v_dasboard!=''){
										// echo '<option value="kab=spatial.v_d_npgt_kabkot_a.kec=spatial.v_d_npgt_kec_a">'.$value->nama_layer.'</option>';
										echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
										
										// echo $value->v_dasboard.'/';
									}
							}?>

                        </select>
                        <!-- <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
							onchange="ptpChangeMonitoringInterop()" name="ptpSelectKabInterop" id="ptpSelectKabInterop" class="bootstrap-select"
							data-live-search="true">
							<option value="">Pilih Kabupaten/Kota</option>
						</select>
						
						<select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
							onchange="ptpProv($(this).val())" name="ptpSelectProvInterop" id="ptpSelectProvInterop" class="bootstrap-select"
							data-live-search="true">
							<option value="">Pilih Provinsi</option>
						</select> -->
                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="ptpOld"></div>
                        </figure>
                    </div>
                </div>

            </div>
            <div class="card" id="divptpInterop" style="background-color: white:20x">
                <h2 style="margin-top:20px">
                    <center>Standart Biaya per Layanan PTP</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">

                    <div class="col-md-12">
                        <select style="align-selt:center;width:500px;float:right!important" name="ptpSelectInterop"
                            id="ptpSelectInterop" onchange="ptpChangeMonitoringInterop($(this).val())"
                            class="bootstrap-select">
                            <!-- <option value="">Pilih Layer</option>; -->
                            <?php foreach ($ptp as $key => $value) {
									if($value->v_dasboard!='' && $value->dashboard != 'spatial.v_d_ptp_berusaha' ){
										// echo '<option value="kab=spatial.v_d_npgt_kabkot_a.kec=spatial.v_d_npgt_kec_a">'.$value->nama_layer.'</option>';
										echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
										
										// echo $value->v_dasboard.'/';
									}
							}?>

                        </select>
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            onchange="ptpChangeMonitoringInterop()" name="ptpSelectKabInterop" id="ptpSelectKabInterop"
                            class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Kabupaten/Kota</option>
                        </select>

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            onchange="ptpProvInterop($(this).val())" name="ptpSelectProvInterop"
                            id="ptpSelectProvInterop" class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Provinsi</option>
                        </select>
                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="ptp"></div>
                        </figure>
                    </div>
                </div>

            </div>
            <div class="card" id="divptpInteropLayanan" style="background-color: white:20x;">
                <h2 style="margin-top:20px">
                    <center>Layanan PTP</center>
                </h2>
                <div class="row">
                    <div class="col-md-12">
                        <select style="align-selt:center;width:500px;float:right!important"
                            name="ptpInteropLayananTahun" id="ptpInteropLayananTahun"
                            onchange="ptpChangeMonitoringInteropLayanan($(this).val())" class="bootstrap-select">
                            <option value="null">Semua Tahun</option>
                            <?php
                            $y=intVal(date('Y'));

                            for ($i=$y; $i >= 2000 ; $i--) { 
                                
                                echo '<option value="'.$i.'">'.$i.'</option>';
                            }
                            ?>
                        </select>
                        <select style="align-selt:center;width:500px;float:right!important"
                            name="ptpSelectInteropLayanan" id="ptpSelectInteropLayanan"
                            onchange="ptpChangeMonitoringInteropLayanan($(this).val())" class="bootstrap-select">
                            <?php foreach ($ptp as $key => $value) {
									if($value->v_dasboard!=''){
										echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
									}
							}?>
                        </select>
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            onchange="ptpChangeMonitoringInteropLayanan()" name="ptpSelectKabInteropLayanan"
                            id="ptpSelectKabInteropLayanan" class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Kabupaten/Kota</option>
                        </select>

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            onchange="ptpProvInteropLayanan($(this).val())" name="ptpSelectProvInteropLayanan"
                            id="ptpSelectProvInteropLayanan" class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Provinsi</option>
                        </select>

                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="ptpInterop"></div>
                        </figure>
                    </div>
                </div>

                <!-- </div> -->
                <div class="card" id="divptp" style="background-color: white:20x;">
                    <h2 style="margin-top:20px">
                        <center>Layanan PTP</center>
                    </h2>
                    <div class="row">
                        <div class="col-md-12">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="ptpChange()" name="ptpSelectYear" id="ptpSelectYear" class="bootstrap-select"
                                data-live-search="true">
                                <option value="">Tahun Pengerjaan</option>
                            </select>
                            <select style="align-selt:center;width:500px;float:right!important" name="ptpSelect"
                                id="ptpSelect" onchange="ptpChangeLayanan($(this).val())" class="bootstrap-select">
                                <?php foreach ($ptp as $key => $value) {
									if($value->v_dasboard!=''){
										echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
									}
							}?>
                            </select>
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="ptpChangeLayanan()" name="ptpSelectKab" id="ptpSelectKab"
                                class="bootstrap-select" data-live-search="true">
                                <option value="">Pilih Kabupaten/Kota</option>
                            </select>


                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="ptpChangeKab($(this).val())" name="ptpSelectProv" id="ptpSelectProv"
                                class="bootstrap-select" data-live-search="true">
                                <option value="">Pilih Provinsi</option>
                            </select>

                        </div>
                        <div class="col-md-12">
                            <figure class="highcharts-figure">
                                <div id="ptpInteropLayanan"></div>
                            </figure>
                        </div>
                    </div>

                </div>
            </div>
            <!-- =========================================================================== -->
            <div class="card" id="divwp3wt" style="background-color: white;">
                <h2 style="margin-top:20px">
                    <center> Penataan WP3WT</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">
                    <div class="col-md-12">
                        <!-- <div class="col-md-"></div> -->


                        <div class="" id="divWp3wtYear">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                name="wp3wtSelectYear" id="wp3wtSelectYear" onchange="wp3wtChange()"
                                class="bootstrap-select">
                                <option value="">Tahun Pengerjaan</option>
                            </select>
                        </div>

                        <div class=" " id="divWp3wtKec" style="display:none">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                name="wp3wtSelectKec" id="wp3wtSelectKec" onchange="wp3wtChangeKec()"
                                class="bootstrap-select">
                                <option value="">Pilih Kecamatan</option>
                            </select>
                        </div>


                        <div class=" " id="divWp3wtKab" style="">
                            <select style="align-selt:center;float:right!important;margin-right:20px!important"
                                name="wp3wtSelectKab" id="wp3wtSelectKab" onchange="wp3wtChangeIf()"
                                class="bootstrap-select">
                                <option value="">Pilih Kabupaten</option>
                            </select>
                        </div>

                        <div class=" " id="divWp3wtProv" style="">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                name="wp3wtSelectProv" id="wp3wtSelectProv" onchange="wp3wtChangeProv()"
                                class="bootstrap-select">
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>

                        <div class=" " id="divWp3wtPxn">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="wp3wtChangeLayer()" name="wp3wtSelectPxn" id="wp3wtSelectPxn"
                                class="bootstrap-select" data-live-search="true">
                                <option value="null">Pilih Jenis Penggunaan</option>
                                <!-- <option value="pfn">PFN</option> -->
                                <!-- <option value="pmn">PMN</option> -->
                                <option value="psn">Penguasaan (O)</option>
                                <option value="ptn">Penggunaan (Q)</option>
                            </select>
                        </div>
                        <div class="" id="divWp3wtLayer" style="margin-left:0px">
                            <select style="" name="wp3wtSelect" id="wp3wtSelect" onchange="wp3wtChangeLayer()"
                                class="bootstrap-select">
                                <option value="null">Pilih Layer</option>;
                                <?php foreach ($wp3wt as $key => $value) {
							if($value->v_dasboard!=''){
								// echo '<option value="kab=spatial.v_d_npgt_kabkot_a.kec=spatial.v_d_npgt_kec_a">'.$value->nama_layer.'</option>';
								echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
								
								// echo $value->v_dasboard.'/';
							}
						}?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">

                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="wp3wt"></div>
                        </figure>
                    </div>
                </div>

            </div>

            <!-- =============================================================================== -->
            <div class="card" id="divtnbh" style="background-color: white;:20x">
                <h2 style="margin-top:20px">
                    <center>Tanah Negara Bekas Hak/ Bekas Kawasan/Tanah Kritis</center>
                </h2>
                <div class="row">

                    <div class="col-md-3">
                        <select style="align-selt:center;width:300px;" name="tanahNegaraSelect" id="tanahNegaraSelect"
                            onchange="tanahNegaraChangeProv($(this).val())" class="bootstrap-select">
                            <option value="null">Pilih Layer</option>;

                            <?php foreach ($tanahNegara as $key => $value) {
					

							if($value->v_dasboard!=''){
								
								echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
							}
						}?>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <select style="align-selt:center;width:300px;;margin-right:20px!important" name="tnSelectProv"
                            id="tnSelectProv" onchange="tanahNegaraChangeKab($(this).val())" class="bootstrap-select">
                            <option value="">Pilih Provinsi</option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <select style="align-selt:center;width:300px;;margin-right:20px!important" name="tnSelectKab"
                            id="tnSelectKab" onchange="tanahNegaraChange()" class="bootstrap-select">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select style="align-selt:center;width:300px;" name="tnSelecttahun" id="tnSelectTahun"
                            onchange="tanahNegaraChange()" class="bootstrap-select">
                            <option value="">Tahun Pengerjaan</option>
                        </select>
                    </div>

                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="tanahNegara"></div>
                        </figure>
                    </div>
                </div>

            </div>
            <!-- =============================================================================== -->
            <div class="card" id="divmppt" style="background-color: white;:20x;">
                <h2 style="margin-top:20px">
                    <center> Monitoring Perubahan Penggunaan Tanah</center>
                </h2>
                <div class="row">
                    <div class="col-md-12">
                        <select style="align-selt:center;width:300px;float:right!important" name="mpptSelectYear"
                            id="mpptSelectTahun" onchange="mpptChange()" class="bootstrap-select">
                            <option value="">Tahun Pengerjaan</option>
                            <!-- <option value="2019">Semua Tahun</option> -->
                            <?php echo $tahunMppt ?>

                        </select>
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="mpptSelectKab" id="mpptSelectKab" onchange="mpptChange()" class="bootstrap-select"
                            data-live-search="true">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                        <div class="noprov" style="float:right!important;">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                name="mpptSelectProv" id="mpptSelectProv" onchange="mpptChangeKab($(this).val())"
                                class="bootstrap-select" data-live-search="true">
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="mppt"></div>
                        </figure>
                    </div>
                </div>

            </div>
            <!-- =============================================================================== -->

            <div class="card" id="divkt" style="background-color: white;:20x">
                <h2 style="margin-top:20px">
                    <center> Kemampuan Tanah </center>
                </h2>
                <div class="row">
                    <div class="col-md-12">

                        <select style="align-selt:center;width:300px;float:right!important" name="kemampuanSelectYear"
                            id="kemampuanSelectTahun" onchange="kemampuanTanahChange()" class="bootstrap-select">
                            <!-- <option value="">2021</option>		 -->
                            <?php echo $tahunKemampuan;
						if(empty($tahunKemampuan)){
							
							echo '<option value="">Tahun Pengerjaan</option>';
						}
					?>

                        </select>
                        <!-- <select style="align-selt:center;width:300px;float:right!important" name="tanahNegaraSelect" id="kemampuanTanahSelect" onchange="kemampuanTanahChange()" class="bootstrap-select">
					<option value="null">Pilih Layer</option>;
					<option value="b_name">Kemiringan Lereng</option>
					<option value="u_name">Kedalaman Efektif</option>
					<option value="x_name">Tekstur Tanah</option>
					<option value="e_name">Erosi</option>
					<option value="d_name">Drainase</option>
					
				</select> -->

                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            name="ktSelectKab" id="ktSelectKab" onchange="kemampuanTanahChange()"
                            class="bootstrap-select" data-live-search="true">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                        <div class="noprov" style="float:right!important;">

                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                name="ktSelectProv" id="ktSelectProv" class="bootstrap-select"
                                onchange="kemampuanTanahChangeKab()" data-live-search="true">
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="kemampuanTanah"></div>
                        </figure>
                    </div>
                </div>

            </div>

            <!-- =========================================================================== -->

            <div class="card" id="divpgtl" style="background-color: white;:20x,display:none">
                <h2 style="margin-top:20px">
                    <center> Penatagunaan Tanah Lainnya</center>
                </h2>
                <!-- <span>Pilih Layer : </span> -->
                <div class="row">

                    <!-- <select style="align-selt:center;width:300px;float:right!important" name="pgtlSelectYear" id="pgtlSelectTahun" onchange="pgtlChange()" class="bootstrap-select"> -->
                    <!-- </select> -->

                    <div class="col-md-12">
                        <!-- <div class="col-md-2"></div> -->

                        <div id="divPgtlYear">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                name="pgtlSelectYear" id="pgtlSelectYear" onchange="pgtlChange()"
                                class="bootstrap-select">
                                <option value="">Tahun Pengerjaan</option>
                            </select>
                        </div>

                        <div id="divPgtlKec">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="pgtlChangeKec()" name="pgtlSelectKec" id="pgtlSelectKec"
                                class="bootstrap-select" data-live-search="true">
                                <option value="">Pilih Kecamatan</option>
                            </select>
                        </div>

                        <div id="divPgtlKab">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="pgtlChange()" name="pgtlSelectKab" id="pgtlSelectKab" class="bootstrap-select"
                                data-live-search="true">
                                <option value="">Pilih Kabupaten</option>
                            </select>
                        </div>
                        <div class="noprov" style="float:right!important;">

                            <div id="divPgtlProv">
                                <select
                                    style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                    onchange="pgtlChangeProv()" name="pgtlSelectProv" id="pgtlSelectProv"
                                    class="bootstrap-select" data-live-search="true">
                                    <option value="">Pilih Provinsi</option>
                                </select>
                            </div>
                        </div>
                        <div id="divPgtlLayer">

                            <select style="align-selt:center;width:300px;float:right!important" name="pgtlSelect"
                                id="pgtlSelect" onchange="pgtlChangeLayer($(this).val())" class="bootstrap-select	">
                                <option value="null">Pilih Layer</option>;
                                <?php foreach ($pgtl as $key => $value) {
						if($value->v_dasboard!=''){
							// echo '<option value="kab=spatial.v_d_npgt_kabkot_a.kec=spatial.v_d_npgt_kec_a">'.$value->nama_layer.'</option>';
							echo '<option value="'.$value->v_dasboard.'">'.$value->nama_layer.'</option>';
							
							// echo $value->v_dasboard.'/';
						}
					}?>

                            </select>
                        </div>

                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="pgtl"></div>
                        </figure>
                    </div>
                </div>

            </div>



            <!-- =============================================================================== -->
            <div class="card" id="divlbs" style="background-color: white;:20x">
                <h2 style="margin-top:20px">
                    <center> Lahan Baku Sawah </center>
                </h2>
                <div class="row">
                    <div class="col-md-12">
                        <select style="align-selt:center;width:300px;float:right!important" name="lahanBakuSelectYear"
                            id="lahanBakuSelectTahun" onchange="lahanBaku()" class="bootstrap-select">
                            <!-- <option value="">2019</option> -->

                        </select>
                        <select style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                            onchange="lahanBaku()" name="lbsSelectKab" id="lbsSelectKab" class="bootstrap-select"
                            data-live-search="true">
                            <option value="">Pilih Kabupaten</option>
                        </select>
                        <div class="noprov" style="float:right!important;">
                            <select
                                style="align-selt:center;width:300px;float:right!important;margin-right:20px!important"
                                onchange="lahanBakuProv($(this).val())" name="lbsSelectProv" id="lbsSelectProv"
                                class="bootstrap-select" data-live-search="true">
                                <option value="">Pilih Provinsi</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <figure class="highcharts-figure">
                            <div id="lahanBaku"></div>
                        </figure>
                    </div>
                </div>

            </div>

            <!-- ======================================================================================= -->
            <!-- task, page, download counter  start -->
            <!-- <div class="col-xl-3 col-md-6">
			<div class="card bg-c-yellow update-card">
				<div class="card-block">
					<div class="row align-items-end">
						<div class="col-8">
							<h4 class="text-white">81</h4>
							<h6 class="text-white m-b-0">Ruas PPJT</h6>
						</div>
						<div class="col-4 text-end">
							<canvas id="update-chart-1" height="50"></canvas>
						</div>
					</div>
				</div>
				<div class="card-footer">
					<p class="text-white m-b-0"><i
							class="feather icon-clock text-white f-14 m-r-10"></i>update
						: 2:15 am</p>
				</div>
			</div>
		</div>
		<div class="col-xl-3 col-md-6">
			<div class="card bg-c-green update-card">
				<div class="card-block">
					<div class="row align-items-end">
						<div class="col-8">
							<h4 class="text-white">70</h4>
							<h6 class="text-white m-b-0">BUJT</h6>
						</div>
						<div class="col-4 text-end">
							<canvas id="update-chart-2" height="50"></canvas>
						</div>
					</div>
				</div>
				<div class="card-footer">
					<p class="text-white m-b-0"><i
							class="feather icon-clock text-white f-14 m-r-10"></i>update
						: 2:15 am</p>
				</div>
			</div>
		</div>
		<div class="col-xl-3 col-md-6">
			<div class="card bg-c-pink update-card">
				<div class="card-block">
					<div class="row align-items-end">
						<div class="col-8">
							<h4 class="text-white">1000 km</h4>
							<h6 class="text-white m-b-0">Panjang Tol</h6>
						</div>
						<div class="col-4 text-end">
							<canvas id="update-chart-3" height="50"></canvas>
						</div>
					</div>
				</div>
				<div class="card-footer">
					<p class="text-white m-b-0"><i
							class="feather icon-clock text-white f-14 m-r-10"></i>update
						: 2:15 am</p>
				</div>
			</div>
		</div>
		<div class="col-xl-3 col-md-6">
			<div class="card bg-c-lite-green update-card">
				<div class="card-block">
					<div class="row align-items-end">
						<div class="col-8">
							<h4 class="text-white"></h4>
							<h6 class="text-white m-b-0"></h6>
						</div>
						<div class="col-4 text-end">
							<canvas id="update-chart-4" height="50"></canvas>
						</div>
					</div>
				</div>
				<div class="card-footer">
					<p class="text-white m-b-0"><i
							class="feather icon-clock text-white f-14 m-r-10"></i>update
						: 2:15 am</p>
				</div>
			</div>
		</div> -->
            <!-- task, page, download counter  end -->
            <!-- sale start -->
            <!-- <div class="col-xl-12 col-md-12">
			<div class="card table-card">
				<div class="card-header">
					<h5>Ruas</h5>
				</div>
				<div class="card-block">
					<div class="table-responsive">
						<table class="table table-hover table-borderless">
							<thead>
								<tr>
									<th>#</th>
									<th>Country</th>
									<th>Sales</th>
									<th class="text-end">Average</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td><img src="../files/assets/images/widget/GERMANY.jpg"
											alt="" class="img-fluid img-30"></td>
									<td>Germany</td>
									<td>3,562</td>
									<td class="text-end">56.23%</td>
								</tr>
								<tr>
									<td><img src="../files/assets/images/widget/USA.jpg"
											alt="" class="img-fluid img-30"></td>
									<td>USA</td>
									<td>2,650</td>
									<td class="text-end">25.23%</td>
								</tr>
								<tr>
									<td><img src="../files/assets/images/widget/AUSTRALIA.jpg"
											alt="" class="img-fluid img-30"></td>
									<td>Australia</td>
									<td>956</td>
									<td class="text-end">12.45%</td>
								</tr>
								<tr>
									<td><img src="../files/assets/images/widget/UK.jpg"
											alt="" class="img-fluid img-30"></td>
									<td>United Kingdom</td>
									<td>689</td>
									<td class="text-end">8.65%</td>
								</tr>
								<tr>
									<td><img src="../files/assets/images/widget/BRAZIL.jpg"
											alt="" class="img-fluid img-30"></td>
									<td>Brazil</td>
									<td>560</td>
									<td class="text-end">3.56%</td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="text-end  m-r-20">
						<a href="#!" class="b-b-primary text-primary">View all Sales
							Locations </a>
					</div>
				</div>
			</div>
		</div> -->

            <!--  sale analytics start -->
            <!-- <div class="col-xl-12 col-md-12">
			<div class="card">
				<div class="card-header">
					<h5>Financial Close</h5>
					<span class="text-muted"></span>
					<div class="card-header-right">
						<ul class="list-unstyled card-option">
							<li><i class="feather icon-maximize full-card"></i></li>
							<li><i class="feather icon-minus minimize-card"></i>
							</li>
							
						</ul>
					</div>
				</div>
				<div class="card-block">
					<div id="container" style="height: 265px;"></div>
				</div>
			</div>
		</div> -->


            <!-- <div class="col-xl-3 col-md-12">
			<div class="card user-card2">
				<div class="card-block text-center">
					<h6 class="m-b-15">Project Risk</h6>
					<div class="risk-rate">
						<span><b>5</b></span>
					</div>
					<h6 class="m-b-10 m-t-10">Balanced</h6>
					<a href="#!" class="text-c-yellow b-b-warning">Change Your
						Risk</a>
					<div
						class="row justify-content-center m-t-10 b-t-default m-l-0 m-r-0">
						<div class="col m-t-15 b-r-default">
							<h6 class="text-muted">Nr</h6>
							<h6>AWS 2455</h6>
						</div>
						<div class="col m-t-15">
							<h6 class="text-muted">Created</h6>
							<h6>30th Sep</h6>
						</div>
					</div>
				</div>
				<div class="d-grid">
					<button class="btn btn-warning p-t-15 p-b-15">
						Download Overall Report
					</button>
				</div>
			</div>
		</div> -->
            <!--  sale analytics end -->

            <!-- <div class="col-xl-8 col-md-12">
			<div class="card table-card">
				<div class="card-header">
					<h5>Application Sales</h5>
					<div class="card-header-right">
						<ul class="list-unstyled card-option">
							<li><i class="feather icon-maximize full-card"></i></li>
							<li><i class="feather icon-minus minimize-card"></i>
							</li>
							<li><i class="feather icon-trash-2 close-card"></i></li>
						</ul>
					</div>
				</div>
				<div class="card-block">
					<div class="table-responsive">
						<table class="table table-hover  table-borderless">
							<thead>
								<tr>
									<th>
										<div class="chk-option">
											<div
												class="checkbox-fade fade-in-primary">
												<label class="form-label check-task">
													<input type="checkbox" value="">
													<span class="cr">
														<i class="cr-icon feather icon-check txt-default"></i>
													</span>
												</label>
											</div>
										</div>
										Application
									</th>
									<th>Sales</th>
									<th>Change</th>
									<th>Avg Price</th>
									<th>Total</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>
										<div class="chk-option">
											<div
												class="checkbox-fade fade-in-primary">
												<label class="form-label check-task">
													<input type="checkbox" value="">
													<span class="cr">
														<i
															class="cr-icon feather icon-check txt-default"></i>
													</span>
												</label>
											</div>
										</div>
										<div class="d-inline-block align-middle">
											<h6>Able Pro</h6>
											<p class="text-muted m-b-0">Powerful
												Admin Theme</p>
										</div>
									</td>
									<td>16,300</td>
									<td><canvas id="app-sale1" height="50"
											width="100"></canvas></td>
									<td>$53</td>
									<td class="text-c-blue">$15,652</td>
								</tr>
								<tr>
									<td>
										<div class="chk-option">
											<div
												class="checkbox-fade fade-in-primary">
												<label class="form-label check-task">
													<input type="checkbox" value="">
													<span class="cr">
														<i
															class="cr-icon feather icon-check txt-default"></i>
													</span>
												</label>
											</div>
										</div>
										<div class="d-inline-block align-middle">
											<h6>Photoshop</h6>
											<p class="text-muted m-b-0">Design
												Software</p>
										</div>
									</td>
									<td>26,421</td>
									<td><canvas id="app-sale2" height="50"
											width="100"></canvas></td>
									<td>$35</td>
									<td class="text-c-blue">$18,785</td>
								</tr>
								<tr>
									<td>
										<div class="chk-option">
											<div
												class="checkbox-fade fade-in-primary">
												<label class="form-label check-task">
													<input type="checkbox" value="">
													<span class="cr">
														<i class="cr-icon feather icon-check txt-default"></i>
													</span>
												</label>
											</div>
										</div>
										<div class="d-inline-block align-middle">
											<h6>Guruable</h6>
											<p class="text-muted m-b-0">Best Admin
												Template</p>
										</div>
									</td>
									<td>8,265</td>
									<td><canvas id="app-sale3" height="50"
											width="100"></canvas></td>
									<td>$98</td>
									<td class="text-c-blue">$9,652</td>
								</tr>
								<tr>
									<td>
										<div class="chk-option">
											<div
												class="checkbox-fade fade-in-primary">
												<label class="form-label check-task">
													<input type="checkbox" value="">
													<span class="cr">
														<i class="cr-icon feather icon-check txt-default"></i>
													</span>
												</label>
											</div>
										</div>
										<div class="d-inline-block align-middle">
											<h6>Flatable</h6>
											<p class="text-muted m-b-0">Admin App
											</p>
										</div>
									</td>
									<td>10,652</td>
									<td><canvas id="app-sale4" height="50"
											width="100"></canvas></td>
									<td>$20</td>
									<td class="text-c-blue">$7,856</td>
								</tr>
							</tbody>
						</table>
						<div class="text-center">
							<a href="#!" class=" b-b-primary text-primary">View all
								Projects</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col-xl-4 col-md-12">
			<div class="card user-activity-card">
				<div class="card-header">
					<h5>User Activity</h5>
				</div>
				<div class="card-block">
					<div class="row m-b-25">
						<div class="col-auto p-r-0">
							<div class="u-img">
								<img src="../files/assets/images/breadcrumb-bg.jpg"
									alt="user image" class="img-radius cover-img">
								<img src="../files/assets/images/avatar-2.jpg"
									alt="user image" class="img-radius profile-img">
							</div>
						</div>
						<div class="col">
							<h6 class="m-b-5">John Deo</h6>
							<p class="text-muted m-b-0">Lorem Ipsum is simply dummy
								text.</p>
							<p class="text-muted m-b-0"><i
									class="feather icon-clock m-r-10"></i>2 min ago
							</p>
						</div>
					</div>
					<div class="row m-b-25">
						<div class="col-auto p-r-0">
							<div class="u-img">
								<img src="../files/assets/images/breadcrumb-bg.jpg"
									alt="user image" class="img-radius cover-img">
								<img src="../files/assets/images/avatar-2.jpg"
									alt="user image" class="img-radius profile-img">
							</div>
						</div>
						<div class="col">
							<h6 class="m-b-5">John Deo</h6>
							<p class="text-muted m-b-0">Lorem Ipsum is simply dummy
								text.</p>
							<p class="text-muted m-b-0"><i
									class="feather icon-clock m-r-10"></i>2 min ago
							</p>
						</div>
					</div>
					<div class="row m-b-25">
						<div class="col-auto p-r-0">
							<div class="u-img">
								<img src="../files/assets/images/breadcrumb-bg.jpg"
									alt="user image" class="img-radius cover-img">
								<img src="../files/assets/images/avatar-2.jpg"
									alt="user image" class="img-radius profile-img">
							</div>
						</div>
						<div class="col">
							<h6 class="m-b-5">John Deo</h6>
							<p class="text-muted m-b-0">Lorem Ipsum is simply dummy
								text.</p>
							<p class="text-muted m-b-0"><i
									class="feather icon-clock m-r-10"></i>2 min ago
							</p>
						</div>
					</div>
					<div class="row m-b-5">
						<div class="col-auto p-r-0">
							<div class="u-img">
								<img src="../files/assets/images/breadcrumb-bg.jpg"
									alt="user image" class="img-radius cover-img">
								<img src="../files/assets/images/avatar-2.jpg"
									alt="user image" class="img-radius profile-img">
							</div>
						</div>
						<div class="col">
							<h6 class="m-b-5">John Deo</h6>
							<p class="text-muted m-b-0">Lorem Ipsum is simply dummy
								text.</p>
							<p class="text-muted m-b-0"><i
									class="feather icon-clock m-r-10"></i>2 min ago
							</p>
						</div>
					</div>

					<div class="text-center">
						<a href="#!" class="b-b-primary text-primary">View all
							Projects</a>
					</div>
				</div>
			</div>
		</div> -->

            <!-- wather user -->
            <!-- <div class="col-xl-6 col-md-12">
			<div class="card latest-update-card">
				<div class="card-header">
					<h5>Latest Updates</h5>
					<div class="card-header-right">
						<ul class="list-unstyled card-option">
							<li><i class="fa fa fa-wrench open-card-option"></i>
							</li>
							<li><i class="fa fa-window-maximize full-card"></i></li>
							<li><i class="fa fa-minus minimize-card"></i></li>
							<li><i class="fa fa-refresh reload-card"></i></li>
							<li><i class="fa fa-trash close-card"></i></li>
						</ul>
					</div>
				</div>
				<div class="card-block">
					<div class="latest-update-box">
						<div class="row p-b-15">
							<div class="col-auto text-end update-meta">
								<p class="text-muted m-b-0 d-inline">4 hrs ago</p>
								<i
									class="feather icon-briefcase bg-simple-c-pink update-icon"></i>
							</div>
							<div class="col">
								<h6>+ 5 New Products were added!</h6>
								<p class="text-muted m-b-0">Congratulations!</p>
							</div>
						</div>
						<div class="row p-b-15">
							<div class="col-auto text-end update-meta">
								<p class="text-muted m-b-0 d-inline">1 day ago</p>
								<i
									class="feather icon-check bg-simple-c-yellow  update-icon"></i>
							</div>
							<div class="col">
								<h6>Database backup completed!</h6>
								<p class="text-muted m-b-0">Download the <span
										class="text-c-blue">latest backup</span>.
								</p>
							</div>
						</div>
						<div class="row p-b-0">
							<div class="col-auto text-end update-meta">
								<p class="text-muted m-b-0 d-inline">2 day ago</p>
								<i
									class="feather icon-facebook bg-simple-c-green update-icon"></i>
							</div>
							<div class="col">
								<h6>+1 Friend Requests</h6>
								<p class="text-muted m-b-10">This is great, keep it
									up!</p>
								<div class="table-responsive">
									<table class="table table-hover m-b-0">
										<tbody>
											<tr>
												<td class="b-none">
													<a href="#!"
														class="align-middle">
														<img src="../files/assets/images/avatar-2.jpg"
															alt="user image"
															class="img-radius img-40 align-top m-r-15">
														<div class="d-inline-block">
															<h6>Jeny William</h6>
															<p
																class="text-muted m-b-0">
																Graphic Designer</p>
														</div>
													</a>
												</td>
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div class="text-center">
						<a href="#!" class="b-b-primary text-primary">View all
							Projects</a>
					</div>
				</div>
			</div>
		</div>

		<div class="col-xl-6 col-md-12">
			<div class="card user-card-full">
				<div class="row m-l-0 m-r-0">
					<div class="col-sm-4 bg-c-lite-green user-profile">
						<div class="card-block text-center text-white">
							<div class="m-b-25">
								<img src="../files/assets/images/avatar-4.jpg"
									class="img-radius" alt="User-Profile-Image">
							</div>
							<h6 class="f-w-600">Jeny William</h6>
							<p>Web Designer</p>
							<i class="feather icon-edit m-t-10 f-16"></i>
						</div>
					</div>
					<div class="col-sm-8">
						<div class="card-block">
							<h6 class="m-b-20 p-b-5 b-b-default f-w-600">Information
							</h6>
							<div class="row">
								<div class="col-sm-6">
									<p class="m-b-10 f-w-600">Email</p>
									<h6 class="text-muted f-w-400"><EMAIL>
									</h6>
								</div>
								<div class="col-sm-6">
									<p class="m-b-10 f-w-600">Phone</p>
									<h6 class="text-muted f-w-400">0023-333-526136
									</h6>
								</div>
							</div>
							<h6 class="m-b-20 m-t-40 p-b-5 b-b-default f-w-600">
								Projects</h6>
							<div class="row">
								<div class="col-sm-6">
									<p class="m-b-10 f-w-600">Recent</p>
									<h6 class="text-muted f-w-400">Guruable Admin
									</h6>
								</div>
								<div class="col-sm-6">
									<p class="m-b-10 f-w-600">Most Viewed</p>
									<h6 class="text-muted f-w-400">Able Pro Admin
									</h6>
								</div>
							</div>
							<ul class="social-link list-unstyled m-t-40 m-b-10">
								<li><a href="#!" data-bs-toggle="tooltip"
										data-bs-placement="bottom" title=""
										data-original-title="facebook"><i
											class="feather icon-facebook facebook"
											aria-hidden="true"></i></a></li>
								<li><a href="#!" data-bs-toggle="tooltip"
										data-bs-placement="bottom" title=""
										data-original-title="twitter"><i
											class="feather icon-twitter twitter"
											aria-hidden="true"></i></a></li>
								<li><a href="#!" data-bs-toggle="tooltip"
										data-bs-placement="bottom" title=""
										data-original-title="instagram"><i
											class="feather icon-instagram instagram"
											aria-hidden="true"></i></a></li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div> -->
            <!-- wather user -->

            <!-- social download  start -->
            <!-- <div class="col-xl-4 col-md-6">
			<div class="card social-card bg-simple-c-blue">
				<div class="card-block">
					<div class="row align-items-center">
						<div class="col-auto">
							<i
								class="feather icon-mail f-34 text-c-blue social-icon"></i>
						</div>
						<div class="col">
							<h6 class="m-b-0">Mail</h6>
							<p>231.2w downloads</p>
							<p class="m-b-0">Lorem Ipsum is simply dummy text of the
								printing</p>
						</div>
					</div>
				</div>
				<a href="#!" class="download-icon"><i
						class="feather icon-arrow-down"></i></a>
			</div>
		</div>
		<div class="col-xl-4 col-md-6">
			<div class="card social-card bg-simple-c-pink">
				<div class="card-block">
					<div class="row align-items-center">
						<div class="col-auto">
							<i
								class="feather icon-twitter f-34 text-c-pink social-icon"></i>
						</div>
						<div class="col">
							<h6 class="m-b-0">twitter</h6>
							<p>231.2w downloads</p>
							<p class="m-b-0">Lorem Ipsum is simply dummy text of the
								printing</p>
						</div>
					</div>
				</div>
				<a href="#!" class="download-icon"><i
						class="feather icon-arrow-down"></i></a>
			</div>
		</div>
		<div class="col-xl-4 col-md-12">
			<div class="card social-card bg-simple-c-green">
				<div class="card-block">
					<div class="row align-items-center">
						<div class="col-auto">
							<i
								class="feather icon-instagram f-34 text-c-green social-icon"></i>
						</div>
						<div class="col">
							<h6 class="m-b-0">Instagram</h6>
							<p>231.2w downloads</p>
							<p class="m-b-0">Lorem Ipsum is simply dummy text of the
								printing</p>
						</div>
					</div>
				</div>
				<a href="#!" class="download-icon"><i
						class="feather icon-arrow-down"></i></a>
			</div>
		</div> -->
            <!-- social download  end -->
        </div>
    </div>

    <?= $js_file?>
    <?= $overlay?>

    <script type="text/javascript">
    <?php
foreach ($js as $j) {
	echo file_get_contents(APPPATH . $j);
}
?>


    // Data retrieved from https://gs.statcounter.com/browser-market-share#monthly-202201-202201-bar
    </script>