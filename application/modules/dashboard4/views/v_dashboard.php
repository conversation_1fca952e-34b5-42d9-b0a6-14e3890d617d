<html>

    <head>
        <!-- Standard Meta -->
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
        <link rel="image_src" type="image/jpeg" href="/images/logo.png" />
        <link rel="icon" href="img/favicon.ico" type="image/x-icon" />
        <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon" />
        <!-- Site Properities -->
        <meta name="generator" content="Visual Studio 2015" />
        <title>Charts - v2 | Golgi Admin</title>
        <meta name="description" content="Golgi Admin Theme" />
        <meta name="keywords" content="html5, ,semantic,ui, library, framework, javascript,jquery,admin,theme" />
        <!-- <link href="plugins/chartist/chartist.min.css" rel="stylesheet" /> -->
        <link href="<?php echo base_url() . 'assets/semantic/dist/semantic.css'; ?>" rel="stylesheet" />
        <link href="<?php echo base_url() . 'assets/semantic/css/main.min.css'; ?>" rel="stylesheet" />
        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/css/bootstrap.min.css">
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/css/oneui.css">
        <!--<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.19/css/jquery.dataTables.css">-->
        <!--link rel="stylesheet" id="css-main" href="<?php //echo base_url();       ?>assets/datatable/datatables.css"-->
        <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
        <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
        <!-- END Stylesheets -->
        <!--smart wizzard
        <link rel="stylesheet" id="css-main" href="<?php //echo base_url();       ?>assets/smartwizard/css/smart_wizard_theme_arrows.css">
        -->

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-tagsinput/bootstrap-tagsinput.css">
        <!--dropdown with search-->
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/css/bootselect/bootstrap-select.min.css">
        <link rel="stylesheet" id="css-main" href="<?php echo base_url(); ?>assets/css/bootselect/bootstrap-multiselect.css">
        <!--link rel="stylesheet" href="<?php echo base_url(); ?>assets/css/bootselect/ajax-bootstrap-select.css"-->

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/datatables/jquery.dataTables.min.css">
        <!--sticky table-->
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.6/css/fixedColumns.dataTables.min.css"/>

        <!--link rel="stylesheet" id="css-main" href=" https://cdn.datatables.net/1.10.19/css/dataTables.bootstrap.min.css"-->

        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datepicker/bootstrap-datepicker3.min.css">
        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/js/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.css">



        <!--webgis plugin-->
        <!--link rel="stylesheet" href="<?php echo base_url(); ?>assets/css/selectize/selectize.bootstrap3.css"-->
        <link rel="stylesheet" href="<?php echo base_url(); ?>assets/webgis/css/webspatial.css">

        <!--
         <link rel="stylesheet" id="css-main" href="<?php // echo base_url();       ?>assets/css/oneui.css">
         <link rel="stylesheet" id="css-main" href="<?php // echo base_url();       ?>assets/css/oneui.css">
        -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.2.1/themes/default/style.min.css" />
        <!--link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.7/css/select2.min.css" rel="stylesheet" /-->
        <link rel="stylesheet" href=" https://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css">
        <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/core/jquery.min.js"></script>
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/wayjs/way.js"></script>
        <style>
            .containerli {
                margin-top: 5px;
                margin-bottom: 5px;
            }

            .ui.segments>.segment {
                border: none;
            }
            .highcharts-table-caption{
                font-size: 17px;
            }
            .highcharts-data-table table{
                width: 100% !important;
                /*                border: 2px solid gray;*/
                text-align: center;
                font-size: 12px;
                box-shadow: 0px 0px 1px 1px rgb(172, 166, 166);
            }
            .highcharts-data-table table thead th{
                padding:15px !important;
                border-bottom: 1px solid gray;
                font-size: 15px !important;
            }
            .highcharts-data-table table th{
                padding:10px;
                border-bottom: 1px solid gray;
            }
            .highcharts-data-table table td{
                padding:10px;
                border-bottom: 1px solid gray;
            }
            .highcharts-axis-title{
                font-weight: bold;
                font-size: 12px;
                font-family: arial;
            }

            .ui.segments>.segment {
                border: none;
            }
            .yuh{
                float: left;
                padding: 10px;
                color: white;
                font-weight: bold;
                font-size: 15px;
            }
            .yuh1{
                float: left;
                padding: 10px;
                color: white;
                font-weight: bold;
                font-size: 15px;
            }
            #boxi{
              float: left;

              margin-left: -5px;
            }
            .form-control{
                height:30px !important;
            }
            #btnsearch{
                /* background: #4b9187;
                color: #fbfbfb;
                padding: 5px;
                border-radius: 10px;
                border-color: gr;
                font-weight: bold; */
            }
        </style>


        <link href="<?php echo base_url() . 'assets/semantic/plugins/pacejs/pace.css'; ?>" rel="stylesheet" />

    </head>



    <body id="framebody" class="admin">

        <div class="pusher">
            <div class="full height">
                <!--Load Sidebar Menu In App.js loadhtml function-->
                <!-- <div class="toc"></div> -->
                <!--Load Sidebar Menu In App.js loadhtml function-->

                <div class="article">

                    <!--Load Navbar Menu In App.js loadhtml function-->
                    <!-- <div class="navbarmenu"></div> -->
                    <!--Load Navbar Menu In App.js loadhtml function-->
                    <!--Begin Container-->

                    <div class="containerli">
                        <div class="ui equal width left aligned padded grid stackable">
                            <div class="row">
                                <div class="column">
                                    <div class="field" style="width:100%;">
                                    <form action="<?php echo base_url(); ?>dashboard4/frame" method="post">
                                      <div id="boxi" style="display:none;">
                                        <div class="yuh"><i class="fa fa-calendar"></i> Tahun</div>
											 <input type="hidden" name="<?php echo $this->security->get_csrf_token_name(); ?>" id="<?php echo $this->security->get_csrf_token_name(); ?>" value="<?php echo $this->security->get_csrf_hash(); ?>">
                                            <select id="thang" class="ui fluid dropdown" name="tah" style="width:auto;margin-left: 100px;" onchange="this.form.submit()">
                                                <?php
                                                $thang = $this->db->get('aset_r_periode')->result_array();
                                                foreach ($thang as $t) {
                                                    if (!empty($tahuns)) {
                                                        if ($t['tahun'] == $tahuns) {
                                                            $select = "selected";
                                                        } else {
                                                            $select = "";
                                                        }
                                                    } else {
                                                        if ($t['tahun'] == $this->session->konfig_tahun_ang) {
                                                            $select = "selected";
                                                        } else {
                                                            $select = "";
                                                        }
                                                    }
                                                    ?>

                                                    <option value="<?php echo $t['tahun']; ?>" <?php echo $select; ?> ><?php echo $t['tahun']; ?></option>
                                                    <?php
                                                }
                                                ?>

                                                
                                            </select>
                                          </div>
                                          <div id="boxi">
                                        <div class="yuh" style="margin-left:20px;"><i class="fa fa-calendar"></i> Provinsi</div>
											 <input type="hidden" name="<?php echo $this->security->get_csrf_token_name(); ?>" id="<?php echo $this->security->get_csrf_token_name(); ?>" value="<?php echo $this->security->get_csrf_hash(); ?>">
                                            <select id="prov" class="ui fluid dropdown" name="prov" style="width:auto;margin-left: 100px;" onchange="this.form.submit()">
                                            <option value=''>--Pilih--</option>    
                                                <?php
                                                //$prov = $this->db->get('aset_r_provinsi')->result_array();
                                                foreach ($ref_provinsi as $t) { 
                                                    if (!empty($kd_prov_s)) {
                                                        if ($t['kd_prov'] == $kd_prov_s) {
                                                            $select = "selected";
                                                        } else {
                                                            $select = "";
                                                        }
                                                    } 
                                                    ?>

                                                    <option value="<?php echo $t['kd_prov']; ?>" <?php echo $select; ?> ><?php echo $t['nama']; ?></option>
                                                    <?php
                                                }
                                                ?>

                                                
                                            </select>
                                          </div>

                 

                                            <?php 
                                                if($this->session->users['role'] !='bujt'){       
                                                    // echo $kd_prov_s."Asdasd";          
                                            ?>
                                            <div id="boxi" style="display:none;">
                                               
                                            <div class="yuh"><i class="fa fa-calendar"></i> RUAS</div>
                                          <select id="ruas" class="ui fluid dropdown" name="ruas" style="width:auto;margin-left: 100px;" onchange="this.form.submit()">
                                                  <option value=''>--Pilih--</option>
                                                  <?php
                                                $kdbujt= $this->session->users['kd_bujt'];
                                                 if($this->session->users['role'] =='bujt'){
                                                    $this->db->where('kd_bujt',$kdbujt);
                                                 }else {
                                                     if(!empty($kd_prov_s)){
                                                        $this->db->where('kd_prov',$kd_prov_s);
                                                        $kd_prov_param = $kd_prov_s;
                                                     }else {
                                                        $this->db->where(1,1);
                                                        $kd_prov_param = NULL;
                                                     }
                                                 }
                                                $ruas = $this->db->get('aset_r_ruas')->result_array();
                                                foreach ($ruas as $t) {
                                                    if (!empty($tahuns)) {
                                                        if ($t['id_ruas'] == $ruas_s) {
                                                            $select = "selected";
                                                        } else {
                                                            $select = ""; 
                                                        }
                                                    }
                                                    ?>

                                                    <option value="<?php echo $t['id_ruas']; ?>" <?php echo $select; ?> ><?php echo $t['nm_ruas']; ?></option>
                                                    <?php
                                                }
                                                ?>
                                                <?php
                                       
                                                ?>

                                                    
                                            </select>
                                            </div>
                                            <?php } ?>
                                            <!-- <div id="boxi">
                                          <div class="yuh"><i class="fa fa-calendar"></i> Jalan Tol</div>
                                          <select id="ruas" class="ui fluid dropdown" name="ruas" style="width:200px;margin-left: 100px;">
                                                    <option value=''>--Pilih--</option>
                                                <?php
                                           $this->db->order_by("nama_bujt", "asc"); 
                                           $bujt = $this->db->get('aset_r_bujt')->result_array();
                                           foreach ($bujt as $t) {
                                               if (!empty($tahuns)) {
                                                   if ($t['kd_bujt'] == $bujt_s) {
                                                       $select = "selected";
                                                   } else {
                                                       $select = "";
                                                   }
                                               }
                                               ?>
                                           

                                               <option value="<?php echo $t['kd_bujt']; ?>" <?php echo $select; ?> ><?php echo $t['nama_bujt']; ?></option>
                                               <?php
                                           }
                                                ?>

                                                
                                            </select>
                                            </div> -->
                                          <!--div id="boxi">
                                            <div class="yuh1"><i class="fa fa-calendar"></i> Tahapan</div>
                                            <form action="<?php //echo base_url(); ?>dashboard4/frame" method="post">
                                                <input type="hidden" name="<?php //echo $this->security->get_csrf_token_name(); ?>" id="<?php echo $this->security->get_csrf_token_name(); ?>" value="<?php echo $this->security->get_csrf_hash(); ?>">

                                                <select id="kdtahap" class="ui fluid dropdown" name="kdtah" style="width:auto;margin-left: 100px;">
                                                   <!-- onchange="this.form.submit()" -->
                                                  <?php
                                                //   $tahap = array("KRG", "PI", "PA", "PAA");
                                                //   foreach($tahap as $t){
                                                //     if($t=='KRG'){
                                                //       $des='Konreg';
                                                //     }
                                                //     elseif($t=='PI'){
                                                //       $des='Pagu Indikatif';
                                                //     }
                                                //     elseif($t=='PA'){
                                                //       $des='Pagu Anggaran';
                                                //     }
                                                //     elseif($t=='PAA'){
                                                //       $des='Pagu Alokasi Anggaran';
                                                //     }
                                                //   //  $tahaps='';
                                                //     if (!empty($tahaps)) {
                                                //         if ($t == $tahaps) {
                                                //             $select = "selected";
                                                //         } else {
                                                //             $select = "";
                                                //         }
                                                //     } else {
                                                //         if ($t == $this->session->konfig_kd_tahapan) {
                                                //             $select = "selected";
                                                //         } else {
                                                //             $select = "";
                                                //         }
                                                //     }
                                                  ?>
                                                  <!--option value="<?php //echo $t; ?>" <?php //echo $select; ?>><?php //echo $des; ?></option>
                                                  <?php
                                                //   }
                                                  ?>

                                                        <!-- <option value="KRG" <?php //echo $select; ?> >Konreg</option>
                                                        <option value="PI" <?php //echo $select; ?> >Pagu Indikatif</option>
                                                        <option value="PA" <?php //echo $select; ?> >Pagu Anggaran</option>
                                                        <option value="PAA" <?php //echo $select; ?> >Pagu Alokasi Anggaran</option> -->

                                                <!--/select>
                                              </div-->


                                              <!-- <div id="boxi">
                                                &nbsp;&nbsp;&nbsp;
                                                <button type="submit" name="button" id="btnsearch"><i class="search icon"></i> Lihat </button>
                                              </div> -->

                                            </form>
                                    </div>
                                    <div class="ui segments" style="margin-top: 49px;">
                                        <!--div class="ui segment">
                                            <h4 id="hjumlahusulan" class="ui horizontal divider header"><i class="tag blue icon"></i>Total: <?php// echo $totaldata; ?> usulan</h4>
                                        </div-->
                                    </div>
                                    <?php
                                     $group = $this->session->users['id_user_group_real'];

                                    //   if($group ==3)
                                    //   {

                                    //   $b="block";
                                    //   }
                                    //   else
                                    //   {

                                    //   $b="none";
                                    //   }
                                       ?>
                                      <div class="ui segments" style="display:block;">
                                        <div class="ui segment">
                                        <div class="col-md-8">  <button class='btn btn-default' id='clickbut1' onclick='clickbut(1)'>Tampilkan List Data Aset Jasa Konsesi</button>
                                            <button class='btn btn-danger' id='hidebut1' onclick='hidebut(1)' style='display:none;'>Hide List Data Aset Jasa Konsesi</button></div> 
                                            <div class="col-md-4" style="text-align:right;"> 
                                            <h5>Total Nilai PPJT adalah Rp. <?php echo number_format($total_ppjt);?></h5>
                                        </div>   
                                         
                                           <div class="col-md-12">
                                            <div class="block-content" id="tab1" style="display:none;">
                                                <!--                //tlist_paket-->
                                                <table id="tlist_paket">
                                                    <thead id="pakets">
                                                        <tr>
                                                            <th>Provinsi</th>
                                                            <th>BUJT</th>
                                                            <th>Nama Ruas</th>
                                                            <th>Nomor PPJT</th>
                                                            <th>Nilai PPJT</th>
                                                        </tr>
                                                    </thead>
                                                </table>
                                                </div>
                                         </div>
                                        <div id="container" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
                                       
                                            
                                            <!-- <div id="container1" style="min-width: 310px; height: 500px; margin: 0 auto">
                                                
                                            </div> -->
                                            
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="column">
                                    <div class="ui segments">
                                        <div class="ui segment">
                                            <div id="container2" style="min-width: 310px; height: 500px; margin: 0 auto"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="column">
                                    <div class="ui segments">
                                        <div class="ui segment">
                                            <div id="container3" style="min-width: 310px; height: 500px; margin: 0 auto"></div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="column">
                                    <div class="ui segments">
                                        <div class="ui segment">
                                            <div id="container4" style="min-width: 310px; height: 700px; margin: 0 auto"></div>
                                        </div>
                                    </div>
                                </div>
                            </div><!--

                            <div class="row">
                                <div class="column">


                                    <div class="ui segments">
                                        <div class="ui segment">
                                            <div id="container6" style="min-width: 310px; height: 700px; margin: 0 auto"></div>
                                        </div>
                                    </div>

                                </div>
                            </div>-->
                        </div>
                    </div>

                </div>
            </div>
        </div>


        <!-- <input type="hidden" id="chart_prov_labels" value="<?php //echo $chart['satker']['labels']; ?>"/> -->
        <!-- <input type="hidden" id="chart_prov_vals"   value="<?php //echo $chart['satker']['vals']; ?>"/> -->
        <!-- <input type="hidden" id="chart_prov_biaya"   value="<?php //echo $chart['satker']['biaya']; ?>"/> -->
 
        <textarea id="chart_aset" style="display:none"><?php  echo $aset_tol;  ?></textarea>
        <textarea id="chart_aset_prov"  style="display:none"><?php  echo $aset_tol_prov;  ?></textarea>

        <textarea id="chart_aset_jembatan" style="display:none"><?php  echo $aset_jembatan;  ?></textarea>
        <textarea id="chart_aset_jembatan_prov"  style="display:none"><?php  echo $aset_jembatan_prov;  ?></textarea>


        <textarea id="chart_aset_bangunan" style="display:none"><?php  echo $aset_bangunan;  ?></textarea>
        <textarea id="chart_aset_bangunan_prov"  style="display:none"><?php  echo $aset_bangunan_prov;  ?></textarea>

        <textarea id="chart_aset_peralatan" style="display:none"><?php  echo $aset_peralatan;  ?></textarea>
        <textarea id="chart_aset_peralatan_prov"  style="display:none"><?php  echo $aset_peralatan_prov;  ?></textarea>

 

        <!-- <input type="hidden" id="chart_labels_6" value="<?php //echo $chart['belanja']['labels']; ?>"/>
        <input type="hidden" id="chart_vals_6"   value="<?php //echo $chart['belanja']['vals']; ?>"/>
        <input type="hidden" id="chart_biaya_6"   value="<?php //echo $chart['belanja']['biaya']; ?>"/> -->

<!--        <input type="hidden" id="chart_labels_5" value="<?php //echo $chart['sdana']['labels'];  ?>"/>
        <input type="hidden" id="chart_vals_5"   value="<?php //echo $chart['sdana']['vals'];  ?>"/>
        <input type="hidden" id="chart_biaya_5"   value="<?php //echo $chart['sdana']['biaya'];  ?>"/>-->
        <!-- <script src="//ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js">
        </script> -->
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.3.1/jspdf.umd.min.js">
        </script>
        <script type="text/javascript" src="//cdn.rawgit.com/niklasvh/html2canvas/0.5.0-alpha2/dist/html2canvas.min.js">
        </script>
        <script src="<?php echo base_url() . 'assets/semantic/js/jquery-2.1.4.min.js'; ?>"></script>
        <script src="<?php echo base_url() . 'assets/semantic/plugins/nicescrool/jquery.nicescroll.min.js'; ?>"></script>
        <script src="<?php echo base_url() . 'assets/semantic/js/chartjs/dist/Chart.bundle.min.js'; ?>"></script>

        <!-- <script src="<?php echo base_url() . 'assets/chart/highcharts.js'; ?>"></script> -->
        <script src="https://code.highcharts.com/highcharts.js"></script>
        <script src="https://code.highcharts.com/modules/drilldown.js"></script>
        <script src="<?php echo base_url() . 'assets/chart/ex/exporting.js'; ?>"></script>
        <script src="<?php echo base_url() . 'assets/chart/ex/export-data.js'; ?>"></script>
        <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/bootselect/bootstrap-select.min.js"></script>
            <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/bootselect/bootstrap-multiselect.js"></script>
        <script type="text/javascript" src="<?php echo base_url(); ?>assets/js/bootselect/ajax-bootstrap-select.js"></script>
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/plugins/datatables/jquery.dataTables.min.js"></script>
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/pages/base_tables_datatables.js"></script>

        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js//dataTables.fixedColumns.min.js"></script>
        <script type="text/javascript" charset="utf8" src="<?php echo base_url(); ?>assets/js/datatablehide/dataTables.hideEmptyColumns.js"></script>
    
        <script src="<?php echo base_url() . 'assets/semantic/dist/semantic.min.js'; ?>"></script>
        <script data-pace-options='{ "ajax": false }' src="<?php echo base_url() . 'assets/semantic/plugins/pacejs/pace.js'; ?>"></script>
                 
        <!--here is cumstom js loader (array of js located at base_path/asset/<js path & filename>)-->
        <script type = "text/javascript">
        <?php
        foreach ($js as $j) {
            echo file_get_contents(APPPATH . $j);
        }
        ?>
        </script>    
        <script>
          
            function clickbut(a) {
                $("#tab"+a).show()
                $("#clickbut"+a).hide()
                $("#hidebut"+a).show()
                $("#tab"+a+" table").css('width','100%')
            }
            function hidebut(a) {
                $("#tab"+a).hide()
                $("#clickbut"+a).show()
                $("#hidebut"+a).hide()
                $("#tab"+a+" table").css('width','100%')
            }
            function dataTablepaket(prov) {
                var valid = true;
                var valid2 = true;

            var showcolum= null;
            var visiblee='';
var tlist_paket = $("#tlist_paket").DataTable({
        "fnRowCallback": function(nRow, aData, iDisplayIndex, iDisplayIndexFull) {

        },
        "draw": 0,
        "fixedHeader": true,
        "responsive": true,
        "processing": true,
        "serverSide": true,
        "deferRender": true,
        "destroy": true,
        "ajax": {
        url: "<?php echo base_url(); ?>dashboard4/ssp_paket",
                type: "POST",
                "iTotalRecords":  8500,
                "iTotalDisplayRecords": 5,
                "data": function (d) {
                  d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>",
                  d.prov = prov
                //   d.bujt = c,
                //   d.status = dd,
                //   d.kondisi = e,
                //   d.thang = yearnow;
                  //d.id_pak = id_pak;
                }

        },
        "aoColumnDefs": [
        { "searchable": false, "targets":showcolum },
        {
        "aTargets": [0],
                "mRender": function (data, type, full) {
                    return full[0];
                }
        },            {
        "aTargets": [1],
                "mRender": function (data, type, full) {
                        return full[1];
                }
        },            {
        "aTargets": [2],
                "mRender": function (data, type, full) {
                    return full[2];
                }
        },            {
        "aTargets": [3],
                "mRender": function (data, type, full) {
                    return full[3];
                }
        },            {
        "aTargets": [4],
                "mRender": function (data, type, full) {
                    return full[4];
                }
        }    
        ],
       // "order": [[15, "desc"]],
        "autoWidth": true,
        "columns": [
        // {"width": "200px"},
        // {"width": "200px"},
        // {"width": "200px"},
        // {"width": "120px"},
        // {"width": "100px"},
        // {"width": "100px"},
        // {"width": "100px"},
        // {"width": "180"},
        // {"width": "90"},
        // {"width": "100px"},
        // {"width": "50px"},

        ],
        "order": [[0, "desc"]],
        "pagingType": "full_numbers",
        "columnDefs": [{"orderable": true, "targets":showcolum}],
        "pageLength": 5,
        "lengthMenu": [[5, 10, 15, 20], [5, 10, 15, 20]],
        "language": {
        "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                "first": "<i class='fa fa-angle-double-left'></i>",
                        "last": "<i class='fa fa-angle-double-right'></i>",
                        "next": "<i class='fa fa-angle-right'></i>",
                        "previous": "<i class='fa fa-angle-left'></i>"
                },
                "aria": {
                "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
        }
});

tlist_paket.on('xhr', function () {
    jalanjson = tlist_paket.ajax.json();
});

}

$(document).ready(function() {
    var prov = '<?php echo $kd_prov_param;?>';
    $("#tlist_paket").addClass("table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer");
    var a=''
    dataTablepaket(prov);

    })
        </script>
    </body>

</html>
