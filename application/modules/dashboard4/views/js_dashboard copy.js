"use strict";

var dtable = null;

$(document).ready(function() {
    $("#tlist_paket").addClass(
        "table table-bordered table-striped js-dataTable-full-pagination dataTable no-footer"
    );
    // Create the chart
    //   $("#containertes").highcharts({
    //     chart: {
    //       type: "column",
    //     },
    //     title: {
    //       text: "Basic drilldown",
    //     },
    //     xAxis: {
    //       type: "category",
    //     },

    //     legend: {
    //       enabled: false,
    //     },

    //     plotOptions: {
    //       series: {
    //         borderWidth: 0,
    //         dataLabels: {
    //           enabled: true,
    //         },
    //       },
    //     },

    //     series: [
    //       {
    //         name: "<PERSON><PERSON><PERSON>",
    //         colorByPoint: true,
    //         data: [
    //           {
    //             name: "Animals",
    //             y: 5,
    //             drilldown: "animals",
    //           },
    //           {
    //             name: "Fruits",
    //             y: 2,
    //             drilldown: "fruits",
    //           },
    //           {
    //             name: "Cars",
    //             y: 4,
    //           },
    //         ],
    //       },
    //     ],
    //     drilldown: {
    //       series: [
    //         {
    //           id: "animals",
    //           data: [
    //             ["Cats", 4, "animals2"],
    //             ["Dogs", 2, "fruits2"],
    //           ],
    //           keys: ["name", "y", "drilldown"],
    //         },
    //         // {
    //         //   id: "fruits",
    //         //   data: [["Apples", 4]],
    //         // },
    //         {
    //           id: "animals2",
    //           data: [
    //             ["Cats", 4],
    //             ["Dogs", 2],
    //             ["Cows", 1],
    //             ["Sheep", 2],
    //             ["Pigs", 1],
    //           ],
    //         },
    //         {
    //           id: "fruits2",
    //           data: [
    //             ["Apples", 4],
    //             ["Oranges", 2],
    //           ],
    //         },
    //       ],
    //     },
    //   });

    // var a=''
    // dataTablepaket(a,a,a,a);

    $("body").niceScroll({
        cursorcolor: "#ddd",
        cursorwidth: 5,
        cursorborderradius: 0,
        cursorborder: 0,
        scrollspeed: 50,
        autohidemode: true,
        zindex: 9999999,
    });
    var dt_aset = JSON.parse($("#chart_aset").val());
    var dt_aset_prov = JSON.parse($("#chart_aset_prov").val());
    console.log(dt_aset);
    // var rep_aset_satu = dt_aset.replace(/"([^"]+)":/g, '$1:');
    // var rep_aset_dua = rep_aset_satu.replace(/\\+/g, '');
    // var rep_aset_tiga = rep_aset_dua.replace(/]"/g, '');
    // var dt_aset_jalan = rep_aset_tiga.substr(2,2000000000000000000000000000000000000000)
    //  alert('['+dt_aset_jalan+']')
    // console.log('['+dt_aset_jalan+']')
    // var yuhu = [dt_aset_jalan]
    // alert(dt_aset)
    // var vlabels = JSON.parse($("#chart_labels_1").val().replace(/'/g, '"'));
    // var vvals = JSON.parse($("#chart_vals_1").val().replace(/'/g, '"'));
    // //var vcolors = JSON.parse($('#chart_cols_1').val().replace(/'/g, '"'));
    // var vbiaya = JSON.parse($("#chart_biaya_1").val().replace(/'/g, '"'));
    // var vbiaya2 = JSON.parse($("#chart_biaya2_1").val().replace(/'/g, '"'));

    var colorCode = "#" + Math.floor(Math.random() * 16777215).toString(16);
    var color = [];
    for (var i = 0; i < 100; i++) {
        color.push("#" + Math.floor(Math.random() * 16777215).toString(16));
    }

    var filteredcol = color.filter(function(value, index, arr) {
        return value != "#000000";
    });

    //alert(color);

    Highcharts.setOptions({
        global: {
            useUTC: false,
        },
        lang: {
            decimalPoint: ".",
            thousandsSep: ",",
        },
        colors: filteredcol,
    });

    Highcharts.chart("container", {
        chart: {
            type: "column",
        },
        title: {
            text: "Nilai PPJT RUAS",
        },
        xAxis: {
            type: "category",
        },

        legend: {
            enabled: false,
        },

        plotOptions: {
            series: {
                borderWidth: 0,
                dataLabels: {
                    enabled: true,
                },
            },
        },

        series: [{
            name: "Jumlah Ruas",
            colorByPoint: true,
            data: dt_aset,
        }, ],
        drilldown: {
            series: dt_aset_prov,
        },
    });

    var dt_aset_jembatan = JSON.parse($("#chart_aset_jembatan").val());
    var dt_aset_jembatan_prov = JSON.parse($("#chart_aset_jembatan_prov").val());

    console.log(dt_aset_jembatan);

    Highcharts.chart("container2", {
        chart: {
            type: "column",
        },
        title: {
            text: "Jumlah Aset Jembatan",
        },
        xAxis: {
            type: "category",
        },

        legend: {
            enabled: false,
        },

        plotOptions: {
            series: {
                borderWidth: 0,
                dataLabels: {
                    enabled: true,
                },
            },
        },

        series: [{
            name: "Jumlah Ruas",
            colorByPoint: true,
            data: dt_aset_jembatan,
        }, ],
        drilldown: {
            series: dt_aset_jembatan_prov,
        },
    });


    var dt_aset_bangunan = JSON.parse($("#chart_aset_bangunan").val());
    var dt_aset_bangunan_prov = JSON.parse($("#chart_aset_bangunan_prov").val());

    console.log(dt_aset_jembatan);

    Highcharts.chart("container3", {
        chart: {
            type: "column",
        },
        title: {
            text: "Jumlah Aset Bangunan",
        },
        xAxis: {
            type: "category",
        },

        legend: {
            enabled: false,
        },

        plotOptions: {
            series: {
                borderWidth: 0,
                dataLabels: {
                    enabled: true,
                },
            },
        },

        series: [{
            name: "Jumlah Ruas",
            colorByPoint: true,
            data: dt_aset_bangunan,
        }, ],
        drilldown: {
            series: dt_aset_bangunan_prov,
        },
    });

    var dt_aset_peralatan = JSON.parse($("#chart_aset_peralatan").val());
    var dt_aset_peralatan_prov = JSON.parse($("#chart_aset_peralatan_prov").val());

    console.log(dt_aset_jembatan);

    Highcharts.chart("container4", {
        chart: {
            type: "column",
        },
        title: {
            text: "Jumlah Aset Peralatan",
        },
        xAxis: {
            type: "category",
        },

        legend: {
            enabled: false,
        },

        plotOptions: {
            series: {
                borderWidth: 0,
                dataLabels: {
                    enabled: true,
                },
            },
        },

        series: [{
            name: "Jumlah Ruas",
            colorByPoint: true,
            data: dt_aset_peralatan,
        }, ],
        drilldown: {
            series: dt_aset_peralatan_prov,
        },
    });

    // Highcharts.chart('container1', {
    //     chart: {
    //         type: 'column',
    //         zoomType: 'y'
    //     },
    //     title: {
    //         text: 'Total Aset Jasa Konsesi'
    //     },
    //     subtitle: {
    //         text: ''
    //     },
    //     xAxis: {
    //         categories: vlabels,
    //         crosshair: true
    //     },

    //     yAxis: {
    //         min: 0,
    //         labels: {
    //             formatter: function() {
    //                 return Highcharts.numberFormat(this.value, 0, ' ', ',')
    //             },
    //             style: {
    //                 color: '#666666'
    //             }
    //         },
    //         title: {
    //             text: 'Nilai Investasi (Rupiah)'
    //         }
    //     },
    //     // [
    //     //     { // Primary yAxis
    //     //     labels: {
    //     //         format: '{value}',
    //     //         style: {
    //     //             color: '#d4a300'
    //     //         }
    //     //     },
    //     //     title: {
    //     //         text: 'Jumlah Paket',
    //     //         style: {
    //     //             color: '#d4a300'
    //     //         }
    //     //     }
    //     // },
    //     // { // Secondary yAxis
    //     //     title: {
    //     //         text: 'Nilai Investasi',
    //     //         style: {
    //     //             color: '#47aded'
    //     //         }
    //     //     },
    //     //     labels: {
    //     //         format: '{value}',
    //     //         style: {
    //     //             color: '#47aded'
    //     //         }
    //     //     }
    //     // }
    //     // ],
    //     tooltip: {
    //         shared: true
    //     },
    //     legend: {
    //         layout: 'vertical',
    //         align: 'left',
    //         x: 100,
    //         verticalAlign: 'top',
    //         y: 0,
    //         floating: true,

    //         backgroundColor: (Highcharts.theme && Highcharts.theme.legendBackgroundColor) || 'rgba(255,255,255,0.25)'
    //     },
    //     plotOptions: {
    //         column: {
    //             pointPadding: 0.2,
    //             borderWidth: 0,
    //             pointWidth: 40
    //         }
    //     },
    //     series: [

    //     //     {
    //     //     name: 'Nilai Perolehan',
    //     //     //      yAxis: 1,
    //     //     data: vbiaya,
    //     //     // color: '#47aded',
    //     //     // borderColor: '#d4a300',
    //     //     tooltip: {
    //     //         valueSuffix: '',
    //     //         valuePrefix: 'Rp.'
    //     //     }

    //     // },
    //     {
    //         name: 'Jumlah Ruas',
    //         // type: 'spline',
    //         // lineWidth: 2,
    //         // // color: '#d4a300',
    //         data: vvals,
    //             tooltip: {
    //                 valueSuffix: ''
    //             }
    //      },
    //      {
    //         name: 'Nilai PPJT',
    //         // lineWidth: 2,
    //         // color: '#d4a300',
    //         data: vbiaya2,
    //         tooltip: {
    //             valueSuffix: '',
    //             valuePrefix: 'Rp.'
    //         }
    //     }]
    // });

    //   var vlabels = JSON.parse($("#chart_labels_2").val().replace(/'/g, '"'));
    //   var vvals = JSON.parse($("#chart_vals_2").val().replace(/'/g, '"'));
    //   var vbiaya = JSON.parse($("#chart_biaya_2").val().replace(/'/g, '"'));

    //   Highcharts.chart("container2", {
    //     chart: {
    //       zoomType: "xy",
    //     },
    //     title: {
    //       text: "Jumlah Aset Jembatan",
    //     },
    //     subtitle: {
    //       text: "",
    //     },
    //     xAxis: [
    //       {
    //         categories: vlabels,
    //         crosshair: true,
    //       },
    //     ],

    //     yAxis: [
    //       {
    //         // Primary yAxis
    //         labels: {
    //           format: "{value}",
    //           style: {
    //             color: "#666666",
    //           },
    //         },
    //         min: 0,
    //         title: {
    //           text: "Jumlah Ruas",
    //           style: {
    //             color: "#666666",
    //           },
    //         },
    //         opposite: false,
    //       },

    //     ],
    //     tooltip: {
    //       formatter: function () {
    //         var s = '<tspan style="font-size: 10px">' + this.x + "</tspan>";
    //         var color = this.points[0].series.color;
    //         var chart = this.points[0].series.chart; //get the chart object
    //         var categories = chart.xAxis[0].categories; //get the categories array
    //         var index = 0;
    //         while (this.x !== categories[index]) {
    //           index++;
    //         } //compute the index of corr y value in each data arrays
    //         $.each(chart.series, function (i, series) {
    //           //loop through series array
    //           var ydata =
    //             series.name == "Nilai Perolehan"
    //               ? "Rp." +
    //                 Highcharts.numberFormat(series.data[index].y, 0, " ", ",")
    //               : series.data[index].y;

    //           s += "<br/>" + series.name + ": " + "<b>" + ydata + "</b>"; //use index to get the y value
    //         });

    //         //console.log(s);
    //         return s;
    //       },
    //       shared: true,
    //     },
    //     legend: {
    //       layout: "vertical",
    //       align: "left",
    //       x: 100,
    //       verticalAlign: "top",
    //       y: 0,
    //       floating: true,
    //       backgroundColor:
    //         (Highcharts.theme && Highcharts.theme.legendBackgroundColor) ||
    //         "rgba(255,255,255,0.25)",
    //     },
    //     plotOptions: {
    //       column: {
    //         colorByPoint: true,
    //         pointWidth: 40,
    //       },
    //       colors: filteredcol,
    //     },
    //     series: [
    //       {
    //         name: "Jumlah Ruas",
    //         visible: true,
    //         type: "column",
    //         // type: 'spline',
    //         // lineWidth: 2,
    //         // // color: '#d4a300',
    //         data: vvals,
    //         // tooltip: {
    //         //     valueSuffix: ''
    //         // }
    //       },

    //     ],
    //   });

    // var vlabels = JSON.parse($("#chart_labels_3").val().replace(/'/g, '"'));
    // var vvals = JSON.parse($("#chart_vals_3").val().replace(/'/g, '"'));
    // var vbiaya = JSON.parse($("#chart_biaya_3").val().replace(/'/g, '"'));

    // Highcharts.chart("container3", {
    //   chart: {
    //     zoomType: "xy",
    //   },
    //   title: {
    //     text: "Jumlah  Aset Bangunan",
    //   },
    //   subtitle: {
    //     text: "",
    //   },
    //   xAxis: [
    //     {
    //       categories: vlabels,
    //       crosshair: true,
    //     },
    //   ],

    //   yAxis: [
    //     {
    //       // Primary yAxis
    //       labels: {
    //         format: "{value}",
    //         style: {
    //           color: "#666666",
    //         },
    //       },
    //       min: 0,
    //       title: {
    //         text: "Jumlah Ruas",
    //         style: {
    //           color: "#666666",
    //         },
    //       },
    //       opposite: false,
    //     },

    //     // { // Secondary yAxis
    //     //     min: 0,
    //     //     title: {
    //     //         text: 'Nilai Investasi (Rupiah)',
    //     //         style: {
    //     //             color: '#666666'
    //     //         }
    //     //     },
    //     //     labels: {
    //     //         formatter: function() {
    //     //             return Highcharts.numberFormat(this.value, 0, ' ', ',')
    //     //         },
    //     //         style: {
    //     //             color: '#666666'
    //     //         }
    //     //     },
    //     //     opposite: false
    //     // }
    //   ],
    //   tooltip: {
    //     formatter: function () {
    //       var s = '<tspan style="font-size: 10px">' + this.x + "</tspan>";
    //       var color = this.points[0].series.color;
    //       var chart = this.points[0].series.chart; //get the chart object
    //       var categories = chart.xAxis[0].categories; //get the categories array
    //       var index = 0;
    //       while (this.x !== categories[index]) {
    //         index++;
    //       } //compute the index of corr y value in each data arrays
    //       $.each(chart.series, function (i, series) {
    //         //loop through series array
    //         var ydata =
    //           series.name == "Nilai Perolehan"
    //             ? "Rp." +
    //               Highcharts.numberFormat(series.data[index].y, 0, " ", ",")
    //             : series.data[index].y;

    //         s += "<br/>" + series.name + ": " + "<b>" + ydata + "</b>"; //use index to get the y value
    //       });

    //       // / console.log(s);
    //       return s;
    //     },
    //     shared: true,
    //   },
    //   legend: {
    //     layout: "vertical",
    //     align: "left",
    //     x: 100,
    //     verticalAlign: "top",
    //     y: 0,
    //     floating: true,
    //     backgroundColor:
    //       (Highcharts.theme && Highcharts.theme.legendBackgroundColor) ||
    //       "rgba(255,255,255,0.25)",
    //   },
    //   plotOptions: {
    //     series: {
    //       colorByPoint: true,
    //       pointWidth: 40,
    //     },
    //     colors: filteredcol,
    //   },
    //   series: [
    //     {
    //       name: "Jumlah Ruas",
    //       visible: true,
    //       type: "column",
    //       // type: 'spline',
    //       // lineWidth: 2,
    //       // // color: '#d4a300',
    //       data: vvals,
    //       // tooltip: {
    //       //     valueSuffix: ''
    //       // }
    //     },
    //     // ,
    //     // {
    //     //     name: 'Nilai Perolehan',
    //     //     //   type: 'column',
    //     //     visible: false,
    //     //     // yAxis: 1,
    //     //     data: vbiaya,
    //     //     // color: '#47aded',
    //     //     // borderColor: '#d4a300',
    //     //     // tooltip: {
    //     //     //     valueSuffix: '',
    //     //     //     valuePrefix: 'Rp.'
    //     //     // }

    //     // }
    //   ],
    // });

    // var vlabels = JSON.parse($("#chart_labels_4").val().replace(/'/g, '"'));
    // var vvals = JSON.parse($("#chart_vals_4").val().replace(/'/g, '"'));
    // var vbiaya = JSON.parse($("#chart_biaya_4").val().replace(/'/g, '"'));

    // Highcharts.chart("container4", {
    //   chart: {
    //     zoomType: "xy",
    //   },
    //   title: {
    //     text: "Jumlah  Aset Peralatan",
    //   },
    //   subtitle: {
    //     text: "",
    //   },
    //   xAxis: [
    //     {
    //       categories: vlabels,
    //       crosshair: true,
    //     },
    //   ],

    //   yAxis: [
    //     {
    //       // Primary yAxis
    //       labels: {
    //         format: "{value}",
    //         style: {
    //           color: "#666666",
    //         },
    //       },
    //       min: 0,
    //       title: {
    //         text: "Jumlah Ruas",
    //         style: {
    //           color: "#666666",
    //         },
    //       },
    //       opposite: false,
    //     },

    //     // { // Secondary yAxis
    //     //     min: 0,
    //     //     title: {
    //     //         text: 'Nilai Investasi (Rupiah)',
    //     //         style: {
    //     //             color: '#666666'
    //     //         }
    //     //     },
    //     //     labels: {
    //     //         formatter: function() {
    //     //             return Highcharts.numberFormat(this.value, 0, ' ', ',')
    //     //         },
    //     //         style: {
    //     //             color: '#666666'
    //     //         }
    //     //     },
    //     //     opposite: false
    //     // }
    //   ],
    //   tooltip: {
    //     formatter: function () {
    //       var s = '<tspan style="font-size: 10px">' + this.x + "</tspan>";
    //       var color = this.points[0].series.color;
    //       var chart = this.points[0].series.chart; //get the chart object
    //       var categories = chart.xAxis[0].categories; //get the categories array
    //       var index = 0;
    //       while (this.x !== categories[index]) {
    //         index++;
    //       } //compute the index of corr y value in each data arrays
    //       $.each(chart.series, function (i, series) {
    //         //loop through series array
    //         var ydata =
    //           series.name == "Nilai Perolehan"
    //             ? "Rp." +
    //               Highcharts.numberFormat(series.data[index].y, 0, " ", ",")
    //             : series.data[index].y;

    //         s += "<br/>" + series.name + ": " + "<b>" + ydata + "</b>"; //use index to get the y value
    //       });

    //       // console.log(s);
    //       return s;
    //     },
    //     shared: true,
    //   },
    //   legend: {
    //     layout: "vertical",
    //     align: "left",
    //     x: 100,
    //     verticalAlign: "top",
    //     y: 0,
    //     floating: true,
    //     backgroundColor:
    //       (Highcharts.theme && Highcharts.theme.legendBackgroundColor) ||
    //       "rgba(255,255,255,0.25)",
    //   },
    //   plotOptions: {
    //     series: {
    //       colorByPoint: true,
    //       pointWidth: 40,
    //     },
    //     colors: filteredcol,
    //   },
    //   series: [
    //     {
    //       name: "Jumlah Ruas",
    //       visible: true,
    //       type: "column",
    //       // type: 'spline',
    //       // lineWidth: 2,
    //       // // color: '#d4a300',
    //       data: vvals,
    //       // tooltip: {
    //       //     valueSuffix: ''
    //       // }
    //     },
    //     // ,
    //     // {
    //     //     name: 'Nilai Perolehan',
    //     //     //   type: 'column',
    //     //     visible: false,
    //     //     // yAxis: 1,
    //     //     data: vbiaya,
    //     //     // color: '#47aded',
    //     //     // borderColor: '#d4a300',
    //     //     // tooltip: {
    //     //     //     valueSuffix: '',
    //     //     //     valuePrefix: 'Rp.'
    //     //     // }

    //     // }
    //   ],
    // });
});