<script>
Highcharts.setOptions({
    global: {
        useUTC: false,
    },
    lang: {
        decimalPoint: ".",
        thousandsSep: ",",
    }
});

var chartWp3wt = new Highcharts.chart('wp3wt', {
    chart: {

        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownWp3wt/" + e.point.kd_prov + '/' + e
                        .point.layer + '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                            // console.log(e.point.layer)
                        });
                    chart.showLoading('Silahkan Tunggu ...');



                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        text: '',
        align: 'center'
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        // min:0,
        // max:
        // tickInterval:400000,
        title: {
            text: 'Luasan Ha / Point '
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    var v = $('#wp3wtSelect').val()
                    var jns = ''
                    if (v == 'spatial.v_d_wp3wt_ppk_point' || v == 'spatial.v_d_wp3wt_ppkt') {
                        jns = ' Point';
                    } else {
                        jns = ' ha'
                    }

                    console.log(v + ' sas')
                    return number_format(this.y) + jns
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormatter: function() {
            // console.log($('#wp3wtSelect').val())
            var layer = $('#wp3wtSelect').val();
            // if(layer == 'spatial.v_d_wp3wt_ppk_point'){
            //     var np = ' Point'
            // }else{
            var np = this.jns
            // }
            // if ( this.series.chart.drilldownLevels == undefined  ) {
            // console.log('if')
            return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' + number_format(
                this.y) + np + '</br>' + this.nama_pulau;
            // } else if(this.series.chart.drilldownLevels.length > 0){
            //     console.log('if bawah')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+this.y+np;
            // } else if(this.series.chart.drilldownLevels.length == 0){
            //     console.log('if bawah 2')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+this.y+np;
            // }else {
            //     console.log('else')
            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+this.y+np;
            // }
        }
    },
    series: [{
        name: 'wp3wt',
        colorByPoint: true,
        data: []
    }],


});
var chartPgtl = new Highcharts.chart('pgtl', {
    chart: {

        type: 'column',
        events: {
            drilldown: function(e) {
                console.log(e.point)
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownPgtl/" + e.point.kd_prov + '/' + e
                        .point.layer + '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                        });
                    chart.showLoading('Silahkan Tunggu ...');

                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        text: '',
        align: 'center'
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        // min:0,
        // max:
        // tickInterval:400000,
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {
                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    return number_format(this.y) + ' Ha'
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormatter: function() {
            // console.log($('#wp3wtSelect').val())
            // var np = '% (Persen) Luasan'
            var np = ' Ha'
            if (this.series.chart.drilldownLevels == undefined) {
                console.log('if')
                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + np;
            } else if (this.series.chart.drilldownLevels.length > 0) {
                console.log('if bawah')

                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + np;
            } else if (this.series.chart.drilldownLevels.length == 0) {
                console.log('if bawah 2')

                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + np;
            } else {
                console.log('else')
                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + np;
            }
        }
    },
    series: [{
        name: 'pgtl',
        colorByPoint: true,
        data: []
    }],


});
var chartTanahNegara = new Highcharts.chart('tanahNegara', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownTanahNegara/" + e.point.kd_prov +
                        '/' + e.point.layer + '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                            // console.log(data)

                        });
                    chart.showLoading('Silahkan Tunggu ...');



                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        text: '',
        align: 'center'
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    return number_format(this.y) + ' Ha'
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormatter: function() {
            // console.log($('#wp3wtSelect').val())
            var np = ' Luas ha'

            // if ( this.series.chart.drilldownLevels == undefined  ) {
            //     console.log('if')
            return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' + number_format(
                this.y) + np;
            // } else if(this.series.chart.drilldownLevels.length > 0){
            //     console.log('if bawah')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+np;
            // } else if(this.series.chart.drilldownLevels.length == 0){
            //     console.log('if bawah 2')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+np;
            // }else {
            //     console.log('else')
            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+np;
            // }
        }
    },

    series: [{
        name: 'tanahNegara',
        colorByPoint: true,
        data: []
    }],


});
var chartLahanBaku = new Highcharts.chart('lahanBaku', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownLahanBaku/" + e.point.kd_prov +
                        '/' + e.point.layer,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;

                        });
                    chart.showLoading('Silahkan Tunggu ...');



                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        text: '',
        align: 'center'
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        // tickInterval:100000,
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {

            borderWidth: 0,
            dataLabels: {
                //    enabled: true,
                //     color: '#000',
                //     style: {fontSize: '7pt'},
                //     formatter: function() {return   number_format(this.y)+' Ha'},
                //     // outside: true,
                //     rotation: 270,
                //     y:-30

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    return number_format(this.y) + ' Ha'
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormatter: function() {
            console.log(this)
            var np = ' Ha'
            // if ( this.series.chart.drilldownLevels == undefined  ) {
            // console.log('if')
            return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' + number_format(
                this.nilai) + np;
            // } else if(this.series.chart.drilldownLevels.length > 0){
            //     console.log('if bawah')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+this.nilai+np;
            // } else if(this.series.chart.drilldownLevels.length == 0){
            //     console.log('if bawah 2')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+this.nilai+np;
            // }else {
            //     console.log('else')
            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+this.nilai+np;
            // }
        }
    },
    series: [{
        name: 'lahanBaku',
        colorByPoint: true,
        data: []
    }],


});
var chartKemampuanTanah = new Highcharts.chart('kemampuanTanah', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    console.log(e.point)
                    var jenis = $('#kemampuanTanahSelect').val()
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownKemampuanTanah/" + e.point.kd_prov +
                        '/' + jenis + '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;

                            // console.log(data)

                        });
                    chart.showLoading('Silahkan Tunggu ...');



                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        // text: 'Digitalisasi Tahun 2012',
        align: 'center'
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value, 0) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    return number_format(this.y) + ' Ha'
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormatter: function() {
            // console.log($('#wp3wtSelect').val())
            var np = ' Ha'
            // if ( this.series.chart.drilldownLevels == undefined  ) {
            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Ha';
            // } else if(this.series.chart.drilldownLevels.length > 0){
            //     console.log('if bawah')
            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+np;
            // } else if(this.series.chart.drilldownLevels.length == 0){
            //     console.log('if bawah 2')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Ha';
            // }else {
            //     console.log('else')
            return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' + number_format(
                this.y) + np;
            // }
        }
    },

    series: [{
        name: 'kemampuanTanah',
        colorByPoint: true,
        data: []
    }],


});
// var chartNpgtProv = new Highcharts.chart('npgtProv', {
//     chart: {
//         type: 'column',
//         events: {
//             drilldown: function (e) {
//                 this.yAxis[0].tickmarkPlacement = 'off'
//                 this.yAxis[0].update({
//                         tickInterval: 100,
//                 })
//                 if (!e.seriesOptions) {
//                     var chart = this,
//                         series = [];
//                         // console.log(e.point.kd_prov)
//                         $.get("<?php echo base_url(); ?>dashboard4/drilldownNpgtProv/" + e.point.kd_prov, function(data) {
//                             data = JSON.parse(data)
//                             series = data;

//                         });
//                         chart.showLoading('Silahkan Tunggu ...');



//                     setTimeout(function () {
//                         chart.hideLoading();
//                         // chart.addSeriesAsDrilldown(e.point,series);
//                         chart.addSeriesAsDrilldown(e.point, series,{
//                         //First solution
//                         tooltip: {
//                         pointFormat: 'Name: {point.name}, Value: {point.value}'
//                         },
//                         name: e.point.name,
//                         data: series,
//                         dataLabels: {
//                         enabled: true,
//                         format: '{point.name}'
//                         }
//                     });
//                     }, 1000);
//                 }

//                 }
//             }


//     },
//     title: {
//         align: 'center',
//         text: ''
//     },
//     credits: {
//         enabled: false
//     },
//     subtitle: {
//         align: 'left',
//         // text: 'Jumlah/Kota'
//     },
//     accessibility: {
//         announceNewData: {
//             enabled: true
//         }
//     },
//     xAxis: {
//         type: 'category'
//     },
//     yAxis: {
//         // tickInterval:500,
//         title: {
//             text: ' NPGT Provinsi'
//         },
//         labels: {
//             // format: '{value} '
//             formatter: function () {

//                 return number_format(this.value, 0)+' ';
//             }
//         }

//     },
//     legend: {
//         enabled: false
//     },
//     plotOptions: {
//         series: {
//             borderWidth: 0,
//             dataLabels: {
//                 enabled: false,
//                 format: '{point.y} '
//             }
//         }
//     },

//     tooltip: {
//         headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
//         // pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y} Luas Ha '
//         pointFormatter: function () {


//             if ( this.series.chart.drilldownLevels == undefined  ) {
//                 console.log('if')
//                 return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha '+this.d;
//             } else if(this.series.chart.drilldownLevels.length > 0){
//                 console.log('if bawah')

//                 return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha';
//             } else if(this.series.chart.drilldownLevels.length == 0){
//                 console.log('if bawah 2')

//                 return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha '+this.d;
//             }else {
//                 console.log('else')
//                 return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha';
//             }
//         }
//     },

//     series: [{
//         name: 'npgtProv',
//         colorByPoint: true,
//         data: []
//     }],


// });
var chartNpgtPerkebunan = new Highcharts.chart('npgtPerkebunan', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    // console.log(e.point.kd_prov)
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownNpgtPerkebunan/" + e.point.kd_prov +
                        '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                            // console.log(data)

                        });
                    chart.showLoading('Silahkan Tunggu ...');



                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        align: 'center',
        text: ''
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        // tickInterval:50000,
        title: {
            text: ' NPGT Perkebunan'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value, 0) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    return number_format(this.y) + ' Ha'
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        // pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y} Luas Ha '
        pointFormatter: function() {

            //    if ( this.series.chart.drilldownLevels == undefined  ) {
            //    console.log('if')
            return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' + number_format(
                this.y) + ' Ha';
            // } else if(this.series.chart.drilldownLevels.length > 0){
            //     console.log('if bawah')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha';
            // } else if(this.series.chart.drilldownLevels.length == 0){
            //     console.log('if bawah 2')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha';
            // }else {
            //     console.log('else')
            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha';
            // }
        }
    },

    series: [{
        name: 'npgtPerkebunan',
        colorByPoint: true,
        data: []
    }],


});

var chartNpgtPerkebunanDual = new Highcharts.chart('npgtPerkebunanStack', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    // console.log(e.point.kd_prov)
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownNpgtPerkebunan/" + e.point.kd_prov +
                        '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                            // console.log(data)

                        });
                    chart.showLoading('Silahkan Tunggu ...');



                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        align: 'center',
        text: ''
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        tickInterval: 1000,
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value, 0) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'black',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    if (this.y > 1) {
                        return number_format(this.y) + ' Ha'
                    } else {
                        return this.y + ' Ha'
                    }
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        // pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y} Luas Ha '
        pointFormatter: function() {

            //    if ( this.series.chart.drilldownLevels == undefined  ) {
            //    console.log('if')
            return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' + number_format(
                this.y) + ' Ha';
            // } else if(this.series.chart.drilldownLevels.length > 0){
            //     console.log('if bawah')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha';
            // } else if(this.series.chart.drilldownLevels.length == 0){
            //     console.log('if bawah 2')

            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha';
            // }else {
            //     console.log('else')
            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Luas Ha';
            // }
        }
    },

    series: [{
        name: 'npgtPerkebunanDual',
        colorByPoint: true,
        data: []
    }],


});
// var chart utama
var chartNpgtKabkota = new Highcharts.chart('npgtKabkota', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    // console.log(e.point.kd_prov)
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownNpgtKabkota/" + e.point.kd_prov +
                        '/' + e.point.layer + '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                            // console.log(data)

                        });
                    chart.showLoading('Silahkan Tunggu ...');
                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        align: 'center',
        text: ''
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value, 0) + ' ';
            }

        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {
                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    return number_format(this.y) + ' Ha'
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        // pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y} Luas Ha '
        pointFormatter: function() {
            // console.log($('#kabkotSelect').val())
            var layer = $('#kabkotSelect').val();
            if (layer == 'spatial.v_d_npgt_kabkot_h_polyline' || layer ==
                'spatial.v_d_npgt_kabkot_k_polyline') {
                var np = ' Panjang m'
            } else {
                var np = ' Luas ha'
            }
            if (this.series.chart.drilldownLevels == undefined) {
                console.log('if')
                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + ' Luas Ha';
            } else if (this.series.chart.drilldownLevels.length > 0) {
                console.log('if bawah')

                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + np;
            } else if (this.series.chart.drilldownLevels.length == 0) {
                console.log('if bawah 2')

                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + ' Luas Ha';
            } else {
                console.log('else')
                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + np;
            }
        }
    },

    series: [{
        name: 'npgtKabkota',
        colorByPoint: true,
        data: []
    }],


});

var chartNpgtKec = new Highcharts.chart('npgtKec', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    // console.log(e.point.kd_prov)
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownNpgtKec/" + e.point.kd_prov + '/' +
                        e.point.layer + '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                            // console.log(data)
                            setTimeout(function() {
                                chart.hideLoading();
                                chart.addSeriesAsDrilldown(e.point, series);
                            }, 1000);
                        });
                    chart.showLoading('Silahkan Tunggu ...');




                }

            }
        }

    },
    title: {
        align: 'center',
        text: ''
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        title: {
            text: ' Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value, 0) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    return number_format(this.y) + ' Ha'
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        // pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y} Luas Ha '
        pointFormatter: function() {
            // console.log($('#kabkotSelect').val())
            var layer = $('#kecSelect').val();
            if (layer == 'spatial.v_d_npgt_kabkot_h_polyline' || layer ==
                'spatial.v_d_npgt_kabkot_k_polyline') {
                var np = ' Panjang m'
            } else {
                var np = ' Luas ha'
            }
            if (this.series.chart.drilldownLevels == undefined) {
                console.log('if')
                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + ' Luas Ha';
            } else if (this.series.chart.drilldownLevels.length > 0) {
                console.log('if bawah')

                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + np;
            } else if (this.series.chart.drilldownLevels.length == 0) {
                console.log('if bawah 2')

                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + ' Luas Ha';
            } else {
                console.log('else')
                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + np;
            }
        }
    },

    series: [{
        name: 'npgtKabkota',
        colorByPoint: true,
        data: []
    }],


});

var chartPtp = new Highcharts.chart('ptp', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    console.log(e.point.kd_prov)
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownPtp/" + e.point.kd_prov + '/' + e
                        .point.layer + '/' + e.point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                            // console.log(data)

                        });
                    chart.showLoading('Silahkan Tunggu ...');



                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        align: 'center',
        text: ''
    },
    credits: {
        enabled: false
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'center',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        title: {
            text: 'Luasan Ha / Point'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value, 0) + ' ';
            }
        },
        // min:0,
        // max :1,
        // tickInterval:0.2,

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                pointFormatter: function() {
                    // console.log(this)
                    if ($('#ptpSelect').val() == 'spatial.v_d_ptpil_prov_tk') {
                        return number_format(this.y) + ' Layanan'
                    } else {
                        return number_format(this.y) + ' Layanan'
                    }
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormatter: function() {
            // console.log($('#ptpSelect').val())
            var layer = $('#ptpSelect').val();
            console.log(this)
            var np = '% (Persen) Luasan'
            return '<span style="color:' + this.color + '">' + this.name +
                '</span>: <b>' + number_format(this.nilaiy) + this.jns + '</br>' + this.luasnya;
            // 'Luas Data : '+number_format(this.luas_ptp)+' Ha</br>'+
            // 'Luas Total : '+number_format(this.luas_bts)+' Ha</br>';

        }
    },

    series: [{
        name: 'ptp',
        colorByPoint: true,
        data: []
    }],


});
var chartPtpInterop = new Highcharts.chart('ptpInterop', {
    chart: {
        type: 'column',


    },
    title: {
        align: 'center',
        text: ''
    },
    credits: {
        enabled: false
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'center',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value, 0) + ' ';
            }
        },
        // min:0,
        // max :1,
        // tickInterval:0.2,

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },

            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormatter: function() {
            // console.log($('#ptpSelect').val())

            return '</span>' + number_format(this.luas) + ' Ha</br>';
            // 'Luas Data : '+number_format(this.luas_ptp)+' Ha</br>'+
            // 'Luas Total : '+number_format(this.luas_bts)+' Ha</br>';

        }
    },

    series: [{
        name: 'ptp',
        colorByPoint: true,
        data: []
    }],


});
var chartPtpInteropLayanan = new Highcharts.chart('ptpInteropLayanan', {
    chart: {
        type: 'column',


    },
    title: {
        align: 'center',
        text: ''
    },
    credits: {
        enabled: false
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'center',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value, 0) + ' ';
            }
        },
        // min:0,
        // max :1,
        // tickInterval:0.2,

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },

            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        pointFormatter: function() {
            // console.log($('#ptpSelect').val())

            return '</span>' + number_format(this.luas) + ' Ha</br>';
            // 'Luas Data : '+number_format(this.luas_ptp)+' Ha</br>'+
            // 'Luas Total : '+number_format(this.luas_bts)+' Ha</br>';

        }
    },

    series: [{
        name: 'ptp',
        colorByPoint: true,
        data: []
    }],


});

var chartMppt = new Highcharts.chart('mppt', {
    chart: {
        type: 'column',
        events: {
            drilldown: function(e) {
                if (!e.seriesOptions) {
                    var chart = this,
                        series = [];
                    $.get("<?php echo base_url(); ?>dashboard4/drilldownMppt/" + e.point.kd_prov + '/' + e
                        .point.tahun,
                        function(data) {
                            data = JSON.parse(data)
                            series = data;
                            // console.log(e.point.layer)
                        });
                    chart.showLoading('Silahkan Tunggu ...');



                    setTimeout(function() {
                        chart.hideLoading();
                        chart.addSeriesAsDrilldown(e.point, series);
                    }, 1000);
                }

            }
        }

    },
    title: {
        align: 'center',
        text: ''
    },
    credits: {
        enabled: false
    },
    subtitle: {
        align: 'left',
        text: ''
    },
    accessibility: {
        announceNewData: {
            enabled: true
        }
    },
    xAxis: {
        type: 'category'
    },
    yAxis: {
        // min:0,
        // max:
        // tickInterval:400000,
        title: {
            text: 'Luasan Ha'
        },
        labels: {
            // format: '{value} '
            formatter: function() {

                return number_format(this.value) + ' ';
            }
        }

    },
    legend: {
        enabled: false
    },
    plotOptions: {
        series: {
            borderWidth: 0,
            dataLabels: {

                // enabled: false,
                // format: '{point.y} '
                enabled: true,
                color: 'white',
                style: {
                    fontSize: '7pt'
                },
                formatter: function() {
                    return number_format(this.y) + ' Ha'
                },
                inside: true,
                rotation: 270,
                y: -30
            }
        }
    },

    tooltip: {
        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
        // pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y} Luas Ha '
        pointFormatter: function() {
            // console.log($('#wp3wtSelect').val())
            if (this.series.chart.drilldownLevels == undefined) {
                console.log('if')
                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + ' Luas Ha';
            } else if (this.series.chart.drilldownLevels.length > 0) {
                console.log('if bawah')

                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + ' Luas ha';
            } else if (this.series.chart.drilldownLevels.length == 0) {
                console.log('if bawah 2')

                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + ' Luas Ha';
            } else {
                console.log('else')
                return '<span style="color:' + this.color + '">' + this.name + '</span>: <b>' +
                    number_format(this.y) + ' luas Ha';
            }
        }
    },

    series: [{
        name: 'mppt',
        colorByPoint: true,
        data: []
    }],


});




var chartNpgtProv = Highcharts.chart('npgtProv', {
    title: {
        text: '',
        align: 'center'
    },
    credits: {
        enabled: false
    },
})
var chartNpgtProvStack;
var role = "<?=$this->session->users['id_user_group_real']?>";
var kdpkab = "<?php echo $this->session->users['kd_kabkot'];?>";
var kdppum = "<?php echo $this->session->users['kd_prov'];?>";
$(document).ready(function() {
    setTimeout(function() {
        $('#ProvSelectYear').val('2019');
        $('#ProvSelectYear').selectpicker('refresh');
        $('#ProvSelectYear').trigger('change');
    }, 2000);
    var cekIndex = "<?=@$cekIndex?>";
    $.get("<?php echo base_url(); ?>dashboard4/getProvInterop/", function(data) {
        data = JSON.parse(data)
        $('#ptpSelectProvInteropLayanan').html(data);
        $('#ptpSelectProvInterop').html(data);
        $('#ptpSelectProvInteropLayanan').selectpicker('refresh')
        $('#ptpSelectProvInterop').selectpicker('refresh')
    });

    // alert(role);
    if (role == 7 || role == 8) {
        // alert(role)
        // alert(kdpkab)

        $('.noprov').hide();
        $('.wkab').show();
        $('#ydivWp3wtProv').hide();
        refreshSelectboot2('kabkotSelectKab', 20, 'kd_kabkot', kdpkab);
        refreshSelectboot2('kecSelectKab', 20, 'kd_kabkot', kdpkab);
        // refreshSelectboot2('kebunSelectKab', 20, 'kd_kabkot', kdpkab);
        refreshSelectboot2('kebunSelectKabDual', 20, 'kd_kabkot', kdpkab);
        refreshSelectboot2('mpptSelectKab', 20, 'kd_kabkot', kdpkab);
        refreshSelectboot2('mpptSelectKab', 20, 'kd_kabkot', kdpkab);
        refreshSelectboot2('ktSelectKab', 20, 'kd_kabkot', kdpkab);
        refreshSelectboot2('lbsSelectKab', 20, 'kd_kabkot', kdpkab);
    } else if (role == 9 || role == 10) {
        // alert(role)
        // alert(kdpkab)

        $('.noprov').show();
        initComboboxProvSelect('provSelectProv', kdppum);
        initComboboxProvSelect('kabkotSelectProv', kdppum);
        initComboboxProvSelect('kecSelectProv', kdppum);
        initComboboxProvSelect('provSelectProvDual', kdppum);
        initComboboxProvSelect('kabSelectProvDual', kdppum);
        initComboboxProvSelect('mpptSelectProv', kdppum);
        initComboboxProvSelect('ktSelectProv', kdppum);
        initComboboxProvSelect('lbsSelectProv', kdppum);
        initComboboxProvSelect('kebunSelectProv', kdppum);
        initComboboxProvSelect('ptpSelectProv', kdppum);
        initComboboxProvSelect('kecSelectProvDual', kdppum);
        initComboboxProvSelect('kebunSelectProvDual', kdppum);
    } else {
        //  alert('iam not there');

        $('.noprov').show();
        initComboboxProv('provSelectProv', 19);
        initComboboxProv('kabkotSelectProv', 19);
        initComboboxProv('kecSelectProv', 19);
        initComboboxProv('provSelectProvDual', 19);
        initComboboxProv('kabSelectProvDual', 19);
        initComboboxProv('mpptSelectProv', 19);
        initComboboxProv('ktSelectProv', 19);
        initComboboxProv('lbsSelectProv', 19);
        initComboboxProv('kebunSelectProv', 19);
        initComboboxProv('ptpSelectProv', 19);
        // initComboboxProv('wp3wtSelectProv',19);
        // initComboboxProv('pgtlSelectProv',19);
        // initComboboxProv('tnSelectProv',19);
        // initComboboxProv('kecSelectProv', 19);
        initComboboxProv('kecSelectProvDual', 19);
        initComboboxProv('kebunSelectProvDual', 19);
    }
    // $("#ktSelectProv").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
    //     refreshSelectboot2('ktSelectKab', 20, 'kd_prov', this.value);
    // });
    // $("#kecSelectProv").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
    //     refreshSelectboot2('kecSelectKab', 20, 'kd_prov', this.value);
    // });

    // $("#kecSelectProvDual").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
    //     refreshSelectboot2('kecSelectKabDual', 20, 'kd_prov', this.value);
    // });


    // $('#wp3wtSelect').css('float','left')


    // $('.dropdown').css('float','left')
    if (role == 2) {
        $('#divnprov').hide()
        $('#divnkab').hide()
        $('#divnkec').hide()
        $('#divnkeb').hide()
        $('#divlbs').hide()
        $('#divwp3wt').hide()
        $('#divnhprov').hide()
        $('#divnhkeb').hide()

    }
    if (role == 4) {
        $('#divnprov').hide()
        $('#divnhprov').hide()
        $('#divnhkeb').hide()
        $('#divnkab').hide()
        $('#divnkec').hide()
        $('#divnkeb').hide()
        $('#divlbs').hide()
        $('#divtnbh').hide()
        $('#divmppt').hide()
        $('#divlbs').hide()
        $('#divptp').hide()
        $('#divpgtl').hide()
        $('#divkt').hide()

    }

    if (role == 3) {
        $('#divwp3wt').hide()
        $('#divtnbh').hide()
        $('#divmppt').hide()
        $('#divptp').hide()
        $('#divpgtl').hide()
        $('#divkt').hide()

    }
    Highcharts.setOptions({
        global: {
            useUTC: false,
        },
        lang: {
            decimalPoint: ".",
            thousandsSep: ",",
        }
    });


    setTimeout(function() {
        chartNpgtProvStack = Highcharts.chart('npgtProvStack', {
            title: {
                text: '',
                align: 'center'
            },
            events: {
                drilldown: function(e) {
                    if (!e.seriesOptions) {
                        var chart = this,
                            series = [];
                        console.log(e.point)
                        $.get("<?php echo base_url(); ?>dashboard4/drilldownNpgtProvDual/",
                            function(data) {
                                data = JSON.parse(data)
                                series = data;
                                // console.log(data)

                            });
                        chart.showLoading('Silahkan Tunggu ...');



                        setTimeout(function() {
                            chart.hideLoading();
                            chart.addSeriesAsDrilldown(e.point, series);
                        }, 1000);
                    }

                }


            },
            credits: {
                enabled: false
            },
        })
        Highcharts.chart('npgtKabkotaStack', {
            title: {
                text: '',
                align: 'center'
            },
            credits: {
                enabled: false
            },
        })
        Highcharts.chart('npgtKecStack', {
            title: {
                text: '',
                align: 'center'
            },
            credits: {
                enabled: false
            },
        })

        // Highcharts.chart('npgtKec', {
        //     title: {
        //                 text: '',
        //                 align:'center'
        //             },
        //     credits: {
        //           enabled: false
        //     },})
        chartNpgtProv = Highcharts.chart('npgtProv', {
            title: {
                text: '',
                align: 'center'
            },
            credits: {
                enabled: false
            },
        })

        // Highcharts.chart('tanahNegara', {
        // title: {
        //             text: '',
        //             align:'center'
        //         },
        // credits: {
        //       enabled: false
        // },})   
        // Highcharts.chart('npgtKabkota', {
        // title: {
        //             text: '',
        //             align:'center'
        //         },
        // credits: {
        //       enabled: false
        // },})
        // Highcharts.chart('npgtPerkebunan', {
        //     title: {
        //                 text: '',
        //                 align:'center'
        //             },
        //     credits: {
        //           enabled: false
        //     },})

        // Highcharts.chart('npgtPerkebunanStack', {
        //     title: {
        //                 text: '',
        //                 align:'center'
        //             },
        //     credits: {
        //           enabled: false
        //     },})

        // Highcharts.chart('mppt', {
        //     title: {
        //                 text: '',
        //                 align:'center'
        //             },
        //     credits: {
        //           enabled: false
        //     },})



        // wp3wtChange()
        // tanahNegaraChange()
        // lahanBaku()
        // npgtProv()
        // npgtPerkebunan()
        // npgtKabkotChange()
        // ptpChange()
        // kemampuanTanahChange()
        // npgtProvDual()
        // npgtKabDual()
        // mpptChange()
    }, 2000);

    // $('#kosong').attr('style', 'float: left !important');


    // $('.bootstrap-select').css('width','800px!important')
    // $('select').attr('title', 'Tidak Ada Data');
    $("select").each(function() {
        // if ($(this).text()) {
        // console.log(this)
        // $(this).attr("title", "Tidak Ada Data");
        // $(this).append(new Option("--Pilih--", ""));
        // $(this).selectpicker('refresh');

        // }else{
        // console.log($(this))
        // }
    });

    // $('#provSelectYearDual').append(new Option("--Pilih--", ""));
    // $('#provSelectYearDual').selectpicker('refresh');

    $('.bootstrap-select').selectpicker();
    // $('.bootstrap-select').selectpicker({noneSelectedText:'Tidak Ada Data'});
    $('.bootstrap-select').css('float', 'right');
});

function ptpProv(val) {
    $.get("<?php echo base_url(); ?>dashboard4/getKabkot/" + val, function(data) {
        data = JSON.parse(data)
        $('#ptpSelectKab').html(data);
        $('#ptpSelectKab').selectpicker('refresh')
        // listing()
    });
}

function ptpProvInterop(val) {
    $.get("<?php echo base_url(); ?>dashboard4/getKabkotInterop/" + val, function(data) {
        data = JSON.parse(data)
        $('#ptpSelectKabInterop').html(data);
        $('#ptpSelectKabInterop').selectpicker('refresh')
        // listing()
    });
}

function ptpProvInteropLayanan(val) {
    $.get("<?php echo base_url(); ?>dashboard4/getKabkotInterop/" + val, function(data) {
        data = JSON.parse(data)
        $('#ptpSelectKabInteropLayanan').html(data);
        $('#ptpSelectKabInteropLayanan').selectpicker('refresh')
        // listing()
    });
}


// function isi data chart
function getchart(url) {
    var url = '<?php echo base_url(); ?>' + url;
    // return false
    $.ajax({
        url: url,
        type: "GET",
        data: {},
        processData: false,
        contentType: false,
        cache: false,
        async: false,
        success: function(d) {
            d = JSON.parse(d)
            // console.log('ini d'+d.data  )
            if (d.module == 'wp3wt') {

                console.log(d)

                $('#wp3wtSelectYear').html(d.tahun)
                $('#wp3wtSelectYear').selectpicker('refresh')
                // chartWp3wt.drillUp();
                chartWp3wt.hideLoading()

                while (chartWp3wt.series.length) {
                    chartWp3wt.series[0].remove();
                }
                chartWp3wt.addSeries({
                    name: "Data",
                    data: d.data
                });
                chartWp3wt.yAxis[0].setTitle({
                    text: d.title
                });

                // chartWp3wt.get('wp3wt').remove();
            } else if (d.module == 'lahanBaku') {

                $('#lahanBakuSelectTahun').html(d.tahun)
                $('#lahanBakuSelectTahun').selectpicker('refresh')
                // chartLahanBaku.drillUp();
                chartLahanBaku.hideLoading()
                while (chartLahanBaku.series.length) {
                    chartLahanBaku.series[0].remove();
                }
                chartLahanBaku.addSeries({
                    name: "Data",
                    data: d.data
                });
            } else if (d.module == 'pgtl') {

                // console.log(d);
                $('#pgtlSelectYear').html(d.tahun)
                $('#pgtlSelectYear').selectpicker('refresh')
                chartPgtl.drillUp();
                chartPgtl.hideLoading()

                while (chartPgtl.series.length) {
                    chartPgtl.series[0].remove();
                }
                chartPgtl.addSeries({
                    name: "Data",
                    data: d.data
                });
            } else if (d.module == 'mppt') {

                $('#mpptSelectTahun').html(d.tahun)
                $('#mpptSelectTahun').selectpicker('refresh')
                chartMppt.drillUp();
                chartMppt.hideLoading()
                while (chartMppt.series.length) {
                    chartMppt.series[0].remove();
                }
                chartMppt.addSeries({
                    name: "Data",
                    data: d.data
                });
                // console.log('ok')
                // var chartMppt =Highcharts.chart('mppt', {

                //     chart: {
                //         type: 'column'

                //     },credits: {
                //         enabled: false
                //     },
                //     title: {
                //         text: '',
                //         align:'center'
                //     },
                //     xAxis: {
                //         categories: d.category  
                //     },

                //     yAxis: {
                //         allowDecimals: false,
                //         min: 0,
                //         title: {
                //             text: ''
                //         },
                //         labels: {
                //             // format: '{value} '
                //             formatter: function () {

                //                 return number_format(this.value)+' ';
                //             }
                //         }
                //     },

                //     tooltip: {
                //         formatter: function () {
                //             return '<b>' + this.x + '</b><br/>' +
                //                 'Total Provinsi: ' + number_format(this.point.stackTotal)+' Provinsi<br/>'+
                //                 'Provinsi '+this.series.name + ': ' + number_format(this.y) + ' Provinsi<br/>' ;
                //         }

                //     },

                //     plotOptions: {
                //         column: {
                //             stacking: 'normal'
                //         }
                //     },
                //     series: d.data

                // });
            } else if (d.module == 'tanahNegara') {

                $('#tnSelectTahun').html(d.tahun)
                $('#tnSelectTahun').selectpicker('refresh')
                // chartTanahNegara.drillUp();
                chartTanahNegara.hideLoading()
                while (chartTanahNegara.series.length) {
                    chartTanahNegara.series[0].remove();
                }
                chartTanahNegara.addSeries({
                    name: "Data",
                    data: d.data
                });

                // var chartTanahNegara =Highcharts.chart('tanahNegara', {

                //     chart: {
                //         type: 'column'

                //     },
                //     events: {
                //         drilldown: function (e) {

                //             console.log(masuk);
                //         }
                //     },
                //     credits: {
                //         enabled: false
                //     },
                //     title: {
                //         text: '',
                //         align:'center'
                //     },
                //     xAxis: {
                //         categories: d.category  
                //     },

                //     yAxis: {
                //         allowDecimals: false,
                //         min: 0,
                //         title: {
                //             text: ''
                //         },
                //         labels: {
                //             // format: '{value} '
                //             formatter: function () {

                //                 return number_format(this.value)+' ';
                //             }
                //         }
                //     },
                //     tooltip: {
                //         formatter: function () {
                //             return '<b>' + this.x + '</b><br/>' +
                //             'Total Provinsi: ' + number_format(this.point.stackTotal)+' Provinsi</br>'+
                //             'Provinsi '+this.series.name + ': ' + number_format(this.y) + ' Provinsi<br/>' ;
                //             // 'Total Luas Wilayah : '+number_format(this.point.luas)+' Ha';
                //         }

                //     },

                //     plotOptions: {
                //         column: {
                //             stacking: 'normal'
                //         }
                //     },
                //     series: d.data

                // });
            } else if (d.module == 'kemampuanTanah') {
                console.log(d)
                chartKemampuanTanah.drillUp();
                chartKemampuanTanah.hideLoading()
                while (chartKemampuanTanah.series.length) {
                    chartKemampuanTanah.series[0].remove();
                }
                chartKemampuanTanah.addSeries({
                    name: "Data",
                    data: d.data
                });
            } else if (d.module == 'npgtProv') {
                // chartNpgtProv.drillUp();
                // while (chartNpgtProv.series.length) {
                //     chartNpgtProv.series[0].remove();
                // }
                // chartNpgtProv.addSeries({
                //     name: "Data",
                //     data: d.data
                // });
                // console.log('ini npgt prov')
                chartNpgtProv.hideLoading()

                chartNpgtProv = Highcharts.chart('npgtProv', {

                    chart: {
                        type: 'column'

                    },
                    events: {
                        drilldown: function(e) {

                            console.log(masuk);
                        }
                    },
                    credits: {
                        enabled: false
                    },
                    title: {
                        text: '',
                        align: 'center'
                    },
                    xAxis: {
                        categories: d.category
                    },

                    yAxis: {
                        allowDecimals: false,
                        min: 0,
                        title: {
                            text: ''
                        },
                        labels: {
                            // format: '{value} '
                            formatter: function() {

                                return number_format(this.value) + ' ';
                            }
                        }
                    },

                    tooltip: {
                        formatter: function() {
                            return '<b>' + this.x + '</b><br/>' +
                                'Jumlah Provinsi: ' + number_format(this.point.stackTotal) +
                                ' Provinsi </br>' +
                                'Provinsi ' + this.series.name + ': ' + number_format(this.y) +
                                ' Provinsi <br/>';
                            // 'Total Luas Wilayah : '+number_format(this.point.luas)+' Ha';
                        }

                    },

                    plotOptions: {
                        column: {
                            stacking: 'normal'
                        }
                    },
                    series: d.data

                });

            } else if (d.module == 'npgtProvDual') {
                $('#provSelectYearDual').html(d.tahun)
                $('#provSelectYearDual').selectpicker('refresh')
                // console.log(d.data)
                chartNpgtProvStack = Highcharts.chart('npgtProvStack', {

                    chart: {
                        type: 'column'
                    },
                    events: {
                        drilldown: function(e) {
                            if (!e.seriesOptions) {
                                var chart = this,
                                    series = [];
                                // console.log(e.point)
                                $.get("<?php echo base_url(); ?>dashboard4/drilldownNpgtProvDual/",
                                    function(data) {
                                        data = JSON.parse(data)
                                        series = data;
                                        // console.log(data)

                                    });
                                chart.showLoading('Silahkan Tunggu ...');



                                setTimeout(function() {
                                    chart.hideLoading();
                                    chart.addSeriesAsDrilldown(e.point, series);
                                }, 1000);
                            }

                        }


                    },
                    credits: {
                        enabled: false
                    },
                    title: {
                        text: '',
                        align: 'center'
                    },
                    xAxis: {
                        categories: d.category
                    },

                    yAxis: {
                        allowDecimals: false,
                        min: 0,
                        title: {
                            text: 'Luasan Ha'
                        },
                        labels: {
                            // format: '{value} '
                            formatter: function() {

                                return number_format(this.value) + ' ';
                            }
                        }
                    },

                    tooltip: {
                        formatter: function() {
                            return '<b>' + this.x + '</b><br/>' +
                                this.point.text + ': ' + number_format(this.y) + ' Ha<br/>';
                            // 'Luas Total: ' + number_format(this.point.stackTotal)+' Ha';
                        }

                    },

                    plotOptions: {
                        column: {
                            stacking: 'normal'
                        },
                        series: {
                            borderWidth: 0,
                            dataLabels: {

                                // enabled: false,
                                // format: '{point.y} '
                                enabled: true,
                                color: 'white',
                                style: {
                                    fontSize: '7pt'
                                },
                                formatter: function() {
                                    if (this.y != 0) {
                                        return number_format(this.y) + ' Ha'
                                    }
                                },
                                inside: true,
                                // rotation: 270,
                                // y:-30
                            }
                        }
                    },

                    series: d.data

                });

                // chartNpgtProvDual.addSeries(
                //     d.data,true
                // );

                // chartNpgtProvDual.addSeries({
                //     name: "Data",
                //     data: d.data
                // });
                chartNpgtProvStack.hideLoading()

            } else if (d.module == 'ptpMonitoring') {

                // console.log(d.data)
                chartPtp = Highcharts.chart('ptp', {

                    chart: {
                        type: 'column'
                    },
                    events: {
                        drilldown: function(e) {

                        }


                    },
                    credits: {
                        enabled: false
                    },
                    title: {
                        text: '',
                        align: 'center'
                    },
                    xAxis: {
                        categories: d.category
                    },

                    yAxis: {
                        allowDecimals: false,
                        min: 0,
                        title: {
                            text: 'Jumlah'
                        },
                        labels: {
                            // format: '{value} '
                            formatter: function() {

                                return number_format(this.value) + ' ';
                            }
                        }
                    },

                    tooltip: {
                        formatter: function() {
                            return '<b>' + this.x + '</b><br/>' +
                                this.point.text + ': Rp.' + number_format(this.y) + '<br/>';
                            // 'Luas Total: ' + number_format(this.point.stackTotal)+' Ha';
                        }

                    },

                    plotOptions: {
                        column: {
                            stacking: 'normal'
                        },
                        series: {
                            borderWidth: 0,
                            dataLabels: {

                                // enabled: false,
                                // format: '{point.y} '
                                enabled: true,
                                color: 'white',
                                style: {
                                    fontSize: '7pt'
                                },
                                formatter: function() {
                                    if (this.y != 0) {
                                        return 'Rp. ' + number_format(this.y)
                                    }
                                },
                                inside: true,
                                // rotation: 270,
                                // y:-30
                            }
                        }
                    },

                    series: d.data

                });

                // chartNpgtProvDual.addSeries(
                //     d.data,true
                // );

                // chartNpgtProvDual.addSeries({
                //     name: "Data",
                //     data: d.data
                // });
                chartPtp.hideLoading()
            } else if (d.module == 'ptpMonitoringInterop') {
                console.log(d)
                chartPtpInterop = Highcharts.chart('ptpInterop', {
                    chart: {
                        type: 'column',

                    },
                    title: {
                        text: '',
                        align: 'center'
                    },
                    credits: {
                        enabled: false
                    },
                    subtitle: {
                        align: 'left',
                        text: ''
                    },
                    accessibility: {
                        announceNewData: {
                            enabled: true
                        }
                    },
                    xAxis: {
                        type: 'category'
                    },
                    yAxis: {
                        title: {
                            text: 'Luasan Ha'
                        },
                        labels: {
                            // format: '{value} '
                            formatter: function() {

                                return number_format(this.value, 0) + ' ';
                            }
                        }

                    },
                    legend: {
                        enabled: false
                    },
                    plotOptions: {
                        series: {
                            borderWidth: 0,
                            dataLabels: {

                                // enabled: false,
                                // format: '{point.y} '
                                enabled: true,
                                color: 'white',
                                style: {
                                    fontSize: '7pt'
                                },
                                formatter: function() {
                                    return number_format(this.y) + ' Layanan <br>'
                                },
                                inside: true,
                                rotation: 270,
                                y: -30
                            }
                        }
                    },

                    tooltip: {
                        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
                        pointFormatter: function() {
                            // console.log($('#wp3wtSelect').val())
                            var np = ' Ha'
                            // if ( this.series.chart.drilldownLevels == undefined  ) {
                            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Ha';
                            // } else if(this.series.chart.drilldownLevels.length > 0){
                            //     console.log('if bawah')
                            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+np;
                            // } else if(this.series.chart.drilldownLevels.length == 0){
                            //     console.log('if bawah 2')

                            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Ha';
                            // }else {
                            //     console.log('else')
                            return '<span style="color:' + this.color +
                                '">Layanan</span>: <b>' + number_format(
                                    this.y) + np + '<br><span style="color:' + this.color +
                                '">Luas</span>: <b> : ' + this.luas + ' ha';
                            // }
                        }
                    },

                    series: [{
                        name: 'kemampuanTanah',
                        colorByPoint: true,
                        data: d.data
                    }],


                });

                chartPtpInterop.hideLoading()

            } else if (d.module == 'ptpMonitoringInteropLayanan') {
                console.log(d)
                // alert('okay')
                chartPtpInteropLayanan = Highcharts.chart('ptpInteropLayanan', {
                    chart: {
                        type: 'column',

                    },
                    title: {
                        text: '',
                        align: 'center'
                    },
                    credits: {
                        enabled: false
                    },
                    subtitle: {
                        align: 'left',
                        text: ''
                    },
                    accessibility: {
                        announceNewData: {
                            enabled: true
                        }
                    },
                    xAxis: {
                        type: 'category'
                    },
                    yAxis: {
                        title: {
                            text: 'Luasan Ha'
                        },
                        labels: {
                            // format: '{value} '
                            formatter: function() {

                                return number_format(this.value, 0) + ' ';
                            }
                        }

                    },
                    legend: {
                        enabled: false
                    },
                    plotOptions: {
                        series: {
                            borderWidth: 0,
                            dataLabels: {

                                // enabled: false,
                                // format: '{point.y} '
                                enabled: true,
                                color: 'white',
                                style: {
                                    fontSize: '7pt'
                                },
                                formatter: function() {
                                    return number_format(this.y) + ' Layanan <br>'
                                },
                                inside: true,
                                rotation: 270,
                                y: -30
                            }
                        }
                    },

                    tooltip: {
                        headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
                        pointFormatter: function() {
                            // console.log($('#wp3wtSelect').val())
                            var np = ' Layanan'
                            // if ( this.series.chart.drilldownLevels == undefined  ) {
                            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Ha';
                            // } else if(this.series.chart.drilldownLevels.length > 0){
                            //     console.log('if bawah')
                            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+np;
                            // } else if(this.series.chart.drilldownLevels.length == 0){
                            //     console.log('if bawah 2')

                            //     return '<span style="color:'+this.color+'">'+this.name+'</span>: <b>'+number_format(this.y)+' Ha';
                            // }else {
                            //     console.log('else')
                            return '<span style="color:' + this.color +
                                '">Layanan</span>: <b>' + number_format(
                                    this.y) + 'Layanan <br><span style="color:' + this.color +
                                '">Luas</span>: <b> : ' + this.luas + ' ha';
                            // }
                        }
                    },

                    series: [{
                        name: 'ptp',
                        colorByPoint: true,
                        data: d.data
                    }],


                });

                chartPtpInterop.hideLoading()

            } else if (d.module == 'npgtKabDual') {
                // while (chartNpgtProvDual.series.length) {
                //     chartNpgtProvDual.series[0].remove();
                // }
                $('#kabSelectYearDual').html(d.tahun)
                $('#kabSelectYearDual').selectpicker('refresh')

                // console.log(d.tahun)
                var chartNpgtKabkotDual = Highcharts.chart('npgtKabkotaStack', {

                    chart: {
                        type: 'column'
                    },

                    title: {
                        text: '',
                        align: 'center'
                    },

                    xAxis: {
                        categories: d.category
                    },

                    yAxis: {
                        allowDecimals: false,
                        min: 0,
                        title: {
                            text: ''
                        },
                        labels: {
                            // format: '{value} '
                            formatter: function() {

                                return number_format(this.value) + ' ';
                            }
                        }
                    },

                    tooltip: {
                        formatter: function() {
                            console.log(this)
                            return '<b>' + this.x + '</b><br/>' +
                                'Luas ' + this.point.text + ': ' + number_format(this.y) +
                                ' Ha<br/>';
                            // 'Luas Total: ' + number_format(this.point.stackTotal)+' Ha';
                        }

                    },

                    plotOptions: {
                        column: {
                            stacking: 'normal'
                        }
                    },

                    series: d.data

                });

                // chartNpgtProvDual.addSeries(
                //     d.data,true
                // );

                // chartNpgtProvDual.addSeries({
                //     name: "Data",
                //     data: d.data
                // });

            } else if (d.module == 'npgtKecDual') {
                // while (chartNpgtProvDual.series.length) {
                //     chartNpgtProvDual.series[0].remove();
                // }
                $('#kecSelectYearDual').html(d.tahun)
                $('#kecSelectYearDual').selectpicker('refresh')

                console.log(d.tahun)
                var chartNpgtKecDual = Highcharts.chart('npgtKecStack', {

                    chart: {
                        type: 'column'
                    },

                    title: {
                        text: '',
                        align: 'center'
                    },

                    xAxis: {
                        categories: d.category
                    },

                    yAxis: {
                        allowDecimals: false,
                        min: 0,
                        title: {
                            text: ''
                        },
                        labels: {
                            // format: '{value} '
                            formatter: function() {

                                return number_format(this.value) + ' ';
                            }
                        }
                    },

                    tooltip: {
                        formatter: function() {
                            return '<b>' + this.x + '</b><br/>' +
                                this.point.text + ': ' + number_format(this.y) + ' Ha<br/>';
                            // 'Luas Total: ' + number_format(this.point.stackTotal)+' Ha';
                        }

                    },

                    plotOptions: {
                        column: {
                            stacking: 'normal'
                        }
                    },

                    series: d.data

                });

                // chartNpgtProvDual.addSeries(
                //     d.data,true
                // );

                // chartNpgtProvDual.addSeries({
                //     name: "Data",
                //     data: d.data
                // });

            } else if (d.module == 'npgtKabkot') {
                $('#kabkotSelectTahun').html(d.tahun)
                $('#kabkotSelectTahun').selectpicker('refresh')

                // $('#kabkotSelectProv').html(d.prov)
                // $('#kabkotSelectProv').selectpicker('refresh')

                // chartNpgtKabkota.drillUp();
                chartNpgtKabkota.hideLoading()
                while (chartNpgtKabkota.series.length) {
                    chartNpgtKabkota.series[0].remove();
                }
                chartNpgtKabkota.addSeries({
                    name: 'Data',
                    data: d.data
                });
                // var chartNpgtKabkota =Highcharts.chart('npgtKabkota', {


                //     chart: {
                //         type: 'column'

                //     },credits: {
                //         enabled: false
                //     },
                //     title: {
                //         text: '',
                //         align:'center'
                //     },
                //     xAxis: {
                //         categories: d.category  
                //     },

                //     yAxis: {
                //         allowDecimals: false,
                //         min: 0,
                //         title: {
                //             text: ''
                //         },
                //         labels: {
                //             // format: '{value} '
                //             formatter: function () {

                //                 return number_format(this.value)+' ';
                //             }
                //         }
                //     },

                //     tooltip: {
                //         formatter: function () {
                //             console.log(this)
                //             return '<b>' + this.x + '</b><br/>' +
                //             'Total Kabupaten: ' + number_format(this.point.stackTotal)+' Kabupaten</br>'+
                //             'Provinsi '+this.series.name + ': ' + number_format(this.y) + ' Kabupaten<br/>' ;
                //             // 'Total Luas Wilayah : '+number_format(this.point.luas)+' Ha';
                //         }

                //     },

                //     plotOptions: {
                //         column: {
                //             stacking: 'normal'
                //         }
                //     },
                //     series: d.data

                // });
            } else if (d.module == 'npgtKec') {
                $('#kecSelectYear').html(d.tahun)
                $('#kecSelectYear').selectpicker('refresh')

                // chartNpgtKec.drillUp();
                chartNpgtKec.hideLoading()
                while (chartNpgtKec.series.length) {
                    chartNpgtKec.series[0].remove();
                }
                chartNpgtKec.addSeries({
                    name: 'Data',
                    data: d.data
                });

                // console.log(d.data)
                // var chartNpgtkec =Highcharts.chart('npgtKec', {


                //     chart: {
                //         type: 'column'

                //     },credits: {
                //         enabled: false
                //     },
                //     title: {
                //         text: '',
                //         align:'center'
                //     },
                //     xAxis: {
                //         categories: d.category  
                //     },

                //     yAxis: {
                //         allowDecimals: false,
                //         min: 0,
                //         title: {
                //             text: ''
                //         },
                //         labels: {
                //             // format: '{value} '
                //             formatter: function () {

                //                 return number_format(this.value)+' ';
                //             }
                //         }
                //     },

                //     tooltip: {
                //         formatter: function () {
                //             console.log(this)
                //             return '<b>' + this.x + '</b><br/>' +
                //             'Total Kecamatan: ' + number_format(this.point.stackTotal)+' Kecamatan </br>'+
                //             'Kecamatan '+this.point.text+':'+ number_format(this.y) + ' Kecamatan <br/>' ;

                //         }

                //     },

                //     plotOptions: {
                //         column: {
                //             stacking: 'normal'
                //         }
                //     },
                //     series: d.data

                // });
            } else if (d.module == 'npgtPerkebunan') {
                // chartNpgtKabkota.drillUp();
                // hideLoad()
                $('#perkebunanSelectTahun').html(d.tahun)
                $('#perkebunanSelectTahun').selectpicker('refresh')
                chartNpgtPerkebunan.hideLoading()
                while (chartNpgtPerkebunan.series.length) {
                    chartNpgtPerkebunan.series[0].remove();
                }
                chartNpgtPerkebunan.addSeries({
                    name: 'Data',
                    data: d.data
                });
                console.log(d.data)

                // var chartNpgtPerkebunan =Highcharts.chart('npgtPerkebunan', {

                //     chart: {
                //         type: 'column'

                //     },credits: {
                //         enabled: false
                //     },
                //     title: {
                //         text: '',
                //         align:'center'
                //     },
                //     xAxis: {
                //         categories: d.category  
                //     },

                //     yAxis: {
                //         allowDecimals: false,
                //         min: 0,
                //         title: {
                //             text: ''
                //         },
                //         labels: {
                //             // format: '{value} '
                //             formatter: function () {

                //                 return number_format(this.value)+' ';
                //             }
                //         }
                //     },

                //     tooltip: {
                //         formatter: function () {
                //             console.log(this)
                //             return '<b>' + this.x + '</b><br/>' +
                //                 'Total Provinsi: ' + number_format(this.point.stackTotal)+'</br>'+                                
                //                 this.point.text + ': ' + number_format(this.y) + '<br/>' ;
                //         }

                //     },

                //     plotOptions: {
                //         column: {
                //             stacking: 'normal'
                //         }
                //     },
                //     series: d.data

                // });
            } else if (d.module == 'npgtPerkebunanStack') {
                // chartNpgtKabkota.drillUp();
                // hideLoad();
                $('#perkebunanSelectTahunDual').html(d.tahun)
                $('#perkebunanSelectTahunDual').selectpicker('refresh')
                chartNpgtPerkebunanDual.hideLoading()
                while (chartNpgtPerkebunanDual.series.length) {
                    chartNpgtPerkebunanDual.series[0].remove();
                }
                chartNpgtPerkebunanDual.addSeries({
                    name: 'Data',
                    data: d.data
                });
                // console.log('oke masuk kebun')

                // var chartNpgtPerkebunanDual =Highcharts.chart('npgtPerkebunanStack', {

                //     chart: {
                //         type: 'column'
                //     },

                //     title: {
                //         text: '',
                //         align:'center'
                //     },

                //     xAxis: {
                //         categories: d.category  
                //     },

                //     yAxis: {
                //         allowDecimals: false,
                //         min: 0,
                //         title: {
                //             text: ''
                //         },
                //         labels: {
                //             // format: '{value} '
                //             formatter: function () {

                //                 return number_format(this.value)+' ';
                //             }
                //         }
                //     },

                //     tooltip: {
                //         formatter: function () {
                //             console.log(this)
                //             return '<b>' + this.x + '</b><br/>' +
                //             this.point.text + ': ' + number_format(this.y) + ' Ha<br/>' ;
                //             // 'Luas Total: ' + number_format(this.point.stackTotal)+' Ha';
                //         }

                //     },

                //     plotOptions: {
                //         column: {
                //             stacking: 'normal'
                //         }
                //     },

                //     series: d.data

                // });
            } else if (d.module == 'ptp') {

                console.log(d.tahun)
                $('#ptpSelectYear').html(d.tahun)
                $('#ptpSelectYear').selectpicker('refresh')

                if (d.isPer > 0) {
                    $('#divPertanian').css('display', 'block');
                } else {
                    $('#divPertanian').css('display', 'none');
                }
                // $('#ptpSelectTanah').html(d.strKlas)
                // $('#ptpSelectTanah').selectpicker('refresh')

                // chartPtpInteropLayanan.hideLoading()
                // while (chartPtpInteropLayanan.series.length) {
                //     chartPtp.drillUp();
                //     chartPtp.series[0].remove();
                // }
                // chartPtpInteropLayanan.addSeries({
                //     name: 'Data',
                //     data: d.data
                // });
                // // chartPtp.axisTitle.attr({
                // //     text: 'new title'
                // // });
                // chartPtp.yAxis[0].setTitle({
                //     text: d.title
                // });
            } else {

            }

        }

    });
}

function wp3wtChangeIf() {
    var v = $('#wp3wtSelect').val();
    if (v == 'spatial.v_d_wp3wt_ppkt' || v == 'spatial.v_d_wp3wt_ppk_point') {
        wp3wtChangeKab()
    } else {
        wp3wtChange()
    }
}


function wp3wtChange() {
    var y = $('#wp3wtSelectYear').val();
    var v = $('#wp3wtSelect').val();
    var k = $('#wp3wtSelectKab').val();
    var kec = $('#wp3wtSelectKec').val();
    var p = $('#wp3wtSelectPxn').val();
    // console.log(y+'/'+v);
    chartWp3wt.showLoading('Silahkan Tunggu ...')

    $('#wp3wtSelectProv').selectpicker('refresh')
    $('#wp3wtSelectKab').selectpicker('refresh')
    $('#wp3wtSelectKec').selectpicker('refresh')
    if (v == 'spatial.v_d_wp3wt_ppkt' || v == 'spatial.v_d_wp3wt_ppk_point') {
        // $('#wp3wtSelectPxn').prop('disabled',true)
        // console.log(kec+'  oke')
        // if (v == 'spatial.v_d_wp3wt_ppk_point') {
        var url = 'dashboard4/wp3wtPulau/' + v + '/' + y + '/' + kec;
        // }else{
        //     var url = 'dashboard4/wp3wt/'+p+'/'+v+'/'+y+'/'+k;
        // }
        // var element = document.getElementById('wp3wtSelectPxn');
        // element.disabled = true;
        // alert($('#wp3wtSelectPxn').val());
        // alert(url)
        getchart(url);
    } else {
        // var element = document.getElementById('wp3wtSelectPxn');
        // element.disabled = false;
        // $('#wp3wtSelectPxn').css('diaplay','block')
        var url = 'dashboard4/wp3wt/' + p + '/' + v + '/' + y + '/' + k;
        // alert(url)
        getchart(url);
    }



}

function wp3wtChangeLayer() {
    var y = $('#wp3wtSelectYear').val();
    var layer = $('#wp3wtSelect').val();
    var p = $('#wp3wtSelectPxn').val();
    // console.log(p)

    if (layer == 'spatial.v_d_wp3wt_ppk_polygon') {
        $('#divWp3wtProv').css('display', 'none')
        $('#divWp3wtKab').css('display', 'none')
        $('#divWp3wtKec').css('display', 'none')
        $('#divWp3wtPxn').css('display', 'none')
        var url = 'dashboard4/wp3wtPkkPoly/' + layer + '/' + y;
        chartWp3wt.showLoading('Silahkan Tunggu ...')

        getchart(url);
    } else if (layer == 'spatial.v_d_wp3wt_ppkt' || layer == 'spatial.v_d_wp3wt_ppk_point') {
        $('#divWp3wtKec').css('display', 'block')
        $('#divWp3wtPxn').css('display', 'none')
        $('#divWp3wtKab').css('display', 'block')
        $('#divWp3wtProv').css('display', 'block')
        chartWp3wt.showLoading('Silahkan Tunggu ...')

        $.get("<?php echo base_url(); ?>dashboard4/getFilterProvWp3wt/" + layer, function(data) {
            data = JSON.parse(data)
            $('#wp3wtSelectProv').html(data.prov)
            // $('#wp3wtSelectYear').html(data.tahun)
            // $('#wp3wtSelectYear').selectpicker('refresh')
            $('#wp3wtSelectProv').selectpicker('refresh')
            wp3wtChangeProv()
            // $('#wp3wtSelectYear').val(); 
        });
    } else {
        $('#divWp3wtProv').css('display', 'block')
        $('#divWp3wtKab').css('display', 'block')
        $('#divWp3wtKec').css('display', 'none')
        $('#divWp3wtPxn').css('display', 'block')
        if (p == 'null') {
            return false
        }
        chartWp3wt.showLoading('Silahkan Tunggu ...')

        $.get("<?php echo base_url(); ?>dashboard4/getFilterProvWp3wt/" + layer + '_' + p, function(data) {
            data = JSON.parse(data)
            $('#wp3wtSelectProv').html(data.prov)
            $('#wp3wtSelectYear').html(data.tahun)
            $('#wp3wtSelectYear').selectpicker('refresh')
            $('#wp3wtSelectProv').selectpicker('refresh')
            wp3wtChangeProv()
            // $('#wp3wtSelectYear').val(); 
        });
    }


}

function wp3wtChangeProv() {
    var y = $('#wp3wtSelectYear').val();
    var layer = $('#wp3wtSelect').val();
    var p = $('#wp3wtSelectPxn').val();
    var prov = $('#wp3wtSelectProv').val();
    chartWp3wt.showLoading('Silahkan Tunggu ...')

    if (layer == 'spatial.v_d_wp3wt_ppkt' || layer == 'spatial.v_d_wp3wt_ppk_point' || layer ==
        'spatial.v_d_wp3wt_ppk_polygon') {
        $.get("<?php echo base_url(); ?>dashboard4/getFilterKabWp3wt/" + layer + '/' + prov, function(data) {
            data = JSON.parse(data)
            $('#wp3wtSelectKab').html(data.kab)
            // $('#wp3wtSelectYear').html(data.tahun)
            // $('#wp3wtSelectYear').selectpicker('refresh')
            $('#wp3wtSelectKab').selectpicker('refresh')
            wp3wtChangeKab()
            // $('#wp3wtSelectYear').val(); 
        });
    } else {
        $.get("<?php echo base_url(); ?>dashboard4/getFilterKabWp3wt/" + layer + '_' + p + '/' + prov, function(data) {
            data = JSON.parse(data)
            $('#wp3wtSelectKab').html(data.kab)
            // $('#wp3wtSelectYear').html(data.tahun)
            // $('#wp3wtSelectYear').selectpicker('refresh')
            $('#wp3wtSelectKab').selectpicker('refresh')
            wp3wtChange()
            // $('#wp3wtSelectYear').val(); 
        });
    }
}

function wp3wtChangeKab() {
    var layer = $('#wp3wtSelect').val()
    var kab = $('#wp3wtSelectKab').val()
    var y = $('#wp3wtSelectYear').val();
    chartWp3wt.showLoading('Silahkan Tunggu ...')

    $.get("<?php echo base_url(); ?>dashboard4/getFilterKecNpgt/" + layer + '/' + kab, function(data) {
        data = JSON.parse(data)
        // console.log(data.tahun)
        // console.log(data.prov)
        // $('#pgtlSelectYear').html(data.tahun)
        $('#wp3wtSelectKec').html(data.kec)
        $('#wp3wtSelectKec').selectpicker('refresh')
        // $('#pgtlSelectYear').selectpicker('refresh')
        wp3wtChange()

    });
}

function pgtlChange() {
    var y = $('#pgtlSelectYear').val();
    var v = $('#pgtlSelect').val();
    var p = $('#pgtlSelectProv').val();
    var k = $('#pgtlSelectKab').val();
    var kec = $('#pgtlSelectKec').val();
    // alert(v)

    chartPgtl.showLoading('Silahkan Tunggu ...')

    if (v == 'spatial.v_d_pgtl_kecamatan') {
        var url = 'dashboard4/pgtlKec/' + v + '/' + k + '/' + y;
        getchart(url);
    } else if (v == 'spatial.v_d_pgtl_rtrw') {
        var url = 'dashboard4/pgtl/' + v + '/' + p + '/' + y;
        getchart(url);
    } else if (v == 'spatial.v_d_pgtl_desa') {
        // alert('masuk')
        // return false
        pgtlChangeKab()
    }
}


function pgtlChangeLayer(v) {
    chartPgtl.showLoading('Silahkan Tunggu ...')

    var layer = $('#pgtlSelect').val()
    $('#divPgtlKab').css('display', 'block')
    $('#divPgtlKec').css('display', 'block')


    if (layer == 'spatial.v_d_pgtl_rtrw') {
        $('#divPgtlKec').css('display', 'none')
        $('#divPgtlKab').css('display', 'none')
        // $('#divPgtlKec').css('display','none')

    } else if (layer == 'spatial.v_d_pgtl_kecamatan') {
        $('#divPgtlKec').css('display', 'none')
    }
    $.get("<?php echo base_url(); ?>dashboard4/getFilterProvPgtl/" + layer, function(data) {
        data = JSON.parse(data)
        console.log(data.tahun)
        console.log(data.prov)

        $('#pgtlSelectYear').html(data.tahun)
        $('#pgtlSelectProv').html(data.prov)
        $('#pgtlSelectProv').selectpicker('refresh')
        $('#pgtlSelectYear').selectpicker('refresh')
        pgtlChangeProv()

    });
}

function pgtlChangeProv() {
    var layer = $('#pgtlSelect').val()
    var p = $('#pgtlSelectProv').val()
    var y = $('#pgtlSelectYear').val();
    chartPgtl.showLoading('Silahkan Tunggu ...')

    if (layer == 'spatial.v_d_pgtl_kecamatan') {
        $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + p, function(data) {
            data = JSON.parse(data)
            $('#pgtlSelectKab').html(data.kab)
            // $('#pgtlSelectYear').html(data.tahun)
            // $('#pgtlSelectYear').selectpicker('refresh')
            $('#pgtlSelectKab').selectpicker('refresh')
            pgtlChange()
            // $('#pgtlSelectYear').val(); 
        });
    } else if (layer == 'spatial.v_d_pgtl_desa') {
        $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + p, function(data) {
            data = JSON.parse(data)
            $('#pgtlSelectKab').html(data.kab)
            // $('#pgtlSelectYear').html(data.tahun)
            // $('#pgtlSelectYear').selectpicker('refresh')
            $('#pgtlSelectKab').selectpicker('refresh')
            pgtlChangeKab()
            // $('#pgtlSelectYear').val(); 
        });
    } else if (layer == 'spatial.v_d_pgtl_rtrw') {
        var url = 'dashboard4/pgtl/' + layer + '/' + p + '/' + y;
        getchart(url);
    }

}

function pgtlChangeKab() {
    var layer = $('#pgtlSelect').val()
    var kab = $('#pgtlSelectKab').val()
    var kec = $('#pgtlSelectKec').val()
    var y = $('#pgtlSelectYear').val();
    chartPgtl.showLoading('Silahkan Tunggu ...')

    $.get("<?php echo base_url(); ?>dashboard4/getFilterKecNpgt/" + layer + '/' + kab, function(data) {
        data = JSON.parse(data)
        // console.log(data.tahun)
        // console.log(data.prov)
        $('#pgtlSelectYear').html(data.tahun)
        $('#pgtlSelectKec').html(data.kec)
        $('#pgtlSelectKec').selectpicker('refresh')
        $('#pgtlSelectYear').selectpicker('refresh')
        console.log('from kab')
        kec = $('#pgtlSelectKec').val()
        var url = 'dashboard4/pgtlDesa/' + layer + '/' + kec + '/' + y;
        getchart(url);

    });
}

function pgtlChangeKec() {
    console.log('from kec')
    chartPgtl.showLoading('Silahkan Tunggu ...')

    var layer = $('#pgtlSelect').val()
    var kab = $('#pgtlSelectKab').val()
    var kec = $('#pgtlSelectKec').val()
    var y = $('#pgtlSelectYear').val();
    var url = 'dashboard4/pgtlDesa/' + layer + '/' + kec + '/' + y;
    getchart(url);
}

function lahanBakuProv(v) {
    var layer = $('#pgtlSelect').val()
    chartLahanBaku.showLoading('Silahkan Tunggu ...')

    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + v, function(data) {
        data = JSON.parse(data)
        // console.log(data.kab)
        $('#lbsSelectKab').html(data.kab)
        // $('#lahanBakuSelectTahun').html(data.tahun)
        // $('#lahanBakuSelectTahun').selectpicker('refresh')
        $('#lbsSelectKab').selectpicker('refresh')
        chartLahanBaku.hideLoading()

        // if(data.sts == 'success'){
        //     lahanBaku()
        // }else{
        //     while (chartLahanBaku.series.length) {
        //         chartLahanBaku.series[0].remove();
        //     }
        // }
    });
}

function lahanBaku() {
    chartLahanBaku.showLoading('Silahkan Tunggu ...')

    var y = $('#lahanBakuSelectTahun').val();
    var p = $('#lbsSelectProv').val() == '' ? 'null' : $('#lbsSelectProv').val();
    var k = $('#lbsSelectKab').val();

    var url = 'dashboard4/lahanBaku/' + y + '/' + p + '/' + k;
    console.log(url)
    setTimeout(function() {
        getchart(url);
    }, 200);

}

function lahanBakuProv(v) {
    var layer = 'spatial.v_d_lahanbaku'
    chartLahanBaku.showLoading('Silahkan Tunggu ...')

    $.get("<?php echo base_url(); ?>dashboard4/getFilterKabLbs/" + layer + '/' + v, function(data) {
        data = JSON.parse(data)
        // console.log(data.kab)
        $('#lbsSelectKab').html(data.kab)
        $('#lahanBakuSelectTahun').html(data.tahun)
        $('#lahanBakuSelectTahun').selectpicker('refresh')
        $('#lbsSelectKab').selectpicker('refresh')
        if (data.sts == 'success') {
            lahanBaku()
        } else {
            chartLahanBaku.showLoading('Silahkan Tunggu ...')
            while (chartLahanBaku.series.length) {
                chartLahanBaku.series[0].remove();
            }
        }
    });
}

function mpptChange() {
    chartMppt.showLoading('Silahkan Tunggu ...')

    var y = $('#mpptSelectTahun').val();
    var k = $('#mpptSelectKab').val();

    var url = 'dashboard4/mppt/' + y + '/' + k;
    setTimeout(function() {
        getchart(url);
    }, 200);

}

function mpptChangeKab() {
    var layer = 'spatial.v_d_mppt';
    var p = $('#mpptSelectProv').val();
    chartMppt.showLoading('Silahkan Tunggu ...')

    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + p, function(data) {
        data = JSON.parse(data)
        // console.log(data.kab)
        $('#mpptSelectKab').html(data.kab)
        $('#mpptSelectTahun').html(data.tahun)
        $('#mpptSelectTahun').selectpicker('refresh')
        $('#mpptSelectKab').selectpicker('refresh')
        if (data.sts == 'success') {
            mpptChange()
        } else {
            chartMppt.showLoading('Silahkan Tunggu ...')

            while (chartMppt.series.length) {
                chartMppt.series[0].remove();
            }
        }
    });
}

function npgtProv() {

    chartNpgtProv.showLoading('Silahkan Tunggu ...')

    var y = $('#ProvSelectYear').val();
    // var p= $('#provSelectProv').val(); 
    var url = 'dashboard4/npgtProv/' + y;
    // alert(url)
    setTimeout(function() {
        getchart(url);
    }, 200);

}

function npgtPerkebunan() {
    chartNpgtPerkebunan.showLoading('Silahkan Tunggu ...')
    // showLoad()
    var y = $('#perkebunanSelectTahun').val();
    var k = $('#kebunSelectKab').val();
    var url = 'dashboard4/npgtPerkebunan/' + y + '/' + k;
    // alert(url)
    setTimeout(function() {
        getchart(url);
    }, 200);

}

function npgtPerkebunanKab() {

    // showLoad()
    chartNpgtPerkebunan.showLoading('Silahkan Tunggu ...')

    var layer = 'spatial.v_d_npgt_perkebunan';
    var p = $('#kebunSelectProv').val();
    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + p, function(data) {
        data = JSON.parse(data)
        // console.log(data.kab)
        // hideLoad()
        $('#kebunSelectKab').html(data.kab)
        $('#perkebunanSelectTahun').html(data.tahun)
        $('#perkebunanSelectTahun').selectpicker('refresh')
        $('#kebunSelectKab').selectpicker('refresh')
        if (data.sts == 'success') {
            // hideLoad()
            npgtPerkebunan()
        } else {
            // hideLoad()
            chartNpgtPerkebunan.hideLoading()
            while (chartNpgtPerkebunan.series.length) {
                chartNpgtPerkebunan.series[0].remove();
            }
        }
    });
}

function npgtPerkebunanDual() {
    // showLoad()
    chartNpgtPerkebunanDual.showLoading('Silahkan Tunggu ...')

    var k = $('#kebunSelectKabDual').val();
    var jns = $('#kebunSelectJnsDual').val();
    var layer = 'spatial.npgt_perkebunan';

    if (jns == "kosong") return false;

    if (role == 7 || role == 8) {
        $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + k, function(data) {
            data = JSON.parse(data);
            $('#perkebunanSelectTahunDual').html('<option>Tahun Pengerjaan</option>');
            $('#perkebunanSelectTahunDual').append(data.tahun);
            $('#perkebunanSelectTahunDual').selectpicker('refresh');
        }).done(function() {
            var url = 'dashboard4/npgtPerkebunanDual/' + k + '/' + $('#perkebunanSelectTahunDual').val() + '/' +
                jns;
            setTimeout(function() {
                getchart(url);
            }, 200);
        });
    } else {
        var y = $('#perkebunanSelectTahunDual').val();
        var url = 'dashboard4/npgtPerkebunanDual/' + k + '/' + y + '/' + jns;
        setTimeout(function() {
            getchart(url);
        }, 200);
    }

}

function npgtPerkebunanDualKab() {
    // showLoad()
    // hideLoad()
    chartNpgtPerkebunanDual.showLoading('Silahkan Tunggu ...')

    var layer = 'spatial.npgt_perkebunan';
    var p = $('#kebunSelectProvDual').val();
    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + p, function(data) {
        data = JSON.parse(data)

        $('#kebunSelectKabDual').html(data.kab)
        $('#perkebunanSelectTahunDual').html(data.tahun)
        // $('#kebunSelectJnsDual').html(str)
        $('#perkebunanSelectTahunDual').selectpicker('refresh')
        $('#kebunSelectKabDual').selectpicker('refresh')
        $('#kebunSelectJnsDual').selectpicker('refresh')
        if (data.sts == 'success') {
            // hideLoad()
            npgtPerkebunanDual()
        } else {
            // hideLoad()
            chartNpgtPerkebunanDual.hideLoading()
            while (chartNpgtPerkebunanDual.series.length) {
                chartNpgtPerkebunanDual.series[0].remove();
            }
        }
    });
}

function npgtProvDual() {
    // showLoad()
    // var l= $('#provSelectLayer').val(); 
    chartNpgtProvStack.showLoading('Silahkan Tunggu ...')

    var t = $('#provSelectYearDual').val();
    var p = $('#provSelectProvDual').val();
    var url = 'dashboard4/npgtProvDual/' + p + '/' + t;
    // alert(url)
    setTimeout(function() {
        getchart(url);
    }, 200);
}

function npgtKabDual() {
    // var v= $('#kabSelect').val(); 
    var y = $('#kabSelectYearDual').val();
    var p = $('#kabSelectProvDual').val();
    var url = 'dashboard4/npgtKabkotDual/' + p + '/' + y;
    setTimeout(function() {
        getchart(url);
    }, 200);

}

// dunction get data 
function npgtKabkotChange() {
    var k = $('#kabkotSelectKab').val();
    var y = $('#kabkotSelectTahun').val();
    // var v= $('#kabkotSelect').val(); 
    // if(v==''){
    //     v='kosong';
    // }
    // alert(v)
    chartNpgtKabkota.showLoading('Silahkan Tunggu ...')

    var url = 'dashboard4/npgtKabkota/' + k + '/' + y;
    setTimeout(function() {
        getchart(url);
    }, 200);

}

function npgtKabkotChangeProv(v) {
    chartNpgtKabkota.showLoading('Silahkan Tunggu ...')

    var layer = 'spatial.v_d_npgt_kabkota'
    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + v, function(data) {
        data = JSON.parse(data)
        // console.log(data.kab)
        $('#kabkotSelectKab').html(data.kab)
        $('#kabkotSelectTahun').html(data.tahun)
        $('#kabkotSelectTahun').selectpicker('refresh')
        $('#kabkotSelectKab').selectpicker('refresh')
        if (data.sts == 'success') {
            npgtKabkotChange()
        } else {
            chartNpgtKabkota.hideLoading()

            while (chartNpgtKabkota.series.length) {
                chartNpgtKabkota.series[0].remove();
            }
        }
    });
}

function npgtKecChange() {
    var y = $('#kecSelectYear').val();
    var k = $('#kecSelectKec').val();

    chartNpgtKec.showLoading('Silahkan Tunggu ...');
    var url = 'dashboard4/npgtKec/' + k + '/' + y;
    // alert(url)

    setTimeout(function() {
        getchart(url);
    }, 200);
}

function npgtKec(v) {
    var layer = 'spatial.v_d_npgt_kec';
    var k = $('#kecSelectKab').val();
    var kec = $('#kecSelectKec').val();
    $.get("<?php echo base_url(); ?>dashboard4/getFilterKecNpgt/" + layer + '/' + k, function(data) {
        data = JSON.parse(data)
        // console.log(data.kab)

        if (data.sts == 'success') {
            $('#kecSelectKec').html(data.kec)
            $('#kecSelectTahun').html(data.tahun)
            $('#kecSelectTahun').selectpicker('refresh')
            $('#kecSelectKec').selectpicker('refresh')
            npgtKecChange()
            // chartNpgtKec.showLoading('Silahkan Tunggu ...')

        } else if (data.sts == 'null') {

        } else {
            chartNpgtKec.hideLoading();
            while (chartNpgtKec.series.length) {
                chartNpgtKec.series[0].remove();
            }
        }
    });
}

function npgtKecProv() {
    chartNpgtKec.showLoading('Silahkan Tunggu ...');

    var layer = 'spatial.v_d_npgt_kec'
    var k = $('#kecSelectProv').val()
    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + k, function(data) {
        data = JSON.parse(data)
        // $('#pgtlSelectYear').html(data.tahun)
        $('#kecSelectKab').html(data.kab)
        $('#kecSelectKab').selectpicker('refresh')
        // $('#kecSelectKec').html(data.kec)
        // $('#kecSelectKec').selectpicker('refresh')
        $('#kecSelectYear').html(data.tahun)
        $('#kecSelectYear').selectpicker('refresh')
        // $('#loader').addClass('hidden')

        if (data.sts == 'success') {
            npgtKec()
            // chartNpgtKec.hideLoading();
        } else {
            // $('#kecSelectKab').html(data.kab)
            // $('#kecSelectKab').selectpicker('refresh')
            // $('#kecSelectKec').html(data.kab)
            // $('#kecSelectKec').selectpicker('refresh')
            // $('#kecSelectYear').html(data.tahun)
            // $('#kecSelectYear').selectpicker('refresh')
            // chartNpgtKec.hideLoading();
            chartNpgtKec.hideLoading();
            while (chartNpgtKec.series.length) {
                chartNpgtKec.series[0].remove();
            }
        }
    });
}


function npgtKecDual() {
    var k = $('#kecSelectKabDual').val();
    var y = $('#kecSelectYearDual').val();
    var url = 'dashboard4/npgtKecDual/' + k + '/' + y;
    setTimeout(function() {
        getchart(url);
    }, 200);

}

function tanahNegaraChange() {
    var y = $('#tnSelectTahun').val();
    var v = $('#tanahNegaraSelect').val();
    var k = $('#tnSelectKab').val();

    var url = 'dashboard4/tanahNegara/' + v + '/' + y + '/' + k;
    getchart(url);
}

function tanahNegaraChangeProv(v) {
    // var layer = 'spatial.v_d_lahanbaku'
    chartTanahNegara.showLoading('Silahkan Tunggu ...')

    $.get("<?php echo base_url(); ?>dashboard4/getFilterProv/" + v, function(data) {
        data = JSON.parse(data)
        console.log(data.tahun)
        console.log(data.prov)
        // $('#pgtlSelectYear').html(data.tahun)
        $('#tnSelectProv').html(data.prov)
        $('#tnSelectProv').selectpicker('refresh')
        // $('#pgtlSelectYear').selectpicker('refresh')
        tanahNegaraChangeKab()
    });
}

function tanahNegaraChangeKab() {
    var layer = $('#tanahNegaraSelect').val();
    var p = $('#tnSelectProv').val();
    chartTanahNegara.showLoading('Silahkan Tunggu ...')

    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + p, function(data) {
        data = JSON.parse(data)
        // console.log(data.kab)
        $('#tnSelectKab').html(data.kab)
        $('#tnSelectTahun').html(data.tahun)
        $('#tnSelectTahun').selectpicker('refresh')
        $('#tnSelectKab').selectpicker('refresh')
        if (data.sts == 'success') {
            tanahNegaraChange()
        } else {
            chartTanahNegara.hideLoading()

            while (chartTanahNegara.series.length) {
                chartTanahNegara.series[0].remove();
            }
        }
    });
}

function kemampuanTanahChange() {
    chartKemampuanTanah.showLoading()

    var y = $('#kemampuanSelectTahun').val();
    var k = $('#ktSelectKab').val();
    var v = $('#kemampuanTanahSelect').val();
    var url = 'dashboard4/kemampuanTanah/' + k + '/' + y;
    // var url = 'dashboard4/kemampuanTanahOld/'+v;
    // alert(y)
    getchart(url);
}

function kemampuanTanahChangeKab() {
    var layer = 'spatial.kemampuan_tanah';
    var p = $('#ktSelectProv').val();
    chartKemampuanTanah.showLoading('Silahkan Tunggu ...')

    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + layer + '/' + p, function(data) {
        data = JSON.parse(data)
        // console.log(data.kab)
        $('#ktSelectKab').html(data.kab)
        $('#ktSelectTahun').html(data.tahun)
        $('#ktSelectTahun').selectpicker('refresh')
        $('#ktSelectKab').selectpicker('refresh')
        if (data.sts == 'success') {
            kemampuanTanahChange()
        } else {
            chartKemampuanTanah.showLoading('Silahkan Tunggu ...')
            while (chartKemampuanTanah.series.length) {
                chartKemampuanTanah.series[0].remove();
            }
        }
    });
}

function ptpChange() {
    chartPtp.showLoading('Silahkan Tunggu ...')

    var y = $('#ptpSelectYear').val();
    var p = $('#ptpSelectProv').val();
    var v = $('#ptpSelect').val();
    var jns = $('#ptpSelectTanah').val();
    if (role == 7 || role == 8) {
        p = $('#ptpSelectKab').val();
    }
    if (p == "") return false;
    var url = 'dashboard4/ptp/' + v + '/' + y + '/' + p + '/' + jns;
    setTimeout(function() {
        getchart(url);
    }, 200);
}

function ptpChangeMonitoring(v) {
    chartPtp.showLoading('Silahkan Tunggu ...')

    v = v.replace('v_d', 'spatial.v_d_monitoring')

    var url = 'dashboard4/ptpMonitoring/' + v;
    setTimeout(function() {
        getchart(url);
    }, 200);
}

function ptpChangeMonitoringInterop() {
    chartPtp.showLoading('Silahkan Tunggu ...')

    var y = $('#ptpSelectYear').val();
    var p = $('#ptpSelectProvInterop').val();
    var k = $('#ptpSelectKabInterop').val();
    var v = $('#ptpSelectInterop').val();
    var jns = $('#ptpSelectTanahInterop').val();
    if (p == "") return false;
    var url = 'dashboard4/ptpMonitoringInterop/' + v + '/' + k;
    setTimeout(function() {
        getchart(url);
    }, 200);
}

function ptpChangeMonitoringInteropLayanan() {
    chartPtpInterop.showLoading('Silahkan Tunggu ...')

    var y = $('#ptpInteropLayananTahun').val();
    var p = $('#ptpSelectProvInteropLayanan').val();
    var k = $('#ptpSelectKabInteropLayanan').val();
    var v = $('#ptpSelectInteropLayanan').val();
    var jns = $('#ptpSelectTanahInterop').val();
    if (p == "") return false;
    var url = 'dashboard4/ptpMonitoringInteropLayanan/' + v + '/' + k + '/' + y;
    setTimeout(function() {
        getchart(url);
    }, 200);
}


function ptpChangeProv(v) {

    // alert(v);

    // var layer = 'spatial.v_d_lahanbaku'


    chartPtp.showLoading('Silahkan Tunggu ...')



    if (role == 7 || role == 8) {
        $.get("<?php echo base_url(); ?>dashboard4/getFilterProv/" + v + '/' + kdpkab, function(data) {
            data = JSON.parse(data);
            $('#ptpSelectYear').html(data.tahun)
            $('#ptpSelectKab').html(data.kab)
            $('#ptpSelectYear').selectpicker('refresh')
            $('#ptpSelectKab').selectpicker('refresh')
        });
    } else {
        $.get("<?php echo base_url(); ?>dashboard4/getFilterProv/" + v, function(data) {
            data = JSON.parse(data)
            // console.log(data.tahun)
            // console.log(data.prov)
            $('#ptpSelectYear').html(data.tahun)
            $('#ptpSelectProv').html(data.prov)
            $('#ptpSelectProv').selectpicker('refresh')
            $('#ptpSelectYear').selectpicker('refresh')
        });
    }

    // ptpChange()
}


function ptpChangeKab(v) {

    // alert(v);

    // var layer = 'spatial.v_d_lahanbaku'


    chartPtp.showLoading('Silahkan Tunggu ...')

    view = $('#ptpSelect').val()


    $.get("<?php echo base_url(); ?>dashboard4/getFilterKab/" + view + '/' + v, function(data) {
        data = JSON.parse(data);
        $('#ptpSelectYear').html(data.tahun)
        $('#ptpSelectKab').html(data.kab)
        $('#ptpSelectYear').selectpicker('refresh')
        $('#ptpSelectKab').selectpicker('refresh')
        ptpChangeLayanan()
    });

}

function ptpChangeLayanan() {
    chartPtp.showLoading('Silahkan Tunggu ...')

    var y = $('#ptpSelectYear').val();
    var p = $('#ptpSelectProv').val();
    var v = $('#ptpSelect').val();
    var k = $('#ptpSelectKab').val();
    if (role == 7 || role == 8) {
        p = $('#ptpSelectKab').val();
    }
    if (p == "") return false;
    var url = 'dashboard4/ptpLayanan/' + v + '/' + y + '/' + p + '/' + k;
    setTimeout(function() {
        getchart(url);
    }, 200);
}

function number_format(number, decimals, dec_point, thousands_sep) {
    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');

    var n = !isFinite(+number) ? 0 : +number,

        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),

        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,

        dec = (typeof dec_point === 'undefined') ? ',' : dec_point,

        s = '',

        toFixedFix = function(n, prec) {
            var k = Math.pow(10, prec);
            return '' + (Math.round(n * k) / k).toFixed(prec);
        };

    // Fix for IE parseFloat(0.55).toFixed(0) = 0;

    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec - s[1].length + 1).join('0');
    }
    return s.join(dec);

}

var spinHandle;

function showLoad(params) {
    spinHandle = loadingOverlay.activate();
}

function hideLoad() {
    loadingOverlay.cancel(spinHandle);
}

function initComboboxProv(divname, refindex, parwhere = null, $rev = 1) {


    var url = null;
    if ($rev === 1) {
        url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex + "/" + parwhere;
    } else {
        url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex + "/" + parwhere + "/" + 0;
    }
    // return false;
    wgiAjaxCache(url, function(ajaxdata) {
        jdata = JSON.parse(ajaxdata);
        // console.log(jdata)
        $('#' + divname).empty();
        $('#' + divname).append(new Option("Pilih Provinsi", ""));
        $.each(jdata, function(i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });
        $('#' + divname).selectpicker('refresh')

    });
}

function initComboboxProvSelect(divname, val_id) {


    url = WGI_APP_BASE_URL + "lookup/fieldlookSelectProv/aset_r_provinsi/kd_prov/" + val_id;
    // return false;
    wgiAjaxCache(url, function(ajaxdata) {
        jdata = JSON.parse(ajaxdata);
        // console.log(jdata)
        $('#' + divname).empty();
        $('#' + divname).append(new Option("Pilih Provinsi", ""));
        $.each(jdata, function(i, el) {
            $('#' + divname).append(new Option(el.nama_prov, el.kd_prov));
        });
        $('#' + divname).selectpicker('refresh')

    });
}
</script>