<?php (defined('BASEPATH')) OR exit('No direct script access allowed');

class M_model extends MY_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }
	
    public function getLayerByParent($module)
    {
        
        
        $this->db->where('parent_tema ',$module);
        // $this->db->where('replace(lower(nama_module)," "," ")', str_replace(' ','',strtolower($module)));
        
        return $this->db->get('v_tema_peta')->result();
        
    }
    public function getLayerByModule($module)
    {
        
        
        $this->db->where('nama_module ',$module);
        // $this->db->where('replace(lower(nama_module)," "," ")', str_replace(' ','',strtolower($module)));
        
        return $this->db->get('v_tema_peta')->result();
        
    }

    public function getYear($layer='')
    {
        $this->db->select('tahun_data');
        $this->db->group_by('tahun_data');
        $this->db->where('tahun_data is not null');
        $this->db->order_by('tahun_data', 'desc');
        return $this->db->get($layer)->result();
        
    }
    public function getYearWhere($layer='',$where)
    {
        $this->db->where($where);
        $this->db->select('tahun_data');
        $this->db->group_by('tahun_data');
        $this->db->where('tahun_data is not null');
        $this->db->order_by('tahun_data', 'desc');
        return $this->db->get($layer)->result();
        
    }
    
    public function getYearKab($layer='',$kab)
    {

        $this->db->where('kdpkab', $kab);
        $this->db->select('tahun_data');
        $this->db->group_by('tahun_data');
        $this->db->where('tahun_data is not null');
        $this->db->order_by('tahun_data', 'desc');
        return $this->db->get($layer)->result();
        
    }

    
    public function getYearArr($arr)
    {
        $data=[];
        foreach ($arr as $key => $value) {
            $this->db->select('tahun_data');
            $this->db->group_by('tahun_data');
            $this->db->where('tahun_data is not null');
            $this->db->order_by('tahun_data', 'desc');
            $datas = $this->db->get('spatial.'.$value)->result();
            if(!empty($datas)){
                foreach ($datas as $k => $v) {
                    if (!in_array($v->tahun_data, $data))
                    {
                       array_push($data,$v->tahun_data);
                    }
                }
            }
        }
        $str ='<option value="null">Tidak Ada Tahun</option>';
        foreach ($data as $key => $value) {
                $str .='<option value="'.@$value.'">'.@$value.'</option>';
        }
        
        return $str;
        
    }
    

    public function getYearProv($layer='',$prov)
    {
        $this->db->select('tahun_data');
        $this->db->group_by('tahun_data');
        $this->db->where('tahun_data is not null');
        $this->db->where('kdppum', $prov);
        $this->db->order_by('tahun_data', 'desc');
        
        return $this->db->get($layer)->result();
        
    }

    
    public function getProv($layer='',$year)
    {
        $this->db->select('kdppum,wadmpr');
        $this->db->group_by('kdppum,wadmpr');
        $this->db->where('kdppum is not null');
        $this->db->where('tahun_data', $year);
        $this->db->order_by('wadmpr');
        
        return $this->db->get($layer)->result();
        
    }

    
    public function getYearByView($layer='')
    {
        $this->db->select('tahun_data');
        $this->db->where('tahun_data is not null');
        $this->db->group_by('tahun_data');
        $this->db->order_by('tahun_data', 'desc');
        
        $data= $this->db->get($layer)->result();
        $str = '';
        foreach ($data as $key => $value) {
            if ($value->tahun_data == '') {
                $str .='<option value="null">Tidak Ada Tahun</option>';
            }else{
                $str .='<option value="'.@$value->tahun_data.'">'.@$value->tahun_data.'</option>';
            }         
        }
        return $str;
       
    }

}
