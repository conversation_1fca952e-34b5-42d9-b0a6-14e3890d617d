<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard4 extends MY_Controller {

    private $colors = [];

    public function __construct() {
        //
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->helper('dtssp');
        $this->load->database();
    
    }

    private function array_to_js_string($a) {
        return "['" . rtrim(implode("','", $a), ',') . "']";
    }

    private function array_to_js_number($a) {
        return "[" . rtrim(implode(',', $a), ',') . "]";
    }

    //param: color variant
    // private function chart_simple_bar($sql) {
    //     $res = $this->db->query($sql);
    //     if (!$res) {
    //         return null;
    //     } else {
    //         $ares = $res->result_array();
    //         $labels = array_column($ares, 'nama');
    //         $vals = array_column($ares, 'jumlah');
    //         $biaya = array_column($ares, 'total');
    //         $biaya2 = array_column($ares, 'total2');

    //         $n = sizeof($labels);
    //         $colors = array();
    //         for ($i = 0; $i < $n; $i++) {
    //             $colors[] = "";
    //         }

    //         $retval = array(
    //             "labels" => $this->array_to_js_string($labels),
    //             "vals" => $this->array_to_js_number($vals),
    //             "biaya" => $this->array_to_js_number($biaya),
    //             "biaya2" => $this->array_to_js_number($biaya2)
    //         );
    //         return $retval;
    //     }
    // }

    private function rand_color($varian, $idx) {
        return $this->colors[$varian][$idx % sizeof($this->colors[$varian])];
    }

    private function chart_simple_bar($sql, $colval) {
        $res = $this->db->query($sql);
        if (!$res) {
            return null;
        } else {
            $ares = $res->result_array();
          $labels = array_column($ares, 'nama');
            $vals = array_column($ares, 'jumlah');
            $biaya = array_column($ares, 'total');
            $biaya2 = array_column($ares, 'total2');

            $n = sizeof($labels);
            $colors = array();
            for ($i = 0; $i < $n; $i++) {
                $colors[] = $this->rand_color($colval, $i);
            }

            $retval = array(
                "labels" => $this->array_to_js_string($labels),
                "vals" => $this->array_to_js_number($vals),
                "biaya" => $this->array_to_js_number($biaya),
                "biaya2" => $this->array_to_js_number($biaya2),
                "colors" => $this->array_to_js_string($colors)
            );

            return $retval;
        }
    }

    function frame() {
        $data['js'] = [
            "modules/dashboard4/views/js_dashboard.js"
        ];
 
        $aa = $this->input->post('ruas');
        $bb = $this->input->post('bujt');
        $cc = $this->input->post('tah');
        $dd = $this->input->post('prov');
        
        
        $a='';
        $b='';
        $c='';
        $d='';
      
        if($aa !=''){
            $a=" b.id_ruas='$aa' and ";
            $data['ruas_s']=$aa;
        }
        if($bb !=''){
           $b=" b.kd_bujt='$bb' and ";
           $data['bujt_s']=$bb;
        }
        if($cc !=''){
            $thang = $cc;
             $c=" b.tahun='$thang' and ";
        }else{
            $thang=$this->session->konfig_tahun_ang;
            $c=" b.tahun='$thang' and ";
        }
        if($dd !=''){
            $d=" b.kd_prov='$dd' and ";
            $data['kd_prov_s']=$dd;
         }
        
        $data['tahuns']=$thang;
        $wh=$a.$b.$d.$c;
        // die();
        $group = $this->session->users['id_user_group_real'];
        $ks = $this->session->users['kd_bujt'];
        $thang=$this->session->konfig_tahun_ang;


        $where = '';
        $is_bujt = '';

             if($group == 3)
             {
                //  echo 'bujt'; die();
                 $where = ' b.kd_bujt ='.$ks;
                 $is_bujt = ',kd_bujt';
             }
             else
             {
                $where = ' 1=1';
                //  echo 'admin'; die();
                
             }

            $data['ref_provinsi'] = $this->db->query("select 
                a.kd_prov,
                a.nama_prov as nama,
                a.tahun,
                count(b.id) as jumlah, 
                (CASE
                    WHEN sum(b.nilai_ppjt_awal) is NULL THEN 0
                    ELSE sum(b.nilai_ppjt_awal)
                END)  as total2,
                0 as total
                from aset_r_provinsi a
                left join aset_t_ruas b on (a.kd_prov=b.kd_prov)
                where b.nilai_ppjt_awal is not null
                group by 
                a.kd_prov,
                a.nama_prov
                order by a.nama_prov asc")->result_array();

        // $data['chart']['ruas'] = $this->chart_simple_bar(
        //         "select 
        //             a.kd_prov,
        //             a.nama_prov as nama,
        //             a.tahun,
        //             count(b.id) as jumlah, 
        //             (CASE
        //                 WHEN sum(b.nilai_ppjt_awal) is NULL THEN 0
        //                 ELSE sum(b.nilai_ppjt_awal)
        //             END)  as total2,
        //             0 as total
        //         from aset_r_provinsi a
        //         left join aset_t_ruas b on (a.kd_prov=b.kd_prov and $wh $where)
        //         group by 
        //         a.kd_prov,
        //         a.nama_prov
        //         order by a.nama_prov asc
        //         "
        //         , 0);
 
        
        
        //         $data['chart']['jenisjbt'] = $this->chart_simple_bar(
        //             "select a.kd_prov,a.nama_prov as nama,count(b.id) as jumlah, 
        //             (CASE
        //                  WHEN sum(b.nilai_perolehan) is NULL THEN 0
        //                  ELSE sum(b.nilai_perolehan)
        //             END)as total 
        //             from aset_r_provinsi a
        //             left join aset_t_jembatan b on (a.kd_prov=b.kd_prov and $wh $where)
        //             left join aset_t_ruas c on (b.id_ruas=c.id_ruas)
        //             where c.nilai_ppjt_awal is not null
        //             group by 
        //             a.kd_prov,
 	    //             a.nama_prov order by a.nama_prov asc"
         
        //             , 0);        


        //             $data['chart']['jenisbgn'] = $this->chart_simple_bar(
        //                 "select a.kd_prov,a.nama_prov as nama,count(b.id_bangunan) as jumlah, 
        //                 (CASE
        //                      WHEN sum(b.nilai_perolehan) is NULL THEN 0
        //                      ELSE sum(b.nilai_perolehan)
        //                 END)as total 
        //                 from aset_r_provinsi a
        //                 left join aset_t_bangunan b on (a.kd_prov=b.kd_prov and $wh $where)
        //                 left join aset_t_ruas c on (b.id_ruas=c.id_ruas)
        //                  where c.nilai_ppjt_awal is not null
        //                 group by 
        //                 a.kd_prov,
        //                 a.nama_prov 
        //                 order by a.nama_prov asc"
                        // "select 
                        //     coalesce(sum(a.nilai_perolehan),0) as total
                        //     ,coalesce(sum(a.kuantitas),0) as jumlah 
                        //     ,a.nm_jnsbangun as nama
                        //     ,a.tahun 
                        //     $is_bujt 
                        // from
                        // (
                        // select z.nilai_perolehan, z.kuantitas, x.nm_jnsbangun, x.tahun, z.kd_bujt,z.id_ruas,z.kd_prov
                        // from v_aset_bangunan z 
                        // left join aset_r_jnsbangunan x on z.kd_jnsbangun = x.kd_jnsbangun and z.tahun = x.tahun
                        // ) a
                        // where $wh $where
                        // group by 
                        //     a.nm_jnsbangun 
                        //     ,a.tahun 
                        //     $is_bujt
                        // "
                        // , 0);  



                        // $data['chart']['jenisalat'] = $this->chart_simple_bar(
                        //     "select a.kd_prov,a.nama_prov as nama,count(b.id_peralatan) as jumlah, 
                        //     (CASE
                        //          WHEN sum(b.nilai_perolehan) is NULL THEN 0
                        //          ELSE sum(b.nilai_perolehan)
                        //     END)as total 
                        //     from aset_r_provinsi a
                        //     left join aset_t_peralatan b on (a.kd_prov=b.kd_prov and $wh $where)
                        //     left join aset_t_ruas c on (b.id_ruas=c.id_ruas)
                        //     where c.nilai_ppjt_awal is not null
                        //     group by 
                        //     a.kd_prov,
                        //     a.nama_prov 
                        //     order by a.nama_prov asc"
                            // "select 
                            //     coalesce(sum(a.nilai_perolehan),0) as total
                            //     ,coalesce(sum(a.kuantitas),0) as jumlah
                            //     ,a.peralatan as nama
                            //     ,a.tahun
                            //     $is_bujt
                            // from
                            // (
                            // select z.nilai_perolehan, z.kuantitas, x.peralatan, x.tahun, z.kd_bujt,z.id_ruas,z.kd_prov
                            // from v_aset_peralatan z
                            // left join aset_r_peralatan x on z.id_alat = x.id_alat and z.kd_jnsalat = x.kd_jnsalat and x.tahun = z.tahun
                            // ) a
                            // where $wh  $where
                            // group by 
                            //     a.peralatan 
                            //     ,a.tahun 
                            //     $is_bujt
                            // "
                            // , 0);  

                

        //  $data['chart']['wilayah'] = $this->chart_simple_bar(
        //             "select nama_prov as nama, paket as jumlah, total  from v_total_usulan_paket_per_wilayah where  $ang $a $kod", 0);
        // if($group ==60)
        //   {
        //       $data['chart']['kegiatan'] = $this->chart_simple_bar(
        //         "select nmgiat as nama, sum(paket) as jumlah, sum(total) as total from v_total_usulan_paket_per_kegiatan where $ang $a $kid group by nmgiat", 0);
        //         $data['chart']['belanja'] = $this->chart_simple_bar(
        //         "select nmoutput as nama , sum(paket) as jumlah, sum(total) as total from v_total_usulan_paket_per_output where $ang $a $kid  group by nmoutput ", 0);
        // }
        // else{
        //   $data['chart']['kegiatan'] = $this->chart_simple_bar(
        //     "select nmgiat as nama, paket as jumlah, total from v_total_usulan_paket_per_kegiatan where $ang $a $kid ", 0);
        //     $data['chart']['belanja'] = $this->chart_simple_bar(
        //             "select nmoutput as nama , paket as jumlah, total from v_total_usulan_paket_per_output where $ang $a $kid", 0);
        // }



//        $data['chart']['sdana'] = $this->chart_simple_bar(
//                "select sdana as nama , paket as jumlah, total from v_jumlah_paket_total_biaya_per_sumber_dana WHERE sdana IS NOT NULL   ", 0);

        $data ['total_ppjt'] = $this->db->query(" select sum(nilai_ppjt_awal)as jml from aset_t_ruas where tahun=$thang ")->row()->jml;
       $dt_aset = $this->db->query("select 
        a.kd_prov,
        a.nama_prov as nama,
        a.tahun,
        count(b.id) as jumlah, 
        (CASE
            WHEN sum(b.nilai_ppjt_awal) is NULL THEN 0
            ELSE sum(b.nilai_ppjt_awal)
        END)  as total2,
        0 as total
        from aset_r_provinsi a
        left join aset_t_ruas b on (a.kd_prov=b.kd_prov and $wh $where)
       -- where b.nilai_ppjt_awal is not null
        group by 
        a.kd_prov,
        a.nama_prov
        order by a.nama_prov asc")->result_array();
        $dta=array();
        for ($i = 0; $i < count($dt_aset); $i++) {
            if($dt_aset[$i]['jumlah'] != 0 ){
            //tambahin ini, untuk set array kosong di setiap loop
            $dt=array();
            
            $datax [] = array(
                'name'   => $dt_aset[$i]['nama'],
                'y'     => (int)$dt_aset[$i]['jumlah'],
                'drilldown' => $dt_aset[$i]['nama']
            );
            $pr=$dt_aset[$i]['kd_prov'];
        
            $dt_aset_prov = $this->db->query("select 
            kd_prov, nama_prov, nm_ruas,
            (CASE
                WHEN nilai_ppjt_awal is NULL THEN 0
                ELSE nilai_ppjt_awal
            END) as jumlah  from v_aset_jalan_tol where kd_prov='$pr' ")->result_array();
            foreach ($dt_aset_prov as $dtx) {
                $dt[]=array($dtx['nm_ruas'],(int)$dtx['jumlah'],$dtx['nama_prov']);
            }
            $datax_prov [] = array(
                'id'   => $dt_aset[$i]['nama'],
                'name' =>'Jumlah',
                'data' => $dt
            );
        }
        }
        $data['aset_tol'] = json_encode($datax);    
        $data['aset_tol_prov'] =json_encode($datax_prov);




        //jembatan

         $dt_aset_j = $this->db->query("select a.kd_prov,a.nama_prov as nama,count(b.id_ruas) as jumlah,b.tahun
                    from aset_r_provinsi a
                    right join (select distinct id_ruas $is_bujt,kd_prov,tahun from aset_t_jembatan) b on (a.kd_prov=b.kd_prov and $wh $where)
                   -- where a.kd_prov is not null
                    group by 
                    a.kd_prov,
                    b.tahun,
 	                a.nama_prov order by a.nama_prov asc")->result_array();
        for ($i = 0; $i < count($dt_aset_j); $i++) {
            if($dt_aset_j[$i]['nama'] != NULL ){
            //tambahin ini, untuk set array kosong di setiap loop
            $dtj=array();
            
            $dataj [] = array(
                'name'   => $dt_aset_j[$i]['nama'],
                'y'     => (int)$dt_aset_j[$i]['jumlah'],
                'drilldown' => $dt_aset_j[$i]['nama']
            );
            $pr=$dt_aset_j[$i]['kd_prov'];
        
            $dt_aset_prov_j = $this->db->query("  select 
            kd_prov, nama_prov, nm_ruas,id_ruas,
            count(id) as jumlah  from v_aset_jembatan  where kd_prov='$pr'  group by kd_prov, nama_prov, nm_ruas, id_ruas")->result_array();
            foreach ($dt_aset_prov_j as $dtx) {
                $dtjd=array();
                  $dtj[]=array($dtx['nm_ruas'],(int)$dtx['jumlah'],$dtx['id_ruas']);
                  $idr=$dtx['id_ruas'];
                  $dt_aset_prov_jd = $this->db->query("select id_ruas,nm_ruas,nm_jnsjembatan, count(nm_jembatan) as jumlah from v_aset_jembatan 
                    where id_ruas='$idr'
            		group by nm_ruas, nm_jnsjembatan, id_ruas")->result_array();
                    foreach ($dt_aset_prov_jd as $dtxj) {
                        $dtjd[]=array($dtxj['nm_jnsjembatan'],(int)$dtxj['jumlah']);
                     }  
                $datajd_prov [] = array(
                    'id'   => $dtx['id_ruas'],
                    'name' =>'Jumlah jembatan',
                    'data' => $dtjd
                    
                );
            }
            $dataj_prov [] = array(
                'id'   => $dt_aset_j[$i]['nama'],
                'name' =>'Jumlah  jembatan',
                'data' => $dtj,
                'keys' => ['name','y','drilldown']
            );
        }
        }
        $dt_gbung=array_merge($dataj_prov,$datajd_prov);
        $data['aset_jembatan'] = json_encode($dataj);    
        $data['aset_jembatan_prov'] =json_encode($dt_gbung);


         //bangunan

         $dt_aset_b = $this->db->query("select a.kd_prov,a.nama_prov as nama,count(b.id_ruas) as jumlah
                    from aset_r_provinsi a
                    right join (select distinct id_ruas $is_bujt ,kd_prov,tahun from aset_t_bangunan) b on (a.kd_prov=b.kd_prov and $wh $where)
                    where a.kd_prov is not null
                    group by 
                    a.kd_prov,
 	                a.nama_prov order by a.nama_prov asc")->result_array();
        for ($i = 0; $i < count($dt_aset_b); $i++) {
            //tambahin ini, untuk set array kosong di setiap loop
            $dtb=array();
            
            $datab [] = array(
                'name'   => $dt_aset_b[$i]['nama'],
                'y'     => (int)$dt_aset_b[$i]['jumlah'],
                'drilldown' => $dt_aset_b[$i]['nama']
            );
            $pr=$dt_aset_b[$i]['kd_prov'];
        
            $dt_aset_prov_b = $this->db->query("  select 
            kd_prov, nama_prov, nm_ruas,id_ruas,
            count(id_bangunan) as jumlah  from v_aset_bangunan  where kd_prov='$pr'  group by kd_prov, nama_prov, nm_ruas, id_ruas")->result_array();
            foreach ($dt_aset_prov_b as $dtx) {
                $dtbd=array();
                  $dtb[]=array($dtx['nm_ruas'],(int)$dtx['jumlah'],$dtx['id_ruas']);
                  $idr=$dtx['id_ruas'];
                  $dt_aset_prov_bd = $this->db->query("select id_ruas,nm_ruas,nm_jnsbangun, count(nm_bangunan) as jumlah from v_aset_bangunan 
                    where id_ruas='$idr'
            		group by nm_ruas, nm_jnsbangun, id_ruas")->result_array();
                    foreach ($dt_aset_prov_bd as $dtxb) {
                        $dtbd[]=array($dtxb['nm_jnsbangun'],(int)$dtxb['jumlah']);
                     }  
                $databd_prov [] = array(
                    'id'   => $dtx['id_ruas'],
                    'name' =>'Jumlah bangunan',
                    'data' => $dtbd
                    
                );
            }
            $datab_prov [] = array(
                'id'   => $dt_aset_b[$i]['nama'],
                'name' =>'Jumlah  bangunan',
                'data' => $dtb,
                'keys' => ['name','y','drilldown']
            );
            
        }
        $dt_gbung_b=array_merge($datab_prov,$databd_prov);
        $data['aset_bangunan'] = json_encode($datab);    
        $data['aset_bangunan_prov'] =json_encode($dt_gbung_b);


         //peralatan

         $dt_aset_p = $this->db->query("select a.kd_prov,a.nama_prov as nama,count(b.id_ruas) as jumlah
                    from aset_r_provinsi a
                    right join (select distinct id_ruas $is_bujt ,kd_prov,tahun from aset_t_peralatan) b on (a.kd_prov=b.kd_prov and $wh $where)
                    where a.kd_prov is not null
                    group by 
                    a.kd_prov,
 	                a.nama_prov order by a.nama_prov asc")->result_array();
        for ($i = 0; $i < count($dt_aset_p); $i++) {
            //tambahin ini, untuk set array kosong di setiap loop
            $dtp=array();
            
            $datap [] = array(
                'name'   => $dt_aset_p[$i]['nama'],
                'y'     => (int)$dt_aset_p[$i]['jumlah'],
                'drilldown' => $dt_aset_p[$i]['nama']
            );
            $pr=$dt_aset_p[$i]['kd_prov'];
        
            $dt_aset_prov_p = $this->db->query("  select 
            kd_prov, nama_prov, nm_ruas,id_ruas,
            sum(kuantitas) as jumlah  from v_aset_peralatan  where kd_prov='$pr'  group by kd_prov, nama_prov, nm_ruas, id_ruas")->result_array();
            foreach ($dt_aset_prov_p as $dtx) {
                $dtpd=array();
                  $dtp[]=array($dtx['nm_ruas'],(int)$dtx['jumlah'],$dtx['id_ruas']);
                  $idr=$dtx['id_ruas'];
                  $dt_aset_prov_pd = $this->db->query("select id_ruas,nm_ruas,peralatan, sum(kuantitas) as jumlah from v_aset_peralatan 
                    where id_ruas='$idr'
            		group by nm_ruas, peralatan, id_ruas")->result_array();
                    foreach ($dt_aset_prov_pd as $dtxb) {
                        $dtpd[]=array($dtxb['peralatan'],(int)$dtxb['jumlah']);
                     }  
                $datapd_prov [] = array(
                    'id'   => $dtx['id_ruas'],
                    'name' =>'Jumlah peralatan',
                    'data' => $dtpd
                    
                );
            }
            $datap_prov [] = array(
                'id'   => $dt_aset_p[$i]['nama'],
                'name' =>'Jumlah  peralatan',
                'data' => $dtp,
                'keys' => ['name','y','drilldown']
            );
            
        }
        $dt_gbung_p=array_merge($datap_prov,$datapd_prov);
        $data['aset_peralatan'] = json_encode($datap);    
        $data['aset_peralatan_prov'] =json_encode($dt_gbung_p);

        // print_r($dt_gbung_b);
        // die();
        $this->load->view('v_dashboard', $data);
    }

    public function ssp_paket() {

      //   $id_pak = $this->input->post('id_pak', TRUE);
         $id_user = $this->session->users['id_user'];
         $role = $this->session->users['id_user_group_real'];
         //$subrole = $this->session->users['id_user_group'];
         $roledesc = $this->session->users['role'];
         $yearnow = $this->session->konfig_tahun_ang;
         $satker = $this->session->users['kd_bujt'];
 
         //echo $roledesc; die();
         $prov = $this->input->post('prov');
        //  $bb = $this->input->post('bujt');
        //  $cc = $this->input->post('status');
        //  $dd = $this->input->post('kondisi');
         
        //  $a='';
        //  $b='';
        //  $c='';
        //  $d='';
        //  $e='';
        //  if($aa !=''){
        //      $a=" id_ruas='$aa' and ";
        //  }
        //  if($bb !=''){
        //     $b=" kd_bujt='$bb' and ";
        //  }
        //  if($cc !=''){
        //       $c="id_status='$cc' and ";
        //  }
        //  if($dd !=''){
        //      $d="kondisi='$dd' and ";
        //    }
        //  $wh=$a.$b.$c.$d;   
 
 
         $table = 'v_aset_jalan_tol';
         $primaryKey = 'id'; //test
 
         $columns = array(
 //            array('db' => 'dicon', 'dt' => 0),
             array('db' => 'nama_prov', 'dt' => 0),
             array('db' => 'nama_bujt', 'dt' => 1),
             array('db' => 'nm_ruas', 'dt' => 2),
             array('db' => 'no_ppjt', 'dt' => 3),
             array('db' => 'nilai_ppjt_awal', 'dt' => 4, 'formatter' => function( $d, $row ) {
                     return number_format($d, 0, ".", ",");
                 })
         );
         if($prov == ''){
            datatable_ssp($table, $primaryKey, $columns,"nama_prov <> '' and tahun=$yearnow");
         }else{
            datatable_ssp($table, $primaryKey, $columns,"nama_prov <> '' and kd_prov='$prov' and tahun=$yearnow");
         }
 
     }


    public function index() {

        $title = "Dashboard";
        header("Access-Control-Allow-Origin: *");
        $data = array();

        $satker = $this->session->users['kd_bujt'];
        $data['js'] = [
            "modules/dashboard4/views/js_dashboard.js"
        ];


        $this->template->set('title', $title);
        $this->template->load('default_layout', 'contents', 'index', $data);
        
      

       
    }









    public function update_ruass(){
        $this->db->where('tahun',2022);
        $db=$this->db->get('aset_t_ruas')->result_array();

        foreach($db as $dt){
            echo $dt['id_ruas']."<br>";
        }
    }

}
