<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard2 extends MY_Controller {

    private $colors=[];

       

    public function __construct() {
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();

            $this->colors[0] = [
            'rgba(166,206,227,0.8)',
            'rgba(31,120,180,0.8)',
            'rgba(178,223,138,0.8)',
            'rgba(51,160,44,0.8)',
            'rgba(251,154,153,0.8)',
            'rgba(227,26,28,0.8)',
            'rgba(253,191,111,0.8)',
            'rgba(255,127,0,0.8)',
            'rgba(202,178,214,0.8)',
            'rgba(106,61,154,0.8)',
            'rgba(255,255,153,0.8)',
            'rgba(177,89,40,0.8)'
        ];

       $this->colors[1] = [
            'rgba(141,211,199,0.8)',
            'rgba(255,255,179,0.8)',
            'rgba(190,186,218,0.8)',
            'rgba(251,128,114,0.8)',
            'rgba(128,177,211,0.8)',
            'rgba(253,180,98,0.8)',
            'rgba(179,222,105,0.8)',
            'rgba(252,205,229,0.8)',
            'rgba(217,217,217,0.8)',
            'rgba(188,128,189,0.8)',
            'rgba(204,235,197,0.8)',
            'rgba(255,237,111,0.8)'
        ];

    $this->colors[2] = ['rgba(141,211,199,0.8)']; 
        
        
        
    }

    private function rand_color($varian, $idx) {
        return $this->colors[$varian][$idx % sizeof($this->colors[$varian])];
    }

    private function array_to_js_string($a) {
        return "['".rtrim(implode("','",$a), ',')."']";
    }
    
    private function array_to_js_number($a) {
        return "[".rtrim(implode(',',$a), ',')."]";
    }
    
    //param: color variant
    private function chart_simple_bar($sql, $colval) {
        $res = $this->db->query($sql);
        if (!$res) {
            return null;
        } else {
            $ares = $res->result_array();
            $labels = array_column($ares, 'nmsatker');
            $vals = array_column($ares, 'paket');

            $n = sizeof($labels);
            $colors = array();
            for ($i = 0; $i < $n; $i++) {
                $colors[] = $this->rand_color($colval, $i);
            }
            
            $retval = array(
                "labels" => $this->array_to_js_string($labels),
                "vals" => $this->array_to_js_number($vals),
                "colors" => $this->array_to_js_string($colors)
            );

            return $retval;
        }
    }

    
    private function get_jml_usulan() {
             
        $a = $this->db->query("select sum (paket) as aday from v_paket_per_balai")->result_array();
//        foreach($a as $aa)
//        {
//            $zz=$aa['paket']+$zz;
//        }
        return $a[0]['aday'];
        
//        error_reporting(2);
//        $a = $this->db->query("select * from v_paket_per_balai")->result_array();
//        foreach($a as $aa)
//        {
//            $zz=$aa['paket']+$zz;
//        }
//        return $zz;
    }

    function frame() {
        $data['js'] = [
            "modules/dashboard2/views/js_dashboard_dpr.js"
        ];
        
        $data['totaldata'] = $this->get_jml_usulan();
        
        $data['chart']['balai'] = $this->chart_simple_bar(
            "select*from v_paket_per_balai",
        0);
      

        $this->load->view('v_dashboard_balai', $data);
    }

    public function index() {
        $title = "Jumlah Usulan Per Balai";
        $data = array();


        $this->template->set('title', $title);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }

}
