<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard4 extends MY_Controller {

    private $colors = [];

    public function __construct() {
        //
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        
        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->model('M_model');
        $this->load->helper('dtssp');
        $this->load->database();
    
    }

    private function array_to_js_string($a) {
        return "['" . rtrim(implode("','", $a), ',') . "']";
    }

    private function array_to_js_number($a) {
        return "[" . rtrim(implode(',', $a), ',') . "]";
    }

    //param: color variant
    // private function chart_simple_bar($sql) {
    //     $res = $this->db->query($sql);
    //     if (!$res) {
    //         return null;
    //     } else {
    //         $ares = $res->result_array();
    //         $labels = array_column($ares, 'nama');
    //         $vals = array_column($ares, 'jumlah');
    //         $biaya = array_column($ares, 'total');
    //         $biaya2 = array_column($ares, 'total2');

    //         $n = sizeof($labels);
    //         $colors = array();
    //         for ($i = 0; $i < $n; $i++) {
    //             $colors[] = "";
    //         }

    //         $retval = array(
    //             "labels" => $this->array_to_js_string($labels),
    //             "vals" => $this->array_to_js_number($vals),
    //             "biaya" => $this->array_to_js_number($biaya),
    //             "biaya2" => $this->array_to_js_number($biaya2)
    //         );
    //         return $retval;
    //     }
    // }

    private function rand_color($varian, $idx) {
        return $this->colors[$varian][$idx % sizeof($this->colors[$varian])];
    }

    private function chart_simple_bar($sql, $colval) {
        $res = $this->db->query($sql);
        if (!$res) {
            return null;
        } else {
            $ares = $res->result_array();
          $labels = array_column($ares, 'nama');
            $vals = array_column($ares, 'jumlah');
            $biaya = array_column($ares, 'total');
            $biaya2 = array_column($ares, 'total2');

            $n = sizeof($labels);
            $colors = array();
            for ($i = 0; $i < $n; $i++) {
                $colors[] = $this->rand_color($colval, $i);
            }

            $retval = array(
                "labels" => $this->array_to_js_string($labels),
                "vals" => $this->array_to_js_number($vals),
                "biaya" => $this->array_to_js_number($biaya),
                "biaya2" => $this->array_to_js_number($biaya2),
                "colors" => $this->array_to_js_string($colors)
            );

            return $retval;
        }
    }

    function frame() {

        $new_users =  $this->session->userdata('users');
        
        // echo "<pre>";
        // print_r ($new_users);
        // echo "</pre>";
        
        // if ($new_users['id_user'] == '565') {
            // $new_users['kd_kabkot'] = '35.73';
            // // $new_users['kd_kabkot'] = '35.10';
            // // $new_users['kd_kabkot'] = '33.29';
            // // $new_users['kd_kabkot'] = '51.71';
            // $new_users['kd_kabkot'] = '32.15';
            // $new_users['kd_prov'] = '';
            // $new_users['id_user_group_real'] = 7;
            // $new_users['id_user_group'] = 7;
            
            // $this->session->unset_userdata('users'); 
            // $this->session->set_userdata('users', $new_users);
        // }
        $data['js'] = [
            "modules/dashboard4/views/js_dashboard.js"
        ];
        $data['overlay'] = $this->load->view('dashboard4/jsoverlay', '', true);
        
        // echo "<pre>";
        // print_r ($this->session->all_userdata());
        // echo "</pre>";
        
        $js = $this->load->view('dashboard4/js_file', '', true);
        $data['npgtKabkot']= $this->M_model->getLayerByModule('NPGT Kabupaten');
        $data['npgtKec']= $this->M_model->getLayerByModule('NPGT Kecamatan');
        $data['pgtl']= $this->M_model->getLayerByParent('Penatagunaan Tanah Lainnya');
        $data['wp3wt']= $this->M_model->getLayerByParent('Penataan WP3WT');
        $data['ptp']= $this->M_model->getLayerByParent('Pertimbangan Teknis Pertanahan');
        foreach ($data['ptp'] as $key => $value) {
            if($value->nama_module == 'PTP IPPT Provinsi' || $value->nama_module ==  'PTP Izin Lokasi Provinsi' ){
                unset($data['ptp'][$key]);
            }
        }
   
        
        $data['tanahNegara']= $this->M_model->getLayerByParent('Tanah Negara/Tanah Kritis');
        $data['tahunNpgtProv']= $this->M_model->getYearByView('spatial.v_d_npgt_prov');
        $data['tahunNpgtKabkot']= $this->M_model->getYearByView('spatial.v_d_npgt_kabkota');
        $data['tahunLahanbaku']= $this->M_model->getYearByView('spatial.v_d_lahanbaku');
        $data['tahunMppt']= $this->M_model->getYearByView('spatial.v_d_mppt');
        $data['tahunKamampuan']= $this->M_model->getYearByView('spatial.v_d_kemampuan_tanah');
        $data['tahunNpgtPerkebunan']= $this->M_model->getYearByView('spatial.v_d_npgt_kebun');
        
        $arrkec = [
            'Administrasi'=>'npgt_kec_a',
            'Jalan' => 'npgt_kec_k_polyline',
            'Sungai Polygon' => 'npgt_kec_h_polygon',
            'Sungai Polyline' => 'npgt_kec_h_polyline',
            'RTRW W'=>'npgt_kec_w',
            'Kesesuaian N'=>'npgt_kec_n',
            'Gambaran Umum Penguasaan Tanah O'=>'npgt_kec_o',
            'Ketersediaan Tanah V'=>'npgt_kec_v'
        ];
        $data['tahunNpgtKec']= $this->M_model->getYearArr($arrkec);
        $data['cekIndex']='frame';
        
      
        
        $data['js_file'] = $js;
 
        $this->load->view('index', $data);
    }

    public function ssp_paket() {

      //   $id_pak = $this->input->post('id_pak', TRUE);
         $id_user = $this->session->users['id_user'];
         $role = $this->session->users['id_user_group_real'];
         //$subrole = $this->session->users['id_user_group'];
         $roledesc = $this->session->users['role'];
         $yearnow = $this->session->konfig_tahun_ang;
         $satker = $this->session->users['kd_bujt'];
 
         //echo $roledesc; die();
         $prov = $this->input->post('prov');
        //  $bb = $this->input->post('bujt');
        //  $cc = $this->input->post('status');
        //  $dd = $this->input->post('kondisi');
         
        //  $a='';
        //  $b='';
        //  $c='';
        //  $d='';
        //  $e='';
        //  if($aa !=''){
        //      $a=" id_ruas='$aa' and ";
        //  }
        //  if($bb !=''){
        //     $b=" kd_bujt='$bb' and ";
        //  }
        //  if($cc !=''){
        //       $c="id_status='$cc' and ";
        //  }
        //  if($dd !=''){
        //      $d="kondisi='$dd' and ";
        //    }
        //  $wh=$a.$b.$c.$d;   
 
 
         $table = 'v_aset_jalan_tol';
         $primaryKey = 'id'; //test
 
         $columns = array(
 //            array('db' => 'dicon', 'dt' => 0),
             array('db' => 'nama_prov', 'dt' => 0),
             array('db' => 'nama_bujt', 'dt' => 1),
             array('db' => 'nm_ruas', 'dt' => 2),
             array('db' => 'no_ppjt', 'dt' => 3),
             array('db' => 'nilai_ppjt_awal', 'dt' => 4, 'formatter' => function( $d, $row ) {
                     return number_format($d, 0, ".", ",");
                 })
         );
         if($prov == ''){
            datatable_ssp($table, $primaryKey, $columns,"nama_prov <> '' and tahun=$yearnow");
         }else{
            datatable_ssp($table, $primaryKey, $columns,"nama_prov <> '' and kd_prov='$prov' and tahun=$yearnow");
         }
 
     }


    public function index() {

        
        $title = "Dashboard";
        header("Access-Control-Allow-Origin: *");
        $data = array();

        // $satker = $this->session->users['kd_bujt'];
        $js = $this->load->view('dashboard4/js_file', '', true);

        $data['js'] = [
            "modules/dashboard4/views/js_dashboard.js"
        ];
        $data['overlay'] = $this->load->view('dashboard4/jsoverlay', '', true);
        $data['js_file']=$js;
        $data['npgtKabkot']= $this->M_model->getLayerByModule('NPGT Kabupaten');
        $data['npgtKec']= $this->M_model->getLayerByModule('NPGT Kecamatan');


        
        $data['pgtl']= $this->M_model->getLayerByParent('Penatagunaan Tanah Lainnya');
        $data['wp3wt']= $this->M_model->getLayerByParent('Penataan WP3WT');
        $data['ptp']= $this->M_model->getLayerByParent('Pertimbangan Teknis Pertanahan');
        $data['ptp']= $this->M_model->getLayerByParent('Pertimbangan Teknis Pertanahan');
        foreach ($data['ptp'] as $key => $value) {
            if($value->nama_module == 'PTP IPPT Provinsi' || $value->nama_module ==  'PTP Izin Lokasi Provinsi' ){
                unset($data['ptp'][$key]);
            }
        }
        $data['tanahNegara']= $this->M_model->getLayerByParent('Tanah Negara/Tanah Kritis');
        $data['tahunNpgtProv']= $this->M_model->getYearByView('spatial.v_d_npgt_prov');
        $data['tahunLahanbaku']= $this->M_model->getYearByView('spatial.v_d_lahanbaku');
        $data['tahunMppt']= $this->M_model->getYearByView('spatial.v_d_mppt');
        $data['tahunKamampuan']= $this->M_model->getYearByView('spatial.v_d_kemampuan_tanah');
        $data['tahunNpgtPerkebunan']= $this->M_model->getYearByView('spatial.v_d_npgt_kebun');
        $data['tahunNpgtKabkot']= $this->M_model->getYearByView('spatial.v_d_npgt_kabkota');
        
        $arrkec = [
            'Administrasi'=>'npgt_kec_a',
            'Jalan' => 'npgt_kec_k_polyline',
            'Sungai Polygon' => 'npgt_kec_h_polygon',
            'Sungai Polyline' => 'npgt_kec_h_polyline',
            'RTRW W'=>'npgt_kec_w',
            'Kesesuaian N'=>'npgt_kec_n',
            'Gambaran Umum Penguasaan Tanah O'=>'npgt_kec_o',
            'Ketersediaan Tanah V'=>'npgt_kec_v'
        ];
        $data['tahunNpgtKec']= $this->M_model->getYearArr($arrkec);
        $data['cekIndex']='index';
        $this->template->set('title', $title);
        $this->template->load('default_layout', 'contents', 'index', $data);
        
      

       
    }

    public function update_ruass(){
        $this->db->where('tahun',2022);
        $db=$this->db->get('aset_t_ruas')->result_array();

        foreach($db as $dt){
            echo $dt['id_ruas']."<br>";
        }
    }



    // ===========================================================================
    public function wp3wtOld($v)
    {
        
        if($v == 'wp3wt_ppkt' ||$v == 'wp3wt_ppk_polygon'){
            $sel ='round(sum(luas_ha)) jml,provinsi';
            $group ='provinsi';

            $dsel ='round(sum(luas_ha)) jml,kabupaten';
            $dgroup ='kabupaten';
            $dwhere = 'provinsi';
            
        }else{
            $sel ='round(sum(luas_ha)) jml,wapname';
            $group ='wapname'; 
            $dsel ='round(sum(luas_ha)) jml,wakname';
            $dgroup ='wakname';
            $dwhere = 'wapname';
        }
        $this->db->select($sel);
        $this->db->group_by($group);
        $this->db->order_by('jml', 'desc');
        $datas = $this->db->get('spatial.'.$v)->result();
        $data=[];
        // echo $this->db->last_query();
        
        foreach ($datas as $key => $value) {
            // $a=[$prov,intVal($value->jml)];
            $prov = @$value->wapname? @$value->wapname: @$value->provinsi;
            $a['layer']=$v;
            $a['name']=$prov;
            $a['y']=floatval($value->jml);
            // $a['drilldown']=$prov;
            array_push($data,$a);
            
        }

      
        $this->db->join('aset_r_provinsi arp ', 'ark.kd_prov =arp.kd_prov', 'left');
        $this->db->select('ark.nama_kabkot,arp.nama_prov');
        $this->db->where("arp.nama_prov like upper('%jawa timur%')");
        
        $prov=$this->db->get('aset_r_kabkota ark')->result();
        
        // echo "<pre>";
        // print_r ($prov);
        // echo "</pre>";
        
        
        

        $ret= ['data' =>$data,'module'=> 'wp3wt'];

        echo json_encode($ret);
    }

    public function drilldownWp3wtOld($name,$layer)
    {

        if($layer == 'wp3wt_ppkt' ||$layer == 'wp3wt_ppk_polygon'){
            $dsel ='round(sum(luas_ha)) jml,kabupaten';
            $dgroup ='kabupaten';
            $dwhere = 'provinsi';
            
        }else{
            $dsel ='round(sum(luas_ha)) jml,wakname';
            $dgroup ='wakname';
            $dwhere = 'wapname';
        }

        $drilldown=[];
        $name=str_replace('%20', ' ',$name);
            
            $this->db->select($dsel);
            $this->db->group_by($dgroup);
            $this->db->order_by('jml', 'desc');
            $this->db->where($dwhere, $name);
            $this->db->where($dgroup.' is not null');
            $datas = $this->db->get('spatial.'.$layer)->result();
            $b['name']=$name;
            $b['id']=$name;
            $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $kota = @$value2->wakname? @$value2->wakname: @$value2->kabupaten;
                $c=[$kota,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                // $a['layer']=$v;
                // $a['name']=$value->wadmkk;
                // $a['y']=floatval($value->jml);
                $a['drilldown']=$value->wadmkk;
                // array_push($data,$a);
                    
            }
        array_push($drilldown,$b);
        echo json_encode($drilldown[0]);

            
    }
    
    public function lahanBakuold()
    {
        
       
        $this->db->select('wadmpr,round(sum(luas_ha)) as jml');
        $this->db->group_by('wadmpr');
        $this->db->order_by('jml', 'desc');
        $datas = $this->db->get('spatial.lahanbakusawah_prov')->result();
        $data=[];
        foreach ($datas as $key => $value) {
            // $a=[$value->wadmpr,intVal($value->jml)];
            $a['name']=$value->wadmpr;
            $a['y']=floatval($value->jml);
            // $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
        }
        
        
        $ret= ['data' =>$data,'module'=> 'lahanBaku'];
        echo json_encode($ret);
    }

    public function drilldownLahanBakuOld($name)
    {


        $drilldown=[];
        $name=str_replace('%20', ' ',$name);
            
            $this->db->select('wadmkk,round(sum(luas_ha)) as jml');
            $this->db->group_by('wadmkk');
            $this->db->order_by('jml', 'desc');
            $this->db->where('wadmpr', $name);
            $this->db->where('wadmkk'.' is not null');
            $datas = $this->db->get('spatial.lahanbakusawah_prov')->result();
            $b['name']=$name;
            $b['id']=$name;
            $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                    // echo "<pre>";
                    // print_r ($value2);
                    // echo "</pre>";break;
                    
            }
        array_push($drilldown,$b);
        echo json_encode($drilldown[0]);

            
    }

    public function tanahNegaraOld($v)
    {
        
        if($v == 'iltk'){
            $sel ='round(sum(luas)) jml,wadmkk';
        }else{
            $sel ='round(sum(luas_ha)) jml,wadmkk';
        }
        $this->db->select($sel);
        $this->db->group_by('wadmkk');
        $this->db->order_by('jml', 'desc');
        $datas = $this->db->get('spatial.'.$v)->result();
        
        $data=[];
        foreach ($datas as $key => $value) {
            // $prov = @$value->wapname? @$value->wapname: @$value->provinsi;
            $a=[$value->wadmkk,intVal($value->jml)];
            array_push($data,$a);
        }

        $ret= ['data' =>$data,'module'=> 'tanahNegara'];

        echo json_encode($ret);
    }
    
    public function kemampuanTanahOld($v)
    {
        
        $this->db->select('upper('.$v.') nama,round(sum(luas_ha)) jml');
        $this->db->group_by('upper('.$v.')');
        $this->db->order_by('jml', 'desc');
        $this->db->where('upper('.$v.') is not null');
        $datas = $this->db->get('spatial.kemampuan_tanah')->result();
        
        echo $this->db->last_query();
        
        
        $data=[];
        foreach ($datas as $key => $value) {
            // $a=[$value->wadmpr,intVal($value->jml)];
            $a['name']=$value->nama;
            $a['y']=floatval($value->jml);
            $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
        }
        // echo $this->db->last_query();exit();
        
        
        $ret= ['data' =>$data,'module'=> 'kemampuanTanah'];
        echo json_encode($ret);
    }

    public function npgtProvDual($prov='',$year='')
    {
 
        $views = 'spatial.v_d_analisis_npgt_prov';
        $years= $this->M_model->getYearProv($views,$prov);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        $data=[];
        $data[0]['name']='Luas Tidak Sesuai/Tidak Tersedia/Tidak berubah';
        $data[0]['data']=[];
        $data[0]['color']='#d11144';
        $data[0]['stack']='Tersedia';
        $data[1]['name']='Luas Sesuai/Tersedia/Berubah';
        $data[1]['data']=[];
        $data[1]['color']='#2196f3';
        $data[1]['stack']='Tersedia';
        $arrcol = [
            'sesuai' => 'Kesesuaian N',
            'tersedia' => 'Ketersediaan Tanah V',
            'berubah' => 'Perubahan Penggunaan Tanah GQ' ,
        ];
        // $str='';
            
        $this->db->where('kdppum', $prov);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get($views)->row_array();
      
        $category=[];
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {
                array_push($category,$value);
                $aa = ['y' => intVal($getdata['jml_'.$key]),'target' =>$value,'text' =>'Luas Tanah '.ucfirst($key) ];           
                $bb = ['y' => intVal($getdata['jml_tidak_'.$key]), 'target' =>$value,'text' =>'Luas Tanah Tidak '.ucfirst($key)];  
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);     
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtProvDual','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);

        
        
    }

    public function npgtPerkebunanDual($kab='null',$year='',$jns='kosong')
    {

        // echo 'kebunku';
        $views='spatial.npgt_perkebunan';
        if ($views =='null') {
            // exit();
            echo json_encode(['data' =>[['name'=>'','y'=>0]],'module'=> 'ptp', 'tahun' => '']);
            exit();
        }

        $arrJns=[];

        $arrJns[0]['jns']= 'Kesesuaian';
        $arrJns[1]['jns']= 'Ketersediaan';
        $arrJns[2]['jns']= 'Rekomendasi';
        $arrJns[0]['views']= 'spatial.v_d_anls_kebun_kssn';
        $arrJns[1]['views']= 'spatial.v_d_anls_kebun_ktsdn';
        $arrJns[2]['views']= 'spatial.v_d_anls_kebun_ocr';



        // print_r($arrJns);
        

        $strJns='';
        $dataJns=[];
        if(!empty($arrJns)){
            foreach ($arrJns as $key => $value) {

                $sel = $jns == $value['jns'] ? 'selected' : '';
                $strJns .='<option '.$sel.' value="'.$value['views'].'">'.$value['jns'].'</option>';
                array_push($dataJns,$value['views']);
            }
        }else{
            $strJns .='<option value="kosong">kosong</option>';
        }

        if (!in_array($jns,$dataJns)){
            $jns=@$jns[0]->views;
        }

        // echo 'views: '.$views.' kab: '.$kab;
        $years= $this->M_model->getYearKab($views,$kab);

        // echo 'tahun-->'.$years[0]->tahun_data;

        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array(@$value->tahun_data,$arryear)){
                array_push($arryear,@$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
       
        
        $data=[];

        // echo 'jenis--->'.$jns;
        $this->db->where('kdpkab', $kab);
        $this->db->where('tahun_data', $year);
        $datas = $this->db->get($jns)->result();
        // echo $this->db->last_query();
        $cat = [];
        if($jns == 'spatial.v_d_anls_kebun_kssn'){
            $this->db->select('kssn as jns');
            $this->db->group_by('kssn');
            $cat = $this->db->get('spatial.npgt_perkebunan')->result();
            
        }else if($jns == 'spatial.v_d_anls_kebun_ktsdn'){
            $this->db->select('ktsdn as jns');
            $this->db->group_by('ktsdn');
            $cat = $this->db->get('spatial.npgt_perkebunan')->result();
            
        }else{
            $this->db->select('ocr as jns');
            $this->db->group_by('ocr');
            $cat = $this->db->get('spatial.npgt_perkebunan')->result();
            
        }
        
   
        
        
       
    //    echo "<pre>";
    //    print_r ($datas);
    //    echo "</pre>";
       
    //    echo "<pre>";
    //    print_r ($cat);
    //    echo "</pre>";
       
        
        foreach ($cat as $k => $v) {
            $a['color'] = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
            $a['name']=@$v->jns;
            $a['y']=floatval(0);
            $cat[$k]=$a;
            foreach ($datas as $key => $value) {
                    
                if(@$value->jns == $v->jns){
                    $a['color'] = sprintf('#%06X', mt_rand(0, 0xFFFFFF));
                    $a['name']=@$value->jns;
                    $a['y']=floatval($value->round);
                    $cat[$k]=$a;
                }
            }
        }
             
        
  
    //    echo "<pre>";
    //    print_r ($cat);
    //    echo "</pre>";exit();
        $data=$cat;
        
        // echo "<pre>";
        // print_r ($cat);
        // echo "</pre>";
        // exit();
        
       
       
   
       
       

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtPerkebunanStack', 'tahun' => $str,'strKlas' =>$strJns];

        echo json_encode($ret);
    }
    public function npgtPerkebunanDualoldsnew($prov='',$year='')
    {
 
        $views = 'spatial.v_d_analisis_npgt_perkebunan';
        $years= $this->M_model->getYearProv($views,$prov);

        
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        $data=[];
        $data[0]['name']='Luas Tidak Sesuai/Tidak Tersedia';
        $data[0]['data']=[];
        $data[0]['color']='#d11144';
        $data[0]['stack']='Tersedia';
        $data[1]['name']='Luas Sesuai/Tersedia';
        $data[1]['data']=[];
        $data[1]['color']='#2196f3';
        $data[1]['stack']='Tersedia';
        $arrcol = [
            'sesuai' => 'Kesesuaian N',
            'tersedia' => 'Ketersediaan Tanah V'
        ];
        // $str='';
            
        $this->db->where('kdppum', $prov);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get($views)->row_array();

        $category=[];
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {
                array_push($category,$value);
                $aa = ['y' => intVal($getdata['jml_'.$key]),'target' =>$value,'text' =>'Luas Tanah '.ucfirst($key) ];           
                $bb = ['y' => intVal($getdata['jml_tidak_'.$key]), 'target' =>$value,'text' =>'Luas Tanah Tidak '.ucfirst($key)];  
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);     
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtPerkebunanStack','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);

        
        
    }

    public function npgtProvDualOld($views='',$prov='',$year='null')
    {
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        
        $year = $year == 'undefined' ? 'null' : $year;
        $years= $this->M_model->getYear($views);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.@$value->tahun_data.'">'.@$value->tahun_data.'</option>';
        }
        
        $str = $str == '' ? '<option value="">Tidak Ada Tahun</option>' : $str;
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }

        if ($years != '') {
            $this->db->where('tahun_data', $year);
            
        }
        if ($prov != '') {
            $this->db->where('kdppum', $prov);
            
        }
        
        $datas = $this->db->get($views)->result();
        // echo $this->db->last_query();
        
        $name= $views == 'spatial.v_d_np_sesuai' ? 'Sesuai':'Tersedia';
        $name_tidak= $views == 'spatial.v_d_np_sesuai' ? 'Tidak Sesuai':'Tidak Tersedia';
        $data=[];
        $data[0]['name']=$name_tidak;
        $data[0]['data']=[];
        $data[0]['stack']='Tersedia';
        $data[0]['layer']=$views;
        $data[0]['color']='#d11144';
        $data[1]['name']=$name;
        $data[1]['data']=[];
        $data[1]['stack']='Tersedia';
        $data[1]['layer']=$views;
        $data[1]['color']='#2196f3';
        $category =[];
        foreach ($datas as $key => $value) {
            
            array_push($category,$value->wadmpr);
            $aa = ['y' => intVal($value->jml), 'target' =>$value->wadmpr];           
            $bb = ['y' => intVal($value->jml_tidak), 'target' =>$value->wadmpr];   
            // $aa = ['y' => intVal($value->jml),'drilldown'=>true, 'target' =>$value->wadmpr];           
            // $bb = ['y' => intVal($value->jml_tidak),'drilldown'=>true, 'target' =>$value->wadmpr];           
            array_push($data[0]['data'],$bb);           
            array_push($data[1]['data'],$aa);           
        }
       
        
        // echo "<pre>";
        // print_r ($data);
        // echo "</pre>";
        
        // $datas=['categories'=>$category,'data'=>$data];
        // $str="";
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtProvDual','category'=>$category,'tahun'=>$str];
        
        // echo "<pre>";
        // print_r ($ret);
        // echo "</pre>";
        
        echo json_encode($ret);
        
    }

    public function drilldownNpgtProvDual($kd_prov='',$views='',$tahun_data=null)
    {
        // echo'masuk';exit();
        
        $drilldown=[];
        $this->db->where('kdppum', $kd_prov);
        $datas = $this->db->get($views.'_drill')->result();
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['jnsPoint']='Ha';
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                    // echo "<pre>";
                    // print_r ($value2);
                    // echo "</pre>";break;
                    
            }
        array_push($drilldown,$b);
        echo json_encode($drilldown[0]);
    }

    public function npgtKabkotDual($prov='',$year='')
    {
 
        // echo $prov;exit();
        $years= $this->M_model->getYearProv('spatial.v_d_analisis_kabkot',$prov);
        $year=$year=='null'?$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        $data=[];
        $data[0]['name']='Luas Tidak Sesuai/Tidak Tersedia/Tidak berubah';
        $data[0]['data']=[];
        $data[0]['stack']='Tersedia';
        $data[0]['color']='#d11144';
        $data[1]['color']='#2196f3';
        $data[1]['name']='Luas Sesuai/Tersedia/Berubah';
        $data[1]['data']=[];
        $data[1]['stack']='Tersedia';
        $arrcol = [
            'sesuai' => 'Kesesuaian N',
            'tersedia' => 'Ketersediaan Tanah V',
            'berubah' => 'Perubahan Penggunaan Tanah GQ' ,
        ];
        // $str='';
            
        $this->db->where('kdppum', $prov);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get('spatial.v_d_analisis_kabkot')->row_array();
        $category=[];
        if(!empty($getdata)){        
                foreach ($arrcol as $key => $value) {
                array_push($category,$value);
                $aa = ['y' => intVal($getdata['jml_'.$key]),'target' =>$value,'text' =>'Jumlah '.ucfirst($key) ];           
                $bb = ['y' => intVal($getdata['jml_tidak_'.$key]), 'target' =>$value,'text' =>'Jumlah Tidak '.ucfirst($key)];  
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);     
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKabDual','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);

        exit();
        $this->db->where('kdppum', $prov);
        $this->db->where('tahun_data', $year);
        $datas = $this->db->get($views)->result();

        $name= $views == 'spatial.v_d_nkab_sesuai' ? 'Sesuai':'Tersedia';
        $name_tidak= $views == 'spatial.v_d_nkab_sesuai' ? 'Tidak Sesuai':'Tidak Tersedia';
        // echo $this->db->last_query();
        
        $data=[];
        $data[0]['name']=$name;
        // $data[0]['kd_Prov']=$datas[0]->kdppum;
        // $data[0]['drilldown']=$datas[0]->wadmpr;
        $data[0]['data']=[];
        $data[0]['stack']='Tersedia';
        $data[1]['name']=$name_tidak;
        // $data[1]['kd_Prov']=$datas[0]->kdppum;
        // $data[0]['drilldown']=$datas[0]->wadmpr;
        $data[1]['data']=[];
        $data[1]['stack']='Tersedia';
        $category =[];
        foreach ($datas as $key => $value) {
            
            array_push($category,$value->wadmkk);           
            array_push($data[0]['data'],intVal($value->jml));           
            array_push($data[1]['data'],intVal($value->jml_tidak));           
        }

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKabDual','category'=>$category,'tahun'=>$str];
        

        echo json_encode($ret);
        
    }

    public function npgtKecDual($kab='',$year='')
    {
 
        $years= $this->M_model->getYearKab('spatial.v_d_analisis_kec',$kab);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        $data=[];
        $data[0]['name']='Luas Tidak Sesuai/Tidak Tersedia';
        $data[0]['data']=[];
        $data[0]['stack']='Tersedia';
        $data[0]['color']='#d11144';
        $data[1]['color']='#2196f3';
        $data[1]['name']='Luas Sesuai/Tersedia';
        $data[1]['data']=[];
        $data[1]['stack']='Tersedia';
        $arrcol = [
            'sesuai' => 'Kesesuaian N',
            'tersedia' => 'Ketersediaan Tanah V'
        ];
        // $str='';
            
        $this->db->where('kdpkab', $kab);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get('spatial.v_d_analisis_kec')->row_array();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($getdata);
        // echo "</pre>";
        
        $category=[];
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {
                array_push($category,$value);
                $aa = ['y' => intVal($getdata['jml_'.$key]),'target' =>$value,'text' =>'Jumlah '.ucfirst($key) ];           
                $bb = ['y' => intVal($getdata['jml_tidak_'.$key]), 'target' =>$value,'text' =>'Jumlah Tidak '.ucfirst($key)];  
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);     
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKecDual','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);

       
        
    }
    public function npgtKecDualOld($views,$year)
    {
        // $views = str_replace('spatial.v_d_npgt_kabkot','spatial.v_d_npgt_kec',$views);
        $years= $this->M_model->getYear($views);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        $this->db->where('tahun_data', $year);
        $datas = $this->db->get($views)->result();
        $name= $views == 'spatial.v_d_nkec_sesuai' ? 'Sesuai':'Tersedia';
        $name_tidak= $views == 'spatial.v_d_nkkec_sesuai' ? 'Tidak Sesuai':'Tidak Tersedia';
        // echo $this->db->last_query();
        
        $data=[];
        $data[0]['name']=$name;
        // $data[0]['kd_Prov']=$datas[0]->kdppum;
        $data[0]['drilldown']=@$datas[0]->wadmpr;
        $data[0]['data']=[];
        $data[0]['stack']='Tersedia';
        $data[1]['name']=$name_tidak;
        // $data[1]['kd_Prov']=$datas[0]->kdppum;
        $data[1]['drilldown']=@$datas[0]->wadmpr;
        $data[1]['data']=[];
        $data[1]['stack']='Tersedia';
        $category =[];
        foreach ($datas as $key => $value) {
            
            array_push($category,$value->wadmpr);           
            array_push($data[0]['data'],intVal($value->jml));           
            array_push($data[1]['data'],intVal($value->jml_tidak));           
        }

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKecDual','category'=>$category,'tahun'=>$str];
        

        echo json_encode($ret);
        
    }
    
    public function npgtProv($year=null)
    {
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        $name='Lengkap';
        $name_tidak= 'Tidak Lengkap';
        $data=[];
        $data[0]['name']=$name_tidak;
        $data[0]['data']=[];
        $data[0]['stack']='Lengkap';
        // $data[0]['layer']='';
        $data[0]['color']='#d11144';
        $data[1]['name']=$name;
        $data[1]['data']=[];
        $data[1]['stack']='Lengkap';
        // $data[1]['layer']=$prov;
        $data[1]['color']='#2196f3';
        $category =[];
        $arrcol = [
            'a' => 'Administrasi (A)',
            'g' => 'Penggunaan Tanah tahun lama (G)',
            'q' => 'Penggunaan Tanah Terakir (Q)',
            'w' => 'RTRW (W)',
            'o' => 'Gambaran Umum Penguasaan Tanah (O)',
            'gq' => 'Perubahan Penggunaan Tanah (GQ)',
            'n' => 'Kesesuaian (N)',
            'v' => 'Ketersediaan Tanah (V)'
        ];
        $str='';
        $this->db->select('kdppum');
        $this->db->group_by('kdppum');
        $this->db->where('length(kdppum)', 2);
        $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
        $batas = count($getBatas);
        // $batas=33;
            
        // $this->db->where('tahun_data', $year);
        $getdata = $this->db->get('spatial.v_d_npgt_prov')->row_array();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($getdata);
        // echo "</pre>";
        
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {

                
                $blmLengkap = $batas-$getdata[$key];

                // $jml = 0;
                // foreach ($getdata as $k => $v) {
                //     $jml = intVal($jml+$v->jml);
                // }
                
                array_push($category,$value);
                $aa = ['y' => intVal($getdata[$key]),'target' =>$value];           
                $bb = ['y' => intVal($blmLengkap), 'target' =>$value];  
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);     
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtProv','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }

    public function npgtKabkota($kab=null,$year=null)
    {
        $years= $this->M_model->getYearKab('spatial.v_d_npgt_kabkota',$kab);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        // $name='Lengkap';
        // $name_tidak= 'Tidak Lengkap';
        // $data=[];
        // $data[0]['name']=$name_tidak;
        // $data[0]['data']=[];
        // $data[0]['stack']='Lengkap';
        // // $data[0]['layer']='';
        // $data[0]['color']='#d11144';
        // $data[1]['name']=$name;
        // $data[1]['data']=[];
        // $data[1]['stack']='Lengkap';
        // // $data[1]['layer']=$prov;
        // $data[1]['color']='#2196f3';
        // $category =[];
        $arrcol = [
            'a' => 'Administrasi (A)',
            'g' => 'Penggunaan Tanah tahun lama (G)',
            'q' => 'Penggunaan Tanah Terakir (Q)',
            'w' => 'RTRW (W)',
            'o' => 'Gambaran Umum Penguasaan Tanah (O)',
            'gq' => 'Perubahan Penggunaan Tanah (GQ)',
            'n' => 'Kesesuaian (N)',
            'v' => 'Ketersediaan Tanah (V)'
        ];
        // $str='';
        // $this->db->select('kdpkab');
        // $this->db->group_by('kdpkab');
        // $this->db->where('length(kdppum)', 2);
        // $this->db->where('kdppum', $prov);
        // $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
        // $batas = count($getBatas);
        // $batas=33;
            
        $this->db->where('kdpkab', $kab);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get('spatial.v_d_npgt_kabkota')->row_array();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($getdata);
        // echo "</pre>";
        $data=[];
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {
                $a['name']=$value; ;
                $a['y']=floatval($getdata['luas']);
                array_push($data,$a);
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKabkot','tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }

    public function npgtKec($kec=null,$year=null)
    {
        $kec = str_replace('%20',' ',$kec);
        $where =['wadmkc' => $kec];
        $years= $this->M_model->getYearWhere('spatial.v_d_npgt_kec',$where);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        // $name='Lengkap';
        // $name_tidak= 'Tidak Lengkap';
        // $data=[];
        // $data[0]['name']=$name_tidak;
        // $data[0]['data']=[];
        // $data[0]['stack']='Lengkap';
        // // $data[0]['layer']='';
        // $data[0]['color']='#d11144';
        // $data[1]['name']=$name;
        // $data[1]['data']=[];
        // $data[1]['stack']='Lengkap';
        // // $data[1]['layer']=$prov;
        // $data[1]['color']='#2196f3';
        // $category =[];
        $arrcol = [
            'a' => 'Administrasi (A)',
            'q' => 'Penggunaan Tanah Terakir (Q)',
            'w' => 'RTRW (W)',
            'o' => 'Gambaran Umum Penguasaan Tanah (O)',
            'n' => 'Kesesuaian (N)',
            'v' => 'Ketersediaan Tanah (V)'
        ];
        // $str='';
        // $this->db->select('kdpkab');
        // $this->db->group_by('kdpkab');
        // $this->db->where('length(kdppum)', 2);
        // $this->db->where('kdppum', $prov);
        // $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
        // $batas = count($getBatas);
        // $batas=33;
            
        $this->db->where('wadmkc', $kec);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get('spatial.v_d_npgt_kec')->row_array();
        // echo $this->db->last_query();
        
   
        $data=[];
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {
                $a['name']=$value; ;
                $a['y']=floatval($getdata['luas_'.$key]);
                array_push($data,$a);
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKec','tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }

    public function npgtKabkotaolds($prov='11',$year=null)
    {
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        $name='Lengkap';
        $name_tidak= 'Tidak Lengkap';
        $data=[];
        $data[0]['name']=$name_tidak;
        $data[0]['data']=[];
        $data[0]['stack']='Lengkap';
        $data[0]['layer']=$prov;
        $data[0]['color']='#d11144';
        $data[1]['name']=$name;
        $data[1]['data']=[];
        $data[1]['stack']='Lengkap';
        $data[1]['layer']=$prov;
        $data[1]['color']='#2196f3';
        $category =[];

        if($prov == '31'){
            $skala = '10';
        }else{
            $skala = 'xx';
        }
        $arrcol = [
            'Administrasi A'=>'kdpkab',
            'Penggunaan Tanah tahun lama (G)'=>'gname',
            'Penggunaan Tanah Terakir Q' => 'qname',
            'RTRW W'=>'wname',
            'Gambaran Umum Penguasaan Tanah O'=>'oname',
            'Perubahan Penggunaan Tanah GQ'=>'gqname',
            'Kesesuaian N'=>'nname',
            'Ketersediaan Tanah V'=>'vname'
        ];


        // $cekJns= $this->M_model->getJnsWil($prov);
        $str='';
        foreach ($arrcol as $key => $value) {
            if($value == 'gname'){
                $this->db->where("case when kdppum = '31' then gname10  is not null and qname10 != '-' and lower(qname10) != 'no data'
                when substring(kdpkab, 4,1) = '7' and kdppum != '31' then gname25  is not null and qname10 != '-' and lower(qname10) != 'no data'
                when  substring(kdpkab, 4,1) != '7' and kdppum != '31' then gname50  is not null and qname10 != '-' and lower(qname10) != 'no data' end ");
            
            }else if($value == 'qname'){
                $this->db->where("case when kdppum = '31' then qname10  is not null and qname10 != '-' and lower(qname10) != 'no data'
                when substring(kdpkab, 4,1) = '7' and kdppum != '31' then qname25  is not null and qname10 != '-' and lower(qname10) != 'no data'
                when  substring(kdpkab, 4,1) != '7' and kdppum != '31' then qname50  is not null and qname10 != '-' and lower(qname10) != 'no data' end ");
            
            }else{
                $this->db->where($value." is not null and ".$value." != '-' and lower(".$value.") != 'no data'");
            }

             // if($value == 'gname'){
            //     $this->db->where('case when kdppum = "31" then gname10  is not null and qname10 != "-" and lower(qname10) != "no data"
            //     when substring(kdpkab, 4,1) = "7" and kdppum != "31" then gname25  is not null and qname10 != "-" and lower(qname10) != "no data"
            //     when  substring(kdpkab, 4,1) != "7" and kdppum != "31" then gname50  is not null and qname10 != "-" and lower(qname10) != "no data" end ');
            
            // }else if($value == 'qname'){
            //     $this->db->where('case when kdppum = "31" then qname10  is not null and qname10 != "-" and lower(qname10) != "no data"
            //     when substring(kdpkab, 4,1) = "7" and kdppum != "31" then qname25  is not null and qname10 != "-" and lower(qname10) != "no data"
            //     when  substring(kdpkab, 4,1) != "7" and kdppum != "31" then qname50  is not null and qname10 != "-" and lower(qname10) != "no data" end ');
            
            // }else{
            //     $this->db->where($value.' is not null and '.$value.' != "-" and lower('.$value.') != "no data"');
            // }
            
            // echo $year;
            // $this->db->where('tahun_data', $year);
            
            $this->db->select('wadmkk,round(sum(luas_ha)) as jml');
            $this->db->where('kdppum', $prov);
            $this->db->group_by('wadmkk');
            $getdata = $this->db->get('spatial.npgt_kabkota')->result();
            // echo $this->db->last_query();
            
            $this->db->select('wadmkk,kdpkab');
            $this->db->where('kdpkab is not null');
            $this->db->where('kdppum', $prov);
            $this->db->group_by('kdpkab,wadmkk');
            $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
            $lengkap = count($getdata);
            $blmLengkap = count($getBatas)-$lengkap;

            $jml = 0;
            foreach ($getdata as $k => $v) {
                $jml = intVal($jml+$v->jml);
            }
            
            array_push($category,$key);
            $aa = ['y' => intVal($lengkap), 'target' =>$key,'luas'=>$jml];           
            $bb = ['y' => intVal($blmLengkap), 'target' =>$key,'luas'=>0];   
            array_push($data[0]['data'],$bb);           
            array_push($data[1]['data'],$aa);     
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKabkot','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);exit();
        
        $datas = $this->db->get('spatial.v_d_npgt_prov')->result();
        $data=[];
        foreach ($datas as $key => $value) {
            // $a=[$prov,intVal($value->jml)];
            $a['layer']='npgt_prov';
            $a['name']=$value->wadmpr;
            $a['kd_prov']=$value->kdppum;
            $a['y']=floatVal($value->jml);
            $a['texty']=floatVal($value->jml)." Kabupaten";
            $a['drilldown']=$value->wadmpr;
            $a['jnsPoint']='Kabupaten';
            array_push($data,$a);
            
        }

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtProv'];

        echo json_encode($ret);
        
    }
    // public function npgtKec($kab,$year=null)
    // {
    //     $years= $this->M_model->getYearKab('spatial.v_d_npgt_kec',$kab);
    //     $year=$year=='null'?@$years[0]->tahun_data:$year;
        
    //     $str='';
    //     $arryear=[];
    //     foreach ($years as $key => $value) {
    //         $sel = intVal($year==$value->tahun_data)?'Selected':'';
    //         if (!in_array($value->tahun_data,$arryear)){
    //             array_push($arryear,$value->tahun_data);
    //         }
            
    //         $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
    //     }
        
    //     // echo "<pre>";
    //     // print_r ($arryear);
    //     // echo "</pre>";
        

    //     if (!in_array($year,$arryear)){
    //         $year=@$years[0]->tahun_data;
    //     }
    //     if($year == 'null' && $year == '' && $year == null){
    //         exit();
    //     }
    //     // $year= $this->M_model->getYear('spatial.npgt_prov');
    //     $name='Lengkap';
    //     $name_tidak= 'Tidak Lengkap';
    //     $data=[];
    //     $data[0]['name']=$name_tidak;
    //     $data[0]['data']=[];
    //     $data[0]['stack']='Lengkap';
    //     // $data[0]['layer']='';
    //     $data[0]['color']='#d11144';
    //     $data[1]['name']=$name;
    //     $data[1]['data']=[];
    //     $data[1]['stack']='Lengkap';
    //     // $data[1]['layer']=$prov;
    //     $data[1]['color']='#2196f3';
    //     $category =[];
    //     $arrcol = [
    //         'a' => 'Administrasi (A)',
    //         'q' => 'Penggunaan Tanah Terakir (Q)',
    //         'w' => 'RTRW (W)',
    //         'o' => 'Gambaran Umum Penguasaan Tanah (O)',
    //         'n' => 'Kesesuaian (N)',
    //         'v' => 'Ketersediaan Tanah (V)'
    //     ];
    //     // $str='';
    //     $this->db->select('namobj');
    //     $this->db->group_by('namobj');
    //     $this->db->where('kdpkab', $kab);
    //     $getBatas = $this->db->get('spatial.batas_administrasi_kecamatan')->result();
    //     $batas = count($getBatas);
    //     // echo $this->db->last_query();
        
    //     // $batas=33;
            
    //     $this->db->where('tahun_data', $year);
    //     $getdata = $this->db->get('spatial.v_d_npgt_kec')->row_array();
    //     if(!empty($getdata)){        
    //         foreach ($arrcol as $key => $value) {

                
    //             $blmLengkap = $batas-$getdata[$key];

    //             // $jml = 0;
    //             // foreach ($getdata as $k => $v) {
    //             //     $jml = intVal($jml+$v->jml);
    //             // }
                
    //             array_push($category,$value);
    //             $aa = ['y' => intVal($getdata[$key]),'target' =>$value,'text' => ' Lengkap  '];           
    //             $bb = ['y' => intVal($blmLengkap), 'target' =>$value,'text' => ' Tidak Lengkap'];  
    //             array_push($data[0]['data'],$bb);           
    //             array_push($data[1]['data'],$aa);     
    //         }
    //     }
    //     $ret= ['data' =>$data,'module'=> 'npgtKec','category'=>$category,'tahun'=>$str];
    //     echo json_encode($ret);exit();
        
        
    // }

    public function npgtPerkebunan($year=null,$kab='null')
    {
        $views='spatial.v_d_npgt_perkebunan';
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        $years= $this->M_model->getYearkab($views,$kab);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        // $name='Lengkap';
        // $name_tidak= 'Tidak Lengkap';
        // $data=[];
        // $data[0]['name']=$name_tidak;
        // $data[0]['data']=[];
        // $data[0]['stack']='Lengkap';
        // // $data[0]['layer']='';
        // $data[0]['color']='#d11144';
        // $data[1]['name']=$name;
        // $data[1]['data']=[];
        // $data[1]['stack']='Lengkap';
        // // $data[1]['layer']=$prov;
        // $data[1]['color']='#2196f3';
        // $category =[];
        $arrcol = [
            'q' => 'Penggunaan Tanah Terakir (Q)',
            'w' => 'RTRW (W)',
            'hgu' => 'O',
            'komoditas' => 'Komoditas',
            'n' => 'Kesesuaian (N)',
            'v' => 'Ketersediaan Tanah (V)'
        ];
        // $str='';
        $this->db->where('kdpkab', $kab);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get($views)->row_array();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($getdata);
        // echo "</pre>";
        $data=[];
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {
                $a['name']=$value; ;
                $a['y']=floatval($getdata['luas']);
                array_push($data,$a);
            }
        }
        
        // echo "<pre>";
        // print_r ($data);
        // echo "</pre>";exit();
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtPerkebunan','tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }

    public function npgtPerkebunanoldnew($year=null)
    {
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        // $name='Lengkap';
        // $name_tidak= 'Tidak Lengkap';
        // $data=[];
        // $data[0]['name']=$name_tidak;
        // $data[0]['data']=[];
        // $data[0]['stack']='Lengkap';
        // // $data[0]['layer']='';
        // $data[0]['color']='#d11144';
        // $data[1]['name']=$name;
        // $data[1]['data']=[];
        // $data[1]['stack']='Lengkap';
        // // $data[1]['layer']=$prov;
        // $data[1]['color']='#2196f3';
        // $category =[];
        $arrcol = [
            // 'a' => 'Administrasi A',
            // 'g' => 'Penggunaan Tanah tahun lama (G)',
            'q' => 'Penggunaan Tanah Terakir (Q)',
            'w' => 'RTRW (W)',
            'hgu' => 'HGU',
            'komoditas' => 'Komoditas',
            'n' => 'Kesesuaian (N)',
            'v' => 'Ketersediaan Tanah (V)'
        ];
        // $str='';
        // $this->db->select('kdppum');
        // $this->db->group_by('kdppum');
        // $this->db->where('length(kdppum)', 2);
        // $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
        // $batas = count($getBatas);
        // $batas=33;
            
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get('spatial.v_d_npgt_perkebunan')->row_array();

        
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {

                
                $blmLengkap = $batas-$getdata[$key];

                // $jml = 0;
                // foreach ($getdata as $k => $v) {
                //     $jml = intVal($jml+$v->jml);
                // }
                
                array_push($category,$value);
                $aa = ['y' => intVal($getdata[$key]),'target' =>$value,'text' => 'Provinsi Lengkap'];           
                $bb = ['y' => intVal($blmLengkap), 'target' =>$value,'text' => 'Provinsi Tidak Lengkap'];  
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);     
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtPerkebunan','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }
    public function mppt($year=null,$kab='null')
    {
        $views='spatial.v_d_mppt';
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        $years= $this->M_model->getYearkab($views,$kab);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        // $name='Lengkap';
        // $name_tidak= 'Tidak Lengkap';
        // $data=[];
        // $data[0]['name']=$name_tidak;
        // $data[0]['data']=[];
        // $data[0]['stack']='Lengkap';
        // // $data[0]['layer']='';
        // $data[0]['color']='#d11144';
        // $data[1]['name']=$name;
        // $data[1]['data']=[];
        // $data[1]['stack']='Lengkap';
        // // $data[1]['layer']=$prov;
        // $data[1]['color']='#2196f3';
        // $category =[];
        $arrcol = [
            'a' => 'Administrasi (A)',
            'g' => 'Penggunaan Tanah tahun lama (G)',
            'q' => 'Penggunaan Tanah Terakir (Q)' 
        ];
        // $str='';
        $this->db->where('kdpkab', $kab);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get($views)->row_array();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($getdata);
        // echo "</pre>";
        $data=[];
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {
                $a['name']=$value; ;
                $a['y']=floatval($getdata['luas']);
                array_push($data,$a);
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'mppt','tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }
    public function mpptOldNew($year=null)
    {
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        $name='Lengkap';
        $name_tidak= 'Tidak Lengkap';
        $data=[];
        $data[0]['name']=$name_tidak;
        $data[0]['data']=[];
        $data[0]['stack']='Lengkap';
        // $data[0]['layer']='';
        $data[0]['color']='#d11144';
        $data[1]['name']=$name;
        $data[1]['data']=[];
        $data[1]['stack']='Lengkap';
        // $data[1]['layer']=$prov;
        $data[1]['color']='#2196f3';
        $category =[];
        $arrcol = [
            'a' => 'Administrasi (A)',
            'g' => 'Penggunaan Tanah tahun lama (G)',
            'q' => 'Penggunaan Tanah Terakir (Q)' 
        ];
        $str='';
        $this->db->select('kdppum');
        $this->db->group_by('kdppum');
        $this->db->where('length(kdppum)', 2);
        $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
        $batas = count($getBatas);
        // $batas=34;
            
        // $this->db->where('tahun_data', $year);
        $getdata = $this->db->get('spatial.v_d_mppt')->row_array();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($getdata);
        // echo "</pre>";
        
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {

                
                $blmLengkap = $batas-$getdata[$key];

                // $jml = 0;
                // foreach ($getdata as $k => $v) {
                //     $jml = intVal($jml+$v->jml);
                // }
                
                array_push($category,$value);
                $aa = ['y' => intVal($getdata[$key]),'target' =>$value];           
                $bb = ['y' => intVal($blmLengkap), 'target' =>$value];  
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);     
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'mppt','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }

    public function tanahNegara($views,$year=null,$kab='null')
    {
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        $views = 'spatial.'.$views;
        $years= $this->M_model->getYearkab($views,$kab);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        // $name='Lengkap';
        // $name_tidak= 'Tidak Lengkap';
        // $data=[];
        // $data[0]['name']=$name_tidak;
        // $data[0]['data']=[];
        // $data[0]['stack']='Lengkap';
        // // $data[0]['layer']='';
        // $data[0]['color']='#d11144';
        // $data[1]['name']=$name;
        // $data[1]['data']=[];
        // $data[1]['stack']='Lengkap';
        // // $data[1]['layer']=$prov;
        // $data[1]['color']='#2196f3';
        // $category =[];
        $arrcol = [
            'a' => 'Administrasi (A)',
            'q' => 'Penggunaan Tanah Terakir (Q)', 
            'w' => 'RTRW (W)',
            'o' => 'Gambaran Umum Penguasaan Tanah (O)' 
        ];
        // $str='';
        $this->db->where('kdpkab', $kab);
        $this->db->where('tahun_data', $year);
        $getdata = $this->db->get($views)->row_array();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($getdata);
        // echo "</pre>";
        $data=[];
        if(!empty($getdata)){        
            foreach ($arrcol as $key => $value) {
                $a['name']=$value; ;
                $a['y']=floatval($getdata['luas']);
                array_push($data,$a);
            }
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'tanahNegara','tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }
    
    // public function npgtPerkebunanDual($year=null)
    // {
    //     if($year == 'null' && $year == '' && $year == null){
    //         exit();
    //     }
    //     // $year= $this->M_model->getYear('spatial.npgt_prov');
    //     $name='Lengkap';
    //     $name_tidak= 'Tidak Lengkap';
    //     $data=[];
    //     $data[0]['name']=$name_tidak;
    //     $data[0]['data']=[];
    //     $data[0]['stack']='Lengkap';
    //     // $data[0]['layer']='';
    //     $data[0]['color']='#d11144';
    //     $data[1]['name']=$name;
    //     $data[1]['data']=[];
    //     $data[1]['stack']='Lengkap';
    //     // $data[1]['layer']=$prov;
    //     $data[1]['color']='#2196f3';
    //     $category =[];
    //     $arrcol = [
    //         // 'a' => 'Administrasi A',
    //         // 'g' => 'Penggunaan Tanah tahun lama (G)',
    //         'q' => 'Penggunaan Tanah Terakir Q' ,
    //         'w' => 'RTRW W',
    //         'hgu' => 'HGU',
    //         'komoditas' => 'Komoditas',
    //         'n' => 'Kesesuaian N',
    //         'v' => 'Ketersediaan Tanah V'
    //     ];
    //     $str='';
    //     $this->db->select('kdppum');
    //     $this->db->group_by('kdppum');
    //     $this->db->where('length(kdppum)', 2);
    //     $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
    //     // $batas = count($getBatas);
    //     $batas=33;
            
    //     $this->db->where('tahun_data', $year);
    //     $getdata = $this->db->get('spatial.v_d_npgt_perkebunan')->row_array();

        
    //     if(!empty($getdata)){        
    //         foreach ($arrcol as $key => $value) {

                
    //             $blmLengkap = $batas-$getdata[$key];

    //             // $jml = 0;
    //             // foreach ($getdata as $k => $v) {
    //             //     $jml = intVal($jml+$v->jml);
    //             // }
                
    //             array_push($category,$value);
    //             $aa = ['y' => intVal($getdata[$key]),'target' =>$value,'text' => 'Provinsi Lengkap'];           
    //             $bb = ['y' => intVal($blmLengkap), 'target' =>$value,'text' => 'Provinsi Tidak Lengkap'];  
    //             array_push($data[0]['data'],$bb);           
    //             array_push($data[1]['data'],$aa);     
    //         }
    //     }
    //     $ret= ['data' =>$data,'module'=> 'npgtPerkebunan','category'=>$category,'tahun'=>$str];
    //     echo json_encode($ret);exit();
        
        
    // }
 
    public function npgtPerkebunanOlds($prov='11',$year=null)
    {
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        $name='Lengkap';
        $name_tidak= 'Tidak Lengkap';
        $data=[];
        $data[0]['name']=$name_tidak;
        $data[0]['data']=[];
        $data[0]['stack']='Lengkap';
        $data[0]['layer']=$prov;
        $data[0]['color']='#d11144';
        $data[1]['name']=$name;
        $data[1]['data']=[];
        $data[1]['stack']='Lengkap';
        $data[1]['layer']=$prov;
        $data[1]['color']='#2196f3';
        $category =[];
        $arrcol = [
            'Penggunaan Tanah Terakir Q' => 'q',
            'RTRW W'=>'w',
            'Verifikasi' => 'verifikasi',
            'HGU' => 'hgu',
            'Komoditas' => 'komoditas',
            'KSSN' => 'kssn',
            'KTSDN' => 'ktsdn',
        ];

        $str='';
        
        foreach ($arrcol as $key => $value) {
            
            $this->db->select('wadmkk,round(sum(luas_ha)) as jml');
            $this->db->where($value.' is not null');
            $this->db->where('kdppum', $prov);
            $this->db->group_by('wadmkk');
            $getdata = $this->db->get('spatial.npgt_perkebunan')->result();
            // echo $this->db->last_query();exit();
            
            $this->db->select('wadmkk,kdpkab,luaswh');
            $this->db->where('kdpkab is not null');
            $this->db->where('kdppum', $prov);
            $this->db->group_by('kdpkab,wadmkk,luaswh');
            $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
            $lengkap = count($getdata);
            $blmLengkap = count($getBatas)-$lengkap;

            $jml = 0;
            foreach ($getdata as $k => $v) {
                $jml = intVal($jml+$v->jml);
            }
            

        
            array_push($category,$key);
            $aa = ['y' => intVal($lengkap), 'target' =>$key,'luas' => $jml];           
            $bb = ['y' => intVal($blmLengkap), 'target' =>$key,'luas' => 0];   
            array_push($data[0]['data'],$bb);           
            array_push($data[1]['data'],$aa);     
        }
        
        // echo "<pre>";
        // print_r ($data);
        // echo "</pre>";
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtPerkebunan','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);exit();
        
        $datas = $this->db->get('spatial.v_d_npgt_prov')->result();
        $data=[];
        foreach ($datas as $key => $value) {
            // $a=[$prov,intVal($value->jml)];
            $a['layer']='npgt_prov';
            $a['name']=$value->wadmpr;
            $a['kd_prov']=$value->kdppum;
            $a['y']=floatVal($value->jml);
            $a['texty']=floatVal($value->jml)." Kabupaten";
            $a['drilldown']=$value->wadmpr;
            $a['jnsPoint']='Kabupaten';
            array_push($data,$a);
            
        }

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtPerkebun'];

        echo json_encode($ret);
        
    }
    public function npgtKecolds($prov='11',$year=null)
    {
        

        $name='Lengkap';
        $name_tidak= 'Tidak Lengkap';
        $data=[];
        $data[0]['name']=$name_tidak;
        $data[0]['data']=[];
        $data[0]['stack']='Lengkap';
        $data[0]['layer']=$prov;
        $data[0]['color']='#d11144';
        $data[1]['name']=$name;
        $data[1]['data']=[];
        $data[1]['stack']='Lengkap';
        $data[1]['layer']=$prov;
        $data[1]['color']='#2196f3';
        $category =[];
        
        $str='';

        $this->db->select('wadmkk,kdpkab');
        $this->db->where('kdpkab is not null');
        $this->db->where('kdppum', $prov);
        $this->db->group_by('kdpkab,wadmkk');
        $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
        
        $arrcol = [
            'Administrasi A'=>'npgt_kec_a',
            'Jalan' => 'npgt_kec_k_polyline',
            'Sungai Polygon' => 'npgt_kec_h_polygon',
            'Sungai Polyline' => 'npgt_kec_h_polyline',
            'RTRW W'=>'npgt_kec_w',
            'Kesesuaian N'=>'npgt_kec_n',
            'Gambaran Umum Penguasaan Tanah O'=>'npgt_kec_o',
            'Ketersediaan Tanah V'=>'npgt_kec_v'
        ];
        
        foreach ($arrcol as $key => $value) {
            if ($year != 'null' && $year != null ) {
                $this->db->where('tahun_data', $year);
            }
            $this->db->select('wadmkk');
            $this->db->where('kdppum', $prov);
            $this->db->where('kdpkab is not null');
            $this->db->group_by('wadmkk');
            $getdata = $this->db->get('spatial.'.$value)->result();
            
            $lengkap = count($getdata);
            $blmLengkap = count($getBatas)-$lengkap;

            
     
            array_push($category,$key);
            $aa = ['y' => intVal($lengkap), 'target' =>$key];           
            $bb = ['y' => intVal($blmLengkap), 'target' =>$key];   
            array_push($data[0]['data'],$bb);           
            array_push($data[1]['data'],$aa);     
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKec','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);
       
        
    }
    public function mpptOlds($prov='11',$year=null)
    {
        $name='Lengkap';
        $name_tidak= 'Tidak Lengkap';
        $data=[];
        $data[0]['name']=$name_tidak;
        $data[0]['data']=[];
        $data[0]['stack']='Lengkap';
        $data[0]['layer']=$prov;
        $data[0]['color']='#d11144';
        $data[1]['name']=$name;
        $data[1]['data']=[];
        $data[1]['stack']='Lengkap';
        $data[1]['layer']=$prov;
        $data[1]['color']='#2196f3';
        $category =[];
        $arrcol = [
            'Penggunaan Tanah tahun lama (G)'=>'gname100',
            'Penggunaan Tanah Terakir Q' => 'qname100'
        ];
        $str='';
        
        foreach ($arrcol as $key => $value) {
            
            $this->db->select('wadmkk,kdpkab');
            $this->db->where($value.' is not null');
            $this->db->where('kdppum', $prov);
            $this->db->group_by('kdpkab,wadmkk');
            $getdata = $this->db->get('spatial.mppt_prov')->result();
            
            $this->db->select('wadmkk,kdpkab');
            $this->db->where('kdpkab is not null');
            $this->db->where('kdppum', $prov);
            $this->db->group_by('kdpkab,wadmkk');
            $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
            // echo $this->db->last_query();
            
            // echo "<pre>";
            // print_r ($getBatas);
            // echo "</pre>";
            
            
            $lengkap = count($getdata);
            $blmLengkap = count($getBatas)-$lengkap;

            
            if($value == 'gname100'){
                $this->db->select('wadmkk');
                $this->db->where('kdppum', $prov);
                $this->db->group_by('wadmkk');
                $getdatas = $this->db->get('spatial.mppt_prov')->result();
                $lengkaps = count($getdatas);
                $blmLengkaps = count($getBatas)-$lengkaps;
                array_push($category,'Administrasi A');
                $aa = ['y' => intVal($lengkaps), 'target' =>'Adminitrasi A'];           
                $bb = ['y' => intVal($blmLengkaps), 'target' =>'Adminitrasi A'];   
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);
            }
            array_push($category,$key);
            $aa = ['y' => intVal($lengkap), 'target' =>$key];           
            $bb = ['y' => intVal($blmLengkap), 'target' =>$key];   
            array_push($data[0]['data'],$bb);           
            array_push($data[1]['data'],$aa);     
        }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'mppt','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);
        
    }
    public function tanahNegaraOldss    ($prov='11',$views='',$year=null)
    {
        if ($views == 'null') {
            exit();
        }

        $arrSkala= [
                    'spatial.v_d_tn_tnbh' => '19',
                    'spatial.v_d_tn_tnbk' => '19',
                    'spatial.v_d_tn_tntk' => '',
                ];
        
        $table= [
                    'spatial.v_d_tn_tnbh' => 'spatial.tanahnegara_prov_tnbh',
                    'spatial.v_d_tn_tnbk' => 'spatial.tanahnegara_prov_tnbk',
                    'spatial.v_d_tn_tntk' => 'spatial.tanahnegara_prov_tntk',
                ];
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        // echo $views;
        $name='Lengkap';
        $name_tidak= 'Tidak Lengkap';
        $data=[];
        $data[0]['name']=$name_tidak;
        $data[0]['data']=[];
        $data[0]['stack']='Lengkap';
        $data[0]['layer']=$prov;
        $data[0]['color']='#d11144';
        $data[1]['name']=$name;
        $data[1]['data']=[];
        $data[1]['stack']='Lengkap';
        $data[1]['layer']=$prov;
        $data[1]['color']='#2196f3';
        $category =[];
        $arrcol = [
            'Penggunaan Tanah Terakir Q' => 'qname'.$arrSkala[$views],
            'RTRW W'=>'wname'.$arrSkala[$views],
            'Gambaran Umum Penguasaan Tanah O'=>'oname'.$arrSkala[$views],
        ];
        $years= $this->M_model->getYearProv($views,$prov);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        foreach ($arrcol as $key => $value) {
            
            $this->db->select('wadmkk,round(sum(luas_ha)) as jml');
            $this->db->where($value.' is not null');
            $this->db->where('kdppum', $prov);
            $this->db->group_by('wadmkk');
            $getdata = $this->db->get($table[$views])->result();
            // echo $this->db->last_query();
            
            $this->db->select('wadmkk,kdpkab');
            $this->db->where('kdpkab is not null');
            $this->db->where('kdppum', $prov);
            $this->db->group_by('kdpkab,wadmkk');
            $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
            $lengkap = count($getdata);
            $blmLengkap = count($getBatas)-$lengkap;

            $jml = 0;
            foreach ($getdata as $k => $v) {
                $jml = intVal($jml+$v->jml);
            }
            
            array_push($category,$key);
            $aa = ['y' => intVal($lengkap),'target' =>$key,'luas'=>$jml];           
            $bb = ['y' => intVal($blmLengkap), 'target' =>$key,'luas'=>0];  
            array_push($data[0]['data'],$bb);           
            array_push($data[1]['data'],$aa);     
        }
 
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'tanahNegara','category'=>$category,'tahun'=>$str];
        echo json_encode($ret);exit();
        
        
    }

    // public function ptp($prov='11',$layer='',$year=null)
    // {
    //     // $year= $this->M_model->getYear('spatial.npgt_prov');
    //     $years= $this->M_model->getYear($views);
    //     $year=$year=='null'?@$years[0]->tahun_data:$year;
        
    //     $str='';
    //     $arryear=[];
    //     foreach ($years as $key => $value) {
    //         $sel = intVal($year==$value->tahun_data)?'Selected':'';
    //         if (!in_array(@$value->tahun_data,$arryear)){
    //             array_push($arryear,@$value->tahun_data);
    //         }
            
    //         $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
    //     }

    //     if (!in_array($year,$arryear)){
    //         $year=@$years[0]->tahun_data;
    //     }
        
    //     $name='Lengkap';
    //     $name_tidak= 'Tidak Lengkap';
    //     $data=[];
    //     $data[0]['name']=$name_tidak;
    //     $data[0]['data']=[];
    //     $data[0]['stack']='Lengkap';
    //     $data[0]['layer']=$prov;
    //     $data[0]['color']='#d11144';
    //     $data[1]['name']=$name;
    //     $data[1]['data']=[];
    //     $data[1]['stack']='Lengkap';
    //     $data[1]['layer']=$prov;
    //     $data[1]['color']='#2196f3';
    //     $category =[];
    //     $arrcol = [
    //         'Administrasi A'=>'kdpkab',
    //         'Penggunaan Tanah tahun lama (G)'=>'gname100',
    //         'Penggunaan Tanah Terakir Q' => 'qname100',
    //         'Perubahan Penggunaan Tanah GQ'=>'gq_name',
    //         'RTRW W'=>'wname',
    //         'Kesesuaian N'=>'nname',
    //         'Gambaran Umum Penguasaan Tanah O'=>'oname',
    //         'Ketersediaan Tanah V'=>'vname'
    //     ];
    //     $str='';
    //     $tabel = [];
        
    //     foreach ($arrcol as $key => $value) {
            
    //         $this->db->select('wadmkk,round(sum(luas_ha)) as jml');
    //         $this->db->where($value.' is not null');
    //         $this->db->where('kdppum', $prov);
    //         $this->db->group_by('wadmkk');
    //         $getdata = $this->db->get($tbl)->result();
            
    //         $this->db->select('wadmkk,kdpkab');
    //         $this->db->where('kdpkab is not null');
    //         $this->db->where('kdppum', $prov);
    //         $this->db->group_by('kdpkab,wadmkk');
    //         $getBatas = $this->db->get('spatial.batas_admin_indonesia')->result();
    //         $lengkap = count($getdata);
    //         $blmLengkap = count($getBatas)-$lengkap;

    //         $jml = 0;
    //         foreach ($getdata as $k => $v) {
    //             $jml = intVal($jml+$v->jml);
    //         }
            
    //         array_push($category,$key);
    //         $aa = ['y' => intVal($lengkap),'drilldown'=>true,'target' =>$key,'luas'=>$jml];           
    //         $bb = ['y' => intVal($blmLengkap), 'target' =>$key,'luas'=>0];  
    //         array_push($data[0]['data'],$bb);           
    //         array_push($data[1]['data'],$aa);     
    //     }
    //     $ret= ['data' =>$data,'module'=> 'npgtProv','category'=>$category,'tahun'=>$str];
    //     echo json_encode($ret);exit();
        
        
    // }
      
    public function npgtPerkebunanOld($prov='',$year='')
    {
        // $year= $this->M_model->getYear('spatial.npgt_prov');
        
        if($prov==''){
            exit();
        }
        if($year != ''){
            $this->db->where('tahun_data', $year);
        }
        $this->db->order_by('jml', 'desc');
        
        $datas = $this->db->get('spatial.v_d_npgt_perkebunan_drill')->result();
        $data=[];
        foreach ($datas as $key => $value) {
            // $a=[$prov,intVal($value->jml)];
            $a['tahun']=$value->tahun_data;
            $a['layer']='npgt_perkebunan';
            $a['name']=$value->wadmkk;
            $a['kd_prov']=$value->kdpkab;
            $a['y']=floatval($value->jml);
            // $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
            
        }

        $ret= ['data' =>$data,'module'=> 'npgtPerkebunan'];

        echo json_encode($ret);
        
    }


    public function drilldownNpgtProv($kd_prov)
    {
        
        $drilldown=[];
        $this->db->where('kdppum', $kd_prov);
        $datas = $this->db->get('spatial.v_d_npgt_prov_drill')->result();
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['jnsPoint']='Ha';
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                    // echo "<pre>";
                    // print_r ($value2);
                    // echo "</pre>";break;
                    
            }
        array_push($drilldown,$b);
        echo json_encode($drilldown[0]);
    }

    public function drilldownNpgtPerkebunan($kd_prov)
    {
        
        $drilldown=[];
        $this->db->where('kdppum', $kd_prov);
        $datas = $this->db->get('spatial.v_d_npgt_perkebunan_drill')->result();
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                    // echo "<pre>";
                    // print_r ($value2);
                    // echo "</pre>";break;
                    
            }
        array_push($drilldown,$b);
        echo json_encode($drilldown[0]);
    }

    // controller utama
    public function npgtKabkotaold($views,$year,$prov='')
    {
        
        $years= $this->M_model->getYear($views);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        $listProv= $this->M_model->getProv($views,$year);
        
 
        
        $str='';
        $arryear=[];
        
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }

        $strprov='';
        $arrprov=[];
        
        foreach ($listProv as $key => $value) {
            $sel = $prov==$value->kdppum?'Selected':'';
            if (!in_array($value->kdppum,$arrprov)){
                array_push($arrprov,$value->wadmpr);
            }
            
            $strprov .='<option '.$sel.' value="'.$value->kdppum.'">'.$value->wadmpr.'</option>';
        }

        if (!in_array($prov,$listProv)){
            $prov=@$listProv[0]->kdppum;
        }
        
        $data=[];
        $vexp = explode('.',$views);
        

        foreach ($vexp as $key => $value) {
            // echo $value;
            
            if (strpos($value,'kab=') !== false) {
                $views=str_replace('kab=','',$value);
                break;
            }elseif (strpos($value,'kec=') !== false) {
                $views=str_replace('kec=','',$value);
                break;
            }else{
                
                $views=str_replace('keb=','',$value);
            }
        }
        if($views!='kosong'){
            $this->db->where('kdppum', $prov);
            
            $this->db->where('tahun_data', $year);
            
            $datas = $this->db->get($views)->result();
            foreach ($datas as $key => $value) {
                // $a=[$prov,intVal($value->jml)];
            $a['tahun']=$year;
            $a['layer']=$views;
            $a['name']=$value->wadmpr;
            $a['kd_prov']=$value->kdppum;
            $a['y']=floatval($value->jml);
            $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
            
            }
        }else{

        }


        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKabkot', 'tahun' => $str,'prov' => $strprov];

        echo json_encode($ret);
        
    }
    // contrller drilldown
    public function drilldownNpgtKabkota($kd_prov,$layer,$tahun)
    {
        $drilldown=[];
        $this->db->where("kdppum",$kd_prov);
        $this->db->where("tahun_data",$tahun);
        $datas = $this->db->get($layer.'_drill')->result();
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
    
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                    
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    }

    public function npgtKecOld($views,$year)
    {
        $views = str_replace('spatial.v_d_npgt_kabkot','spatial.v_d_npgt_kec',$views);
        // echo $views;exit();
        $years= $this->M_model->getYear($views);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        $data=[];
        $vexp = explode('.',$views);
        

        foreach ($vexp as $key => $value) {
            // echo $value;
            
            if (strpos($value,'kab=') !== false) {
                $views=str_replace('kab=','',$value);
                break;
            }elseif (strpos($value,'kec=') !== false) {
                $views=str_replace('kec=','',$value);
                break;
            }else{
                
                $views=str_replace('keb=','',$value);
            }
        }
        if($views!='kosong'){
            $this->db->where('tahun_data', $year);
            
            $datas = $this->db->get($views)->result();
            foreach ($datas as $key => $value) {
                // $a=[$prov,intVal($value->jml)];
            $a['tahun']=$year;
            $a['layer']=$views;
            $a['name']=$value->wadmpr;
            $a['kd_prov']=$value->kdppum;
            $a['y']=floatval($value->jml);
            $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
            
            }
        }else{

        }

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'npgtKec', 'tahun' => $str];

        echo json_encode($ret);
        
    }

    public function drilldownNpgtKec($kd_prov,$layer,$tahun)
    {
        $views = str_replace('spatial.v_d_npgt_kabkot','spatial.v_d_npgt_kec',$layer);

        $drilldown=[];
        $this->db->where("kdppum",$kd_prov);
        $this->db->where("tahun_data",$tahun);
        $datas = $this->db->get($layer.'_drill')->result();
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
   
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                    
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    }


    public function wp3wtolds($prov,$views='',$year=null)
    {
        if ($prov == '' || $views =='') {
            exit();
        }

        $years= $this->M_model->getYearProv($views,$prov);
        $year=$year=='null'?$years[0]->tahun_data:$year;

        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.@$value->tahun_data.'">'.@$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        $data=[];
        $this->db->where('kdppum', $prov);
        $this->db->where('tahun_data', $year);

        $datas = $this->db->get($views.'_drill')->result();
        // echo $this->db->last_query();
        if (!empty($datas)) {
            foreach ($datas as $key => $value) {
                $this->db->select('round(luaswh) luaswh');
                $this->db->where('kdpkab', $value->kdpkab);
                $bts = $this->db->get('spatial.batas_admin_indonesia')->row_array()['luaswh'];
                // echo $this->db->last_query();
                
                $persen =  round(($value->jml/$bts)*100,2);

                // echo $value->wadmkk.'</br>';
                // echo $value->jml.'/'.$bts.'</br>';
                // echo $persen;
                // echo '</br></br>';
                $a['tahun']=@$year;
                $a['name']=@$value->wadmkk;
                $a['kd_prov']=$value->kdpkab;
                $a['layer']=$views;
                $a['y']=floatval($persen);
                // $a['drilldown']=@$value->wadmpr;
                array_push($data,$a);
            }
        }
       

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'wp3wt', 'tahun' => $str];

        echo json_encode($ret);
    }

    public function drilldownWp3wt($kd_prov,$layer,$tahun=null)
    {
        // echo $prov.$layer;
        // exit();
        $tahun = $tahun == 'null' ? null :  $tahun;
        $drilldown=[];
        $this->db->where("kdppum",$kd_prov);
        $this->db->where("tahun_data",$tahun);
        $datas = $this->db->get($layer.'_drill')->result();
        // echo $this->db->last_query();exit();
        
        // echo "<pre>";
        // print_r ($datas);
        // echo "</pre>";
        
        $b['kd_prov']=$datas[0]->kdppum;
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
             
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    }
    public function pgtl($views='null',$prov='null',$year='null')
    {
        if ($views =='null' || $prov == 'null') {
            // exit();
            echo json_encode(['data' =>[['name'=>'','y'=>0]],'module'=> 'pgtl', 'tahun' => '']);
            exit();
        }
        $years= $this->M_model->getYearProv($views,$prov);
    
        
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.@$value->tahun_data.'">'.@$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data != '' ? '':@$years[0]->tahun_data;
        }
        
        $data=[];
        
        // echo "<pre>";
        // print_r ($year);
        // echo "</pre>";
        
        if(!empty($year)){
            $this->db->where('tahun_data',$year);
        };
        $this->db->where('kdppum', $prov);
        $datas = $this->db->get($views)->result();
        // echo $this->db->last_query();
        
   
        
        foreach ($datas as $key => $value) {
            // $this->db->select('round(luaswh) luaswh');
            // $this->db->where('kdpkab', $value->kdpkab);
            // $bts = $this->db->get('spatial.batas_admin_indonesia')->row_array()['luaswh'];
            // // echo $this->db->last_query();
            
            // $persen =  round(($value->jml/$bts)*100,2);

            // echo $value->wadmkk.'</br>';
            // echo $value->jml.'/'.$bts.'</br>';
            // echo $persen;
            // echo '</br></br>';
            
            // $a=[$prov,intVal($value->jml)];
            $a['tahun']=@$year;
            $a['name']=@$value->wadmkk;
            // $a['kd_prov']=$value->kdpkab;
            // $a['layer']=$views;
            $a['y']=floatval($value->luas);
            // $a['drilldown']=@$value->wadmpr;
            array_push($data,$a);
            
        }

        $ret= ['data' =>$data,'module'=> 'pgtl', 'tahun' => $str];

        echo json_encode($ret);
    }
    public function pgtlKec($views='null',$kab='null',$year=null)
    {
        // echo $views.$prov.$year;
        if ($views =='null' || $kab == 'null') {
            // exit();
            echo json_encode(['data' =>[['name'=>'','y'=>0]],'module'=> 'pgtl', 'tahun' => '']);
            exit();
        }
        $years= $this->M_model->getYearKab($views,$kab);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.@$value->tahun_data.'">'.@$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data != '' ? '':@$years[0]->tahun_data;
        }
        
        $data=[];
        
        // echo "<pre>";
        // print_r ($year);
        // echo "</pre>";
        
        if(!empty($year)){
            $this->db->where('tahun_data',$year);
        };
        $this->db->select('kdpkab,namobj,tahun_data,luas');
        $this->db->group_by('namobj,kdpkab,tahun_data,luas');
        $this->db->where('kdpkab', $kab);
        $datas = $this->db->get($views)->result();
        // echo $this->db->last_query();
        
   
        
        foreach ($datas as $key => $value) {
            // $this->db->select('round(luaswh) luaswh');
            // $this->db->where('kdpkab', $value->kdpkab);
            // $bts = $this->db->get('spatial.batas_admin_indonesia')->row_array()['luaswh'];
            // // echo $this->db->last_query();
            
            // $persen =  round(($value->jml/$bts)*100,2);

            // echo $value->wadmkk.'</br>';
            // echo $value->jml.'/'.$bts.'</br>';
            // echo $persen;
            // echo '</br></br>';
            
            // $a=[$prov,intVal($value->jml)];
            $a['tahun']=@$year;
            $a['name']=@$value->namobj;
            // $a['kd_prov']=$value->kdpkab;
            // $a['layer']=$views;
            $a['y']=floatval($value->luas);
            // $a['drilldown']=@$value->wadmpr;
            array_push($data,$a);
            
        }
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }


        $ret= ['data' =>$data,'module'=> 'pgtl', 'tahun' => $str];

        echo json_encode($ret);
    }
    public function pgtlDesa($views='null',$kec='null',$year=null)
    {
        // echo $views.$prov.$year;
        if ($views =='null' || $kec == 'null') {
            // exit();
            echo json_encode(['data' =>[['name'=>'','y'=>0]],'module'=> 'pgtl', 'tahun' => '']);
            exit();
        }
        $where = ['wadmkc' => $kec];
        $years= $this->M_model->getYearWhere($views,$where);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.@$value->tahun_data.'">'.@$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data != '' ? '':@$years[0]->tahun_data;
        }
        
        $data=[];
        
        // echo "<pre>";
        // print_r ($year);
        // echo "</pre>";
        
        if(!empty($year)){
            $this->db->where('tahun_data',$year);
        };
        $this->db->select('kdpkab,namobj,tahun_data,luas,wadmkc');
        $this->db->group_by('namobj,kdpkab,tahun_data,luas,wadmkc');
        $this->db->where('wadmkc', $kec);
        $datas = $this->db->get($views)->result();
        // echo $this->db->last_query();
        
   
        
        foreach ($datas as $key => $value) {
            // $this->db->select('round(luaswh) luaswh');
            // $this->db->where('kdpkab', $value->kdpkab);
            // $bts = $this->db->get('spatial.batas_admin_indonesia')->row_array()['luaswh'];
            // // echo $this->db->last_query();
            
            // $persen =  round(($value->jml/$bts)*100,2);

            // echo $value->wadmkk.'</br>';
            // echo $value->jml.'/'.$bts.'</br>';
            // echo $persen;
            // echo '</br></br>';
            
            // $a=[$prov,intVal($value->jml)];
            $a['tahun']=@$year;
            $a['name']=@$value->namobj;
            // $a['kd_prov']=$value->kdpkab;
            // $a['layer']=$views;
            $a['y']=floatval($value->luas);
            // $a['drilldown']=@$value->wadmpr;
            array_push($data,$a);
            
        }
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
      
        $ret= ['data' =>$data,'module'=> 'pgtl', 'tahun' => $str];

        echo json_encode($ret);
    }
    public function drilldownPgtl($kd_prov,$layer,$tahun=null)
    {
        // $year = $year == null ? 
        $drilldown=[];
        $this->db->where("kdppum",$kd_prov);
        $this->db->where("tahun_data",$tahun);
        $datas = $this->db->get($layer.'_drill')->result();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($datas);
        // echo "</pre>";
        
        $b['kd_prov']=$datas[0]->kdppum;
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
             
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    } 


    function ptpLayanan($views='',$year='',$prov,$kab='null') {
        if($kab != 'null'){
            $years= $this->M_model->getYearKab($views,$kab);
        }else{
            $years= $this->M_model->getYearProv($views,$prov);
        }
        $year=$year=='null'?@$years[0]->tahun_data:$year;
            // echo $year.$prov,$kab;
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        if ($year != 'null') {
            $this->db->where('tahun_data', $year);
        }
        if($kab == 'null'){
            $this->db->where('kdppum', $prov);
        }else{
            $this->db->where('kdpkab', $kab);
        }
        $datas = $this->db->get($views)->result();
        
        // echo "<pre>";
        // print_r ($datas);
        // echo "</pre>";
        
        $data=[];
        foreach ($datas as $key => $value) {
            
            
            // $this->db->select('round(luaswh) luaswh');
            // $this->db->where('wadmpr', $value->wadmpr);
            // $this->db->group_by('title');
            
            // $bts = $this->db->get('spatial.batas_admin_indonesia')->row_array()['luaswh'];

            // echo $value->wadmkk.'</br>';
            // echo $value->jml.'/'.$bts.'</br>';
            // echo $persen;
            // echo '</br></br>';
            
            // $a=[$prov,intVal($value->jml)];
            // $persen =  round(($value->jml/$bts)*100,2);
            $a['name']=$value->wadmkk; ;
            $a['luas']=floatval($value->luas);
            // $value->persen = $value->persen < 1 ? $value->persen+1:$value->persen;
            $a['y']=floatval($value->pemohon);
            // $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
        }

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'ptpMonitoringInteropLayanan','tahun' => $str];
        echo json_encode($ret);

    }
    
    public function ptp($views='',$year='',$prov,$per='kosong')
    {
        // echo $views;

        // echo $views.'-'.$year.'-'.$prov;
        $role=$this->session->users['id_user_group_real'];
        $kabkot=$this->session->users['kd_kabkot'];
        // echo $role; die();

        if($role==7 || $role==8) {
            $prov = explode('.',$kabkot)[0];
        }
        $this->db->where('kdppum', $prov);
        if ($views =="") {
            // exit();
            $str='<option value="null">Tahun Pengerjaan</option>';

            echo json_encode(['data' =>[['name'=>'','y'=>0]],'module'=> 'ptp', 'tahun' => $str]);
            exit();
        }

        $klas=[];
        if($views == 'spatial.v_d_ptp_p3t' ||  $views == 'spatial.v_d_ptpil_prov' || $views == 'spatial.v_d_ptpil_prov_tk'){
                // $this->db->select('klas');
                // $this->db->group_by('klas');
                // $this->db->where('klas is not null');
                // $klas = $this->db->get($views)->result();
                $klas[0]= ['klas'=>'Pertanian'];
                $klas[1]= ['klas'=>'Non-Pertanian'];
        }
        
        // echo "<pre>";
        // print_r ($klas);
        // echo "</pre>";
        
        $strKlas='';
        $dataKlas=[];
        if(!empty($klas)){
            foreach ($klas as $key => $value) {
                $sel = $per == $value['klas'] ? 'selected' : '';
                $strKlas .='<option '.$sel.' value="'.str_replace(',','.',$value['klas']).'">'.$value['klas'].'</option>';
                array_push($dataKlas,$value['klas']);
            }
        }else{
            $strKlas .='<option value="kosong">kosong</option>';
        }
        
        
        if (!in_array($per,$dataKlas)){
            $per=@$klas[0]['klas'];
        }
        $per = str_replace('.',',',$per);
        if($per!='kosong' && $per != ''){
            $klas = ['klas'=> $per,
                        'kdppum' => $prov];
            $years= $this->M_model->getYearWhere($views,$klas);

        }else{
            $years= $this->M_model->getYearProv($views,$prov);
        }

        $year=$year==""?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array(@$value->tahun_data,$arryear)){
                array_push($arryear,@$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        $data=[];
        
     
        
        if($per!='kosong' && $per != ''){
            $this->db->where('klas', $per);
        }
        $this->db->where('kdppum', $prov);
        $this->db->where('tahun_data', $year);
        $datas = $this->db->get($views)->result();
        
        // echo $this->db->last_query();
        
        
        // echo $this->db->last_query();
        
        
        foreach ($datas as $key => $value) {

            $a['tahun']=@$year;
            $a['name']=@$value->wadmkk;
            // $a['kd_prov']=$value->kdpkab;
            $a['layer']=$views;
            // echo $views;

            if($views=='spatial.v_d_ptpil_prov_tk'){
                $jns= ' Layanan';
                $a['luasnya']='Jumlah Point : '.$value->pemohon.' Point';
                $y = $value->pemohon;
                $title = 'Point';
            }else{
                $jns= ' Layanan';
                $y = $value->pemohon;
                $title = 'Luasan Ha';
                
                $a['luas_ptp']=floatval($value->luas);
                $a['luasnya']='Luas : '.number_format(floatval($value->luas)).' ha';
                $a['luas_bts']=floatval($value->luaswh);
            }
            $a['nilaiy']=floatval($y);
         
            // $value->persen = $value->persen < 1 ? $value->persen+1: $value->persen;
            $a['y']=floatval($y);
            $a['jns']=$jns;
            
            // $a['drilldown']=@$value->wadmpr;
            array_push($data,$a);
            
        }
       if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        if (empty($data)) {
            $this->db->select('wadmpr');
            $this->db->where('kdppum', $prov);
            $datas = $this->db->get('spatial.batas_admin_indonesia')->row_array();
            // echo $this->db->last_query();
            
            $data[0] = ['name' => $datas['wadmpr'], 
                    'luasnya' => '',
                    'y' => 0,
                    'jns' => ' Layanan',
                ];
                
        }

             
                

        $ret= ['data' =>$data,'module'=> 'ptp', 'tahun' => $str,'strKlas' =>$strKlas,'isPer' => count($klas),'title' => @$title];

        echo json_encode($ret);
    }

    function ptpMonitoringInterop($views='',$kabkot) {
        $curl = curl_init();
  
        $arr = [
                'v_d_ptp_tnh_timbul' => 'tanah%20timbul',
                'v_d_ptp_stranas' => 'strategis%20nasional',
                'v_d_ptp_non_berusaha' => 'kegiatan%20non%20berusaha',
                'v_d_ptp_berusaha' => 'kegiatan%20berusaha',
                'v_d_ptp_pk_p3t' => 'pemanfaatan%20tanah',
        ];

        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api-interop.atrbpn.go.id/api/internal/dirpgt/signata/data-layanan-ptp?kodekab=".$kabkot."&jenislayanan=".$arr[$views],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => [
                "Accept: */*",
                "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************.SFrpz4MIod1D7tp8XAwaY56m-NeCbKqAWRla-leNbmE",
                "User-Agent: Thunder Client (https://www.thunderclient.com)"
            ],
        ]);

        $response = curl_exec($curl);
        
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            echo "cURL Error #:" . $err;
        } else {
            $response = json_decode($response);
            $datas = $response->data;

            

            $data=[];
            $data[0]['name']='Persiapan Lapangan';
            $data[0]['data']=[];
            $data[0]['color']='orange';
            $data[0]['stack']='Persiapan';
            $data[1]['name']='Peninjauan Lapangan';
            $data[1]['data']=[];
            $data[1]['color']='#2196f3';
            $data[1]['stack']='Persiapan';
            $data[2]['name']='Perumusan PTP';
            $data[2]['data']=[];
            $data[2]['color']='#3dfc03';
            $data[2]['stack']='Persiapan';

            $getdata=[];
            $category = [];            
            if (!empty($datas)) {
                $jml = 0;
                
                $getdata['kd']=0;
                $getdata['ld']=0;
                $getdata['jml']=0;
                $getdata['j152']=0;
                $getdata['j1315']=0;
                $getdata['j5213']=0;
                $getdata['jl17']=0;
                $getdata['persiapan_kd']=0;
                $getdata['peninjauan_kd']=0;
                $getdata['perumusan_kd']=0;
                $getdata['persiapan_ld']=0;
                $getdata['peninjauan_ld']=0;
                $getdata['perumusan_ld']=0;
                $getdata['perumusan_kd']=0;
                $getdata['persiapan_jml']=0;
                $getdata['peninjauan_jml']=0;
                $getdata['perumusan_jml']=0;
                $getdata['perumusan_kd']=0;
                $getdata['persiapan_j152']=0;
                $getdata['peninjauan_j152']=0;
                $getdata['perumusan_j152']=0;
                $getdata['persiapan_j5213']=0;
                $getdata['peninjauan_j5213']=0;
                $getdata['perumusan_j5213']=0;
                $getdata['persiapan_j5213']=0;
                $getdata['peninjauan_j5213']=0;
                $getdata['perumusan_j5213']=0;
                $getdata['persiapan_jl17']=0;
                $getdata['peninjauan_jl17']=0;
                $getdata['perumusan_jl17']=0;
                $getdata['perumusan_j5213']=0;
                $getdata['persiapan_j1315']=0;
                $getdata['peninjauan_j1315']=0;
                $getdata['perumusan_j1315']=0;
                
                foreach ($datas as $k => $v) {
                       $jml = intVal($jml)+intVal($v->luasdimohon);
                       if($views == 'spatial.v_d_ptp_non_berusaha'){
                            if ($v->luasdimohon <= 10000 ) {
                                $getdata['kd'] = intVal($getdata['kd']+($v->luasdimohon));
                                $getdata['persiapan_kd'] = intVal($getdata['persiapan_kd']+($v->luasdimohon*20000));
                                $getdata['peninjauan_kd'] = intVal($getdata['peninjauan_kd']+($v->luasdimohon*80000));
                                $getdata['perumusan_kd'] = intVal($getdata['perumusan_kd']+($v->luasdimohon*180000));
                            }else{
                                $getdata['ld'] = intVal($getdata['ld']+($v->luasdimohon));
                                $getdata['persiapan_ld'] = intVal($getdata['persiapan_ld']+($v->luasdimohon*200000));
                                $getdata['peninjauan_ld'] = intVal($getdata['peninjauan_ld']+($v->luasdimohon*890000));
                                $getdata['perumusan_ld'] = intVal($getdata['perumusan_ld']+($v->luasdimohon*245000));
                            }
                              
                        }elseif($views == 'spatial.v_d_ptp_stranas'){
                                $getdata['jml'] = intVal($getdata['jml']+($v->luasdimohon));
                                $getdata['persiapan_jml'] = intVal($getdata['persiapan_jml']+($v->luasdimohon*200000));
                                $getdata['peninjauan_jml'] = intVal($getdata['peninjauan_jml']+($v->luasdimohon*890000));
                                $getdata['perumusan_jml'] = intVal($getdata['perumusan_jml']+($v->luasdimohon*245000));
                    
                        }elseif($views == 'spatial.v_d_ptp_tnh_timbul'){
                            $getdata['jml'] = intVal($getdata['jml']+($v->luasdimohon));
                            $getdata['persiapan_jml'] = intVal($getdata['persiapan_jml']+($v->luasdimohon*200000));
                            $getdata['peninjauan_jml'] = intVal($getdata['peninjauan_jml']+($v->luasdimohon*890000));
                            $getdata['perumusan_jml'] = intVal($getdata['perumusan_jml']+($v->luasdimohon*245000));
                        }elseif($views == 'spatial.v_d_ptp_pk_p3t'){
            
                            if ($v->luasdimohon <= 10000 ) {
                                $getdata['kd'] = intVal($getdata['kd']+($v->luasdimohon));
                                $getdata['persiapan_kd'] = intVal($getdata['persiapan_kd']+($v->luasdimohon*20000));
                                $getdata['peninjauan_kd'] = intVal($getdata['peninjauan_kd']+($v->luasdimohon*80000));
                                $getdata['perumusan_kd'] = intVal($getdata['perumusan_kd']+($v->luasdimohon*180000));
                            }else{
                                $getdata['ld'] = intVal($getdata['ld']+($v->luasdimohon));
                                $getdata['persiapan_ld'] = intVal($getdata['persiapan_ld']+($v->luasdimohon*200000));
                                $getdata['peninjauan_ld'] = intVal($getdata['peninjauan_ld']+($v->luasdimohon*890000));
                                $getdata['perumusan_ld'] = intVal($getdata['perumusan_ld']+($v->luasdimohon*245000));
                            }
                                 
                        } elseif($views == 'spatial.v_d_ptp_berusaha'){
                            if ($v->luasdimohon <= 10000 ) {
                                $getdata['kd'] = intVal($getdata['kd']+($v->luasdimohon));
                                $getdata['persiapan_kd'] = intVal($getdata['persiapan_kd']+($v->luasdimohon*20000));
                                $getdata['peninjauan_kd'] = intVal($getdata['peninjauan_kd']+($v->luasdimohon*80000));
                                $getdata['perumusan_kd'] = intVal($getdata['perumusan_kd']+($v->luasdimohon*180000));
                            }else  if ($v->luasdimohon > 10000 && $v->luasdimohon <= 500000){
                                $getdata['j152'] = intVal($getdata['j152']+($v->luasdimohon));
                                $getdata['persiapan_j152'] = intVal($getdata['persiapan_j152']+($v->luasdimohon*200000));
                                $getdata['peninjauan_j152'] = intVal($getdata['peninjauan_j152']+($v->luasdimohon*890000));
                                $getdata['perumusan_j152'] = intVal($getdata['perumusan_j152']+($v->luasdimohon*245000));
                            }else  if ($v->luasdimohon > 5000000 && $v->luasdimohon <= 100000000){
                                $getdata['j5213'] = intVal($getdata['j5213']+($v->luasdimohon));
                                $getdata['persiapan_j5213'] = intVal($getdata['persiapan_j5213']+($v->luasdimohon*200000));
                                $getdata['peninjauan_j5213'] = intVal($getdata['peninjauan_j5213']+($v->luasdimohon*890000));
                                $getdata['perumusan_j5213'] = intVal($getdata['perumusan_j5213']+($v->luasdimohon*245000));
                            }else  if ($v->luasdimohon > 10000000 && $v->luasdimohon <= 100000000){
                                $getdata['j1315'] = intVal($getdata['j1315']+($v->luasdimohon));
                                $getdata['persiapan_j1315'] = intVal($getdata['persiapan_j1315']+($v->luasdimohon*200000));
                                $getdata['peninjauan_j1315'] = intVal($getdata['peninjauan_j1315']+($v->luasdimohon*890000));
                                $getdata['perumusan_j1315'] = intVal($getdata['perumusan_j1315']+($v->luasdimohon*245000));
                            }else  if ($v->luasdimohon > 100000000){
                                $getdata['jl17'] = intVal($getdata['jl17']+($v->luasdimohon));
                                $getdata['persiapan_jl17'] = intVal($getdata['persiapan_jl17']+($v->luasdimohon*200000));
                                $getdata['peninjauan_jl17'] = intVal($getdata['peninjauan_jl17']+($v->luasdimohon*890000));
                                $getdata['perumusan_jl17'] = intVal($getdata['perumusan_jl17']+($v->luasdimohon*245000));
                            }
                            $arrcol = [
                                'kd' => 'PTP PKKPR Berusaha <= 1 Ha (<b>'.$getdata['kd'].'</b>)',
                                'j152' => 'PTP PKKPR Non Berusaha >1 - 500  Ha (<b>'.$getdata['j152'].'</b>)',
                                'j5213' => 'PTP PKKPR Non Berusaha >500 - 1000  Ha (<b>'.$getdata['j5213'].'</b>)',
                                'j1315' => 'PTP PKKPR Non Berusaha >1000 - 10000  Ha (<b>'.$getdata['j1315'].'</b>)',
                                'jl17' => 'PTP PKKPR Non Berusaha >10000  Ha (<b>'.$getdata['jl17'].'</b>)',
                            ];     
                        }   
                }

                if($views == 'spatial.v_d_ptp_non_berusaha'){

                    $arrcol = [
                        'kd' => 'PTP PKKPR Non Berusaha <= 1 Ha (<b>'.$getdata['kd'].'</b>)',
                        'ld' => 'PTP PKKPR Non Berusaha > 1 Ha (<b>'.$getdata['ld'].'</b>)',
                    ];     
                }elseif($views == 'spatial.v_d_ptp_stranas'){
                    $arrcol = [
                        'jml' => 'PTP RKKPR/PKKPR Strategis Nasional (<b>'.$getdata['jml'].'</b>)',
                    ];   
               
                }elseif($views == 'spatial.v_d_ptp_tnh_timbul'){
                    $arrcol = [
                        'jml' => 'PTP dalam Rangka Penegasan Status dan Rekomendasi Penguasaan Tanah Timbul (<b>'.$getdata['jml'].'</b>)',
                    ];   
                }elseif($views == 'spatial.v_d_ptp_pk_p3t'){
    
                    $arrcol = [
                        'kd' => 'PTP Penyelenggaraan Kebijakan Penggunaan dan Pemanfaatan Tanah  <= 1 Ha (<b>'.$getdata['kd'].'</b>)',
                        'ld' => 'PTP Penyelenggaraan Kebijakan Penggunaan dan Pemanfaatan Tanah  > 1 Ha (<b>'.$getdata['ld'].'</b>)',
                    ];     
                } elseif($views == 'spatial.v_d_ptp_berusaha'){
    
                    $arrcol = [
                        'kd' => 'PTP PKKPR Berusaha <= 1 Ha (<b>'.$getdata['kd'].'</b>)',
                        'j152' => 'PTP PKKPR Non Berusaha >1 - 500  Ha (<b>'.$getdata['j152'].'</b>)',
                        'j5213' => 'PTP PKKPR Non Berusaha >500 - 1000  Ha (<b>'.$getdata['j5213'].'</b>)',
                        'j1315' => 'PTP PKKPR Non Berusaha >1000 - 10000  Ha (<b>'.$getdata['j1315'].'</b>)',
                        'jl17' => 'PTP PKKPR Non Berusaha >10000  Ha (<b>'.$getdata['jl17'].'</b>)',
                    ];     
                }   
                
                // echo "<pre>";
                // print_r ($getdata);
                // echo "</pre>";
                
                
                foreach ($arrcol as $key => $value) {
                    array_push($category,$value);
                    $bb = ['y' => intVal($getdata['persiapan_'.$key]),'target' =>$value,'text' =>'Persiapan Pelanggan  ' ];           
                    $aa = ['y' => intVal($getdata['peninjauan_'.$key]), 'target' =>$value,'text' =>'Peninjauan Lapangan '];  
                    $cc = ['y' => intVal($getdata['perumusan_'.$key]), 'target' =>$value,'text' =>'Perumusan PTP '];  
                    array_push($data[0]['data'],$bb);           
                    array_push($data[1]['data'],$aa);     
                    array_push($data[2]['data'],$cc);     
                }
                // array_push($data,$a);
            }

            $ret= ['data' =>$data,'module'=> 'ptpMonitoring','category'=>$category];
            echo json_encode($ret);
        }
        
    }

    function ptpMonitoringInteropLayanan($v='',$kabkot,$tahun='null') {
        $curl = curl_init();
  
        $arr = [
                'v_d_ptp_tnh_timbul' => 'tanah%20timbul',
                'v_d_ptp_stranas' => 'strategis%20nasional',
                'v_d_ptp_non_berusaha' => 'kegiatan%20non%20berusaha',
                'v_d_ptp_berusaha' => 'kegiatan%20berusaha',
                'v_d_ptp_pk_p3t' => 'pemanfaatan%20tanah',
        ];

        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api-interop.atrbpn.go.id/api/internal/dirpgt/signata/data-layanan-ptp?kodekab=".$kabkot."&jenislayanan=".$arr[$v],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => [
                "Accept: */*",
                "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************.SFrpz4MIod1D7tp8XAwaY56m-NeCbKqAWRla-leNbmE",
                "User-Agent: Thunder Client (https://www.thunderclient.com)"
            ],
        ]);

        $response = curl_exec($curl);
        
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            echo "cURL Error #:" . $err;
        } else {
            $response = json_decode($response);
            $datas = $response->data;

            
            // echo "<pre>";
            // print_r ($datas);
            // echo "</pre>";
            
            $data = [];            
            if (!empty($datas)) {
                $jml = 0;
                $luas = 0;
                $a['name']='';
                foreach ($datas as $key => $value) {
                    if ($tahun != 'null' && $tahun != null) {
                        if (strpos($value->nomorberkas, $tahun) !== false) {
                            $jml++;
                            $luas = intVal($luas+$value->luasdimohon);     
                        }
                        
                    }else{
                        $jml++;
                        $luas = intVal($luas+$value->luasdimohon); 
                    }
                }
                $a['y']=$jml;
                $a['luas']=$luas;
                array_push($data,$a);
            }

            $ret= ['data' =>$data,'module'=> 'ptpMonitoringInterop','datas' => $datas];
            echo json_encode($ret);
        }
        
    }


    public function ptpMonitoring($views)
    {
 
        $getdata = $this->db->get($views)->row_array();
        $data=[];
        $data[0]['name']='Persiapan Lapangan';
        $data[0]['data']=[];
        $data[0]['color']='orange';
        $data[0]['stack']='Persiapan';
        $data[1]['name']='Peninjauan Lapangan';
        $data[1]['data']=[];
        $data[1]['color']='#2196f3';
        $data[1]['stack']='Persiapan';
        $data[2]['name']='Perumusan PTP';
        $data[2]['data']=[];
        $data[2]['color']='#3dfc03';
        $data[2]['stack']='Persiapan';
        

        $category=[];
        if(!empty($getdata)){   
            if($views == 'spatial.v_d_monitoring_ptp_non_berusaha'){

                $arrcol = [
                    'kd' => 'PTP PKKPR Non Berusaha <= 1 Ha (<b>'.$getdata['kd'].'</b>)',
                    'ld' => 'PTP PKKPR Non Berusaha > 1 Ha (<b>'.$getdata['ld'].'</b>)',
                ];     
            }elseif($views == 'spatial.v_d_monitoring_ptp_stranas'){
                $arrcol = [
                    'jml' => 'PTP RKKPR/PKKPR Strategis Nasional (<b>'.$getdata['jml'].'</b>)',
                ];   
           
            }elseif($views == 'spatial.v_d_monitoring_ptp_tnh_timbul'){
                $arrcol = [
                    'jml' => 'PTP dalam Rangka Penegasan Status dan Rekomendasi Penguasaan Tanah Timbul (<b>'.$getdata['jml'].'</b>)',
                ];   
            }elseif($views == 'spatial.v_d_monitoring_ptp_pk_p3t'){

                $arrcol = [
                    'kd' => 'PTP Penyelenggaraan Kebijakan Penggunaan dan Pemanfaatan Tanah  <= 1 Ha (<b>'.$getdata['kd'].'</b>)',
                    'ld' => 'PTP Penyelenggaraan Kebijakan Penggunaan dan Pemanfaatan Tanah  > 1 Ha (<b>'.$getdata['ld'].'</b>)',
                ];     
            } elseif($views == 'spatial.v_d_monitoring_ptp_berusaha'){

                $arrcol = [
                    'kd' => 'PTP PKKPR Berusaha <= 1 Ha (<b>'.$getdata['kd'].'</b>)',
                    'j152' => 'PTP PKKPR Non Berusaha >1 - 500  Ha (<b>'.$getdata['j152'].'</b>)',
                    'j5213' => 'PTP PKKPR Non Berusaha >500 - 1000  Ha (<b>'.$getdata['j5213'].'</b>)',
                    'j1315' => 'PTP PKKPR Non Berusaha >1000 - 10000  Ha (<b>'.$getdata['j1315'].'</b>)',
                    'jl17' => 'PTP PKKPR Non Berusaha >10000  Ha (<b>'.$getdata['jl17'].'</b>)',
                ];     
            }   
            

            foreach ($arrcol as $key => $value) {
                array_push($category,$value);
                $bb = ['y' => intVal($getdata['persiapan_'.$key]),'target' =>$value,'text' =>'Persiapan Pelanggan  ' ];           
                $aa = ['y' => intVal($getdata['peninjauan_'.$key]), 'target' =>$value,'text' =>'Peninjauan Lapangan '];  
                $cc = ['y' => intVal($getdata['perumusan_'.$key]), 'target' =>$value,'text' =>'Perumusan PTP '];  
                array_push($data[0]['data'],$bb);           
                array_push($data[1]['data'],$aa);     
                array_push($data[2]['data'],$cc);     
            }
        }
  

        $ret= ['data' =>$data,'module'=> 'ptpMonitoring','category'=>$category];
        echo json_encode($ret);

        
        
    }
    public function ptpold($prov='',$views='',$year='')
    {
        if ($prov == '' || $views =='') {
            exit();
        }
        $years= $this->M_model->getYearProv($views,$prov);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array(@$value->tahun_data,$arryear)){
                array_push($arryear,@$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        $data=[];
        $this->db->where('kdppum', $prov);
        $this->db->where('tahun_data', $year);
        $datas = $this->db->get($views)->result();

    
        
        // echo $this->db->last_query();
        
        
        foreach ($datas as $key => $value) {

            $a['tahun']=@$year;
            $a['name']=@$value->wadmkk;
            $a['kd_prov']=$value->kdpkab;
            $a['layer']=$views;
            $a['y']=floatval($value->jml);
            // $a['drilldown']=@$value->wadmpr;
            array_push($data,$a);
            
        }
       
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'ptp', 'tahun' => $str];

        echo json_encode($ret);
    }

    
    public function drilldownPtp($kd_prov,$layer,$tahun)
    {
        
        $drilldown=[];
        $this->db->where("kdppum",$kd_prov);
        $this->db->where("tahun_data",$tahun);
        $datas = $this->db->get($layer.'_drill')->result();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($datas);
        // echo "</pre>";
        
        $b['kd_prov']=$datas[0]->kdppum;
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
             
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    } 
    public function mpptold($year)
    {
        $years= $this->M_model->getYear('spatial.v_d_mppt');
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        $data=[];
        $this->db->where('tahun_data', $year);
        $datas = $this->db->get('spatial.v_d_mppt')->result();
        foreach ($datas as $key => $value) {

            $a['tahun']=@$year;
            $a['name']=@$value->wadmpr;
            $a['kd_prov']=$value->kdppum;
            // $a['layer']=$views;
            $a['y']=floatval($value->jml);
            $a['drilldown']=@$value->wadmpr;
            array_push($data,$a);
            
        }
       
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'mppt', 'tahun' => $str];

        echo json_encode($ret);
    }

    
    public function drilldownMppt($kd_prov,$tahun)
    {
        // echo $prov.$layer;
        // exit();
        $drilldown=[];
        $this->db->where("kdppum",$kd_prov);
        $this->db->where("tahun_data",$tahun);
        $datas = $this->db->get('spatial.v_d_mppt_drill')->result();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($datas);
        // echo "</pre>";
        
        $b['kd_prov']=@$datas[0]->kdppum;
        $b['name']=@$datas[0]->wadmpr;
        $b['id']=@$datas[0]->wadmpr;
        $b['data']=[];
        if(!empty($datas)){

            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                
            }
        }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    }
    public function tanahNegaraOlds($prov='',$views='',$year='')
    {
        if ($views=='') {
            exit();
        }
 
        
        $years= $this->M_model->getYear($views);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        $data=[];
        $this->db->where('tahun_data', $year);
        $datas = $this->db->get($views)->result();
        foreach ($datas as $key => $value) {

            $a['tahun']=@$year;
            $a['name']=@$value->wadmpr;
            $a['kd_prov']=$value->kdppum;
            $a['layer']=$views;
            $a['y']=floatval($value->jml);
            $a['drilldown']=@$value->wadmpr;
            array_push($data,$a);
            
        }
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }

        $ret= ['data' =>$data,'module'=> 'tanahNegara', 'tahun' => $str];

        echo json_encode($ret);
    }

    
    public function drilldownTanahNegara($kd_prov,$layer,$tahun)
    {
        // echo $prov.$layer;
        // exit();
        $drilldown=[];
        $this->db->where("kdppum",$kd_prov);
        $this->db->where("tahun_data",$tahun);
        $datas = $this->db->get($layer.'_drill')->result();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($datas);
        // echo "</pre>";
        
        $b['kd_prov']=$datas[0]->kdppum;
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
             
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    }

    public function lahanBakuOldS()
    {
        
        $data=[];
        
            $datas = $this->db->get('spatial.v_d_lahanbaku')->result();
            foreach ($datas as $key => $value) {
                // $a=[$prov,intVal($value->jml)];
            $a['layer']='spatial.v_d_lahanbaku';
            $a['name']=$value->wadmpr;
            $a['kd_prov']=$value->kdppum;
            $a['y']=floatval($value->jml);
            $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
            
            }
        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'lahanBaku'];

        echo json_encode($ret);
        
    }

    public function LahanBaku($year='',$prov=null,$kab='null')
    {

     

        $drilldown=[];
        // if ($kd_prov == '') {
        //     exit();
        // }
        // if ($year != 'null' && $year != '' && $year != null   ) {
            
        // }
        if($kab != 'null'){
            $years= $this->M_model->getYearKab('spatial.v_d_lahanbaku',$kab);
        }else{
            $years= $this->M_model->getYearProv('spatial.v_d_lahanbaku',$prov);
        }
        $year=$year=='null'?@$years[0]->tahun_data:$year;
            // echo $year.$prov,$kab;
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        if ($year != 'null') {
            $this->db->where('tahun_data', $year);
        }
        if($kab == 'null'){
            $this->db->where('kdppum', $prov);
        }else{
            $this->db->where('kdpkab', $kab);
        }
        $datas = $this->db->get('spatial.v_d_lahanbaku')->result();
        
        $data=[];
        foreach ($datas as $key => $value) {
            
            
            // $this->db->select('round(luaswh) luaswh');
            // $this->db->where('wadmpr', $value->wadmpr);
            // $this->db->group_by('title');
            
            // $bts = $this->db->get('spatial.batas_admin_indonesia')->row_array()['luaswh'];

            // echo $value->wadmkk.'</br>';
            // echo $value->jml.'/'.$bts.'</br>';
            // echo $persen;
            // echo '</br></br>';
            
            // $a=[$prov,intVal($value->jml)];
            // $persen =  round(($value->jml/$bts)*100,2);
            $a['name']=$value->wadmkk; ;
            $a['nilai']=floatval($value->luas);
            // $value->persen = $value->persen < 1 ? $value->persen+1:$value->persen;
            $a['y']=floatval($value->luas);
            // $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
        }

        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'lahanBaku','tahun' => $str];
            
        echo json_encode($ret);
        exit();
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                    // echo "<pre>";
                    // print_r ($value2);
                    // echo "</pre>";break;
                    
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    }

    public function wp3wt($pxn='null',$views,$year='',$kab)
    {
        // echo $pxn;
        // echo $views;
        // if ($views =='null' || $pxn== 'null') {
        //     // exit();
        //     echo json_encode(['data' =>[['name'=>'','y'=>0]],'module'=> 'wp3wt', 'tahun' => '']);
        //     exit();
        if($views == 'spatial.v_d_wp3wt_ppk_polygon' || $views == 'spatial.v_d_wp3wt_ppkt' || $views == 'spatial.v_d_wp3wt_ppk_point'){
            $years= $this->M_model->getYearKab($views,$kab);
        }else{
            $years= $this->M_model->getYearKab($views.'_'.$pxn,$kab);
        }
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        if ($year != '') {
            $this->db->where('tahun_data', $year);
        }
        // $this->db->select('wadmkc,tahun_data,');
        
        $this->db->where('kdpkab', $kab);
        if($views == 'spatial.v_d_wp3wt_ppk_polygon' || $views == 'spatial.v_d_wp3wt_ppkt' || $views == 'spatial.v_d_wp3wt_ppk_point'){
            $datas = $this->db->get($views)->result();
        }else{
            $this->db->select('wadmkc,luas_ha');
            $this->db->group_by('wadmkc,luas_ha');
            
            $datas = $this->db->get($views.'_'.$pxn)->result();
        }
        // echo $this->db->last_query();
        
        $data=[];
        foreach ($datas as $key => $value) {
            
            $a['name']=$value->wadmkc; 
            if($views == 'spatial.v_d_wp3wt_ppk_point'){
                $a['nilai']=floatval($value->jml);
                // $value->jml = $value->jml < 1 ? $value->jml+1:$value->jml;
                $a['y']=floatval($value->jml);
                $a['jns']=' Point';
                $a['nama_pulau']='';
                $title = 'Point';

            }else{
                // $value->persen = round($value->persen,2);
                $a['nilai']=floatval($value->luas_ha);
                // $value->persen = $value->persen < 1 ? $value->persen+1:$value->persen;
                $a['y']=floatval($value->luas_ha);
                $a['nama_pulau']='';
                $a['jns']=' Ha';
                $title = 'Luasan Ha';

            }
            
            // $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
        }
        

        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'wp3wt','tahun' => $str,'title' =>@$title];
            
        echo json_encode($ret);
        
    }

    public function wp3wtPkkPoly($views,$year='')
    {

        $years= $this->M_model->getYear($views);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        if ($year != '') {
            $this->db->where('tahun_data', $year);
        }
        // $this->db->select('wadmkc,tahun_data,');
        $role=$this->session->users['id_user_group_real'];
        $kabkot=$this->session->users['kd_kabkot'];
        $prov=$this->session->users['kd_prov'];
        // echo $role; die();

        if($role==7 || $role==8) {
            $prov = explode('.',$kabkot)[0];
            $this->db->where('kdppum', $prov);
        }

        if($role==9 || $role==10) {
            $this->db->where('kdppum', $prov);
        }
        $this->db->select('wadmpr,kdppum,sum(jml) as luas');
        $this->db->group_by('wadmpr,kdppum');
        $this->db->order_by('luas', 'desc');
        
        $datas = $this->db->get($views)->result();
            
        
        // echo $this->db->last_query();
        
        $data=[];
        foreach ($datas as $key => $value) {
            
                $a['name']=$value->wadmpr;
                $a['nilai']=floatval($value->luas);
                // $value->jml = $value->jml < 1 ? $value->jml+1:$value->jml;
                $a['y']=floatval($value->luas);
                $a['nama_pulau']='';
                $a['jns']=' Ha';
            
            // $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
        }
        

        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'wp3wt','tahun' => $str,'title'=>'Luas Ha'];
            
        echo json_encode($ret);
        
    }


    public function wp3wtPulau($views='',$year='',$kec='')
    {
        // echo $pxn;
        // echo $views;
        // if ($views =='null' || $pxn== 'null') {
        //     // exit();
        //     echo json_encode(['data' =>[['name'=>'','y'=>0]],'module'=> 'wp3wt', 'tahun' => '']);
        //     exit();

        $kec = str_replace('%20',' ',$kec);
        if($views == 'spatial.v_d_wp3wt_ppk_polygon' || $views == 'spatial.v_d_wp3wt_ppkt' || $views == 'spatial.v_d_wp3wt_ppk_point'){
            $where =['wadmkc' => $kec];
            $years= $this->M_model->getYearWhere($views,$where);
        }else{
            $where =['wadmkc' => $kec];
            $years= $this->M_model->getYearWhere($views.'_'.$pxn,$where);
        }
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }
        
        // echo "<pre>";
        // print_r ($arryear);
        // echo "</pre>";
        

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        if($year == 'null' && $year == '' && $year == null){
            exit();
        }
        if ($year != '') {
            $this->db->where('tahun_data', $year);
        }
        // $this->db->select('wadmkc,tahun_data,');
        
        $this->db->where('wadmkc', $kec);
        if($views == 'spatial.v_d_wp3wt_ppk_polygon' || $views == 'spatial.v_d_wp3wt_ppkt' || $views == 'spatial.v_d_wp3wt_ppk_point'){
            $datas = $this->db->get($views)->result();
        }else{
            $this->db->select('wadmkc,luas_ha');
            $this->db->group_by('wadmkc,luas_ha');
            
            $datas = $this->db->get($views.'_'.$pxn)->result();
        }
        // echo $this->db->last_query();
        
        $data=[];
        // foreach ($datas as $key => $value) {
            
            $a['name']=$datas[0]->wadmkc; 
            // if($views == 'spatial.v_d_wp3wt_ppk_point' || $views == 'spatial.v_d_wp3wt_ppkt'){
                $a['nilai']=floatval(count($datas));
                // $value->jml = $value->jml < 1 ? $value->jml+1:$value->jml;
                $a['y']=floatval(count($datas));
                $a['jns']=' Point';
                $str='List Pulau :</br>';
                foreach ($datas as $key => $value) {
                    $str .= '- '.$value->nama_pulau.'</br>';
                }
                $a['nama_pulau']=$str;
                
            // }else{
                // $value->persen = round($value->persen,2);
                // $a['nilai']=floatval($value->luas_ha);
                // $value->persen = $value->persen < 1 ? $value->persen+1:$value->persen;
                // $a['y']=floatval($value->luas_ha);
                // $a['jns']=' Ha';
            // }
        
            // $a['drilldown']=$value->wadmpr;
            array_push($data,$a);
        // }
        

        
        if($str == ''){
            $str='<option value="null">Tahun Pengerjaan</option>';

        }
        $ret= ['data' =>$data,'module'=> 'wp3wt','tahun' => $str,'title' => 'Luas Ha'];
            
        echo json_encode($ret);
        
    }

    public function drilldownLahanBaku($kd_prov,$views)
    {
        $drilldown=[];
        $this->db->where('kdppum', $kd_prov);
        $datas = $this->db->get($views.'_drill')->result();
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->wadmkk,floatval($value2->jml)];
                
                array_push($b['data'],$c);
                    // echo "<pre>";
                    // print_r ($value2);
                    // echo "</pre>";break;
                    
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    }
    public function kemampuanTanah($kab='',$year=null)
    {
        // $years= $this->M_model->getYear('spatial.v_d_kt_'.$views);
        // $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $data=[];
        $arrcol = [
            'Kemiringan Lereng' => 'b_name',
            'Kedalaman Efektif' => 'u_name',
            'Tekstur Tanah' => 'x_name',
            'Erosi' => 'e_name',
            'Drainase' => 'd_name',
        ];
        
        $arrViews = [
            'b_name' => 'spatial.v_d_kt_b_name',
            'u_name' => 'spatial.v_d_kt_u_name',
            'x_name' => 'spatial.v_d_kt_x_name',
            'e_name' => 'spatial.v_d_kt_e_name',
            'd_name' => 'spatial.v_d_kt_d_name'
        ];
        $year = $year=='null' ? null : $year;
        foreach ($arrcol as $key => $value) {
            if ($year != '') {
                $this->db->where('tahun_data', $year);
            }
            $this->db->where('kdpkab', $kab);
            $datas = $this->db->get($arrViews[$value])->row_array();
            // echo $this->db->last_query();
            
            // echo "<pre>";
            // print_r ($datas);
            // echo "</pre>";
            // exit();
    
                        
            
            $a['name']=@$key;
            // $a['layer']=$views;
            $a['y']=floatval(@$datas['luas']);
            array_push($data,$a);
            
        }
        // exit();
                
        // // echo "<pre>";
        // // print_r ($datas);
        // // echo "</pre>";exit();
        
        // foreach ($datas as $key => $value) {

        //     $a['name']=@$value->wadmpr;
        //     $a['kd_prov']=$value->kdppum;
        //     $a['tahun']=@$year;
        //     // $a['layer']=$views;
        //     $a['y']=floatval(@$value->jml);
        //     $a['drilldown']=@$value->wadmpr;
        //     array_push($data,$a);
            
        // }
       

        $ret= ['data' =>$data,'module'=> 'kemampuanTanah'];

        echo json_encode($ret);
    }
    public function kemampuanTanahNew($views='',$year)
    {
        $years= $this->M_model->getYear('spatial.v_d_kt_'.$views);
        $year=$year=='null'?@$years[0]->tahun_data:$year;
        
        $str='';
        $arryear=[];
        foreach ($years as $key => $value) {
            $sel = intVal($year==$value->tahun_data)?'Selected':'';
            if (!in_array($value->tahun_data,$arryear)){
                array_push($arryear,$value->tahun_data);
            }
            
            $str .='<option '.$sel.' value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
        }

        if (!in_array($year,$arryear)){
            $year=@$years[0]->tahun_data;
        }
        
        $data=[];
        $datas = $this->db->get('spatial.v_d_kt_'.$views)->result();
        
        // echo "<pre>";
        // print_r ($datas);
        // echo "</pre>";exit();
        
        foreach ($datas as $key => $value) {

            $a['name']=@$value->wadmpr;
            $a['kd_prov']=$value->kdppum;
            $a['tahun']=@$year;
            // $a['layer']=$views;
            $a['y']=floatval(@$value->jml);
            $a['drilldown']=@$value->wadmpr;
            array_push($data,$a);
            
        }
       

        $ret= ['data' =>$data,'module'=> 'kemampuanTanah'];

        echo json_encode($ret);
    }

    
    public function drilldownKemampuanTanah($kd_prov=null,$jenis=null,$year=null)
    {
        $drilldown=[];
        // echo $kd_prov;
        // echo $jenis;
        // exit();
        // $colom
        $year = $year == 'null'? null : $year;
        $this->db->where("kdppum",$kd_prov);
        $this->db->where("tahun_data",$year);
        $datas = $this->db->get('spatial.v_d_kt_'.$jenis.'_drill')->result();
        // echo $this->db->last_query();
        
 
        
        $b['name']=$datas[0]->wadmpr;
        $b['id']=$datas[0]->wadmpr;
        $b['data']=[];
            foreach ($datas as $key2 => $value2) {
                $c=[$value2->nama,floatval($value2->jml)];
                
                array_push($b['data'],$c);
             
            }
        array_push($drilldown,$b);
        
        echo json_encode($drilldown[0]);
    }

    public function getFilterProv($views="")
    {
        $role=$this->session->users['id_user_group_real'];
        $kabkot=$this->session->users['kd_kabkot'];
        $prov=$this->session->users['kd_prov'];
        // echo $role; die();

        if($role==7 || $role==8) {
            $prov = explode('.',$kabkot)[0];
            $this->db->where('kdppum', $prov);
        }

        if($role==9 || $role==10) {
            $this->db->where('kdppum', $prov);
        }
        $this->db->select('kdppum,wadmpr');
        $this->db->group_by('kdppum,wadmpr');
        $this->db->order_by('wadmpr', 'asc');
        $years = $this->db->get($views)->result();

        
        $strProv='';
        $strYear='';
        $arrYear=[];
        if (!empty($years)) {
            
            foreach ($years as $k => $v) {
                $strProv .= '<option value="'.$v->kdppum.'">'.$v->wadmpr.'</option>';
            }
            
        
            
            $this->db->select('tahun_data');
            // $this->db->where('kdppum', $years[0]->kdppum);
            // $this->db->where('kdppum', $years[0]->kdppum);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data');
            $this->db->order_by('tahun_data', 'desc');
            $datas = $this->db->get($views)->result();
            // echo $this->db->last_query();
            
            // echo "<pre>";
            // print_r ($datas);
            // echo "</pre>";
            
            foreach ($datas as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            
            echo json_encode(['sts'=>'success','prov'=>$strProv,'tahun'=>$strYear]);
                        
        }else{
            
            echo json_encode(['sts'=>'null','prov'=>'<option>Tidak Ada Data</option>','tahun'=>'<option value="null">Tahun Pengerjaan</option>']);
                        
        }
    }

    public function getFilterProvWp3wt($views="")
    {
        $role=$this->session->users['id_user_group_real'];
        $kabkot=$this->session->users['kd_kabkot'];
        $prov=$this->session->users['kd_prov'];
        // echo $role; die();

        if($role==7 || $role==8) {
            $prov = explode('.',$kabkot)[0];
            $this->db->where('kdppum', $prov);
        }

        if($role==9 || $role==10) {
            $this->db->where('kdppum', $prov);
        }
        $this->db->select('kdppum,wadmpr');
        $this->db->group_by('kdppum,wadmpr');
        $this->db->order_by('wadmpr', 'asc');
        $years = $this->db->get($views)->result();

        
        $strProv='';
        $strYear='';
        $arrYear=[];
        if (!empty($years)) {
            
            foreach ($years as $k => $v) {
                $strProv .= '<option value="'.$v->kdppum.'">'.$v->wadmpr.'</option>';
            }
            
        
            
            $this->db->select('tahun_data');
            // $this->db->where('kdppum', $years[0]->kdppum);
            // $this->db->where('kdppum', $years[0]->kdppum);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data');
            $this->db->order_by('tahun_data', 'desc');
            $datas = $this->db->get($views)->result();
            // echo $this->db->last_query();
            
            // echo "<pre>";
            // print_r ($datas);
            // echo "</pre>";
            
            foreach ($datas as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            
            echo json_encode(['sts'=>'success','prov'=>$strProv,'tahun'=>$strYear]);
                        
        }else{
            
            echo json_encode(['sts'=>'null','prov'=>'<option>Tidak Ada Data</option>','tahun'=>'<option value="null">Tahun Pengerjaan</option>']);
                        
        }
    }
    public function getFilterProvPgtl($views = null)
    {
        $role=$this->session->users['id_user_group_real'];
        $kabkot=$this->session->users['kd_kabkot'];
        $prov=$this->session->users['kd_prov'];
        // echo $role; die();

        if($role==7 || $role==8) {
            $prov = explode('.',$kabkot)[0];
            $this->db->where('kdppum', $prov);
        }

        if($role==9 || $role==10) {
            $this->db->where('kdppum', $prov);
        }
        
        $this->db->select('kdppum,wadmpr');
        $this->db->group_by('kdppum,wadmpr');
        $this->db->order_by('wadmpr', 'asc');
        $years = $this->db->get($views)->result();
        // echo $this->db->last_query();

        
        $strProv='';
        $strYear='';
        $arrYear=[];
        if (!empty($years)) {
            
            foreach ($years as $k => $v) {
                $strProv .= '<option value="'.$v->kdppum.'">'.$v->wadmpr.'</option>';
            }
            
        
            
            $this->db->select('tahun_data');
            $this->db->where('kdppum', $years[0]->kdppum);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data');
            $this->db->order_by('tahun_data', 'desc');
            $datas = $this->db->get($views)->result();


            
            // echo "<pre>";
            // print_r ($datas);
            // echo "</pre>";
            
            foreach ($datas as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            
            echo json_encode(['sts'=>'success','prov'=>$strProv,'tahun'=>$strYear,'kabSelect' => @$years[0]->kdppum ]);
                        
        }else{
            
            echo json_encode(['sts'=>'null']);
                        
        }
    }

    public function getFilterKab($views = null,$prov=null)
    {
        $role=$this->session->users['id_user_group_real'];
        // echo $role; die();

        if($role==7 && $role == 8) {
            $kabkot=$this->session->users['kd_kabkot'];
            $prov = explode('.',$kabkot)[0];
            $this->db->where('kdpkab', $kabkot);
        }
        // echo $prov;
        $this->db->select('wadmkk,kdpkab');
        $this->db->where('kdppum', $prov);
        $this->db->group_by('wadmkk,kdpkab');
        $this->db->order_by('wadmkk', 'asc');
        $datas = $this->db->get($views)->result();
        
        //  echo $this->db->last_query();
        $strKab='';
        $strYear='';
        $arrYear=[];
        if (!empty($datas)) {
            
            if($role!==7 || $role!==8) {
                foreach ($datas as $k => $v) {
                    $strKab .= '<option value="'.$v->kdpkab.'">'.$v->wadmkk.'</option>';
                }
                $kdpkab = $datas[0]->kdpkab;
            } else {
                $kdpkab=$kdpkab;
            }
        
            // echo $kdpkab;
            
            $this->db->select('tahun_data');
            $this->db->where('kdpkab', $kdpkab);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data');
            $this->db->order_by('tahun_data', 'desc');
            $year = $this->db->get($views)->result();
            
          
            foreach ($year as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            
            
            echo json_encode(['sts'=>'success','kab'=>$strKab,'tahun'=>$strYear]);
                        
        }else{
            $strKab='<option>Tidak Ada Data</option>';
            $strYear='<option value="null">Tahun Pengerjaan</option>';
            echo json_encode(['sts'=>'null','kab'=>$strKab,'tahun'=>$strYear]);
                        
        }
    }

    public function getFilterKabWp3wt($views = null,$prov=null)
    {
        $role=$this->session->users['id_user_group_real'];
        // echo $role; die();

        if($role==7) {
            $kabkot=$this->session->users['kd_kabkot'];
            $prov = explode('.',$kabkot)[0];
            $this->db->where('kdpkab', $kabkot);
        }
        // echo $prov;
        $this->db->select('wadmkk,kdpkab');
        $this->db->where('kdppum', $prov);
        $this->db->group_by('wadmkk,kdpkab');
        $this->db->order_by('wadmkk', 'asc');
        $datas = $this->db->get($views)->result();
        
        //  echo $this->db->last_query();
        $strKab='';
        $strYear='';
        $arrYear=[];
        if (!empty($datas)) {
            
            if($role!==7 || $role!==8) {
                foreach ($datas as $k => $v) {
                    $strKab .= '<option value="'.$v->kdpkab.'">'.$v->wadmkk.'</option>';
                }
                $kdpkab = $datas[0]->kdpkab;
            } else {
                $kdpkab=$kdpkab;
            }
        
            // echo $kdpkab;
            
            $this->db->select('tahun_data');
            $this->db->where('kdpkab', $kdpkab);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data');
            $this->db->order_by('tahun_data', 'desc');
            $year = $this->db->get($views)->result();
            
          
            foreach ($year as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            
            
            echo json_encode(['sts'=>'success','kab'=>$strKab,'tahun'=>$strYear]);
                        
        }else{
            $strKab='<option>Tidak Ada Data</option>';
            $strYear='<option value="null">Tahun Pengerjaan</option>';
            echo json_encode(['sts'=>'null','kab'=>$strKab,'tahun'=>$strYear]);
                        
        }
    }
    
    public function getFilterKabLbs($views = null,$prov)
    {
        $this->db->select('tahun_data,wadmkk,kdpkab');
        $this->db->where('kdppum', $prov);
        $this->db->group_by('tahun_data,wadmkk,kdpkab');
        $this->db->order_by('wadmkk', 'asc');
        $datas = $this->db->get($views)->result();
        // echo $this->db->last_query();

        
        $strKab='<option value="null">Semua Kabupaten</option>';
        $strYear='';
        $arrYear=[];
        if (!empty($datas)) {
            
            foreach ($datas as $k => $v) {
                $strKab .= '<option value="'.$v->kdpkab.'">'.$v->wadmkk.'</option>';
            }
            
        
            
            $this->db->select('tahun_data');
            $this->db->where('kdpkab', $datas[0]->kdpkab);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data');
            $this->db->order_by('tahun_data', 'desc');
            $year = $this->db->get($views)->result();
            
            // echo "<pre>";
            // print_r ($year);
            // echo "</pre>";
            
            foreach ($year as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            
            echo json_encode(['sts'=>'success','kab'=>$strKab,'tahun'=>$strYear]);
                        
        }else{
            $strKab='<option>Dalam Pengerjaan</option>';
            $strYear='<option value="null">Tahun Pengerjaan</option>';
            echo json_encode(['sts'=>'null','kab'=>$strKab,'tahun'=>$strYear]);
                        
        }
    }
    
    public function getFilterKabPgtl($views = null,$prov)
    {
        $this->db->select('tahun_data,wadmkk,kdpkab');
        $this->db->where('kdppum', $prov);
        $this->db->group_by('tahun_data,wadmkk,kdpkab');
        $this->db->order_by('wadmkk', 'asc');
        $datas = $this->db->get($views)->result();
        // echo $this->db->last_query();

        
        $strKab='<option value="null">Semua Kabupaten</option>';
        $strYear='';
        $arrYear=[];
        if (!empty($datas)) {
            
            foreach ($datas as $k => $v) {
                $strKab .= '<option value="'.$v->kdpkab.'">'.$v->wadmkk.'</option>';
            }
            
        
            
            $this->db->select('tahun_data');
            $this->db->where('kdpkab', $datas[0]->kdpkab);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data');
            $this->db->order_by('tahun_data', 'desc');
            $year = $this->db->get($views)->result();
            
            // echo "<pre>";
            // print_r ($year);
            // echo "</pre>";
            
            foreach ($year as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            
            echo json_encode(['sts'=>'success','kab'=>$strKab,'tahun'=>$strYear]);
                        
        }else{
            $strKab='<option>Dalam Pengerjaan</option>';
            $strYear='<option value="null">Tahun Pengerjaan</option>';
            echo json_encode(['sts'=>'null','kab'=>$strKab,'tahun'=>$strYear,'kabSelect' => @$datas[0]->kdpkab]);
                        
        }
    }
    public function getFilterKec($views = null,$kab)
    {
        $this->db->select('namobj,kdpkab');
        $this->db->where('kdpkab', $kab);
        $this->db->group_by('namobj,kdpkab');
        $this->db->order_by('namobj', 'asc');
        $datas = $this->db->get($views)->result();
        $strKec='';
        $strYear='';
        $arrYear=[];
        if (!empty($datas)) {
            
            foreach ($datas as $k => $v) {
                $strKec .= '<option value="'.$v->namobj.'">'.$v->namobj.'</option>';
            }
            
        
            
            $this->db->select('tahun_data');
            $this->db->where('namobj', $datas[0]->namobj);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data,namobj');
            $this->db->order_by('tahun_data', 'desc');
            $year = $this->db->get($views)->result();
            // echo $this->db->last_query();
            
            // echo "<pre>";
            // print_r ($year);
            // echo "</pre>";
            
            foreach ($year as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            if($strYear == ''){
                $strYear='<option value="null">Tahun Pengerjaan</option>';

            }
          
            
            echo json_encode(['sts'=>'success','kec'=>$strKec,'tahun'=>$strYear]);
                        
        }else{
            $strKec='<option>Tidak Ada Data</option>';
            $strYear='<option value="null">Tahun Pengerjaan</option>';
            echo json_encode(['sts'=>'null','kab'=>$strKec,'tahun'=>$strYear]);
                        
        }
    }
    public function getFilterKecNpgt($views = null,$kab)
    {
        $this->db->select('wadmkc,kdpkab');
        $this->db->where('kdpkab', $kab);
        $this->db->group_by('wadmkc,kdpkab');
        $this->db->order_by('wadmkc', 'asc');
        $datas = $this->db->get($views)->result();
   
        
        $strKec='';
        $strYear='';
        $arrYear=[];
        if (!empty($datas)) {
            
            foreach ($datas as $k => $v) {
                $strKec .= '<option value="'.$v->wadmkc.'">'.$v->wadmkc.'</option>';
            }
            
        
            
            $this->db->select('tahun_data');
            $this->db->where('wadmkc', $datas[0]->wadmkc);
            $this->db->where('tahun_data is not null');
            $this->db->group_by('tahun_data,wadmkc');
            $this->db->order_by('tahun_data', 'desc');
            $year = $this->db->get($views)->result();
            // echo $this->db->last_query();
            
            // echo "<pre>";
            // print_r ($year);
            // echo "</pre>";
            
            foreach ($year as $key => $value) {
                $strYear .= '<option value="'.$value->tahun_data.'">'.$value->tahun_data.'</option>';
            }
            
            echo json_encode(['sts'=>'success','kec'=>$strKec,'tahun'=>$strYear]);
                        
        }else{
            $strKec='<option>Tidak Ada Data</option>';
            $strYear='<option value="null">Tahun Pengerjaan</option>';
            echo json_encode(['sts'=>'null','kab'=>$strKec,'tahun'=>$strYear]);
                        
        }
    }

    
    public function getProvInterop()
    {
         $role=$this->session->users['id_user_group_real'];
         if($role == 7 || $role == 8) {
            $kdpkab=$this->session->users['kd_kabkot'];
            $kdppum = substr($kdpkab, 0, 2);
            $this->db->where('kode_prov', $kdppum);
        }else if($role == 9 || $role == 10){
            $kdppum=$this->session->users['kd_prov'];
            $this->db->where('kode_prov', $kdppum);

         }
            // echo $kdpkab=$this->session->users['kd_kabkot'];exit();
            

        $this->db->where("kode_provinsi != '00'");
        $datas = $this->db->get('r_kanwil')->result();
        $str='<option  value="">Pilih Provinsi</option>';
        foreach ($datas as $key => $value) {
            $str .= '<option  value="'.$value->kode_provinsi.'">'.str_replace('Kantor Wilayah Provinsi ','',$value->nama_kantor).'</option>';
        }
        echo json_encode($str);
        
    }

    public function getKabkotInterop($prov=00)
    {
         $role=$this->session->users['id_user_group_real'];
         if($role == 7 || $role == 8) {
            $kdpkab=str_replace('.','',$this->session->users['kd_kabkot']);
            $this->db->where('kode_kab_kota', $kdpkab);
        }
        $this->db->where("kode_provinsi ",$prov);
        $datas = $this->db->get('r_wilkantah')->result();
        $str='<option  value="">Pilih Kabupaten/Kota</option>';
        foreach ($datas as $key => $value) {
            $str .= '<option  value="'.$value->kode_kantor.'">'.str_replace('Kantor Pertanahan Kota ','',str_replace('Kantor Pertanahan Kabupaten ','',$value->nama_kantor)).'</option>';
        }
        echo json_encode($str);
        
    }

    function login($var) : Returntype {
        redirect(base_url('login/logout'));
    }
    
}