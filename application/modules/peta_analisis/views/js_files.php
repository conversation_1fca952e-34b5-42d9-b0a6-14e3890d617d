<!-- <script src="https://unpkg.com/leaflet@1.6.0/dist/leaflet.js" integrity="sha512-gZwIG9x3wUXg2hdXF6+rVkLF/0Vi9U8D2Ntg4Ga5I5BZpVkVxlJWbSQtXPSiUTtC0TjtGOmxa1AJPuV0CPthew==" crossorigin=""></script> -->
<!-- <script src="<? //echo base_url();?>assets/js/leaflet-panel-layers-master/src/leaflet-panel-layers.js"></script> -->
<!-- <script src="<? //echo base_url();?>assets/js/leaflet-panel-layers-master/src/leflet-panel.js"></script> -->
<!-- <script src="https://code.jquery.com/jquery-3.4.1.min.js" integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous"></script> -->



<!-- <script src="<?php //echo base_url(); ?>assets/peta/select2/select2.min.js"></script> -->
<!-- <script type="text/javascript" src="<?php// echo base_url(); ?>assets/js/jquery-3.4.1.min.js"></script> -->
<!-- <script src="<?//=base_url()?>assets/peta/cropper/cropper.min.js"></script>
<script src="<?//=base_url()?>assets/peta/cropper/popper.min.js"></script> -->
<!-- <script src="<?php //echo base_url(); ?>assets/peta/domtoimage/dom-to-image.min.js"></script> -->

<!-- <script src="<?//=base_url()?>assets/peta/leafletbigimage/Leaflet.BigImage.min.js"></script> -->
<!-- <script type="text/javascript" src="<?php //echo base_url(); ?>node_modules/leaflet.bigimage/dist/Leaflet.BigImage.min.js"></script> -->
<script type="text/javascript"
    src="<?=base_url();?>assets/themes/adminity/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>

<script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/leaflet/leaflet.js"></script>






<!-- <script src="<?php //echo base_url(); ?>assets/peta/geoman/leaflet-geoman.min.js"></script>   -->
<script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/leaflet-draw/js/L.Control.ZoomBox.min.js">
</script>
<script>
var zoombox = L.control.zoomBox()
</script>
<!-- <script type="text/javascript" src="<?php //echo base_url(); ?>assets/peta/leaflet-draw/js/leaflet.draw.js"></script> -->



<!-- <link rel="stylesheet" href="<?php //echo base_url(); ?>node_modules/leaflet.bigimage/dist/Leaflet.BigImage.min.js" /> -->



<!-- <script>

</script> -->
<script src="https://unpkg.com/geojson-vt@3.2.0/geojson-vt.js"></script>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/js/leaflet-geojson-vt.js"></script>

<script src="<? echo base_url();?>assets/js/pgt-maps/leaflet-src.js"></script>
<script src="<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.js"></script>
<script src="<?php echo base_url(); ?>assets/peta/mapbox/leaflet-mapbox-gl.js"></script>

<script src="<? echo base_url();?>node_modules/leaflet-control-geocoder/dist/Control.Geocoder.js"></script>


<script type="text/javascript" src="<?=base_url();?>node_modules/leaflet-easyprint/dist/bundle.analisis.js"></script>
<script type="text/javascript" src="<?=base_url();?>node_modules/dom-to-image/src/dom-to-image.js"></script>
<script type="text/javascript" src="<?=base_url();?>node_modules/file-saver/FileSaver.js"></script>


<script src="<? echo base_url();?>assets/js/pgt-maps/L.Control.Layers.Tree.js"></script>

<script src="<? echo base_url();?>assets/js/L.TileLayer.BetterWMS.js"></script>
<script src="<? echo base_url();?>assets/peta/leaflet-betterscale-master/L.Control.BetterScale.js"></script>


<script src="https://unpkg.com/@mapbox/geojsonhint@latest/geojsonhint.js"></script>

<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script> -->


<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/dom-to-image/2.6.0/dom-to-image.min.js"></script> -->

<!-- <script src="<? //echo base_url();?>assets/peta/libs/leaflet-betterwms/leaflet-betterwms_wgi.js"></script> -->
<!-- <script src="<? //echo base_url();?>assets/peta/libs/leaflet-sidebar-wgi/leaflet-sidebar2.js"></script> -->


<style>
.checkPeta {
    background-color: transparent;
}
</style>


<!-- <script src="<?php //echo base_url(); ?>assets/js/app.js"></script> -->

<!-- GEOJSON DATA -->
<!-- <script src="data/bar.js"></script>
<script src="data/drinking_water.js"></script> -->

<script>
var gl = L.mapboxGL({
    style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
})
var map2 = L.map('map2', {
        zoom: 5,
        center: L.latLng([-1.736, 119.246]),
        attributionControl: false
    }),
    osmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');
map2.addLayer(osmLayer);

// var map = L.map('map', {
//         zoom: 5,
//         center: L.latLng([-1.736, 119.246]),
//         attributionControl: false
//     }),
//     osmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');
// map.addLayer(osmLayer);

// var p1 = new L.TileLayer('https://petadasar.atrbpn.go.id/main/wms/{x}/{y}/{z}');
// var p2 = new L.TileLayer('https://petadasar.atrbpn.go.id/main/tmi/{x}/{y}/{z}');
var map = L.map('map', {
        zoom: 5,
        center: L.latLng([-1.736, 119.246]),
        attributionControl: false
    }),
    osmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');
map.addLayer(osmLayer);

// map.addLayer(p2);
var geomJson = $('#geom_json').val()
console.log(geomJson)
geomJson = JSON.parse(geomJson)
var polygonLayer = L.geoJSON(geomJson, {
    style: function(feature) {
        return {
            fillColor: 'transparent', // Transparent fill color
            fillOpacity: 0, // Fully transparent
            opacity: 0, // Fully transparent
            color: 'rgb(227, 76, 230)' // Border color in RGB (227, 76, 230)
        };
    }
}).addTo(map);
// Fit the map to the bounds of the polygon
map.fitBounds(polygonLayer.getBounds());
polygonLayer.bringToFront()



var geocoder = L.Control.Geocoder.nominatim();
if (URLSearchParams && location.search) {
    // parse /?geocoder=nominatim from URL
    var params = new URLSearchParams(location.search);
    var geocoderString = params.get('geocoder');
    if (geocoderString && L.Control.Geocoder[geocoderString]) {
        console.log('Using geocoder', geocoderString);
        geocoder = L.Control.Geocoder[geocoderString]();
    } else if (geocoderString) {
        console.warn('Unsupported geocoder', geocoderString);
    }
}
var searchcontrol = L.Control.geocoder({
    geocoder: geocoder,
    position: 'topleft'
}).addTo(map);
var marker;

var kotak = {
    width: 600,
    height: 500,
    className: 'customSize',
    name: 'Kotak'
};

var printer = L.easyPrint({
    tileLayer: osmLayer,
    filename: 'cropmap',
    exportOnly: true,
    sizeModes: [kotak],
    hideControlContainer: true
}).addTo(map);
var tombol_upload = L.control({
    position: 'topleft'
});
tombol_upload.onAdd = function(map) {
    var buttonDiv = L.DomUtil.create('div', 'custom-button');
    buttonDiv.innerHTML =
        '<button type="" id="capture-button" onclick="manualPrint()" class=""><svg fill="#000000" height="20px" width="16px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64" enable-background="new 0 0 64 64" xml:space="preserve"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="Printer"> <path d="M57.7881012,14.03125H52.5v-8.0625c0-2.2091999-1.7909012-4-4-4h-33c-2.2091999,0-4,1.7908001-4,4v8.0625H6.2119002 C2.7871001,14.03125,0,16.8183498,0,20.2431507V46.513649c0,3.4248009,2.7871001,6.2119026,6.2119002,6.2119026h2.3798995 c0.5527,0,1-0.4472008,1-1c0-0.5527-0.4473-1-1-1H6.2119002C3.8896,50.7255516,2,48.8359489,2,46.513649V20.2431507 c0-2.3223,1.8896-4.2119007,4.2119002-4.2119007h51.5762024C60.1102982,16.03125,62,17.9208508,62,20.2431507V46.513649 c0,2.3223-1.8897018,4.2119026-4.2118988,4.2119026H56c-0.5527992,0-1,0.4473-1,1c0,0.5527992,0.4472008,1,1,1h1.7881012 C61.2128983,52.7255516,64,49.9384499,64,46.513649V20.2431507C64,16.8183498,61.2128983,14.03125,57.7881012,14.03125z M13.5,5.96875c0-1.1027999,0.8971996-2,2-2h33c1.1027985,0,2,0.8972001,2,2v8h-37V5.96875z"></path> <path d="M44,45.0322495H20c-0.5517998,0-0.9990005,0.4472008-0.9990005,0.9990005S19.4482002,47.0302505,20,47.0302505h24 c0.5517006,0,0.9990005-0.4472008,0.9990005-0.9990005S44.5517006,45.0322495,44,45.0322495z"></path> <path d="M44,52.0322495H20c-0.5517998,0-0.9990005,0.4472008-0.9990005,0.9990005S19.4482002,54.0302505,20,54.0302505h24 c0.5517006,0,0.9990005-0.4472008,0.9990005-0.9990005S44.5517006,52.0322495,44,52.0322495z"></path> <circle cx="7.9590998" cy="21.8405495" r="2"></circle> <circle cx="14.2856998" cy="21.8405495" r="2"></circle> <circle cx="20.6121998" cy="21.8405495" r="2"></circle> <path d="M11,62.03125h42v-26H11V62.03125z M13.4036999,38.4349518h37.1925964v21.1925964H13.4036999V38.4349518z"></path> </g> </g></svg></button>';
    return buttonDiv;
};
// tombol_upload.addTo(map);
function manualPrint() {
    printer.printMap('customSize', 'MyManualPrint')
}

// captures()


var baseURL = "<?php echo base_url(); ?>";
//load maps	


var thnData = '1=1';

// var topPane = map.createPane('leaflet-top-pane', map.getPanes().mapPane);
// var bottomPane = map.createPane('leaflet-bottom-pane', map.getPanes().tilePane);

// var ptp_p3t = L.Geoserver.wms("http://103.6.53.254:4980/geoserver/pgt/wms", {
//   layers: "pgt:ptp_p3t"
// });
// var prov = L.control();
// prov.onAdd = function(map) {
//     var div = L.DomUtil.create('div', 'prov');
//     div.innerHTML = '<select onchange="prov_filter(this)"><option value="">Provinsi</option></select>';
//     div.firstChild.onmousedown = div.firstChild.ondblclick = L.DomEvent.disableClickPropagation(div);
//     return div;
// };
// prov.addTo(map);

// var kabkot = L.control();
// kabkot.onAdd = function(map) {
//     var div = L.DomUtil.create('div', 'kabkot');
//     div.innerHTML = '<select onchange="kabkot_filter(this)"><option value="">Kab./Kota</option></select>';
//     div.firstChild.onmousedown = div.firstChild.ondblclick = L.DomEvent.disableClickPropagation(div);
//     return div;
// };
// kabkot.addTo(map);



var mapSelectTop = L.control();
mapSelectTop.onAdd = function(map) {
    var div = L.DomUtil.create('div', 'tahun');
    div.innerHTML =
        '<select id="provSelect" style="width:200px;border-radius:5px;padding-left:10px" onchange="provKabFilter(this)" class="bootstrap-select form-control"><option id="kabSelect"  value=""> Semua Provinsi</option></select>' +
        '&nbsp;<select id="kabSelect" style="width:200px;border-radius:5px;padding-left:10px" class="bootstrap-select form-control" data-live-search="true" onchange="provKabFilter(this)"><option value=""> Semua Kabupaten/Kota</option></select>' +
        '&nbsp;<select id="thnSelect" style="width:170px;border-radius:5px;padding-left:10px" class="bootstrap-select form-control" data-live-search="true" onchange="provKabFilter(this)"></select>';
    //  '<select id="provSelect"  onchange="prov_filter(this)"><option id="kabSelect"  value="">Provinsi</option></select>&nbsp;<select id="kabSelect" class="bootstrap-select" data-live-search="true" onchange="kabkot_filter(this)"><option value="">Kabupaten/Kota</option></select>&nbsp;<select id="thnSelect" class="bootstrap-select" data-live-search="true" onchange="thn_filter(this)"></select>';
    div.firstChild.onmousedown = div.firstChild.ondblclick = L.DomEvent.disableClickPropagation(div);
    return div;
};
mapSelectTop.addTo(map);

// p1.addTo(map);



// var info = L.control({position: 'topleft'});
// info.onAdd = function (map) {
//     var div = L.DomUtil.create('div', 'informasi'); // create a div with a class "info"
// 	div.innerHTML = '<table border="1"><tr><td>xxx</td><td>xx</td></tr></table>';
// 	div.firstChild.onmousedown = div.firstChild.ondblclick =  L.DomEvent.disableClickPropagation(div);
//     return div;
// };

// info.addTo(map);

// function getComboA(el) {
//   var value = el.value;  

// //   console.log(value);
//   thn_filter(value);
// }

function setFilter() {
    var prov = $('#provSelect').val();
    var kab = $('#kabSelect').val();
    var thn = $('#thnSelect').val();
    if (prov != '') {
        thnData = 'kdppum= ' + prov;
    } else {
        thnData = '1=1';

    }
    if (prov != '' && kab != '') {
        thnData += ' and kdpkab = ' + kab
    }
    if (prov != '' && thn != null && thn != '') {
        thnData += ' and tahun_data= ' + thn;
    } else if (prov == '' && thn != null && thn != '') {
        thnData = ' tahun_data= ' + thn;
    }
}

function provKabFilter(el) {
    console.log('change')
    var prov = $('#provSelect').val();
    var kab = $('#kabSelect').val();
    var thn = $('#thnSelect').val();
    if (prov != '') {
        thnData = 'kdppum= ' + prov;
    } else {
        thnData = '1=1';

    }
    if (prov != '' && kab != '') {
        thnData += ' and kdpkab = ' + kab
    }
    if (prov != '' && thn != null && thn != '') {
        thnData += ' and tahun_data= ' + thn;
    } else if (prov == '' && thn != null && thn != '') {
        thnData = ' tahun_data= ' + thn;
    }
    // removeLayer()
    if (map.hasLayer(tanahnegara_prov_tnbh)) {
        map.removeLayer(tanahnegara_prov_tnbh);
        tnbh(thnData);
        tanahnegara_prov_tnbh.addTo(map);
        tanahnegara_prov_tnbh.bringToFront();

    };


    if (map.hasLayer(tanahnegara_prov_tnbk)) {
        map.removeLayer(tanahnegara_prov_tnbk);
        tnbk(thnData);
        tanahnegara_prov_tnbk.addTo(map);
        tanahnegara_prov_tnbk.bringToFront();

    };


    if (map.hasLayer(tanahnegara_prov_tntk)) {
        map.removeLayer(tanahnegara_prov_tntk);
        tntk(thnData);
        tanahnegara_prov_tntk.addTo(map);
        tanahnegara_prov_tntk.bringToFront();

    };
    if (map.hasLayer(npgt_prov)) {
        map.removeLayer(npgt_prov);
        npgtProv(thnData);
        npgt_prov.addTo(map);
        npgt_prov.bringToFront();

    };

    if (map.hasLayer(npgt_kabkota)) {
        map.removeLayer(npgt_kabkota);
        kabkotA(thnData);
        npgt_kabkota.addTo(map);
        npgt_kabkota.bringToFront();

    };
    // if (map.hasLayer(npgt_kabkot_a)) {
    //     map.removeLayer(npgt_kabkot_a);
    //     kabkotA(thnData);
    //     npgt_kabkot_a.addTo(map);
    //    npgt_kabkot_a.bringToFront();

    // };

    // if (map.hasLayer(npgt_kabkot_g)) {
    //     map.removeLayer(npgt_kabkot_g);
    //     kabkotG(thnData);
    //     npgt_kabkot_g.addTo(map);
    //    npgt_kabkot_g.bringToFront();

    // };

    // if (map.hasLayer(npgt_kabkot_gq)) {
    //     map.removeLayer(npgt_kabkot_gq);
    //     kabkotGQ(thnData);
    //     npgt_kabkot_gq.addTo(map);
    //    npgt_kabkot_gq.bringToFront();

    // };
    // if (map.hasLayer(npgt_kabkot_h)) {
    //     map.removeLayer(npgt_kabkot_h);
    //     kabkotH(thnData);
    //     npgt_kabkot_h.addTo(map);
    //    npgt_kabkot_h.bringToFront();

    // };

    // if (map.hasLayer(npgt_kabkot_k)) {
    //     map.removeLayer(npgt_kabkot_k);
    //     kabkotK(thnData);
    //     npgt_kabkot_k.addTo(map);
    //    npgt_kabkot_k.bringToFront();

    // };
    // if (map.hasLayer(npgt_kabkot_n)) {
    //     map.removeLayer(npgt_kabkot_n);
    //     kabkotN(thnData);
    //     npgt_kabkot_n.addTo(map);
    //    npgt_kabkot_n.bringToFront();

    // };

    // if (map.hasLayer(npgt_kabkot_o)) {
    //     map.removeLayer(npgt_kabkot_o);
    //     kabkotO(thnData);
    //     npgt_kabkot_o.addTo(map);
    //    npgt_kabkot_o.bringToFront();

    // };

    // if (map.hasLayer(npgt_kabkot_q)) {
    //     map.removeLayer(npgt_kabkot_q);
    //     kabkotQ(thnData);
    //     npgt_kabkot_q.addTo(map);
    //    npgt_kabkot_q.bringToFront();

    // };

    // if (map.hasLayer(npgt_kabkot_v)) {
    //     map.removeLayer(npgt_kabkot_v);
    //     kabkotV(thnData);
    //     npgt_kabkot_v.addTo(map);
    //    npgt_kabkot_v.bringToFront();

    // };

    // if (map.hasLayer(npgt_kabkot_w)) {
    //     map.removeLayer(npgt_kabkot_w);
    //     kabkotW(thnData);
    //     npgt_kabkot_w.addTo(map);
    //    npgt_kabkot_w.bringToFront();

    // };

    if (map.hasLayer(npgt_kec_a)) {
        map.removeLayer(npgt_kec_a);
        kecA(thnData);
        npgt_kec_a.addTo(map);
        npgt_kec_a.bringToFront();

    };

    // if (map.hasLayer(npgt_kec_h)) {
    //     map.removeLayer(npgt_kec_h);
    //     kecH(thnData);
    //     npgt_kec_h.addTo(map);
    //     npgt_kec_h.bringToFront();

    // };

    // if (map.hasLayer(npgt_kec_k)) {
    //     map.removeLayer(npgt_kec_k);
    //     kecK(thnData);
    //     npgt_kec_k.addTo(map);
    //     npgt_kec_k.bringToFront();

    // };
    // if (map.hasLayer(npgt_kec_n)) {
    //     map.removeLayer(npgt_kec_n);
    //     kecN(thnData);
    //     npgt_kec_n.addTo(map);
    //     npgt_kec_n.bringToFront();

    // };

    // if (map.hasLayer(npgt_kec_o)) {
    //     map.removeLayer(npgt_kec_o);
    //     kecO(thnData);
    //     npgt_kec_o.addTo(map);
    //     npgt_kec_o.bringToFront();

    // };

    // if (map.hasLayer(npgt_kec_q)) {
    //     map.removeLayer(npgt_kec_q);
    //     kecQ(thnData);
    //     npgt_kec_q.addTo(map);
    //     npgt_kec_q.bringToFront();

    // };

    // if (map.hasLayer(npgt_kec_v)) {
    //     map.removeLayer(npgt_kec_v);
    //     kecV(thnData);
    //     npgt_kec_v.addTo(map);
    //     npgt_kec_v.bringToFront();

    // };

    // if (map.hasLayer(npgt_kec_w)) {
    //     map.removeLayer(npgt_kec_w);
    //     kecW(thnData);
    //     npgt_kec_w.addTo(map);
    //     npgt_kec_w.bringToFront();

    // };
    if (map.hasLayer(npgt_perkebunan)) {
        map.removeLayer(npgt_perkebunan);
        npgtKebun(thnData);
        npgt_perkebunan.addTo(map);
        npgt_perkebunan.bringToFront();

    };

    if (map.hasLayer(mppt_prov)) {
        map.removeLayer(mppt_prov);
        mppt(thnData);
        mppt_prov.addTo(map);
        mppt_prov.bringToFront();

    };
    if (map.hasLayer(kemampuan_tanah)) {
        map.removeLayer(kemampuan_tanah);
        kemampuanTanah(thnData);
        kemampuan_tanah.addTo(map);
        // kemampuan_tanah.bringToFront();

    };

    if (map.hasLayer(lahanbakusawah_prov)) {
        map.removeLayer(lahanbakusawah_prov);
        lahanBaku(thnData);
        lahanbakusawah_prov.addTo(map);
        lahanbakusawah_prov.bringToFront();

    };

    if (map.hasLayer(ptp_p3t)) {
        map.removeLayer(ptp_p3t);
        ptpP3t(thnData);
        ptp_p3t.addTo(map);
        ptp_p3t.bringToFront();

    };

    if (map.hasLayer(ptpil_prov)) {
        map.removeLayer(ptpil_prov);
        ptpilProv(thnData);
        ptpil_prov.addTo(map);
        ptpil_prov.bringToFront();

    };

    if (map.hasLayer(ptpil_prov_tk)) {
        map.removeLayer(ptpil_prov_tk);
        ptpilProvPOI(thnData);
        ptpil_prov_tk.addTo(map);
        ptpil_prov_tk.bringToFront();

    };

    if (map.hasLayer(ptp_kppr_berusaha)) {
        map.removeLayer(ptp_kppr_berusaha);
        ptpBerusaha(thnData);
        ptp_kppr_berusaha.addTo(map);
        ptp_kppr_berusaha.bringToFront();

    };
    if (map.hasLayer(ptp_kppr_stranas)) {
        map.removeLayer(ptp_kppr_stranas);
        ptpStranas(thnData);
        ptp_kppr_stranas.addTo(map);
        ptp_kppr_stranas.bringToFront();

    };
    if (map.hasLayer(ptp_kppr_non_berusaha)) {
        map.removeLayer(ptp_kppr_non_berusaha);
        ptpNonBerusaha(thnData);
        ptp_kppr_non_berusaha.addTo(map);
        ptp_kppr_non_berusaha.bringToFront();

    };
    if (map.hasLayer(ptp_pk_p3t)) {
        map.removeLayer(ptp_pk_p3t);
        ptpPkP3t(thnData);
        ptp_pk_p3t.addTo(map);
        ptp_pk_p3t.bringToFront();

    };
    if (map.hasLayer(ptp_tnh_timbul)) {
        map.removeLayer(ptp_tnh_timbul);
        ptpTnhTimbul(thnData);
        ptp_tnh_timbul.addTo(map);
        ptp_tnh_timbul.bringToFront();

    };
    if (map.hasLayer(wp3wt_ppk_polygon)) {
        map.removeLayer(wp3wt_ppk_polygon);
        Wp3wtPpkPolygon(thnData);
        wp3wt_ppk_polygon.addTo(map);
        wp3wt_ppk_polygon.bringToFront();

    };
    if (map.hasLayer(wp3wt_ppk_poi)) {
        map.removeLayer(wp3wt_ppk_poi);
        Wp3wtPpkPOI(thnData);
        wp3wt_ppk_poi.addTo(map);
        wp3wt_ppk_poi.bringToFront();

    };
    if (map.hasLayer(wp3wt_ppkt)) {
        map.removeLayer(wp3wt_ppkt);
        wp3wtPpkt(thnData);
        wp3wt_ppkt.addTo(map);
        wp3wt_ppkt.bringToFront();

    };
    if (map.hasLayer(wp3wt_perbatasan)) {
        map.removeLayer(wp3wt_perbatasan);
        wp3wtPerbatasan(thnData);
        wp3wt_perbatasan.addTo(map);
        wp3wt_perbatasan.bringToFront();

        console.log('filter ')
    };
    if (map.hasLayer(wp3wt_pesisir)) {
        map.removeLayer(wp3wt_pesisir);
        wp3wtPesisir(thnData);
        wp3wt_pesisir.addTo(map);
        wp3wt_pesisir.bringToFront();

    };

    if (map.hasLayer(wp3wt_pulau)) {
        map.removeLayer(wp3wt_pulau);
        wp3wtPulau(thnData);
        wp3wt_pulau.addTo(map);
        wp3wt_pulau.bringToFront();

    };

    if (map.hasLayer(wp3wt_tertentu)) {
        map.removeLayer(wp3wt_tertentu);
        wp3wtTertentu(thnData);
        wp3wt_tertentu.addTo(map);
        wp3wt_tertentu.bringToFront();

    };

    if (map.hasLayer(wp3wt_timbul)) {
        map.removeLayer(wp3wt_timbul);
        wp3wtTimbul(thnData);
        wp3wt_timbul.addTo(map);
        wp3wt_timbul.bringToFront();

    };
    if (map.hasLayer(rtrw)) {
        map.removeLayer(rtrw);
        pgtlRtrw(thnData);
        rtrw.addTo(map);
        rtrw.bringToFront();

    };

    if (map.hasLayer(bts_desa)) {
        map.removeLayer(bts_desa);
        pgtlDesa(thnData);
        bts_desa.addTo(map);
        bts_desa.bringToFront();

    };

    if (map.hasLayer(bts_kecamatan)) {
        map.removeLayer(bts_kecamatan);
        pgtlDesa(thnData);
        bts_kecamatan.addTo(map);
        bts_kecamatan.bringToFront();

    };
    if (map.hasLayer(petaTR)) {
        buildMap()

    };


}
// var url = 'http://103.6.53.254:4980/geoserver/pgt/wms';
var url = WGI_APP_GEOSERVER_URL + 'pgt/wms';
var gsAuthKey = WGI_APP_GEOSERVER_AUTHKEY;
var prefix = 'spatial_'
var cqlFilter = 'id_form =' + $('#id_form').val()
var petanalisis = L.tileLayer.wms(url, {
    layers: 'pgt:' + prefix + 'form_petanalisis',
    format: 'image/png',
    transparent: true,
    attribution: '',
    cql_filter: cqlFilter,
    authkey: gsAuthKey

}).addTo(map);


//NPGT Provinsi
var npgt_prov = L.tileLayer.betterWms()
var npgt_kabkota = L.tileLayer.betterWms()
var npgt_kec_a = L.tileLayer.betterWms()

var npgt_perkebunan = L.tileLayer.betterWms()
var mppt_prov = L.tileLayer.betterWms()
var kemampuan_tanah = L.tileLayer.betterWms()
var lahanbakusawah_prov = L.tileLayer.betterWms()
var ptp_p3t = L.tileLayer.betterWms()
var ptp_pk_p3t = L.tileLayer.betterWms()
var ptpil_prov = L.tileLayer.betterWms()
var ptpil_prov_tk = L.tileLayer.betterWms()
var ptp_kppr_berusaha = L.tileLayer.betterWms()
var ptp_kppr_non_berusaha = L.tileLayer.betterWms()
var ptp_kppr_stranas = L.tileLayer.betterWms()
var ptp_tnh_timbul = L.tileLayer.betterWms()
var wp3wt_ppk_polygon = L.tileLayer.betterWms()
var wp3wt_ppk_poi = L.tileLayer.betterWms()
var wp3wt_ppkt = L.tileLayer.betterWms()
var wp3wt_perbatasan = L.tileLayer.betterWms()
var wp3wt_pesisir = L.tileLayer.betterWms()
var wp3wt_pulau = L.tileLayer.betterWms()
var wp3wt_tertentu = L.tileLayer.betterWms()
var wp3wt_timbul = L.tileLayer.betterWms()
var rtrw = L.tileLayer.betterWms()
var bts_desa = L.tileLayer.betterWms()
var bts_kecamatan = L.tileLayer.betterWms()

var lokasi = L.tileLayer.betterWms()
var jalan = L.tileLayer.betterWms()
var sungai = L.tileLayer.betterWms()
var land_use = L.tileLayer.betterWms()
// cql_filter: thnData,
var tanahnegara_prov_tnbh = L.tileLayer.betterWms()
var tanahnegara_prov_tnbk = L.tileLayer.betterWms()
var tanahnegara_prov_tntk = L.tileLayer.betterWms()

var legenda = L.tileLayer.betterWms()

function rLegenda(id_append) {
    $('#d' + id_append).remove();
}

function aLegenda(id_append, layer) {


    var str = '<div class="containers" id="d' + id_append + '"><img id="img_' + id_append +
        '" src="' + url + '?service=WMS&request=GetLegendGraphic&layer=pgt%3A' + prefix +
        layer + '&style=pgt%3A' + prefix + layer + '&format=image%2Fpng&authkey=' + gsAuthKey + '"/></div>';
    $('#' + id_append).next('button').after(str);



    // Image is already loaded, get its height
    // var imageHeight = img.naturalHeight;
    // console.log('Image height:', imageHeight);
    var imgElement = $('#img_' + id_append)
    imgElement.on('load', function() {
        // Get the height of the loaded image
        var imageHeight = $(this).height();

        if (imageHeight > 100) {
            var heightScroll = 100
        } else {
            var heightScroll = imageHeight

        }
        $('#d' + id_append).slimScroll({
            size: '3px',
            height: heightScroll,
            alwaysVisible: true
        })
    });

}
//NPGT Kabupaten/Kota
function legendaCall(v) {
    // removeLayer()
    map.removeLayer(npgt_kabkota);
    if (v == 1) {
        if ($('#kabkotACheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#kabkotACheck').prop('checked', true);
        }
    } else {
        if ($('#kabkotACheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#kabkotACheck').prop('checked', true);
            // return false 
        } else {
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kabkota')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        legenda()
    });
}

function legenda() {
    map.closePopup()
    npgt_kabkota = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'npgt_kabkota',
        // styles: 'pgt:' + prefix + 'npgt_kabkota',
        // tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        title: 'NPGT Kabupaten/Kota'

    });
    npgt_kabkota.addTo(map)
    npgt_kabkota.bringToFront();
    petanalisis.bringToFront();

}

function kabkotACall(v) {
    // removeLayer()
    map.removeLayer(npgt_kabkota);
    if (v == 1) {
        if ($('#kabkotACheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);

            rLegenda('kabkotACheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#kabkotACheck').prop('checked', true);
            aLegenda('kabkotACheck', 'npgt_kabkota')

        }
    } else {
        if ($('#kabkotACheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#kabkotACheck').prop('checked', true);
            aLegenda('kabkotACheck', 'npgt_kabkota')

            // return false 
        } else {

            rLegenda('kabkotACheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kabkota')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kabkotA()
    });
}

function kabkotA() {
    map.closePopup()
    npgt_kabkota = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'npgt_kabkota',
        styles: 'pgt:' + prefix + 'npgt_kabkota',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        title: 'NPGT Kabupaten/Kota'

    });
    npgt_kabkota.addTo(map)
    npgt_kabkota.bringToFront();
    petanalisis.bringToFront();

}




//NPGT Kecamatan
function kecACall(v) {
    // removeLayer()
    map.removeLayer(npgt_kec_a);

    if (v == 1) {
        if ($('#kecACheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);

            rLegenda('kecACheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#kecACheck').prop('checked', true);
            aLegenda('kecACheck', 'npgt_kec_polygon')

        }
    } else {
        if ($('#kecACheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#kecACheck').prop('checked', true);
            aLegenda('kecACheck', 'npgt_kec_polygon')

            // return false 
        } else {
            rLegenda('kecACheck')
            return false
        }
    }

    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_kec')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kecA()
    });
}

function kecA(params) {
    map.closePopup()

    npgt_kec_a = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'npgt_kec_polygon',
        styles: 'pgt:' + prefix + 'npgt_kec_polygon',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'NPGT Kecamatan Administrasi (A)'
    });
    npgt_kec_a.addTo(map)
    npgt_kec_a.bringToFront();
    petanalisis.bringToFront();
}


function npgtKebunCall(v) {
    // removeLayer()
    map.removeLayer(npgt_perkebunan);
    if (v == 1) {
        if ($('#npgtKebunCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('npgtKebunCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#npgtKebunCheck').prop('checked', true);
            aLegenda('npgtKebunCheck', 'npgt_perkebunan')


            // $('#img_npgtKebun').attr('src' ,src);
            // $('#simg_npgtKebun').css('display' ,'block');
        }
    } else {
        if ($('#npgtKebunCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#npgtKebunCheck').prop('checked', true);
            aLegenda('npgtKebunCheck', 'npgt_perkebunan')

            // return false 
        } else {
            rLegenda('npgtKebunCheck')

            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_perkebunan')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        npgtKebun()
    });
}

function npgtKebun() {
    map.closePopup()



    // Log or use the legend URL
    npgt_perkebunan = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'npgt_perkebunan',
        styles: 'pgt:' + prefix + 'npgt_perkebunan',
        tiled: true,
        transparent: true,
        format: 'image/png',
        // bgcolor: '#FAFA7D'
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "q",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "w",
                "alias": "RTRW"
            },
            // {
            //     "name": "hgu",
            //     "alias": "No. HGU"
            // },
            {
                "name": "hgu_reclas",
                "alias": "HGU"
            },
            {
                "name": "tipe",
                "alias": "Tipe"
            },
            {
                "name": "pemilik",
                "alias": "Pemilik"
            },
            {
                "name": "sk",
                "alias": "SK"
            },
            {
                "name": "verifikasi",
                "alias": "Verifikasi"
            },
            // {
            //     "name": "komoditas",
            //     "alias": "Komoditas"
            // },
            {
                "name": "hat_reclas",
                "alias": "HAT"
            },
            {
                "name": "lp2b_recla",
                "alias": "LP2B"
            },
            {
                "name": "ppb_reclas",
                "alias": "PPB"
            },
            {
                "name": "kh_reclass",
                "alias": "Kawasan"
            },
            {
                "name": "kssn",
                "alias": "KSSN"
            },
            {
                "name": "ktsdn",
                "alias": "KTSDN"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }

        ],
        title: 'NPGT Perkebunan'
    });
    npgt_perkebunan.addTo(map)

    npgt_perkebunan.bringToFront();
    petanalisis.bringToFront();
}

function pgtlRtrwCall(v) {
    // removeLayer()
    map.removeLayer(rtrw);
    if (v == 1) {
        if ($('#pgtlRtrwCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('pgtlRtrwCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlRtrwCheck').prop('checked', true);
            aLegenda('pgtlRtrwCheck', 'rtrw')
        }
    } else {
        if ($('#pgtlRtrwCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlRtrwCheck').prop('checked', true);
            aLegenda('pgtlRtrwCheck', 'rtrw')
            // return false 
        } else {
            rLegenda('pgtlRtrwCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_pgtl_rtrw')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        pgtlRtrw()
    });
}

function pgtlRtrw(params) {

    map.closePopup()
    rtrw = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'rtrw',
        styles: 'pgt:' + prefix + 'rtrw',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "no_perda",
                "alias": "No. Perda"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "jnsrpr",
                "alias": "Rencana Pola Ruang"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'RTRW'

    });
    rtrw.addTo(map)
    rtrw.bringToFront();
    petanalisis.bringToFront();
}






// var npgt_industri = L.tileLayer.betterWms(url, {
// 	layers: 'pgt:' + prefix + 'temp_npgt_industri',
// 	transparent: true,
// 	format: 'image/png8'
// });

// var npgt_rumah= L.tileLayer.betterWms(url, {
// 	layers: 'pgt:' + prefix + 'temp_npgt_perumahan',
// 	transparent: true,
// 	format: 'image/png8'
// });



function tnbkCall(v) {
    // removeLayer()
    map.removeLayer(tanahnegara_prov_tnbk);
    if (v == 1) {
        if ($('#tnbkCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('tnbkCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#tnbkCheck').prop('checked', true);
            aLegenda('tnbkCheck', 'tanahnegara_prov_tnbk')
        }
    } else {
        if ($('#tnbkCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#tnbkCheck').prop('checked', true);
            aLegenda('tnbkCheck', 'tanahnegara_prov_tnbk')
            // return false 
        } else {
            rLegenda('tnbkCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_tnbk')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        tnbk()
    });
}

function tnbk() {

    map.closePopup()
    tanahnegara_prov_tnbk = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'tanahnegara_prov_tnbk',
        styles: 'pgt:' + prefix + 'tanahnegara_prov_tnbk',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "arahan",
                "alias": "Arahan"
            },
            {
                "name": "riwayat",
                "alias": "Riwayat"
            },
            {
                "name": "oname19",
                "alias": "Gambaran Umum"
            },
            {
                "name": "qname19",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "pmanfaatan",
                "alias": "Pemanfaatan"
            },
            {
                "name": "wname19",
                "alias": "RTRW"
            },
            {
                "name": "kegiatan",
                "alias": "Kegiatan"
            },
            {
                "name": "aprogram",
                "alias": "Program"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Tanah Negara Bekas Kawasan'


    });
    tanahnegara_prov_tnbk.addTo(map)
    tanahnegara_prov_tnbk.bringToFront();
    petanalisis.bringToFront();
}

// var tanahnegara_prov_tntk = L.tileLayer.betterWms(url, {
//     layers: 'pgt:' + prefix + 'tanahnegara_prov_tntk',
//     transparent: true,
//     format: 'image/png8'
// });
// var tanahnegara_prov_tnbk = L.tileLayer.betterWms(url, {
//     layers: 'pgt:' + prefix + 'tanahnegara_prov_tnbk',
//     transparent: true,
//     format: 'image/png8'
// });

function lahanBakuCall(v) {
    // removeLayer()
    map.removeLayer(lahanbakusawah_prov);
    if (v == 1) {
        if ($('#lahanBakuCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('lahanBakuCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#lahanBakuCheck').prop('checked', true);
            aLegenda('lahanBakuCheck', 'lahanbakusawah_prov')
        }
    } else {
        if ($('#lahanBakuCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#lahanBakuCheck').prop('checked', true);
            aLegenda('lahanBakuCheck', 'lahanbakusawah_prov')
            // return false 
        } else {
            rLegenda('lahanBakuCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_lahanbakusawah')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        lahanBaku()
    });
}

function lahanBaku() {

    map.closePopup()
    lahanbakusawah_prov = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'lahanbakusawah_prov',
        styles: 'pgt:' + prefix + 'lahanbakusawah_prov',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Lahan Baku Sawah'


    });
    lahanbakusawah_prov.addTo(map)
    lahanbakusawah_prov.bringToFront();
    petanalisis.bringToFront();
}


function jalanCall(v) {
    // removeLayer()
    map.removeLayer(jalan);
    if (v == 1) {
        if ($('#jalanCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('jalanCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            aLegenda('jalanCheck', 'jalan_indonesia')
            $('#jalanCheck').prop('checked', true);
        }
    } else {
        if ($('#jalanCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#jalanCheck').prop('checked', true);
            aLegenda('jalanCheck', 'jalan_indonesia')
            // return false 
        } else {
            rLegenda('jalanCheck')
            return false
        }
    }
    // var url = "<?php echo base_url('lookup/getYear/spatial.vm_lahanbakusawah')?>";
    // $.get(url, function(data) {
    // data = JSON.parse(data)
    // $('#thnSelect').html(data.year)
    // setFilter()
    jalan_indonesia()
    // });
}


function jalan_indonesia() {

    map.closePopup()
    jalan = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'jalan_indonesia',
        styles: 'pgt:' + prefix + 'jalan_indonesia',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Jalan',



    });
    jalan.addTo(map)
    jalan.bringToFront();
    lokasi.bringToFront();
    petanalisis.bringToFront();
}

function landUseCall(v) {
    // removeLayer()
    map.removeLayer(land_use);
    if (v == 1) {
        if ($('#landUseCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('landUseCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#landUseCheck').prop('checked', true);
            aLegenda('landUseCheck', 'landuse_indonesia')
        }
    } else {
        if ($('#landUseCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#landUseCheck').prop('checked', true);
            aLegenda('landUseCheck', 'landuse_indonesia')
            // return false 
        } else {
            rLegenda('landUseCheck')
            return false
        }
    }
    // var url = "<?php echo base_url('lookup/getYear/spatial.vm_lahanbakusawah')?>";
    // $.get(url, function(data) {
    // data = JSON.parse(data)
    // $('#thnSelect').html(data.year)
    // setFilter()
    landuse_indonesia()
    // });
}


function landuse_indonesia() {
    console.log('landuse')
    map.closePopup()
    land_use = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'landuse_indonesia',
        styles: 'pgt:' + prefix + 'landuse_indonesia',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Penggunaan Tanah'


    });
    land_use.addTo(map)
    land_use.bringToFront();
    lokasi.bringToFront();
    petanalisis.bringToFront();

}

function sungaiCall(v) {
    // removeLayer()
    map.removeLayer(sungai);
    if (v == 1) {
        if ($('#sungaiCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('sungaiCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#sungaiCheck').prop('checked', true);
            aLegenda('sungaiCheck', 'sungai_indonesia')
        }
    } else {
        if ($('#sungaiCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#sungaiCheck').prop('checked', true);
            aLegenda('sungaiCheck', 'sungai_indonesia')
            // return false 
        } else {
            rLegenda('sungaiCheck')
            return false
        }
    }
    // var url = "<?php echo base_url('lookup/getYear/spatial.vm_lahanbakusawah')?>";
    // $.get(url, function(data) {
    // data = JSON.parse(data)
    // $('#thnSelect').html(data.year)
    // setFilter()
    sungai_indonesia()
    // });
}


function sungai_indonesia() {

    map.closePopup()
    sungai = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'sungai_indonesia',
        styles: 'pgt:' + prefix + 'sungai_indonesia',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Jalan'


    });
    sungai.addTo(map)
    sungai.bringToFront();
    petanalisis.bringToFront();
}


function mpptCall(v) {
    // removeLayer()
    map.removeLayer(mppt_prov);
    if (v == 1) {
        if ($('#mpptCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            return false
            rLegenda('mpptCheck')
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#mpptCheck').prop('checked', true);
            aLegenda('mpptCheck', 'mppt_prov')
        }
    } else {
        if ($('#mpptCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#mpptCheck').prop('checked', true);
            aLegenda('mpptCheck', 'mppt_prov')
            // return false 
        } else {
            rLegenda('mpptCheck')
            return false
        }
    }

    var url = "<?php echo base_url('lookup/getYear/spatial.vm_mppt')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        mppt()
    });
}

function mppt() {
    map.closePopup()
    mppt_prov = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'mppt_prov',
        styles: 'pgt:' + prefix + 'mppt_prov',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "qname100",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "gname100",
                "alias": "Penggunaan Lama"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Monitoring PPT'

    });
    mppt_prov.addTo(map)
    mppt_prov.bringToFront();
    petanalisis.bringToFront();
}


function ptpP3tCall(v) {
    // removeLayer()
    map.removeLayer(ptp_p3t);
    if (v == 1) {
        if ($('#ptpP3tCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('ptpP3tCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#ptpP3tCheck').prop('checked', true);
            aLegenda('ptpP3tCheck', 'ptp_p3t')

        }
    } else {
        if ($('#ptpP3tCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#ptpP3tCheck').prop('checked', true);
            aLegenda('ptpP3tCheck', 'ptp_p3t')

            // return false 
        } else {
            rLegenda('ptpP3tCheck')

            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_p3t')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpP3t()
    });
}

function ptpP3t(params) {
    map.closePopup()
    ptp_p3t = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'ptp_p3t',
        styles: 'pgt:' + prefix + 'ptp_p3t',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "alamat_prs",
                "alias": "Alamat"
            },
            {
                "name": "peruntukan",
                "alias": "Peruntukan"
            },
            {
                "name": "no_berkas",
                "alias": "No. Berkas"
            },
            {
                "name": "tg_risalah",
                "alias": "Tgl. Risalah"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "no_ilok",
                "alias": "No. Ilok"
            },
            {
                "name": "tgl_ilok",
                "alias": "Tgl. Ilok"
            },
            {
                "name": "posisi",
                "alias": "Posisi"
            },
            {
                "name": "info",
                "alias": "Info"
            },
            {
                "name": "klas",
                "alias": "Kelas"
            }
        ],
        title: 'IP4T'

    });
    ptp_p3t.addTo(map)
    ptp_p3t.bringToFront();
    petanalisis.bringToFront();
}


function ptpilProvCall(v) {
    // removeLayer()
    map.removeLayer(ptpil_prov);
    if (v == 1) {
        if ($('#ptpilProvCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('ptpilProvCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvCheck').prop('checked', true);
            aLegenda('ptpilProvCheck', 'ptpil_prov')

        }
    } else {
        if ($('#ptpilProvCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvCheck').prop('checked', true);
            aLegenda('ptpilProvCheck', 'ptpil_prov')

            // return false 
        } else {
            rLegenda('ptpilProvCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptpil_prov')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpilProv()
    });
}

function ptpilProv(params) {

    map.closePopup()
    ptpil_prov = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'ptpil_prov',
        styles: 'pgt:' + prefix + 'ptpil_prov',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "namprh",
                "alias": "Perusahaan"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "alamat_prs",
                "alias": "Alamat Perusahaan"
            },
            {
                "name": "peruntukan",
                "alias": "Peruntukan"
            },
            // {
            //     "name": "alamat_ptk",
            //     "alias": "Alamat PTK"
            // },
            {
                "name": "no_berkas",
                "alias": "No. Berkas"
            },
            {
                "name": "tg_risalah",
                "alias": "Tgl. Risalah"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            // {
            //     "name": "luas_ptp",
            //     "alias": "Luas PTP"
            // },
            {
                "name": "no_ilok",
                "alias": "No. Ilok"
            },
            {
                "name": "tgl_ilok",
                "alias": "Tgl. Ilok"
            },
            // {
            //     "name": "luas_ilok",
            //     "alias": "Luas Ilok"
            // },
            {
                "name": "posisi",
                "alias": "Posisi"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "info",
                "alias": "Info"
            },
            {
                "name": "klas",
                "alias": "Kelas"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan Izin Lokasi Provinsi- AOI'

    });
    ptpil_prov.addTo(map)
    ptpil_prov.bringToFront();
    petanalisis.bringToFront();

}


function ptpilProvPoiCall(v) {
    // removeLayer()
    map.removeLayer(ptpil_prov_tk);
    if (v == 1) {
        if ($('#ptpilProvPoiCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('ptpilProvPoiCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvPoiCheck').prop('checked', true);
            aLegenda('ptpilProvPoiCheck', 'ptpil_prov_tk')

        }
    } else {
        if ($('#ptpilProvPoiCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#ptpilProvPoiCheck').prop('checked', true);
            aLegenda('ptpilProvPoiCheck', 'ptpil_prov_tk')

            // return false 
        } else {
            rLegenda('ptpilProvPoiCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptpil_prov_tk')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpilProvPOI()
    });
}

function ptpilProvPOI(params) {

    map.closePopup()
    ptpil_prov_tk = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'ptpil_prov_tk',
        styles: 'pgt:' + prefix + 'ptpil_prov_tk',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "alamat_prs",
                "alias": "Alamat"
            },
            {
                "name": "peruntukan",
                "alias": "Peruntukan"
            },
            {
                "name": "alamat_ptk",
                "alias": "Alamat PTK"
            },
            {
                "name": "no_berkas",
                "alias": "No. Berkas"
            },
            {
                "name": "tg_risalah",
                "alias": "Tgl. Risalah"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "luas_ptp",
                "alias": "Luas PTP"
            },
            {
                "name": "no_ilok",
                "alias": "No. Ilok"
            },
            {
                "name": "tgl_ilok",
                "alias": "Tgl. Ilok"
            },
            {
                "name": "luas_ilok",
                "alias": "Luas Ilok"
            },
            {
                "name": "posisi",
                "alias": "Posisi"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "info",
                "alias": "Info"
            },
            {
                "name": "klas",
                "alias": "Kelas"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "luas",
                "alias": "Luasan(ha)"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan Izin Lokasi Provinsi - POI'

    });
    ptpil_prov_tk.addTo(map)
    ptpil_prov_tk.bringToFront();
    petanalisis.bringToFront();

}


function ptpBerusahaCall(v) {
    // removeLayer()
    map.removeLayer(ptp_kppr_berusaha);
    if (v == 1) {
        if ($('#ptpBerusahaCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('ptpBerusahaCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#ptpBerusahaCheck').prop('checked', true);
            aLegenda('ptpBerusahaCheck', 'ptp_kppr_berusaha')

        }
    } else {
        if ($('#ptpBerusahaCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#ptpBerusahaCheck').prop('checked', true);
            aLegenda('ptpBerusahaCheck', 'ptp_kppr_berusaha')

            // return false 
        } else {
            rLegenda('ptpBerusahaCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_berusaha')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpBerusaha()
    });
}

function ptpBerusaha() {

    map.closePopup()
    ptp_kppr_berusaha = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'ptp_kppr_berusaha',
        styles: 'pgt:' + prefix + 'ptp_kppr_berusaha',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "lokasi",
                "alias": "Lokasi"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "nama_perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "alamat_perusahaan",
                "alias": "Alamat"
            },
            {
                "name": "rencana_kegiatan",
                "alias": "Rencana"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "jns_ptp",
                "alias": "Jenis"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            // {
            //     "name": "luas_m2",
            //     "alias": "Luasan(m2)"
            // },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "kbli",
                "alias": "KBLI"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan KKPR Berusaha',



    });
    ptp_kppr_berusaha.addTo(map)
    ptp_kppr_berusaha.bringToFront();
    petanalisis.bringToFront();

}


function ptpNonBerusahaCall(v) {
    // removeLayer()
    map.removeLayer(ptp_kppr_non_berusaha);
    if (v == 1) {
        if ($('#ptpNonBerusahaCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('ptpNonBerusahaCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#ptpNonBerusahaCheck').prop('checked', true);
            aLegenda('ptpNonBerusahaCheck', 'ptp_kppr_non_berusaha')


        }
    } else {
        if ($('#ptpNonBerusahaCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#ptpNonBerusahaCheck').prop('checked', true);
            aLegenda('ptpNonBerusahaCheck', 'ptp_kppr_non_berusaha')
        } else {
            rLegenda('ptpNonBerusahaCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_non_berusaha')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpNonBerusaha()
    });
}

function ptpNonBerusaha() {

    map.closePopup()
    ptp_kppr_non_berusaha = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'ptp_kppr_non_berusaha',
        styles: 'pgt:' + prefix + 'ptp_kppr_non_berusaha',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "lokasi",
                "alias": "Lokasi"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "nama_perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "alamat_perusahaan",
                "alias": "Alamat"
            },
            {
                "name": "rencana_kegiatan",
                "alias": "Rencana"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "jns_ptp",
                "alias": "Jenis"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            // {
            //     "name": "luas_m2",
            //     "alias": "Luasan(m2)"
            // },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "kbli",
                "alias": "KBLI"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan KKPR Non Berusaha'


    });
    ptp_kppr_non_berusaha.addTo(map)
    ptp_kppr_non_berusaha.bringToFront();
    petanalisis.bringToFront();

}


function ptpPkP3tCall(v) {
    // removeLayer()
    map.removeLayer(ptp_pk_p3t);
    if (v == 1) {
        if ($('#ptpPkP3tCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('ptpPkP3tCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#ptpPkP3tCheck').prop('checked', true);
            aLegenda('ptpPkP3tCheck', 'ptp_pk_p3t')

        }
    } else {
        if ($('#ptpPkP3tCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#ptpPkP3tCheck').prop('checked', true);
            aLegenda('ptpPkP3tCheck', 'ptp_pk_p3t')

            // return false 
        } else {
            rLegenda('ptpPkP3tCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_pk_p3t')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpPkP3t()
    });
}

function ptpPkP3t() {
    console.log('pkp3t')
    map.closePopup()
    ptp_pk_p3t = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'ptp_pk_p3t',
        styles: 'pgt:' + prefix + 'ptp_pk_p3t',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "lokasi",
                "alias": "Lokasi"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "nama_perusahaan",
                "alias": "Perusahaan"
            },
            {
                "name": "alamat_perusahaan",
                "alias": "Alamat"
            },
            {
                "name": "rencana_kegiatan",
                "alias": "Rencana"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "jns_ptp",
                "alias": "Jenis"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "kbli",
                "alias": "KBLI"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan PK P3T'

    });
    ptp_pk_p3t.addTo(map)
    ptp_pk_p3t.bringToFront();
    petanalisis.bringToFront();

}


function ptpStranasCall(v) {
    // removeLayer()
    map.removeLayer(ptp_kppr_stranas);
    if (v == 1) {
        if ($('#ptpStranasCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#dptpStranasCheck').remove();
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#ptpStranasCheck').prop('checked', true);
            aLegenda('ptpStranasCheck', 'ptp_kppr_stranas')

        }
    } else {
        if ($('#ptpStranasCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);

            $('#ptpStranasCheck').prop('checked', true);
            aLegenda('ptpStranasCheck', 'ptp_kppr_stranas')
            // return false 
        } else {
            $('#dptpStranasCheck').remove();
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_stranas')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpStranas()
    });
}

function ptpStranas() {

    map.closePopup()
    ptp_kppr_stranas = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'ptp_kppr_stranas',
        styles: 'pgt:' + prefix + 'ptp_kppr_stranas',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "land_use",
                "alias": "Penggunaan"
            },
            {
                "name": "tata_ruang",
                "alias": "RTRW"
            },
            {
                "name": "kesesuain",
                "alias": "Kesesuaian"
            },
            {
                "name": "ketersedia",
                "alias": "Ketersediaan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "jns_ptp",
                "alias": "Jenis"
            },
            {
                "name": "no_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "renc_kegiatan",
                "alias": "Rencana"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan KKPR Stranas'
    });
    ptp_kppr_stranas.addTo(map)
    ptp_kppr_stranas.bringToFront();
    petanalisis.bringToFront();

}


function ptpTnhTimbulCall(v) {
    // removeLayer()
    map.removeLayer(ptp_tnh_timbul);
    if (v == 1) {
        if ($('#ptpTnhTimbulCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('ptpTnhTimbulCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#ptpTnhTimbulCheck').prop('checked', true);
            aLegenda('ptpTnhTimbulCheck', 'ptp_tnh_timbul')

        }
    } else {
        if ($('#ptpTnhTimbulCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#ptpTnhTimbulCheck').prop('checked', true);
            aLegenda('ptpTnhTimbulCheck', 'ptp_tnh_timbul')
            // return false 
        } else {
            rLegenda('ptpTnhTimbulCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_ptp_tnh_timbul')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        ptpTnhTimbul()
    });
}

function ptpTnhTimbul() {

    map.closePopup()
    ptp_tnh_timbul = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'ptp_tnh_timbul',
        styles: 'pgt:' + prefix + 'ptp_tnh_timbul',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "pemohon",
                "alias": "Pemohon"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "kd_bidang",
                "alias": "KD Bidang"
            },
            {
                "name": "nib",
                "alias": "NIB"
            },
            {
                "name": "pemilik",
                "alias": "Pemilik"
            },
            {
                "name": "sk",
                "alias": "SK"
            },
            {
                "name": "tanggal_sk",
                "alias": "Tgl. SK"
            },
            {
                "name": "kriteria",
                "alias": "Kriteria"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "kelayakan",
                "alias": "Kelayakan"
            },
            {
                "name": "aprogram",
                "alias": "Program"
            },
            {
                "name": "kluster",
                "alias": "Kluster"
            },
            {
                "name": "penggarap",
                "alias": "Penggarap"
            },
            {
                "name": "jml_kk",
                "alias": "Jml. KK"
            },
            {
                "name": "pokgar",
                "alias": "Garapan"
            },
            {
                "name": "lokasi",
                "alias": "Lokasi"
            },
            {
                "name": "bdn_hukum",
                "alias": "Bdn. Hukum"
            },
            {
                "name": "rncana_keg",
                "alias": "Rencana"
            },
            {
                "name": "nomor_ptp",
                "alias": "No. PTP"
            },
            {
                "name": "jenis_ptp",
                "alias": "Jenis PTP"
            },
            {
                "name": "tgl_ptp",
                "alias": "Tgl. PTP"
            },
            {
                "name": "hasil_ptp",
                "alias": "Hasil"
            }
        ],
        title: 'Pertimbangan Teknis Pertanahan Tanah Timbul'
    });
    ptp_tnh_timbul.addTo(map)
    ptp_tnh_timbul.bringToFront();
    petanalisis.bringToFront();
}

function Wp3wtPpkPolygonCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_ppk_polygon);
    if (v == 1) {
        if ($('#Wp3wtPpkPolygonCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('Wp3wtPpkPolygonCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#Wp3wtPpkPolygonCheck').prop('checked', true);
            aLegenda('Wp3wtPpkPolygonCheck', 'wp3wt_ppk_polygon')
        }
    } else {
        if ($('#Wp3wtPpkPolygonCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#Wp3wtPpkPolygonCheck').prop('checked', true);
            aLegenda('Wp3wtPpkPolygonCheck', 'wp3wt_ppk_polygon')
            // return false 
        } else {
            rLegenda('Wp3wtPpkPolygonCheck')
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_ppk_polygon');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_ppk_polygon')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        Wp3wtPpkPolygon()
    });
}

function Wp3wtPpkPolygon() {

    map.closePopup()
    wp3wt_ppk_polygon = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'wp3wt_ppk_polygon',
        styles: 'pgt:' + prefix + 'wp3wt_ppk_polygon',
        transparent: true,
        tiled: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "desa",
                "alias": "Desa"
            },
            {
                "name": "tutupan_la",
                "alias": "Keterangan"
            },
            {
                "name": "objek",
                "alias": "Objek"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "nama_pulau",
                "alias": "Pulau"
            },
            {
                "name": "pemilikan",
                "alias": "Pemilikan"
            },
            {
                "name": "kawasan_hu",
                "alias": "Kawasan"
            }
        ],
        title: 'WP3WT Sebaran Pulau Pulau Kecil '
    });
    wp3wt_ppk_polygon.addTo(map)
    wp3wt_ppk_polygon.bringToFront();
    petanalisis.bringToFront();
}

function wp3wtPpkPoiCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_ppk_poi);
    if (v == 1) {
        if ($('#wp3wtPpkPoiCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('wp3wtPpkPoiCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpkPoiCheck').prop('checked', true);
            aLegenda('wp3wtPpkPoiCheck', 'wp3wt_ppk_point')
        }
    } else {
        if ($('#wp3wtPpkPoiCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpkPoiCheck').prop('checked', true);
            aLegenda('wp3wtPpkPoiCheck', 'wp3wt_ppk_point')
            // return false 
        } else {
            rLegenda('wp3wtPpkPoiCheck')
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_ppk_point');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_ppk_point')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        Wp3wtPpkPOI()
    });
}



function Wp3wtPpkPOI() {

    map.closePopup()
    wp3wt_ppk_poi = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'wp3wt_ppk_point',
        styles: 'pgt:' + prefix + 'wp3wt_ppk_point',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "desa",
                "alias": "Desa"
            },
            {
                "name": "tutupan_la",
                "alias": "Keterangan"
            },
            {
                "name": "objek",
                "alias": "Objek"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "verifikasi",
                "alias": "Verifikasi"
            },
            {
                "name": "nama_pulau",
                "alias": "Pulau"
            }
        ],
        title: 'WP3WT Sebaran Pulau Pulau Kecil (POI) '
    });
    wp3wt_ppk_poi.addTo(map)
    wp3wt_ppk_poi.bringToFront();
    petanalisis.bringToFront();
}

function wp3wtPpktCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_ppkt);
    if (v == 1) {
        if ($('#wp3wtPpktCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('wp3wtPpktCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpktCheck').prop('checked', true);
            aLegenda('wp3wtPpktCheck', 'wp3wt_ppkt')
        }
    } else {
        if ($('#wp3wtPpktCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPpktCheck').prop('checked', true);
            aLegenda('wp3wtPpktCheck', 'wp3wt_ppkt')
            // return false 
        } else {
            rLegenda('wp3wtPpktCheck')
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_ppkt');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_ppkt')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtPpkt()
    });
}

function wp3wtPpkt() {

    map.closePopup()
    wp3wt_ppkt = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'wp3wt_ppkt',
        styles: 'pgt:' + prefix + 'wp3wt_ppkt',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "tutupan_la",
                "alias": "Lahan"
            },
            {
                "name": "objek",
                "alias": "Objek"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Sebaran Pulau Pulau Kecil Terluar'

    });
    wp3wt_ppkt.addTo(map)
    wp3wt_ppkt.bringToFront();
    petanalisis.bringToFront();
}


function wp3wtPerbatasanCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_perbatasan);
    if (v == 1) {
        if ($('#wp3wtPerbatasanCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('wp3wtPerbatasanCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPerbatasanCheck').prop('checked', true);
            aLegenda('wp3wtPerbatasanCheck', 'wp3wt_penataan_perbatasan')

        }
    } else {
        if ($('#wp3wtPerbatasanCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPerbatasanCheck').prop('checked', true);
            aLegenda('wp3wtPerbatasanCheck', 'wp3wt_penataan_perbatasan')
            // return false 
        } else {
            rLegenda('wp3wtPerbatasanCheck')
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_penataan_perbatasan');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_penataan_perbatasan')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtPerbatasan()
    });
}

function wp3wtPerbatasan() {
    // console.log('thnData')
    // console.log(thnData)
    map.closePopup()
    wp3wt_perbatasan = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'wp3wt_penataan_perbatasan',
        styles: 'pgt:' + prefix + 'wp3wt_penataan_perbatasan',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        // bgcolor: 'FAFA7D',
        // outFields: [{
        //         "name": "wadmpr",
        //         "alias": "Provinsi"
        //     },
        //     {
        //         "name": "wadmkk",
        //         "alias": "Kabupaten"
        //     },
        //     {
        //         "name": "wadmkc",
        //         "alias": "Kecamatan"
        //     },
        //     {
        //         "name": "wadmkd",
        //         "alias": "Kelurahan"
        //     },
        //     {
        //         "name": "pmnobjname",
        //         "alias": "PMN"
        //     },
        //     {
        //         "name": "ptnobjname",
        //         "alias": "PTN"
        //     },
        //     {
        //         "name": "psnobjname",
        //         "alias": "PSN"
        //     },
        //     {
        //         "name": "pfnobjname",
        //         "alias": "PFN"
        //     },
        //     {
        //         "name": "ket",
        //         "alias": "Keterangan"
        //     },
        //     {
        //         "name": "luas_m2",
        //         "alias": "Luasan(m2)"
        //     },
        //     {
        //         "name": "tahun_data",
        //         "alias": "Tahun"
        //     }
        // ],
        title: 'WP3WT Penataan Perbatasan'

    });
    wp3wt_perbatasan.addTo(map)
    wp3wt_perbatasan.bringToFront();
    petanalisis.bringToFront();

}

function wp3wtPesisirCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_pesisir);
    if (v == 1) {
        if ($('#wp3wtPesisirCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('wp3wtPesisirCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPesisirCheck').prop('checked', true);
            aLegenda('wp3wtPesisirCheck', 'wp3wt_penataan_pesisir')

        }
    } else {
        if ($('#wp3wtPesisirCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPesisirCheck').prop('checked', true);
            aLegenda('wp3wtPesisirCheck', 'wp3wt_penataan_pesisir')

            // return false 
        } else {
            rLegenda('wp3wtPesisirCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_penataan_pesisir')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtPesisir()
    });
}

function wp3wtPesisir() {

    map.closePopup()
    wp3wt_pesisir = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'wp3wt_penataan_pesisir',
        styles: 'pgt:' + prefix + 'wp3wt_penataan_pesisir',
        transparent: true,
        tiled: true,
        cql_filter: thnData,
        authkey: gsAuthKey,
        format: 'image/png',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_m2",
                "alias": "Luasan(m2)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Pesisir'

    });
    wp3wt_pesisir.addTo(map)
    wp3wt_pesisir.bringToFront();
    petanalisis.bringToFront();

}


function wp3wtPulauCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_pulau);
    if (v == 1) {
        if ($('#wp3wtPulauCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('wp3wtPulauCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPulauCheck').prop('checked', true);
            aLegenda('wp3wtPulauCheck', 'wp3wt_penataan_pulau_kecil')

        }
    } else {
        if ($('#wp3wtPulauCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtPulauCheck').prop('checked', true);
            aLegenda('wp3wtPulauCheck', 'wp3wt_penataan_pulau_kecil')

            // return false 
        } else {
            rLegenda('wp3wtPulauCheck')
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_penataan_pulau_kecil');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_penataan_pulau_kecil')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtPulau()
    });
}

function wp3wtPulau() {

    map.closePopup()
    wp3wt_pulau = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'wp3wt_penataan_pulau_kecil',
        styles: 'pgt:' + prefix + 'wp3wt_penataan_pulau_kecil',
        tiled: true,
        transparent: true,
        cql_filter: thnData,
        authkey: gsAuthKey,
        format: 'image/png',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_m2",
                "alias": "Luasan(m2)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Pulau Pulau Kecil'

    });
    wp3wt_pulau.addTo(map)
    wp3wt_pulau.bringToFront();
    petanalisis.bringToFront();

}

function wp3wtTertentuCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_tertentu);
    if (v == 1) {
        if ($('#wp3wtTertentuCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('wp3wtTertentuCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTertentuCheck').prop('checked', true);
            aLegenda('wp3wtTertentuCheck', 'wp3wt_penataan_wilayah_tertentu')

        }
    } else {
        if ($('#wp3wtTertentuCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTertentuCheck').prop('checked', true);
            aLegenda('wp3wtTertentuCheck', 'wp3wt_penataan_wilayah_tertentu')
            // return false 
        } else {
            rLegenda('wp3wtTertentuCheck')
            return false
        }
    }
    // getYear('spatial.vm_wp3wt_penataan_wilayah_tertentu');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_penataan_wilayah_tertentu')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtTertentu()
    });
}

function wp3wtTertentu() {
    // console.log(tnhData)
    map.closePopup()
    wp3wt_tertentu = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'wp3wt_penataan_wilayah_tertentu',
        styles: 'pgt:' + prefix + 'wp3wt_penataan_wilayah_tertentu',
        tiled: true,
        transparent: true,
        cql_filter: thnData,
        authkey: gsAuthKey,
        format: 'image/png',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_m2",
                "alias": "Luasan(m2)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Wilayah Tertentu'

    });
    wp3wt_tertentu.addTo(map)
    wp3wt_tertentu.bringToFront();
    petanalisis.bringToFront();
}


function wp3wtTimbulCall(v) {
    // removeLayer()
    map.removeLayer(wp3wt_timbul);
    if (v == 1) {
        if ($('#wp3wtTimbulCheck').is(':checked')) {
            rLegenda('wp3wtTimbulCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTimbulCheck').prop('checked', true);
            aLegenda('wp3wtTimbulCheck', 'wp3wt_tnh_timbul')


        }
    } else {
        if ($('#wp3wtTimbulCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#wp3wtTimbulCheck').prop('checked', true);
            aLegenda('wp3wtTimbulCheck', 'wp3wt_tnh_timbul')

            // return false 
        } else {
            rLegenda('wp3wtTimbulCheck')
            $('#dwp3wtTimbulCheck').remove();
            return false
        }
    }

    // getYear('');
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_wp3wt_tnh_timbul')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        wp3wtTimbul()
    });
}

function wp3wtTimbul() {
    // console.log()
    map.closePopup()
    wp3wt_timbul = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'wp3wt_tnh_timbul',
        styles: 'pgt:' + prefix + 'wp3wt_tnh_timbul',
        tiled: true,
        transparent: true,
        cql_filter: thnData,
        authkey: gsAuthKey,
        format: 'image/png',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "pmnobjname",
                "alias": "PMN"
            },
            {
                "name": "ptnobjname",
                "alias": "PTN"
            },
            {
                "name": "psnobjname",
                "alias": "PSN"
            },
            {
                "name": "pfnobjname",
                "alias": "PFN"
            },
            {
                "name": "ket",
                "alias": "Keterangan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'WP3WT Penataan Tanah Timbul'

    });
    wp3wt_timbul.addTo(map)
    wp3wt_timbul.bringToFront();
    petanalisis.bringToFront();
}
// var lahanbakusawah_prov = L.tileLayer.betterWms(url, {
//     layers: 'pgt:' + prefix + 'lahanbakusawah_prov',
//     styles: 'pgt:' + prefix + 'lahanbakusawah_prov',
//     transparent: true,
//     format: 'image/png',
//     outFields: [{
//             "name": "wadmpr",
//             "alias": "Provinsi"
//         },
//         {
//             "name": "wadmkk",
//             "alias": "Kabupaten"
//         },
//         {
//             "name": "wadmkc",
//             "alias": "Kecamatan"
//         },
//         {
//             "name": "wadmkd",
//             "alias": "Kelurahan"
//         },
//         // {"name": "tipadm", "alias": "Tipadm"},
//         {
//             "name": "luas_ha",
//             "alias": "Luasan(ha)"
//         },
//         {
//             "name": "tahun_data",
//             "alias": "Tahun"
//         }
//     ],
//     title: 'Lahan Baku Sawah'
// });

function pgtlDesaCall(v) {
    // removeLayer()
    map.removeLayer(bts_desa);
    if (v == 1) {
        if ($('#pgtlDesaCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('pgtlDesaCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlDesaCheck').prop('checked', true);
            aLegenda('pgtlDesaCheck', 'batas_administrasi_desa')
        }
    } else {
        if ($('#pgtlDesaCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlDesaCheck').prop('checked', true);
            aLegenda('pgtlDesaCheck', 'batas_administrasi_desa')
            // return false 
        } else {
            rLegenda('pgtlDesaCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_pgtl_desa')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        pgtlDesa()
    });
    // pgtlDesa()
}

function pgtlDesa() {

    map.closePopup()
    bts_desa = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'batas_administrasi_desa',
        styles: 'pgt:' + prefix + 'batas_administrasi_desa',
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            // {"name": "tipadm", "alias": "Tipadm"},
            {
                "name": "luaswh",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Wilayah Administrasi Desa'
    });
    bts_desa.addTo(map)
    bts_desa.bringToFront();
    petanalisis.bringToFront();

}

function pgtlKecamatanCall(v) {
    // removeLayer()
    map.removeLayer(bts_kecamatan);
    if (v == 1) {
        if ($('#pgtlKecamatanCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('pgtlKecamatanCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlKecamatanCheck').prop('checked', true);
            aLegenda('pgtlKecamatanCheck', 'batas_administrasi_kecamatan')
        }
    } else {
        if ($('#pgtlKecamatanCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#pgtlKecamatanCheck').prop('checked', true);
            aLegenda('pgtlKecamatanCheck', 'batas_administrasi_kecamatan')
            // return false 
        } else {
            rLegenda('pgtlKecamatanCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_pgtl_kecamatan')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        pgtlKecamatan()
    });
    // pgtlKecamatan()
}

function pgtlKecamatan() {

    map.closePopup()
    bts_kecamatan = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'batas_administrasi_kecamatan',
        styles: 'pgt:' + prefix + 'batas_administrasi_kecamatan',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            // {"name": "tipadm", "alias": "Tipadm"},
            {
                "name": "luaswh",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Wilayah Administrasi Kecamatan'
    });
    bts_kecamatan.addTo(map)
    bts_kecamatan.bringToFront();
    petanalisis.bringToFront();
}


function kemampuanTanahCall(v) {
    // removeLayer()
    map.removeLayer(kemampuan_tanah);
    if (v == 1) {
        if ($('#kemampuanTanahCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('kemampuanTanahCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#kemampuanTanahCheck').prop('checked', true);
            aLegenda('kemampuanTanahCheck', 'kemampuan_tanah')
        }
    } else {
        if ($('#kemampuanTanahCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#kemampuanTanahCheck').prop('checked', true);
            aLegenda('kemampuanTanahCheck', 'kemampuan_tanah')
            // return false 
        } else {
            rLegenda('kemampuanTanahCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_kemampuan_tanah')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        kemampuanTanah()
    });
}

function kemampuanTanah() {

    map.closePopup()
    kemampuan_tanah = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'kemampuan_tanah',
        styles: 'pgt:' + prefix + 'kemampuan_tanah',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        tiled: true,
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "b_name",
                "alias": "Lereng"
            },
            {
                "name": "u_name",
                "alias": "Kedalaman Efektif"
            },
            {
                "name": "x_name",
                "alias": "Tekstur"
            },
            {
                "name": "d_name",
                "alias": "Drainase"
            },
            {
                "name": "e_name",
                "alias": "Erosi"
            },
            {
                "name": "l_name",
                "alias": "Faktor Pembatas"
            },
            // {"name": "tipadm", "alias": "Tipadm"},
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Kemampuan Tanah'
    });
    kemampuan_tanah.addTo(map)

    // kemampuan_tanah.bringToFront();
    // Bring polygonLayer to the top
    petanalisis.bringToFront();
    petanalisis.bringToFront();

}




function npgtProvCall(v) {
    // removeLayer()
    map.removeLayer(npgt_prov);
    if (v == 1) {
        if ($('#npgtProvCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('npgtProvCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#npgtProvCheck').prop('checked', true);
            aLegenda('npgtProvCheck', 'npgt_prov')

        }
    } else {
        if ($('#npgtProvCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#npgtProvCheck').prop('checked', true);
            aLegenda('npgtProvCheck', 'npgt_prov')

            // return false 
        } else {
            rLegenda('npgtProvCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_npgt_prov')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        npgtProv()
    });
}

function npgtProv() {
    map.closePopup()
    npgt_prov = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'npgt_prov',
        styles: 'pgt:' + prefix + 'npgt_prov',
        tiled: true,
        transparent: true,
        cql_filter: thnData,
        authkey: gsAuthKey,
        format: 'image/png',
        outFields: [{
                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "wadmkd",
                "alias": "Kelurahan"
            },
            {
                "name": "gname100",
                "alias": "Penggunaan Lama"
            },
            // {
            //     "name": "qname100",
            //     "alias": "Penggunaan Akhir"
            // },
            {
                "name": "gq_name",
                "alias": "Perubahan"
            },
            {
                "name": "wname",
                "alias": "RTRW"
            },
            {
                "name": "nname",
                "alias": "Kesesuaian"
            },
            {
                "name": "oname",
                "alias": "Gambaran Umum"
            },
            {
                "name": "vname",
                "alias": "Ketersediaan"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "kaw_hutan",
                "alias": "Kawasan"
            }
        ],
        title: 'NPGT Provinsi'
    });
    npgt_prov.addTo(map);
    npgt_prov.bringToFront();
    petanalisis.bringToFront();

}

function tnbhCall(v) {
    // removeLayer()
    map.removeLayer(tanahnegara_prov_tnbh);
    if (v == 1) {
        if ($('#tnbhCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('tnbhCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#tnbhCheck').prop('checked', true);
            aLegenda('tnbhCheck', 'tanahnegara_prov_tnbh')

        }
    } else {
        if ($('#tnbhCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            $('#tnbhCheck').prop('checked', true);
            aLegenda('tnbhCheck', 'tanahnegara_prov_tnbh')
            // return false 
        } else {
            rLegenda('tnbhCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_tnbh')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        tnbh()
    });
    // tnbh()
}

function tnbh() {
    map.closePopup()

    tanahnegara_prov_tnbh = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'tanahnegara_prov_tnbh',
        styles: 'pgt:' + prefix + 'tanahnegara_prov_tnbh',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        zIndex: 600,
        opacity: 1,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "arahan",
                "alias": "Arahan"
            },
            {
                "name": "riwayat",
                "alias": "Riwayat"
            },
            {
                "name": "oname19",
                "alias": "Gambaran Umum"
            },
            {
                "name": "qname19",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "pmanfaatan",
                "alias": "Pemanfaatan"
            },
            {
                "name": "wname19",
                "alias": "RTRW"
            },
            {
                "name": "kegiatan",
                "alias": "Kegiatan"
            },
            {
                "name": "aprogram",
                "alias": "Program"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Tanah Negara Bekas Hak'
    });
    tanahnegara_prov_tnbh.addTo(map)

    tanahnegara_prov_tnbh.bringToFront();
    petanalisis.bringToFront();

    //  tanahnegara_prov_tnbh.infoSetting = {
    // 	infoFields: ['tahun_data', 'qname100', 'luas_ha'],
    // 	infoLabels: ['Tahun', 'Penggunaan', 'Luas'],
    // 	headerFields: '',
    // 	headerPrefix: '',
    // 	headerSuffix: '',
    // 	callbackFields: [],
    //     headerLabel: 'Tanah Negara Bekas Hak',
    // 	cbInformasi: function (data, infoContent, latlng, feat) {
    //     //    sidebar.close();
    // 		// (tanahnegara_prov_tnbh.infoSetting.headerLabel)
    // 		// $("#informasi div").empty();





    // 		//return list;

    // 		// $('#infoobjek').append('<h5><b>TNBH<b></h5>');
    // 		//$("#infoobjek").html("");

    // 		if(tanahnegara_prov_tnbh) {
    // 			var list = document.getElementById('infoobjek');	
    // 		    list.innerHTML = infoContent;
    // 			$('#infoobjek').html('<hr style="border-width: 4px;">');

    // 		} else {
    // 			$("#infoobjek").append(infoContent);
    // 			$('#infoobjek').append('<hr style="border-width: 4px;">');

    // 		}

    // 		// sidebar.open("informasi");




    // 		// console.log('3--showInfo ' + infoContent);
    // 		//$('#infoobjek').append('<h5><b>TNBH<b></h5>');

    // 		//$("#infoobjek").innerHTML(infoContent);
    // 		//$('#infoobjek').append('<hr style="border-width: 4px;">');




    // 	}
    // };
};


function tntkCall(v) {
    // removeLayer()
    map.removeLayer(tanahnegara_prov_tntk);
    if (v == 1) {
        if ($('#tntkCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            rLegenda('tntkCheck')
            return false
        } else {
            // $('.checkPeta').prop('checked', false);
            $('#tntkCheck').prop('checked', true);
            aLegenda('tntkCheck', 'tanahnegara_prov_tntk')
        }
    } else {
        if ($('#tntkCheck').is(':checked')) {
            // $('.checkPeta').prop('checked', false);
            aLegenda('tntkCheck', 'tanahnegara_prov_tntk')
            $('#tntkCheck').prop('checked', true);
            // return false 
        } else {
            rLegenda('tntkCheck')
            return false
        }
    }
    var url = "<?php echo base_url('lookup/getYear/spatial.vm_tntk')?>";
    $.get(url, function(data) {
        data = JSON.parse(data)
        $('#thnSelect').html(data.year)
        setFilter()
        tntk()
    });
    // tntk()
}

function tntk() {
    map.closePopup()
    tanahnegara_prov_tntk = L.tileLayer.betterWms(url, {
        layers: 'pgt:' + prefix + 'tanahnegara_prov_tntk',
        styles: 'pgt:' + prefix + 'tanahnegara_prov_tntk',
        tiled: true,
        transparent: true,
        format: 'image/png',
        cql_filter: thnData,
        authkey: gsAuthKey,
        zIndex: 600,
        opacity: 1,
        outFields: [{

                "name": "wadmpr",
                "alias": "Provinsi"
            },
            {
                "name": "wadmkk",
                "alias": "Kabupaten"
            },
            {
                "name": "wadmkc",
                "alias": "Kecamatan"
            },
            {
                "name": "arahan",
                "alias": "Arahan"
            },
            {
                "name": "riwayat",
                "alias": "Riwayat"
            },
            {
                "name": "oname",
                "alias": "Gambaran Umum"
            },
            {
                "name": "qname",
                "alias": "Penggunaan Akhir"
            },
            {
                "name": "pmanfaatan",
                "alias": "Pemanfaatan"
            },
            {
                "name": "wname",
                "alias": "RTRW"
            },
            {
                "name": "kegiatan",
                "alias": "Kegiatan"
            },
            {
                "name": "aprogram",
                "alias": "Program"
            },
            {
                "name": "luas_ha",
                "alias": "Luasan(ha)"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            },
            {
                "name": "pengelolah",
                "alias": "Pengolahan"
            },
            {
                "name": "program",
                "alias": "Program"
            },
            {
                "name": "tahun_data",
                "alias": "Tahun"
            }
        ],
        title: 'Tanah Kritis'
    });

    tanahnegara_prov_tntk.addTo(map)

    tanahnegara_prov_tntk.bringToFront();
    petanalisis.bringToFront();
    // tanahnegara_prov_tntk.infoSetting = {
    // 	infoFields: ['tahun_data', 'qname100', 'luas_ha'],
    // 	infoLabels: ['Tahun', 'Penggunaan', 'Luas'],
    // 	headerFields: '',
    // 	headerPrefix: '',
    // 	headerSuffix: '',
    // 	callbackFields: ['objectid'],
    //     headerLabel: 'Tanah Negara Tanah Kritis',
    // 	cbInformasi: function (data, infoContent, latlng, feat) {
    //     //    sidebar.close();
    // 		// (tanahnegara_prov_tnbh.infoSetting.headerLabel)
    // 		// $("#informasi div").empty();

    // 	//	console.log(feat);


    // 		// var list = document.getElementById('infoobjek');	
    // 		// list.innerHTML = '';


    // 		//return list;

    // 		// $('#infoobjek').append('<h5><b>TNTK<b></h5>');


    // 		if(tanahnegara_prov_tntk) {
    // 			var list = document.getElementById('infoobjek');	
    // 		    list.innerHTML = infoContent;
    // 			$('#infoobjek').html('<hr style="border-width: 4px;">');

    // 		} else {
    // 			$("#infoobjek").append(infoContent);
    // 			$('#infoobjek').append('<hr style="border-width: 4px;">');

    // 		}




    // 		// console.log('3--showInfo ' + infoContent);
    // 		//$('#infoobjek').append('<h5><b>TNBH<b></h5>');

    // 		//$("#infoobjek").innerHTML(infoContent);
    // 		//$('#infoobjek').append('<hr style="border-width: 4px;">');




    // 	}
    // };
};

function thn_filter(el) {

    // console.log(val);

    //sql_value = document.getElementById('tahun').value;
    thnData = 'tahun_data= ' + el.value;
    // console.log(thnData);

    //Remove old schools to prevent "a new point is added but the old one is not removed"
    //check if map has schools layer
    if (map.hasLayer(tanahnegara_prov_tnbh)) {
        map.removeLayer(tanahnegara_prov_tnbh);
        //if so, remove previously added schools layer before loading schools with new cql filter
    };

    if (map.hasLayer(tanahnegara_prov_tntk)) {
        map.removeLayer(tanahnegara_prov_tntk);

        //if so, remove previously added schools layer before loading schools with new cql filter
    };

    tnbh(thnData);
    tntk(thnData);
    tanahnegara_prov_tnbh.addTo(map);
    tanahnegara_prov_tnbh.bringToFront();
    tanahnegara_prov_tntk.addTo(map);
    tanahnegara_prov_tntk.bringToFront();
    petanalisis.bringToFront();
}


// var ptp_p3t  = L.Geoserver.wfs("http://103.6.53.254:4980/geoserver/pgt/wfs", {
// 	layers: "pgt:ptp_p3t",
// 	style: {
//     color: "black",
//     fillOpacity: "0",
//     opacity: "0.5",
// 	},
// 	onEachFeature: function (feature, layer) {

// 		var popup = 'Provinsi: ' + feature.properties.wadmpr + '<br>' +
//                        'Kabupaten: ' + feature.properties.wadmkk +  '<br>' +
//                        'Tahun Data: ' + feature.properties.tahun_data +  '<br>' +
// 					   'Luas(Ha):' + feature.properties.luas_ha +  '<br>' +
// 					   'Penggunaan: ' + feature.properties.qname50 +  '<br>' +
// 					   'No PTP:' + feature.properties.no_ptp
//         layer.bindPopup(popup);

// 	}
// });



// var wp3wt_pfn  = L.Geoserver.wfs("http://103.6.53.254:4980/geoserver/pgt/wfs", {
// 	layers: "pgt:wp3wt_pfn",
// 	style: {
//     color: "black",
//     fillOpacity: "0",
//     opacity: "0.5",
// 	},
// 	onEachFeature: function (feature, layer) {

// 		var popup2 = 'Provinsi: ' + feature.properties.wadmpr + '<br>' +
//                        'Kabupaten: ' + feature.properties.wadmkk +  '<br>' +
//                        'Tahun Data: ' + feature.properties.tahun_data +  '<br>' +
// 					   'Luas(Ha):' + feature.properties.luas_ha +  '<br>' +
// 					   'Pemanfaatan: ' + feature.properties.pfnobjname
//         layer.bindPopup(popup2);

// 	}
// });



// var npgt_prov  = L.Geoserver.wfs("http://103.6.53.254:4980/geoserver/pgt/wfs", {
// 	layers: "pgt:npgt_prov",
// 	style: {
//     color: "blue",
//     fillOpacity: "0",
//     opacity: "0.5",
// 	},
// 	onEachFeature: function (feature, layer) {
// 		var popupTxt = 'Provinsi: ' + feature.properties.wadmpr + '<br>' +
//                        'Kabupaten: ' + feature.properties.wadmkk +  '<br>' +
// 					   'Penggunaan Terakhir: ' + feature.properties.qname100 + '<br>' +
//                        'Perubahan: ' + feature.properties.gq_name +  '<br>' +
// 					   'RTRW: ' + feature.properties.wname +  '<br>' +
// 					   'Kesesuaian: ' + feature.properties.nname +  '<br>' +
// 					   'Penguasaan: ' + feature.properties.oname +  '<br>' +
// 					   'Ketersediaan: ' + feature.properties.vname +  '<br>' +
//                        'Tahun Data: ' + feature.properties.tahun_data
//         layer.bindPopup(popupTxt);

// 	}
// });

// map.fitBounds(ptp_p3t.getBounds());


var googleHybrid = L.tileLayer('http://{s}.google.com/vt?lyrs=s,h&x={x}&y={y}&z={z}', {
    subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
});
var baseTree = [{
        label: 'Peta Dasar',
        layer: osmLayer
    },
    {
        label: 'Map Box',
        layer: gl
    },
    {
        label: 'Google Hybrid',
        layer: googleHybrid
    }
];
var role = "<?=$this->session->users['id_user_group']?>"

if (role == 1 || role == 7 || role == 8 || role == 9 || role == 10) {

    var overlaysTree = [
        // {
        //     label: 'Index',
        //     layer: p2

        // },
        {
            label: 'Neraca Penatagunaan Tanah Regional',
            selectAllCheckbox: false,
            children: [{
                    label: 'NPGT Provinsi',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>' +
                            '<input class="checkPeta" style="boder:none" onclick="npgtProvCall(0)" type="checkbox" id="npgtProvCheck">' +
                            '<button style="border:none;background-color:transparent" onclick="npgtProvCall(1)">NPGT Provinsi</button></div>',
                        // label: 'NPGT Provinsi',
                        // layer: npgt_prov
                    }, ]
                },
                {
                    label: 'NPGT Kabupaten/Kota',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>' +
                            '<input class="checkPeta" type="checkbox" onclick="kabkotACall(0)" id="kabkotACheck">' +
                            '<button style="border:none;background-color:transparent" onclick="kabkotACall(1)">Kabupaten/Kota </button></div>',

                        // label: 'NPGT Kabupaten/Kota',
                        // layer: npgt_kabkota
                    }, ]
                },

                {
                    label: 'NPGT Kecamatan',
                    selectAllCheckbox: false,
                    children: [{
                            label: '<div>' +
                                '<input class="checkPeta" type="checkbox" onclick="kecACall(0)" id="kecACheck">' +
                                '<button style="border:none;background-color:transparent" onclick="kecACall(1)">NPGT Kecamatan </button></div>',

                            // label: 'Administrasi(A)',
                            // layer: npgt_kec_a
                        },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecHCall(0)" id="kecHCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecHCall(1)">Sungai(H) </button></div>',
                        //     // label: 'Sungai(H)',
                        //     //     layer: npgt_kec_h
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecKCall(0)" id="kecKCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecKCall(1)">Jalan(K) </button></div>',
                        //     // label: 'Jalan(K)',
                        //     //     layer: npgt_kec_k
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecNCall(0)" id="kecNCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecNCall(1)">Kesesuaian(N) </button></div>',
                        //     // label: 'Kesesuaian(N)',
                        //     //     layer: npgt_kec_n
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecOCall(0)" id="kecOCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecOCall(1)">Penggunaan Tanah Terakir(O) </button></div>',
                        //     // label: 'Gambaran Umum Penguasaan Tanah(O)',
                        //     // layer: npgt_kec_o
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecQCall(0)" id="kecQCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecQCall(1)">Penggunaan Tanah Terakhir(Q) </button></div>',
                        //     // label: 'Penggunaan Tanah Terakhir(Q)',
                        //     //     layer: npgt_kec_q
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecVCall(0)" id="kecVCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecVCall(1)">Ketersediaan(V) </button></div>',
                        //     // label: 'Ketersediaan(V)',
                        //     //     layer: npgt_kec_v
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecWCall(0)" id="kecWCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecWCall(1)">RTRW(W) </button></div>',
                        //     // label: 'RTRW(W)',
                        //     // layer: npgt_kec_w
                        // }
                    ]
                },
            ],
        },
        {
            label: 'Neraca Penatagunaan Tanah Sektoral',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="npgtKebunCall(0)" id="npgtKebunCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="npgtKebunCall(1)">Npgt Perkebunan </button></div>',
                    //         label: 'NPGT Perkebunan',
                    // layer: npgt_perkebunan
                }
                // {
                //     label: 'NPGT Industri',
                //     layer: null
                // }, //npgt_industri},
                // {
                //     label: 'NPGT Perumahan',
                //     layer: null
                // }, //npgt_perumahan}
            ]
        },
        {
            label: 'Pertimbangan Teknis Pertanahan',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpilProvCall(0)" id="ptpilProvCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpilProvCall(1)">PTP Izin Lokasi Provinsi-AOI </button></div>',
                    // label: 'PTP Izin Lokasi Provinsi-AOI',
                    // layer: ptpil_prov
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpilProvPoiCall(0)" id="ptpilProvPoiCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpilProvPoiCall(1)">PTP Izin Lokasi Provinsi-POI </button></div>',
                    // label: 'PTP Izin Lokasi Provinsi-POI',
                    // layer: ptpil_prov_tk
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpP3tCall(0)" id="ptpP3tCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpP3tCall(1)">PTP IPPT Provinsi </button></div>',
                    // label: 'PTP IPPT Provinsi',
                    // layer: ptp_p3t
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpPkP3tCall(0)" id="ptpPkP3tCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpPkP3tCall(1)">PTP P Kebijakan P3T </button></div>',
                    // label: 'PTP P Kebijakan P3T',
                    // layer: ptp_pk_p3t
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpBerusahaCall(0)" id="ptpBerusahaCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpBerusahaCall(1)">PTP PKKPR Kegiatan Berusaha </button></div>',
                    // label: 'PTP PKKPR Kegiatan Berusaha',
                    // layer: ptp_kppr_berusaha
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpNonBerusahaCall(0)" id="ptpNonBerusahaCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpNonBerusahaCall(1)">PTP PKKPR Kegiatan Non Berusaha </button></div>',
                    // label: 'PTP PKKPR Kegiatan Non Berusaha',
                    // layer: ptp_kppr_non_berusaha
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpStranasCall(0)" id="ptpStranasCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpStranasCall(1)">PTP P/RKKPR Kegiatan Stranas </button></div>',
                    // label: 'PTP P/RKKPR Kegiatan Stranas',
                    // layer: ptp_kppr_stranas
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpTnhTimbulCall(0)" id="ptpTnhTimbulCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpTnhTimbulCall(1)">PTP PSRP Tanah Timbul </button></div>',
                    // label: 'PTP PSRP Tanah Timbul',
                    // layer: ptp_tnh_timbul
                },
            ]
        },
        {
            label: 'Penataan WP3WT',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPerbatasanCall(0)" id="wp3wtPerbatasanCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPerbatasanCall(1)">Penataan Perbatasan </button></div>',
                    // label: 'Penataan Perbatasan',
                    // layer: wp3wt_perbatasan
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPulauCall(0)" id="wp3wtPulauCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPulauCall(1)">Penataan Pulau Pulau Kecil </button></div>',
                    // label: 'Penataan Pulau Pulau Kecil',
                    // layer: wp3wt_pulau
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtTertentuCall(0)" id="wp3wtTertentuCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtTertentuCall(1)">Penataan Wilayah Tertentu </button></div>',
                    // label: 'Penataan Wilayah Tertentu',
                    // layer: wp3wt_tertentu
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPesisirCall(0)" id="wp3wtPesisirCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPesisirCall(1)">Penataan Pesisir </button></div>',
                    // label: 'Penataan Pesisir',
                    // layer: wp3wt_pesisir
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtTimbulCall(0)" id="wp3wtTimbulCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtTimbulCall(1)">Tanah Timbul </button></div>',
                    // label: 'Tanah Timbul',
                    // layer: wp3wt_timbul
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPpkPoiCall(0)" id="wp3wtPpkPoiCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPpkPoiCall(1)">Sebaran Pulau Pulau Kecil (POI) </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil (POI)',
                    // layer: wp3wt_ppk_poi
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="Wp3wtPpkPolygonCall(0)" id="Wp3wtPpkPolygonCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="Wp3wtPpkPolygonCall(1)">Sebaran Pulau Pulau Kecil </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil',
                    // layer: wp3wt_ppk_polygon
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPpktCall(0)" id="wp3wtPpktCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPpktCall(1)">Sebaran Pulau Pulau Kecil Terluar </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil Terluar',
                    // layer: wp3wt_ppkt
                }


            ]
        },
        {
            label: 'Tanah Negara Bekas Hak/Bekas Kawasan/Tanah Kritis',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="tnbhCall(0)" id="tnbhCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="tnbhCall(1)">Tanah Negara Bekas Hak </button></div>',
                    // label: 'Tanah Negara Bekas Hak',
                    // layer: tanahnegara_prov_tnbh
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="tnbkCall(0)" id="tnbkCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="tnbkCall(1)">Tanah Negara Bekas Kawasan </button></div>',
                    // label: 'Tanah Negara Bekas Kawasan',
                    // layer: tanahnegara_prov_tnbk
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="tntkCall(0)" id="tntkCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="tntkCall(1)">Tanah Negara Tanah Kritis </button></div>',
                    // label: 'Tanah Kritis',
                    // layer: tanahnegara_prov_tntk
                },
            ]
        },
        {
            label: 'Monitoring PPT',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="mpptCall(0)" id="mpptCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="mpptCall(1)">Monitoring PPT </button></div>',
                //     label: 'Monitoring PPT',
                // layer: mppt_prov
            }, ]
        },
        {
            label: 'Kemampuan Tanah',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="kemampuanTanahCall(0)" id="kemampuanTanahCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="kemampuanTanahCall(1)">Kemampuan Tanah </button></div>',
                //     label: 'Kemampuan Tanah',
                // layer: kemampuan_tanah
            }, ]
        },
        {
            label: 'Penatagunaan Tanah Lainnya',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="pgtlDesaCall(0)" id="pgtlDesaCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="pgtlDesaCall(1)">Wilayah Administrasi Desa </button></div>',
                    //     label: 'Wilayah Administrasi Desa',
                    // layer: bts_desa
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="pgtlKecamatanCall(0)" id="pgtlKecamatanCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="pgtlKecamatanCall(1)">Wilayah Administrasi Kecamatan </button></div>',
                    //     label: 'Wilayah Administrasi Kecamatan',
                    // layer: bts_kecamatan
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="pgtlRtrwCall(0)" id="pgtlRtrwCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="pgtlRtrwCall(1)">RTRW </button></div>',
                    //     label: 'RTRW',
                    // layer: rtrw
                },
                {

                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="landUseCall(0)" id="landUseCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="landUseCall(1)">Penggunaan Tanah</button></div>',

                }
            ]
        },
        {
            label: 'Lahan Baku Sawah',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="lahanBakuCall(0)" id="lahanBakuCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="lahanBakuCall(1)">Lahan Baku Sawah </button></div>',
                //     label: 'Lahan Baku Sawah',
                // layer: lahanbakusawah_prov
            }, ]
        },
        {
            label: 'Sungai',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="sungaiCall(0)" id="sungaiCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="sungaiCall(1)">Sungai </button></div>',
                //     label: 'Lahan Baku Sawah',
                // layer: lahanbakusawah_prov
            }, ]
        },
        {
            label: 'Jalan',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="jalanCall(0)" id="jalanCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="jalanCall(1)">Jalan</button></div>',
                //     label: 'Lahan Baku Sawah',
                // layer: lahanbakusawah_prov
            }, ]
        },
        {
            label: 'Monitoring Layanan PTP',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="buildMap(0)" id="buildMapCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="buildMapCall(1)">PTP P KKPR Berusaha</button></div>',
                //     label: 'Lahan Baku Sawah',
                // layer: lahanbakusawah_prov
            }, ]
        },

    ]
}
if (role == 2) {


    var overlaysTree = [{
            label: 'Pertimbangan Teknis Pertanahan',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpilProvCall(0)" id="ptpilProvCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpilProvCall(1)">PTP Izin Lokasi Provinsi-AOI </button></div>',
                    // label: 'PTP Izin Lokasi Provinsi-AOI',
                    // layer: ptpil_prov
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpilProvPoiCall(0)" id="ptpilProvPoiCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpilProvPoiCall(1)">PTP Izin Lokasi Provinsi-POI </button></div>',
                    // label: 'PTP Izin Lokasi Provinsi-POI',
                    // layer: ptpil_prov_tk
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpP3tCall(0)" id="ptpP3tCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpP3tCall(1)">PTP IPPT Provinsi </button></div>',
                    // label: 'PTP IPPT Provinsi',
                    // layer: ptp_p3t
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpPkP3tCall(0)" id="ptpPkP3tCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpPkP3tCall(1)">PTP P Kebijakan P3T </button></div>',
                    // label: 'PTP P Kebijakan P3T',
                    // layer: ptp_pk_p3t
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpBerusahaCall(0)" id="ptpBerusahaCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpBerusahaCall(1)">PTP PKKPR Kegiatan Berusaha </button></div>',
                    // label: 'PTP PKKPR Kegiatan Berusaha',
                    // layer: ptp_kppr_berusaha
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpNonBerusahaCall(0)" id="ptpNonBerusahaCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpNonBerusahaCall(1)">PTP PKKPR Kegiatan Non Berusaha </button></div>',
                    // label: 'PTP PKKPR Kegiatan Non Berusaha',
                    // layer: ptp_kppr_non_berusaha
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpStranasCall(0)" id="ptpStranasCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpStranasCall(1)">PTP P/RKKPR Kegiatan Stranas </button></div>',
                    // label: 'PTP P/RKKPR Kegiatan Stranas',
                    // layer: ptp_kppr_stranas
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="ptpTnhTimbulCall(0)" id="ptpTnhTimbulCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="ptpTnhTimbulCall(1)">PTP PSRP Tanah Timbul </button></div>',
                    // label: 'PTP PSRP Tanah Timbul',
                    // layer: ptp_tnh_timbul
                },
            ]
        },
        {
            label: 'Tanah Negara Bekas Hak/Bekas Kawasan/Tanah Kritis',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="tnbhCall(0)" id="tnbhCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="tnbhCall(1)">Tanah Negara Bekas Hak </button></div>',
                    // label: 'Tanah Negara Bekas Hak',
                    // layer: tanahnegara_prov_tnbh
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="tnbkCall(0)" id="tnbkCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="tnbkCall(1)">Tanah Negara Bekas Kawasan </button></div>',
                    // label: 'Tanah Negara Bekas Kawasan',
                    // layer: tanahnegara_prov_tnbk
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="tntkCall(0)" id="tntkCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="tntkCall(1)">Tanah Negara Tanah Kritis </button></div>',
                    // label: 'Tanah Kritis',
                    // layer: tanahnegara_prov_tntk
                },
            ]
        },
        {
            label: 'Monitoring PPT',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="mpptCall(0)" id="mpptCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="mpptCall(1)">MOnitoring PPT </button></div>',
                //     label: 'Monitoring PPT',
                // layer: mppt_prov
            }, ]
        },
        {
            label: 'Kemampuan Tanah',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="kemampuanTanahCall(0)" id="kemampuanTanahCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="kemampuanTanahCall(1)">Kemampuan Tanah </button></div>',
                //     label: 'Kemampuan Tanah',
                // layer: kemampuan_tanah
            }, ]
        },
        {
            label: 'Penatagunaan Tanah Lainnya',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="pgtlDesaCall(0)" id="pgtlDesaCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="pgtlDesaCall(1)">Wilayah Administrasi Desa </button></div>',
                    //     label: 'Wilayah Administrasi Desa',
                    // layer: bts_desa
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="pgtlKecamatanCall(0)" id="pgtlKecamatanCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="pgtlKecamatanCall(1)">Wilayah Administrasi Kecamatan </button></div>',
                    //     label: 'Wilayah Administrasi Kecamatan',
                    // layer: bts_kecamatan
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="pgtlRtrwCall(0)" id="pgtlRtrwCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="pgtlRtrwCall(1)">RTRW </button></div>',
                    //     label: 'RTRW',
                    // layer: rtrw
                }
            ]
        }
    ]
}

if (role == 3) {

    var overlaysTree = [{
            label: 'Neraca Penatagunaan Tanah Regional',
            selectAllCheckbox: false,
            children: [{
                    label: 'NPGT Provinsi',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>' +
                            '<input class="checkPeta" style="boder:none" onclick="npgtProvCall(0)" type="checkbox" id="npgtProvCheck">' +
                            '<button style="border:none;background-color:transparent" onclick="npgtProvCall(1)">NPGT Provinsi</button></div>',
                        // label: 'NPGT Provinsi',
                        // layer: npgt_prov
                    }, ]
                },
                {
                    label: 'NPGT Kabupaten/Kota',
                    selectAllCheckbox: false,
                    children: [{
                        label: '<div>' +
                            '<input class="checkPeta" type="checkbox" onclick="kabkotACall(0)" id="kabkotACheck">' +
                            '<button style="border:none;background-color:transparent" onclick="kabkotACall(1)">Kabupaten/Kota </button></div>',

                        // label: 'NPGT Kabupaten/Kota',
                        // layer: npgt_kabkota
                    }, ]
                },

                {
                    label: 'NPGT Kecamatan',
                    selectAllCheckbox: false,
                    children: [{
                            label: '<div>' +
                                '<input class="checkPeta" type="checkbox" onclick="kecACall(0)" id="kecACheck">' +
                                '<button style="border:none;background-color:transparent" onclick="kecACall(1)">NPGT Kecamatan </button></div>',

                            // label: 'Administrasi(A)',
                            // layer: npgt_kec_a
                        },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecHCall(0)" id="kecHCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecHCall(1)">Sungai(H) </button></div>',
                        //     // label: 'Sungai(H)',
                        //     //     layer: npgt_kec_h
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecKCall(0)" id="kecKCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecKCall(1)">Jalan(K) </button></div>',
                        //     // label: 'Jalan(K)',
                        //     //     layer: npgt_kec_k
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecNCall(0)" id="kecNCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecNCall(1)">Kesesuaian(N) </button></div>',
                        //     // label: 'Kesesuaian(N)',
                        //     //     layer: npgt_kec_n
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecOCall(0)" id="kecOCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecOCall(1)">Penggunaan Tanah Terakir(O) </button></div>',
                        //     // label: 'Gambaran Umum Penguasaan Tanah(O)',
                        //     // layer: npgt_kec_o
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecQCall(0)" id="kecQCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecQCall(1)">Penggunaan Tanah Terakhir(Q) </button></div>',
                        //     // label: 'Penggunaan Tanah Terakhir(Q)',
                        //     //     layer: npgt_kec_q
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecVCall(0)" id="kecVCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecVCall(1)">Ketersediaan(V) </button></div>',
                        //     // label: 'Ketersediaan(V)',
                        //     //     layer: npgt_kec_v
                        // },
                        // {
                        //     label: '<div>' +
                        //         '<input class="checkPeta" type="checkbox" onclick="kecWCall(0)" id="kecWCheck">' +
                        //         '<button style="border:none;background-color:transparent" onclick="kecWCall(1)">RTRW(W) </button></div>',
                        //     // label: 'RTRW(W)',
                        //     // layer: npgt_kec_w
                        // }
                    ]
                },
            ],
        },
        {
            label: 'Neraca Penatagunaan Tanah Sektoral',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="npgtKebunCall(0)" id="npgtKebunCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="npgtKebunCall(1)">Npgt Perkebunan </button></div>',
                    //         label: 'NPGT Perkebunan',
                    // layer: npgt_perkebunan
                }
                // {
                //     label: 'NPGT Industri',
                //     layer: null
                // }, //npgt_industri},
                // {
                //     label: 'NPGT Perumahan',
                //     layer: null
                // }, //npgt_perumahan}
            ]
        },
        {
            label: 'Lahan Baku Sawah',
            selectAllCheckbox: false,
            children: [{
                label: '<div>' +
                    '<input class="checkPeta" type="checkbox" onclick="lahanBakuCall(0)" id="lahanBakuCheck">' +
                    '<button style="border:none;background-color:transparent" onclick="lahanBakuCall(1)">Lahan Baku Sawah </button></div>',
                //     label: 'Lahan Baku Sawah',
                // layer: lahanbakusawah_prov
            }, ]
        },
    ]
}

if (role == 4) {

    var overlaysTree = [

        {
            label: 'Penataan WP3WT',
            selectAllCheckbox: false,
            children: [{
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPerbatasanCall(0)" id="wp3wtPerbatasanCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPerbatasanCall(1)">Penataan Perbatasan </button></div>',
                    // label: 'Penataan Perbatasan',
                    // layer: wp3wt_perbatasan
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPulauCall(0)" id="wp3wtPulauCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPulauCall(1)">Penataan Pulau Pulau Kecil </button></div>',
                    // label: 'Penataan Pulau Pulau Kecil',
                    // layer: wp3wt_pulau
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtTertentuCall(0)" id="wp3wtTertentuCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtTertentuCall(1)">Penataan Wilayah Tertentu </button></div>',
                    // label: 'Penataan Wilayah Tertentu',
                    // layer: wp3wt_tertentu
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPesisirCall(0)" id="wp3wtPesisirCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPesisirCall(1)">Penataan Pesisir </button></div>',
                    // label: 'Penataan Pesisir',
                    // layer: wp3wt_pesisir
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtTimbulCall(0)" id="wp3wtTimbulCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtTimbulCall(1)">Tanah Timbul </button></div>',
                    // label: 'Tanah Timbul',
                    // layer: wp3wt_timbul
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPpkPoiCall(0)" id="wp3wtPpkPoiCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPpkPoiCall(1)">Sebaran Pulau Pulau Kecil (POI) </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil (POI)',
                    // layer: wp3wt_ppk_poi
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="Wp3wtPpkPolygonCall(0)" id="Wp3wtPpkPolygonCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="Wp3wtPpkPolygonCall(1)">Sebaran Pulau Pulau Kecil </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil',
                    // layer: wp3wt_ppk_polygon
                },
                {
                    label: '<div>' +
                        '<input class="checkPeta" type="checkbox" onclick="wp3wtPpktCall(0)" id="wp3wtPpktCheck">' +
                        '<button style="border:none;background-color:transparent" onclick="wp3wtPpktCall(1)">Sebaran Pulau Pulau Kecil Terluar </button></div>',
                    // label: 'Sebaran Pulau Pulau Kecil Terluar',
                    // layer: wp3wt_ppkt
                }


            ]
        }
    ]
}


var lay = L.control.layers.tree(baseTree, overlaysTree, {
    namedToggle: true,
    selectorBack: false,
    closedSymbol: '&#8862; &#x1f5c0;',
    openedSymbol: '&#8863; &#x1f5c1;',
    // collapseAll: 'Collapse all',
    // expandAll: 'Expand all',
    collapsed: false,
});

// scale.addTo(map);
L.control.betterscale({
    imperial: false, // Disable the imperial system
    metric: true, // Enable the metric system (meters)
}).addTo(map);
zoombox.addTo(map);
map.on('baselayerchange', function(event) {
    provKabFilter()
});

lay.addTo(map).collapseTree().expandSelected().collapseTree(true);


// L.DomEvent.on(L.DomUtil.get('onlysel'), 'click', function() {
//     lay.collapseTree(true).expandSelected(true);
// });




// console.log(lay.getOverlays())

// var baseLayers = [
// 	{
// 		name: "Open Street Map",
// 		layer: osmLayer
// 	}
// 	// {
// 	// 	name: "Hiking",
// 	// 	layer: L.tileLayer("https://toolserver.org/tiles/hikebike/{z}/{x}/{y}.png")
// 	// },
// 	// {
// 	// 	name: "Aerial",
// 	// 	layer: L.tileLayer('https://otile{s}.mqcdn.com/tiles/1.0.0/{type}/{z}/{x}/{y}.{ext}', {
// 	// 		type: 'sat',
// 	// 		ext: 'jpg',
// 	// 		attribution: 'Tiles Courtesy of <a href="https://www.mapquest.com/">MapQuest</a> &mdash; Portions Courtesy NASA/JPL-Caltech and U.S. Depart. of Agriculture, Farm Service Agency',
// 	// 		subdomains: '1234'
// 	// 	})
// 	// }
// 	// {
// 	// 	group: "Road Layers",
// 	// 	collapsed: true,
// 	// 	layers: [
// 	// 		{
// 	// 			name: "Open Cycle Map",
// 	// 			layer: L.tileLayer('https://{s}.tile.opencyclemap.org/cycle/{z}/{x}/{y}.png')
// 	// 		},
// 	// 		{
// 	// 			name: "Transports",
// 	// 			layer: L.tileLayer('https://{s}.tile2.opencyclemap.org/transport/{z}/{x}/{y}.png')
// 	// 		}
// 	// 	]
// 	// }
// ];

// var overLayers = [
// 	{
// 		group: "Neraca Penatagunaan Tanah Regional",
// 		collapsed:false,
// 		layers: [
// 			{
// 				active: false,
// 				name: "NPGT Provinsi",
// 				// icon: '',
// 				layer: npgt_prov
// 			}
// 		]
// 	},
// 	{
// 		group: "Lahan Baku Sawah",
// 		layers: [
// 			{
// 				active: false,
// 				name: "Lahan Baku Sawah",
// 				// icon: '',
// 				layer: lahanbakusawah_prov
// 			}
// 		]
// 	},
// 	{
// 		group: "Monitoring PPT",
// 		layers: [
// 			{
// 				active: false,
// 				name: "Monitoring PPT",
// 				// icon: '',
// 				layer: mppt_prov
// 			}
// 		]
// 	},
// 	{
// 		group: "Tanah Negara Bekas Hak",
// 		layers: [
// 			{
// 				active: false,
// 				name: "Tanah Negara Bekas Hak",
// 				// icon: '',
// 				layer: tanahnegara_prov_tnbh
// 			},
// 			{
// 				active: false,
// 				name: "Tanah Negara Bekas Kawasan",
// 				// icon: '',
// 				layer: tanahnegara_prov_tnbk
// 			},
// 			{
// 				active: false,
// 				name: "Tanah Negara Tanah Kritis",
// 				// icon: '',
// 				layer: tanahnegara_prov_tntk
// 			}
// 		]
// 	},
// 	{
// 		group: "Penataan WP3WT",
// 		layers: [
// 			{
// 				active: false,
// 				name: "Penggunaan Tanah (PTN)",
// 				layer: wp3wt_ptn
// 			},
// 			{
// 				active: false
// 				name: "Pemanfaatan Tanah (PFN)",
// 				layer: wp3wt_pfn
// 			},
// 			{
// 				active: false,
// 				name: "Penguasaan Tanah (PSN)",
// 				layer: wp3wt_psn
// 			},
// 			{
// 				active: false,
// 				name: "Pemilikan Tanah (PMN)",
// 				layer: wp3wt_pmn
// 			},
// 			{
// 				active: false,
// 				name: "Pulau-Pulau Kecil",
// 				layer: wp3wt_ppk_polygon
// 			},
// 			{
// 				active: false,
// 				name: "Pulau-Pulau Kecil (POI)",
// 				layer: wp3wt_ppk_point
// 			},
// 			{
// 				active: false,
// 				name: "Pulau-Pulau Kecil Terluar",
// 				layer: wp3wt_ppkt
// 			},


// 			// {
// 			// 	active: true,
// 			// 	name: "Streams",
// 			// 	layer: {
// 			// 		type: "tileLayer.wms",
// 			// 		args: ["https://siat.regione.umbria.it/arcgis/services/public/DBT_04_Idrografia/MapServer/WMSServer", {
// 			// 				layers: '6',
// 			// 				format: 'image/png',
// 			// 				transparent: true,
// 			// 			}
// 			// 		]
// 			// 	}
// 			// }
// 		]
// 	},
// 	{
// 		group: "Pertimbangan Teknis Pertanahan",
// 		layers: [
// 			{
// 				active: false,
// 				name: "PTP-IL Provinsi",
// 				layer: ptpil_prov
// 			},
// 			{
// 				active: false,
// 				name: "PTP-IL Provinsi (POI)",
// 				layer: ptpil_prov_tk
// 			},
// 			{
// 				active: false,
// 				name: "PTP P3T",
// 				layer: ptp_p3t
// 			}

// 			// {
// 			// 	active: true,
// 			// 	name: "Streams",
// 			// 	layer: {
// 			// 		type: "tileLayer.wms",
// 			// 		args: ["https://siat.regione.umbria.it/arcgis/services/public/DBT_04_Idrografia/MapServer/WMSServer", {
// 			// 				layers: '6',
// 			// 				format: 'image/png',
// 			// 				transparent: true,
// 			// 			}
// 			// 		]
// 			// 	}
// 			// }
// 		]
// 	}
// ];
map.on('popupopen', function(e) {
    var px = map.project(e.target._popup
        ._latlng); // find the pixel location on the map where the popup anchor is
    px.y -= e.target._popup._container.clientHeight /
        2; // find the height of the popup container, divide by 2, subtract from the Y axis of marker location
    map.panTo(map.unproject(px), {
        animate: true
    }); // pan to new center
});


// var panelLayers = new L.Control.PanelLayers(baseLayers, overLayers, {
// 	// compact: true,
// 	// collapsed: false,
// 	collapsibleGroups: true
// });

// map.addControl(panelLayers);

var cek = 0;
// var sidebar = L.control.sidebar('sidebar-panel').addTo(map);
var cropper

function show_modal() {
    $('#imageModal').modal('show');
    $.get("<?= base_url('peta/getLegenda')?>", function(data) {
        data.forEach(function(item) {
            $("#legenda").append('<option value="' + item.id +
                '" data-image="<?php echo base_url('uploads/legenda/')?>' + item.img + '">' + item
                .text + '</option>');
            $("#legenda1").append('<option value="' + item.id +
                '" data-image="<?php echo base_url('uploads/legenda/')?>' + item.img + '">' + item
                .text + '</option>');
        });

        // Trigger an event to refresh the Select2 dropdown
        $("#legenda").trigger("change");
        // $(".js-example-basic-multiple").select2()
        initComboboxPemohon('id_ptp', 26)
        // $("#legenda").select2({
        //     templateResult: formatState,
        //     templateSelection: formatState
        // });
        // $('#select2-multiple').select2();
        // $('#imageModal').on('show.bs.modal', function () {
        console.log('modal show')

        var elementToCapture = document.getElementById("map2");
        setTimeout(function() {
            elementToCapture.style.display = "block";
            map2.invalidateSize();
            setTimeout(function() {
                domtoimage.toPng(elementToCapture)
                    .then(function(dataUrl) {
                        var img = document.getElementById("captured-image");
                        img.src = dataUrl;
                    })
                    .catch(function(error) {
                        console.error("Error capturing element:", error);
                    })
                    .finally(function() {
                        // Hide the div again after capture
                        elementToCapture.style.display = "none";
                    });
            }, 2000);
        }, 10);
        var elementToCaptures = document.getElementById("div-mataangin");
        setTimeout(function() {
            elementToCaptures.style.display = "block";
            map2.invalidateSize();
            setTimeout(function() {
                domtoimage.toPng(elementToCaptures)
                    .then(function(dataUrl) {
                        var img = document.getElementById("captured-mataangin");
                        img.src = dataUrl;
                    })
                    .catch(function(error) {
                        console.error("Error capturing element:", error);
                    })
                    .finally(function() {
                        // Hide the div again after capture
                        elementToCaptures.style.display = "none";
                    });
            }, 2000);
        }, 10);
        // });
    }, "json");
    // cropper = new Cropper(document.getElementById('capturedImage'), {
    //             aspectRatio: 19 / 17,
    //             viewMode: 10, // Crop box can cover the whole preview
    //             guides: true,
    //             autoCropArea: 1,
    //             background: false,
    //             movable: false,
    //             zoomable: false,
    //             rotatable: false,
    //             scalable: false,
    //         });
}

function formatState(opt) {
    if (!opt.id) {
        return opt.text.toUpperCase();
    }

    var optimage = $(opt.element).attr('data-image');
    if (!optimage) {
        return opt.text.toUpperCase();
    } else {
        var $opt = $(
            '<span ><img src="' + optimage +
            '" width="60px" /> <span class"opt_select" style="color:black!important">' + opt.text.toUpperCase() +
            '</span></span>'
        );
        return $opt;
    }
};
$(document).ready(function() {
    $("#imageChange").change(function() {
        $('#imageModal').modal('show');
    });
    $('#jabatan').selectpicker()




    map.on('move', function(e) {
        var zoomLevel = map.getZoom();
        var center = map.getCenter();
        var newLatLng = L.latLng(center.lat, center
            .lng); // Replace with the desired latitude and longitude
        var newZoomLevel = 10; // Replace with the desired zoom level

        map2.setView(newLatLng, newZoomLevel);


        var customIcon = L.icon({
            iconUrl: '<?=base_url()?>uploads/print_peta/default/red_location.jpg', // Replace with the path to your custom icon image
            iconSize: [32, 32], // Width and height of the icon
            iconAnchor: [16, 16], // Position of the icon's anchor point (center by default)
        });
        if (marker) {
            map2.removeLayer(marker);
        }
        // marker = L.marker([center.lat, center.lng]).addTo(map2); // Latitude and longitude of the marker
        marker = L.marker([center.lat, center.lng], {
            icon: customIcon
        }).addTo(map2);

        var scal = $('.leaflet-control-better-scale.leaflet-control').html()
        scal = scal.replace(
            'leaflet-control-better-scale-label leaflet-control-better-scale-first-number',
            'leaflet-control-better-scale-label leaflet-control-better-scale-first-number num-first'
        )
        scal = scal.replace(
            'leaflet-control-better-scale-label leaflet-control-better-scale-second-number',
            'leaflet-control-better-scale-label leaflet-control-better-scale-second-number num-second'
        )

        $('#scales').html(scal)
        var scalStr = $('.leaflet-control-better-scale-second-number').html()
        if (scalStr.includes('km')) {
            console.log(parseFloat($('.num-second').html()) * 1000)
            var first = parseFloat($('.num-first').html()) * 1000
            var second = parseFloat($('.num-second').html().replace('km', '')) * 1000
            $('.num-first').html(number_format(first))
            $('.num-second').html(number_format(second) + 'm')
            $('#span_skala').html(number_format(second))
        } else {
            var first = parseFloat($('.num-first').html())
            var second = parseFloat($('.num-second').html().replace('m', ''))
            $('.num-first').html(number_format(first))
            $('.num-second').html(number_format(second) + 'm')
            $('#span_skala').html(second)
        }

        var width = $('.leaflet-control-better-scale-label-div').width()
        $('.leaflet-control-better-scale-ruler').width(width + 80)
        $('.leaflet-control-better-scale-label-div').width(width + 40)
        //  console.log(second)
        // Update a DOM element with the zoom level and coordinates
        // document.getElementById('zoom').innerHTML = 'Zoom Level: ' + zoomLevel;
        // document.getElementById('lat').innerHTML = 'Latitude: ' + center.lat;
        // document.getElementById('lng').innerHTML = 'Longitude: ' + center.lng;
    });

    function number_format(meters) {

        if (meters > 2000) {
            const powerOfTen = Math.pow(10, Math.floor(Math.log10(meters)));
            meters = Math.round(meters / powerOfTen) * powerOfTen;
            return (meters / 1000).toFixed(3);

        } else if (meters < 2000 && meters > 1000) {
            return (meters / 1000).toFixed(3);

        } else {
            return meters;

        }
    }

    // document.getElementById("capture-button").addEventListener("click", function () {

    // Show the hidden div temporarily
    // Use dom-to-image library to capture the DOM element as an image

    // });
    // var tnhData='';
    // alert('masuk')
    // console.log('masuk')

    // for (var name in overlaysTree) {
    // map.removeLayer(overlaysTree[name]);
    // }
    // $('.bootstrap-select').slimscroll({
    //     color: 'rgba(0,0,0,0.5)',
    //     size: '0px',
    //     height: '473px',
    //     alwaysVisible: true
    // });


    if (role == 7 || role == 8) {
        var kabkot = "<?=$this->session->users['kd_kabkot']?>"
        var url = '<?=base_url()?>peta2/getProvKab/' + kabkot
        $.get(url, function(data) {
            data = JSON.parse(data)
            console.log(data)
            var kab = data.kab
            var prov = data.prov
            var select = document.getElementById("provSelect");
            select.innerHTML = "";
            var option = document.createElement("option");
            option.text = prov.nama_prov;
            option.value = prov.kd_prov; // Change to the desired value
            select.appendChild(option);
            var select = document.getElementById("kabSelect");
            select.innerHTML = "";
            var option = document.createElement("option");
            option.text = kab.nama_kabkot;
            option.value = kab.kd_kabkot; // Change to the desired value
            select.appendChild(option);
        });
    } else if (role == 9 || role == 10) {

        var prov = "<?=$this->session->users['kd_prov']?>"

        var url = '<?=base_url()?>peta2/getProv/' + prov
        $.get(url, function(data) {
            data = JSON.parse(data)
            console.log(data)
            var kab = data.kab
            var prov = data.prov
            var select = document.getElementById("provSelect");
            select.innerHTML = "";
            var option = document.createElement("option");
            option.text = prov.nama_prov;
            option.value = prov.kd_prov; // Change to the desired value
            select.appendChild(option);
            var select = document.getElementById("kabSelect");
            select.innerHTML = "";
            var option = document.createElement("option");
            option.text = '-- Pilih Kab/kota -- ';
            option.value = ''; // Change to the desired value
            select.appendChild(option);
            $.each(kab, function(index, v) {
                var option = document.createElement("option");
                option.text = v.nama_kabkot;
                option.value = v.kd_kabkot; // Change to the desired value
                select.appendChild(option);
            });

        });
    } else {

        initComboboxPeta('provSelect', 19)
        console.log('masuk else admin')
    }

    var str = '<option value="">Pilih Tahun</option>'
    // var year = '<?=date('Y')?>'

    // for (let i = year; i >= 1990; i--) {
    //     str += '<option value="' + i + '">' + i + '</option>';
    //     // console.log(i)
    // }
    $('#thnSelect').html(str)
    $("#provSelect").change(function() {
        refreshSelectbootPeta('kabSelect', 20, 'kd_prov', this.value);
        // removeSelectpicker('kabSelect')

    });
    // setTimeout(removeLayer, 5000)
    removeLayer()
    // console.log('cek'+cek)
});

function initComboboxPeta(divname, refindex, jns, $rev = 1) {
    var url = null;
    if ($rev === 1) {
        url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex;
    } else {
        url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex + "/" + 0;
    }
    wgiAjaxCache(url, function(ajaxdata) {
        //console.log(ajaxdata);
        jdata = JSON.parse(ajaxdata);
        $('#' + divname).empty();
        $('#' + divname).append(new Option(" Semua Provinsi", ""));
        $.each(jdata, function(i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });
        // $('#' + divname).selectpicker('refresh')

    });


}

function initComboboxPemohon(divname, refindex, jns, $rev = 1) {
    var url = null;
    if ($rev === 1) {
        url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex;
    } else {
        url = WGI_APP_BASE_URL + "lookup/fieldlook/" + refindex + "/" + 0;
    }
    wgiAjaxCache(url, function(ajaxdata) {
        //console.log(ajaxdata);
        jdata = JSON.parse(ajaxdata);
        $('#' + divname).empty();
        $('#' + divname).append(new Option(" -- Pilih Pemohon -- ", ""));
        $.each(jdata, function(i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });
        // $('#' + divname).selectpicker('refresh')

    });


}

function refreshSelectbootPeta(divname, refindex, refresh_field, refresh_value, $rev = 1) {
    var url = null;
    if ($rev === 1) {
        url = WGI_APP_BASE_URL + "lookup/refreshlookVchar/" + refindex + "/" + refresh_field + "/" + refresh_value;
    } else {
        url = WGI_APP_BASE_URL + "lookup/refreshlookVchar/" + refindex + "/" + refresh_field + "/" + refresh_value +
            "/" + 0;
    }
    wgiAjaxCache(url, function(ajaxdata) {
        jdata = JSON.parse(ajaxdata);

        // console.log(jdata);

        $('#' + divname).empty();
        $('#' + divname).append(new Option(" Semua Kabupaten/Kota", ''));
        $.each(jdata, function(i, el) {
            $('#' + divname).append(new Option(el.val, el.id));
        });

        // $('#' + divname).selectpicker('refresh');
        // $('#' + divname).trigger('change');
    });



}

function removeLayer(v) {

    if (map.hasLayer(tanahnegara_prov_tnbh)) {
        map.removeLayer(tanahnegara_prov_tnbh);
    };


    if (map.hasLayer(tanahnegara_prov_tnbk)) {
        map.removeLayer(tanahnegara_prov_tnbk);
    };


    if (map.hasLayer(tanahnegara_prov_tntk)) {
        map.removeLayer(tanahnegara_prov_tntk);
    };
    if (map.hasLayer(npgt_prov)) {
        map.removeLayer(npgt_prov);
    };

    if (map.hasLayer(npgt_kabkota)) {
        map.removeLayer(npgt_kabkota);
    };

    if (map.hasLayer(npgt_kec_a)) {
        map.removeLayer(npgt_kec_a);
    };

    // if (map.hasLayer(npgt_kec_h)) {
    //     map.removeLayer(npgt_kec_h);
    // };

    // if (map.hasLayer(npgt_kec_k)) {
    //     map.removeLayer(npgt_kec_k);
    // };
    // if (map.hasLayer(npgt_kec_n)) {
    //     map.removeLayer(npgt_kec_n);
    // };

    // if (map.hasLayer(npgt_kec_o)) {
    //     map.removeLayer(npgt_kec_o);
    // };

    // if (map.hasLayer(npgt_kec_q)) {
    //     map.removeLayer(npgt_kec_q);
    // };

    // if (map.hasLayer(npgt_kec_v)) {
    //     map.removeLayer(npgt_kec_v);
    // };

    // if (map.hasLayer(npgt_kec_w)) {
    //     map.removeLayer(npgt_kec_w);
    // };
    if (map.hasLayer(npgt_perkebunan)) {
        map.removeLayer(npgt_perkebunan);
    };

    if (map.hasLayer(mppt_prov)) {
        map.removeLayer(mppt_prov);
    };
    if (map.hasLayer(kemampuan_tanah)) {
        map.removeLayer(kemampuan_tanah);
    };

    if (map.hasLayer(lahanbakusawah_prov)) {
        map.removeLayer(lahanbakusawah_prov);
    };

    if (map.hasLayer(ptp_p3t)) {
        map.removeLayer(ptp_p3t);
    };

    if (map.hasLayer(ptpil_prov)) {
        map.removeLayer(ptpil_prov);
    };

    if (map.hasLayer(ptpil_prov_tk)) {
        map.removeLayer(ptpil_prov_tk);
    };

    if (map.hasLayer(ptp_kppr_berusaha)) {
        map.removeLayer(ptp_kppr_berusaha);
    };
    if (map.hasLayer(ptp_kppr_stranas)) {
        map.removeLayer(ptp_kppr_stranas);
    };
    if (map.hasLayer(ptp_kppr_non_berusaha)) {
        map.removeLayer(ptp_kppr_non_berusaha);
    };
    if (map.hasLayer(ptp_pk_p3t)) {
        map.removeLayer(ptp_pk_p3t);
    };
    if (map.hasLayer(ptp_tnh_timbul)) {
        map.removeLayer(ptp_tnh_timbul);
    };
    if (map.hasLayer(wp3wt_ppk_polygon)) {
        map.removeLayer(wp3wt_ppk_polygon);
    };
    if (map.hasLayer(wp3wt_ppk_poi)) {
        map.removeLayer(wp3wt_ppk_poi);
    };
    if (map.hasLayer(wp3wt_ppkt)) {
        map.removeLayer(wp3wt_ppkt);
    };
    if (map.hasLayer(wp3wt_perbatasan)) {
        map.removeLayer(wp3wt_perbatasan);
    };
    if (map.hasLayer(wp3wt_pesisir)) {
        map.removeLayer(wp3wt_pesisir);
    };

    if (map.hasLayer(wp3wt_pulau)) {
        map.removeLayer(wp3wt_pulau);
    };

    if (map.hasLayer(wp3wt_tertentu)) {
        map.removeLayer(wp3wt_tertentu);
    };

    if (map.hasLayer(wp3wt_timbul)) {
        map.removeLayer(wp3wt_timbul);
    };
    if (map.hasLayer(rtrw)) {
        map.removeLayer(rtrw);
    };
    if (map.hasLayer(bts_desa)) {
        map.removeLayer(bts_desa);
    };
    if (map.hasLayer(bts_kecamatan)) {
        map.removeLayer(bts_kecamatan);
    };
}
var active = '';

function getYear(layer) {
    if (active != layer) {
        var url = "<?php echo base_url('lookup/getYear/')?>" + layer;
        $.get(url, function(data) {
            data = JSON.parse(data)
            $('#thnSelect').html(data.year)

        });
    }
}
var petaTR = L.layerGroup()

function buildMap() {
    // var layer = 'lahanbakusawah_prov';
    // if ($('#buildMapCheck').checked) {
    if ($('#buildMapCheck').prop('checked')) {
        if (map.hasLayer(petaTR)) {
            map.removeLayer(petaTR);
            petaTR = L.layerGroup()
        }
        var kab = $('#kabSelect').val()
        var prov = $('#provSelect').val()
        var urls = "<?php echo base_url().'peta2/getPetaTataRuang/'?>" + prov + '/' + kab

        $.getJSON("<?php echo base_url().'peta2/getPetaTataRuang/'?>" + prov + '/' + kab,
            function(data) {

                $.each(data.features, function(index, value) {
                    const errors = geojsonhint.hint(value);
                    if (errors.length === 0) {
                        L.geoJSON(value).addTo(petaTR);
                        petaTR.addTo(map)
                    } else {
                        console.error("GeoJSON validation errors:", errors);
                    }
                });

                // if(map.hasLayer(petaTR)) {
                //     map.removeLayer(petaTR);
                // }
                // if (data) {
                //     console.log('masuk data')
                //     petaTR =  L.geoJSON(data, {
                //         onEachFeature: function (feature, layer) {
                //             // Check if the geometry is valid before adding to the map
                //             if (feature.geometry && L.GeoJSON.geometryToLayer(feature.geometry)) {
                //                 layer.addTo(map);
                //             } else {
                //                 console.error('Invalid geometry:', feature);
                //             }
                //         },
                //         style: function (feature) {
                //             return {
                //                 tolerance: 3,
                //                 debug: 0,
                //                 style: {
                //                     fillColor: "red", 
                //                     color: "red",
                //                     weight: 0.5,
                //                     fillOpacity: .50
                //                 }
                //             };
                //         }
                //     })
                // //fast render layer with VT
                // var vtLayer = L.geoJson.vt(jsData, options).addTo(map);
                // if(map.hasLayer(vtLayer))  map.fitBounds(L.geoJson(data).getBounds());

                // }

            });
    } else {
        if (map.hasLayer(petaTR)) {
            map.removeLayer(petaTR);
            petaTR = L.layerGroup()
        }
    }
}

function callPage(pageRefInput) {

    var data_post = {
        "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>",
        "uri": pageRefInput
    };

    $.ajax({
        dataType: "text",
        type: "POST",
        data: data_post,
        url: pageRefInput,
        success: function(data) {
            $('.content').empty();
            $('.content').html(data);
        },
        error: function(xhr, ajaxOptions, thrownError) {

        }
    });

}
</script>