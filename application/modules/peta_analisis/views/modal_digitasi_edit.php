
<div class="modal " id="modDigitasiEdit" tabindex="-1">
  <div class="modal-dialog modal-fullscreen">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Digitasi Edit</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal" id="frm-edit">
            <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
            <input type="hidden" name="xid_form" id="xid_form">
            <input type="hidden" id="xwadmpr" name="xwadmpr">
            <input type="hidden" id="xwadmkk" name="xwadmkk">
            <input type="hidden" id="xwadmkc" name="xwadmkc">
            <input type="hidden" id="xwadmkd" name="xwadmkd">
            <div class="row">
                <div class="col-sm-12 col-md-12 col-xs-12">
                    <div class="card" id="xdivMap" style="">
                    <!-- <button type="button" onclick="clearAllLayer()">clear</button> -->
                        <div id="xmap2" style="height: 80vh;width: 100%"></div>
                    </div>
                </div>
             </div>
            
             <div id="divRow" class="row">
                <div class="form-group  col-md-6  ">
                    <label class="col-md-12 control-label" for="no_berkas">Nomor Berkas</label>  
                        <div class="col-md-12">
                        <input id="xno_berkas" name="xno_berkas" type="text" placeholder="" class="form-control input-md">
                        </div>
                    </div>
                <div class="form-group  col-md-6  ">
                    <label class="col-md-12 control-label" for="tanggal_surat">Tanggal Penerbitan</label>  
                    <div class="col-md-12">
                        <input id="xtanggal_surat" name="xtanggal_surat" type="date" placeholder="" class="form-control input-md">
                    </div>
                </div>
                <div class="form-group  col-md-6  ">
                    <label class="col-md-12 control-label" for="nama_pemohon">Nama Pemohon</label> 
                        <div class="col-md-12">
                            <input id="xnama_pemohon" name="xnama_pemohon" type="text" placeholder="" class="form-control input-md">
                        </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="alamat">Alamat</label>   
                    <div class="col-md-12">
                    <textarea class="form-control" name="xalamat" id="xalamat" cols="30" rows="5"></textarea>
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="nik">NIK</label>   
                    <div class="col-md-12">
                        <input id="xnik" name="xnik" type="text" placeholder="" class="form-control input-md">
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="jabatan_pemohon">Jabatan</label>  
                    <div class="col-md-12">
                        <input id="xjabatan_pemohon" name="xjabatan_pemohon" type="text" placeholder="" class="form-control input-md">
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="id_subjek">Bertindak untuk dan Atas Nama</label>  
                    <div class="col-md-12">
                        <select id="xid_subjek" name="xid_subjek" required class="bootstrap-select form-control" data-live-search="true">
                            </select>
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="rcnkeg">Atas nama</label> <label class="col-md-12 control-label" for="atas_nama"></label> 
                    <div class="col-md-12">
                        <input id="xatas_nama" name="xatas_nama" type="text" placeholder="" class="form-control input-md">
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="luas_tanah">Luas Tanah</label>  
                    <div class="col-md-12">
                        <input id="xluas_tanah" name="xluas_tanah" type="text" placeholder="" class="form-control input-md">
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="kdppum">Provinsi</label>  
                    <div class="col-md-12">
                        <select id="xkdppum" name="xkdppum" required class="bootstrap-select form-control" data-live-search="true">
                        </select>
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="kdpkab">Kabupaten/Kota</label>  
                    <div class="col-md-12">
                        <select id="xkdpkab" name="xkdpkab" required class="bootstrap-select form-control" data-live-search="true">
                            </select>
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="kdcpum">Kecamatan</label>  
                    <div class="col-md-12">
                        <select id="xkdcpum" name="xkdcpum" required class="bootstrap-select form-control" data-live-search="true">
                            </select>
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="kdepum">Desa</label>  
                    <div class="col-md-12">
                        <select id="xkdepum" name="xkdepum" required class="bootstrap-select form-control" data-live-search="true">
                        </select>
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="id_tema">Tema Analisis Penatagunaan Tanah</label>   
                    <div class="col-md-12">
                        <select id="xid_tema" name="xid_tema" required class="bootstrap-select form-control" data-live-search="true">
                        </select>
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="kd_format">Format Peta</label>    
                    <div class="col-md-12">
                        <select id="xkd_format" name="xkd_format" required class="bootstrap-select form-control" data-live-search="true">
                            </select>
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="keperluan">Keperluan</label>   
                    <div class="col-md-12">
                        <input id="xkeperluan" name="xkeperluan" type="text" placeholder="" class="form-control input-md">
                    </div>
                </div>
                <div class="form-group  col-md-6 ">
                    <label class="col-md-12 control-label" for="keterangan_tambahan">Keterangan tambahan</label>   
                    <div class="col-md-12">
                        <input id="xketerangan_tambahan" name="xketerangan_tambahan" type="text" placeholder="" class="form-control input-md">
                    </div>
                    <input id="xgeom_json" name="xgeom_json" type="hidden" placeholder="" class="form-control input-md float-number">                    

                </div>                                   
                <div class="form-group  col-md-6  ">
                    <label class="col-md-12 control-label" for="luas"></label>  
                    <div class="col-md-12">
                    <input id="xgeom" name="xgeom" type="hidden" placeholder="" class="form-control input-md float-number">                    
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="xbtn-close" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit"  class="btn btn-primary">Save changes</button>
            </form>
            </div>
    </div>
  </div>
</div>


<script>


    $(document).ready(function () {
     
        $('#frm-edit').submit(function (e) {
            e.preventDefault();
            getPolygons()
            // console.log('ok')
            var wadmpr = $("#xkdppum option:selected").text();
            $("#xwadmpr").val(wadmpr)
            var wadmkk = $("#xkdpkab option:selected").text();
            $("#xwadmkk").val(wadmkk)
            var wadmkc = $("#xkdcpum option:selected").text();
            $("#xwadmkc").val(wadmkc)
            var wadmkd = $("#xkdepum option:selected").text();
            $("#xwadmkd").val(wadmkd)
            var file = new FormData(this);
            $.ajax({
                    // url: '<?php echo base_url(); ?>digit_pertimbangan_psrp_tnh_timbul/up',
                    url: '<?php echo base_url(); ?>peta_analisis/updateShp',
                    type: "post",
                    data: file,
                    processData: false,
                    contentType: false,
                    cache: false,
                    async: false,
                    success: function (data) {
                        // $("select").val('--pilih--');
                        // $("select").selectpicker("refresh");
                        data=JSON.parse(data)
                        if(data.sts=='gagal'){
                            swal.close()
                            Swal.fire(
                                'Gagal!',
                                data.msg,
                                'error'
                            )
                        }else{

                            $("#filess").val('')
                            var tab = $('#dt-server-processing').DataTable();
                            tab.ajax.reload();
                            $('#dt-server-processing_processing').css('display','none')
                            swal.close()
                            Swal.fire(
                                'Sukses!',
                                'Data Tersimpan!',
                                'success'
                            )
                            $('#xbtn-close').click()

                        }
                        
                    },error: function (jqXHR, exception) {
                        // console.log(jqXHR);
                        swal.close()
                        Swal.fire(
                            'Gagal!',
                            'Data Gagal Tersimpan!',
                            'error'
                        )
                    }
                });
        });
    });
    var xmap
    var drawnPolygons
    var drawnItems 
    var dataGeojson
    var multiPolygonLayer
    var drawControl
    var geojsonBounds;
    var dataGeom
    var isMulti = 0;
    var xrole = "<?=$this->session->users['id_user_group_real']?>";
    var xkdpkab = "<?php echo $this->session->users['kd_kabkot'];?>";
    var xkdppum = "<?php echo $this->session->users['kd_prov'];?>";

    $('#modDigitasiEdit').on('shown.bs.modal', function (e) {
        var gid = $('#xform_id').val()
        var table = 'dok_ptp_tnh_timbul'
        
        var mapContainer = document.getElementById('xmap2');
        if (mapContainer && mapContainer.classList.contains('leaflet-container')) {
            
            var dataGeojson = JSON.parse($('#xgeom_json').val())
            console.log(dataGeojson);
            if (dataGeojson.type === 'MultiPolygon') {
                var polygonCoordinates = dataGeojson.coordinates[0][0]; // Extract coordinates
                 dataGeojson = {
                "type": "Polygon",
                "coordinates": [polygonCoordinates]
                };
                isMulti = 1;
            }else{
                isMulti = 0;
            }
            
            xmap.eachLayer(function(layer){
                if (layer._path != null) {
                    layer.remove()
                }
            });
            drawnItems = L.geoJSON(dataGeojson);
            xmap.addLayer(drawnItems);
            setTimeout(() => {
                xmap.fitBounds(drawnItems.getBounds());
                
            }, 1000);
            xmap.removeControl(drawControl);
            drawControl = new L.Control.Draw({
                draw: false,
                edit: {
                    featureGroup: drawnItems,
                    edit: true,
                    remove: true
                }
            });
            xmap.addControl(drawControl);
        
            xmap.on('draw:edited', function (e) {
                var layers = L.PM.Utils.findLayers(xmap);
                var lay = e.layers
                lay.eachLayer(function (layer) {
                    var editedLayerBounds = layer.getBounds();
                    if (xrole == 7 || xrole == 8 || xrole == 9 || xrole == 10) {
                        if (!isPolygonWithinOuter(layer)) {
                            Swal.fire(
                                'Gagal!',
                                'Tidak Bisa Melebihi Wilayah yang ditentukan.',
                                'error'
                            );
                            drawnItems.clearLayers();
                            xmap.removeControl(drawControl);
                            drawnItems = L.geoJSON(dataGeojson);
                            xmap.addLayer(drawnItems);
                            drawControl = new L.Control.Draw({
                                draw: false,
                                edit: {
                                    featureGroup: drawnItems,
                                    edit: true,
                                    remove: true
                                }
                            });
                            xmap.addControl(drawControl);
                            return false;
                        }
                    }
                });
                var coordinates = layers[0].getLatLngs()[0];
                var arr =[]
                $.each(coordinates, function(index, value) {
                    arr.push([value.lat,value.lng])
                });
                var area = L.GeometryUtil.geodesicArea(coordinates);
                $('#xluasm2').val(area)
                $('#xluas_ha').val(area/10000)
                
            });

            if (xrole == 7 || xrole == 8 || xrole == 9 || xrole == 10) {
                fetch('<?php echo base_url();?>digit_pertimbangan_berusaha/getBoundary/')
                .then(response => {
                    if (!response.ok) {
                    throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Handle the data
                    dataGeom = JSON.parse(data.geom)
                    geojsonBounds = L.geoJSON(dataGeom, {
                            style: {
                            fillColor: 'transparent',  
                            color: 'red',      
                            weight: 2,         
                            opacity: 1,        
                            fillOpacity: 0.5   
                        }
                    });
                    
                    geojsonBounds.addTo(xmap);
                    xmap.setMaxBounds(geojsonBounds.getBounds());
                    xmap.on('drag', function() {
                        xmap.panInsideBounds(geojsonBounds.getBounds(), { animate: false });
                    });
                    xmap.setMinZoom(xmap.getBoundsZoom(geojsonBounds.getBounds()));
                    
                    console.log(geojsonBounds)
                    
                })
                .catch(error => {
                    // Handle errors
                    console.error('Error:', error);
                });
            }

        }else{
            
            xmap = L.map('xmap2').setView([-2.7521401146517785, 116.07226320582281], 5);


            var xgl = L.mapboxGL({
                style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
            });
            var xosmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(xmap);

            var xgoogleHybrid = L.tileLayer('http://{s}.google.com/vt?lyrs=s,h&x={x}&y={y}&z={z}', {
                subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
            });

           
            drawnPolygons = [];
            var dataGeojson = JSON.parse($('#xgeom_json').val())
            
            if (dataGeojson.type === 'MultiPolygon') {
                var polygonCoordinates = dataGeojson.coordinates[0][0]; // Extract coordinates
                 dataGeojson = {
                "type": "Polygon",
                "coordinates": [polygonCoordinates]
                };
                isMulti = 1;
            }else{
                isMulti = 0;
            }
            
    // Output the resulting Polygon GeoJSON
            drawnItems = L.geoJSON(dataGeojson);
            xmap.addLayer(drawnItems);
            setTimeout(() => {
                xmap.fitBounds(drawnItems.getBounds());
                
            }, 100);

            drawControl = new L.Control.Draw({
                draw: false,
                edit: {
                    featureGroup: drawnItems,
                    edit: true,
                    remove: true
                }
            });
            xmap.addControl(drawControl);
            
            xmap.on('draw:edited', function (e) {
                var layers = L.PM.Utils.findLayers(xmap);
                var lay = e.layers;
                lay.eachLayer(function (layer) {
                    var editedLayerBounds = layer.getBounds();
                    if (xrole == 7 || xrole == 8 || xrole == 9 || xrole == 10) {
                        if (!isPolygonWithinOuter(layer)) {
                            Swal.fire(
                                'Gagal!',
                                'Tidak Bisa Melebihi Wilayah yang ditentukan.',
                                'error'
                                );
                            drawnItems.clearLayers();
                            xmap.removeControl(drawControl);
                            drawnItems = L.geoJSON(dataGeojson);
                            xmap.addLayer(drawnItems);
                            drawControl = new L.Control.Draw({
                                draw: false,
                                edit: {
                                    featureGroup: drawnItems,
                                    edit: true,
                                    remove: true
                                }
                            });
                            xmap.addControl(drawControl);
                            return false;
                        }   
                    }
                });
                var coordinates = layers[0].getLatLngs()[0];
                var arr =[]
                $.each(coordinates, function(index, value) {
                    arr.push([value.lat,value.lng])
                });
                var area = L.GeometryUtil.geodesicArea(coordinates);
                $('#xluasm2').val(area)
                $('#xluas_ha').val(area/10000)
            });
            
            
            if (xrole == 7 || xrole == 8 || xrole == 9 || xrole == 10) {
                fetch('<?php echo base_url();?>digit_pertimbangan_berusaha/getBoundary/')
                .then(response => {
            // Check if the response is successful (status code in the range 200-299)
                    if (!response.ok) {
                    throw new Error('Network response was not ok');
                    }
                    // Parse the response as JSON
                    return response.json();
                })
                .then(data => {
                    // Handle the data
                    dataGeom = JSON.parse(data.geom)
                    geojsonBounds = L.geoJSON(dataGeom, {
                            style: {
                            fillColor: 'transparent',  
                            color: 'red',      
                            weight: 2,         
                            opacity: 1,        
                            fillOpacity: 0.5   
                        }
                    });
                    
                    geojsonBounds.addTo(xmap);
                    xmap.setMaxBounds(geojsonBounds.getBounds());
                    xmap.on('drag', function() {
                        xmap.panInsideBounds(geojsonBounds.getBounds(), { animate: false });
                    });
                    xmap.setMinZoom(xmap.getBoundsZoom(geojsonBounds.getBounds()));
                    
                    console.log(geojsonBounds)
                    
                })
                .catch(error => {
                    // Handle errors
                    console.error('Error:', error);
                });
            }
       
            
        }
       
    
    })  

    function isPolygonWithinOuter(polygon) {
        var drawnPolygonGeoJSON = polygon.toGeoJSON();
        var isInside = turf.booleanWithin(drawnPolygonGeoJSON, dataGeom);
        return isInside;
    }

   
    function clearAllLayer() {
        xmap.eachLayer(function(layer){
            if (layer._path != null) {
                layer.remove()
            }
        });
    }

    function getPolygons() {
        var layers = L.PM.Utils.findLayers(xmap);
        var group = L.featureGroup();
        // layers.forEach((layer)=>{
            group.addLayer(layers[0]);
        // });
        shapes = group.toGeoJSON();
        if (isMulti == 1) {
            shapes = {
                "type": "MultiPolygon",
                "coordinates": [[
                    shapes.features[0].geometry.coordinates[0]  // Wrap the coordinates in an additional set of brackets
                ]]
            };
        }

        $('#xgeom').val(JSON.stringify(shapes))
        
    }


    var ptp_kppr_berusaha
    var rtrw
    var kemampuan_tanah
    <?php
            require_once(FCPATH."env.php");
            echo 'var WGI_APP_BASE_URL = "'.WGI_APP_BASE_URL.'"; ';
            echo 'var WGI_NODE_API_URL = "'.WGI_NODE_API_URL.'"; ';

        ?>
    
    var url = WGI_APP_BASE_URL+'geoserver/pgt/wms';

    function xptpBerusaha() {
        // alert('okay')
        if ($('#xBerusaha').prop('checked')) {
            
            if (xmap.hasLayer(rtrw)) {
                xmap.removeLayer(rtrw);
            };
            xmap.closePopup()
            rtrw = L.tileLayer.betterWms(url, {
                layers: 'pgt:rtrw',
                styles: 'pgt:rtrw',
                transparent: true,
                format: 'image/png8',
                title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
                cache: false,
                onEachFeature: function(feature, layer) {
                    layer.on({
                        click:  closePopup()// Attach the custom click function to each feature
                    });
                }
            });
            rtrw.addTo(map)
            rtrw.bringToFront();
        }else{
            if (xmap.hasLayer(rtrw)) {
                xmap.removeLayer(rtrw);
            };
        }
    }

    function xpgtlRtrw() {
        // alert('okay')
        if ($('#rtrw').prop('checked')) {
            
            if (xmap.hasLayer(rtrw)) {
                xmap.removeLayer(rtrw);
            };
            xmap.closePopup()
            rtrw = L.tileLayer.betterWms(url, {
                layers: 'pgt:rtrw',
                styles: 'pgt:rtrw',
                transparent: true,
                format: 'image/png8',
                title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
                cache: false,
                onEachFeature: function(feature, layer) {
                    layer.on({
                        click:  closePopup()// Attach the custom click function to each feature
                    });
                }
            });
            rtrw.addTo(map)
            rtrw.bringToFront();
        }else{
            if (xmap.hasLayer(rtrw)) {
                xmap.removeLayer(rtrw);
            };
        }
    }

    function xkemampuan_tanah() {
        // alert('okay')
        if ($('#xkemampuantanah').prop('checked')) {
            if (xmap.hasLayer(kemampuan_tanah)) {
                xmap.removeLayer(kemampuan_tanah);
            };
            xmap.closePopup()
            kemampuan_tanah = L.tileLayer.betterWms(url, {
                layers: 'pgt:kemampuan_tanah',
                styles: 'pgt:kemampuan_tanah',
                transparent: true,
                format: 'image/png8',
                title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
                cache: false,
                onEachFeature: function(feature, layer) {
                    layer.on({
                        click:  closePopup()// Attach the custom click function to each feature
                    });
                }
            });
            kemampuan_tanah.addTo(map)
            kemampuan_tanah.bringToFront();
        }else{
            if (xmap.hasLayer(kemampuan_tanah)) {
                xmap.removeLayer(kemampuan_tanah);
            };
        }
    }

</script>