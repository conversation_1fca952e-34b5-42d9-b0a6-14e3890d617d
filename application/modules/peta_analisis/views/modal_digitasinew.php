<script src="<? echo base_url();?>assets/js/L.TileLayer.BetterWMS.digit.js"></script>

<div class="modal " id="modDigitasi" tabindex="-1">
  <div class="modal-dialog modal-fullscreen">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Digitasi</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal" id="frm-tambah">
            <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
            <input type="hidden" id="idDelete" name="idDelete">
            <div class="row">
                <div class="col-sm-12 col-md-12 col-xs-12">
                    <div class="card" id="divMap" style="">
                    <!-- <button type="button" onclick="calculateArea()">get</button> -->
                        <div id="map2" style="height: 80vh;width: 100%"></div>
                    </div>
                </div>
             </div>
           
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <!-- <button type="submit" class="btn btn-primary">Save changes</button> -->
        </form>
       </div>
    </div>
  </div>
</div>


<script>
      
$(document).ready(function () {
    $('.leaflet-popup-scrolled').slimScroll({
        height: '250px'
    });
            $('#frm-tambah').submit(function (e) {
                e.preventDefault();
                // getPolygon()
                var selectedOption = $("#kd_prov option:selected");
                $('#wadmpr').val(selectedOption.text());
                 selectedOption = $("#kd_kabkot option:selected");
                $('#wadmkk').val(selectedOption.text());
                // return false
                var file = new FormData(this);
                $.ajax({
                        // url: '<?php echo base_url(); ?>digit_pertimbangan_berusaha/up',
                        url: '<?php echo base_url(); ?>digit_pertimbangan_berusaha/insertShp',
                        type: "post",
                        data: file,
                        processData: false,
                        contentType: false,
                        cache: false,
                        async: false,
                        success: function (data) {
                            // $("select").val('--pilih--');
                            // $("select").selectpicker("refresh");
                            data=JSON.parse(data)
                            console.log(data.sts)
                            if(data.sts=='gagal'){
                                swal.close()
                                Swal.fire(
                                    'Gagal!',
                                    data.msg,
                                    'error'
                                )
                            }else{
       
                                $("#filess").val('')
                                var table = $('#dt-server-processing').DataTable();
                                table.ajax.reload();
                                swal.close()
                                Swal.fire(
                                    'Sukses!',
                                    'Data Tersimpan!',
                                    'success'
                                )
                                map.closePopup()
                                // clearAllLayer()
                                // drawnItems
                                var drawnLayers = map.pm.getGeomanDrawLayers();
                                var drawnLayer = drawnLayers[0];
                                map.removeLayer(drawnLayer);
                                var polygonLayer = drawnLayer
                                polygonLayer.addTo(map);
                                if ($('#stranas').prop('checked')) {
                                    ptpBerusaha()
                                }
                            }
                        },error: function (jqXHR, exception) {
                            // console.log(jqXHR);
                            swal.close()
                            Swal.fire(
                                'Gagal!',
                                'Data Gagal Tersimpan!',
                                'error'
                            )
                        }
                    });
            });
        });
    var map
    var drawnPolygons
    var drawnItems 
    $('#modDigitasi').on('shown.bs.modal', function (e) {
        $('#geom').val('')
         var mapContainer = document.getElementById('map2');

        if (mapContainer && mapContainer.classList.contains('leaflet-container')) {
            drawnItems.clearLayers();
        }else{
            map = L.map('map2').setView([-2.7521401146517785, 116.07226320582281], 5);
            var gl = L.mapboxGL({
                style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
            }).addTo(map)
            // var baseMaps = {
            //     "Mapbox Streets": gl
            // };

             // var layerControl = L.control.layers(baseMaps).addTo(map);
             L.control.layers({
                "MapBox": gl.addTo(map)
            }, {
                
            }, {
                position: 'topright',
                collapsed: false
            }).addTo(map);
            var lay = '<hr><div class="form-check">'+
                        '<input class="form-check-input" onchange="ptpBerusaha()" type="checkbox" value="" id="stranas">'+
                        '<label class="form-check-label"  for="stranas">'+
                            'PTP P KKPR Berusaha'+
                        '</label>'+
                       '</div>'+
            $('.leaflet-control-layers').append(lay) 
            

            map.pm.addControls({  
                position: 'topleft',  
                drawCircleMarker: false,
                rotateMode: false,
                drawCircle:false,
                drawText:false,
                cutPolygon:false,
                drawPolygon:true,
                drawMarker:false,
                drawPolyline:false,
                drawRectangle:true,
                dragMode:false,

            }); 

        
            drawnPolygons = [];
            drawnItems = new L.FeatureGroup().addTo(map);

            map.on('pm:create', function (e) {
                var layer = e.layer;
                var latlngs = layer.getLatLngs();
                drawnItems.addLayer(layer);
                // Generate a unique ID for the new polygon
                var newPolygonId = Date.now();

                // Save the new polygon along with its ID to the array
                drawnPolygons.push({ id: newPolygonId, latlngs: latlngs });

                // Get the GeoJSON of all drawn polygons (including the edited ones)

                layer.bindPopup(<?=$popup?>).openPopup();
                initCombobox('kd_prov', 19);
                $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                     refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
                });
                var shapes = layer.toGeoJSON();
                $('#geom').val(JSON.stringify(shapes))
                $('.modal-bodyp').slimScroll({
                    height: '250px'
                });
                
                
            });

           
        }

    
    })  

    function addPopup(layer) {
        
        layer.bindPopup(content).openPopup();
    }
 

    var ptp_kppr_berusaha
    var url = 'http://103.6.53.254:4980/geoserver/pgt/wms';

    function ptpBerusaha() {
        // alert('okay')
        if ($('#stranas').prop('checked')) {
            if (map.hasLayer(ptp_kppr_berusaha)) {
                map.removeLayer(ptp_kppr_berusaha);
            };
            map.closePopup()
            ptp_kppr_berusaha = L.tileLayer.betterWms(url, {
                layers: 'pgt:ptp_kppr_berusaha',
                styles: 'pgt:ptp_kppr_berusaha',
                transparent: true,
                format: 'image/png8',
                title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
                onEachFeature: function(feature, layer) {
                    layer.on({
                        click:  closePopup()// Attach the custom click function to each feature
                    });
                }
            });
            ptp_kppr_berusaha.addTo(map)
        }else{
            if (map.hasLayer(ptp_kppr_berusaha)) {
                map.removeLayer(ptp_kppr_berusaha);
            };
        }
    }

    function closePopup() {
        map.closePopup()
    }
    function numericPopup(v) {
        var id = v.attr('id')
        var inputValue = $('#'+id).val();
            var numericValue = inputValue.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            var formattedValue = numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.'); // Format with commas
            $('#'+id).val(formattedValue); // Set the formatted value to the input
    }
</script>