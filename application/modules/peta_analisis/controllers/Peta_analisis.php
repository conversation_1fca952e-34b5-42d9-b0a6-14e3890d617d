<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");
use \Shapefile\Shapefile;
use \Shapefile\ShapefileException;
use \Shapefile\ShapefileReader;
use \diversen\gps;

class Peta_analisis extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
		$this->load->library('pdf');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        // if( !isset($_SERVER['HTTP_REFERER'])) {
        //     redirect('page_not_found');
        // }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index() {
        // $new_users =  $this->session->userdata('users');
      
        // if ($new_users['id_user'] == '1') {
        //     $new_users['kd_kabkot'] = '35.73';
        //     // $new_users['kd_kabkot'] = '35.10';
        //     // $new_users['kd_kabkot'] = '33.29';
        //     // $new_users['kd_kabkot'] = '51.71';
        //     $new_users['kd_kabkot'] = '32.15';
        //     $new_users['id_user_group_real'] = 7;
        //     $new_users['id_user_group'] = 7;
            
        //     $this->session->unset_userdata('users'); 
        //     $this->session->set_userdata('users', $new_users);
        // }
        header("Access-Control-Allow-Origin: *");
        $data = array();

        $satker = $this->session->users['kd_bujt'];
        $title = "Peta Analisis";
        $this->db->where('table_name','ptp_kkppr_stran');
        $datas['column'] = $this->db->get('spatial.list_column')->result();
        $popup = $this->load->view('peta_analisis/popup', $datas, true);
        $datas['popup'] = json_encode($popup);
        $js_wms = $this->load->view('peta_analisis/js_wms', $datas, true);
        $datas['js_wms'] = $js_wms;
        
        $js_file = $this->load->view('peta_analisis/js_file', '', true);
        $modal_tambah = $this->load->view('peta_analisis/modal_tambah', '', true);
        $modal_edit = $this->load->view('peta_analisis/modal_edit', '', true);
        $modal_download = $this->load->view('peta_analisis/modal_download', '', true);
        $modal_digitasi = $this->load->view('peta_analisis/modal_digitasi', $datas, true);
        $modal_history = $this->load->view('peta_analisis/modal_history', $datas, true);
        $modal_digitasi_edit = $this->load->view('peta_analisis/modal_digitasi_edit', '', true);
        $modal_print = $this->load->view('peta_analisis/modal_print', '', true);
        $modal_print_form = $this->load->view('peta_analisis/modal_print', '', true);
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $menu=explode('/',$this->input->post('uri'));
        $data = array(/* "modal_filter" => $modal_filter, */
            "modal_digitasi" => $modal_digitasi,
            "js_wms" => $js_wms,
            "modal_digitasi_edit" => $modal_digitasi_edit,
            "modal_download" => $modal_download,
            "modal_history" => $modal_history,
            "modal_print" => $modal_print,
            "modal_print_form" => $modal_print_form,
            "title" => $title,
            "jv_script" => $js_file,
            "popup" => $datas['popup'],
        );
        
        $this->load->view('index', $data);
    }
    
    function printPeta($id='') {
        $this->db->where('gid', $id);
        $dataPtp = $this->db->get('spatial.ptp_kppr_berusaha')->row_array();
        
        
        $data= [
            'id'=> $id,
            'data'=> $dataPtp
        ];
        
        $this->load->view('print', $data);

    }

    function getBoundary() {
        $data = [];
        $role =  $this->session->users['id_user_group_real'];
        $kdpkab = $this->session->users['kd_kabkot'];
        $kdppum = $this->session->users['kd_prov'];
        if ($role == 7 || $role == 8) {
            $this->db->where('kdpkab', $kdpkab);
            $data = $this->db->get('spatial.v_batas_kab_kota_new')->row_array();
        }else if ($role == 9 || $role == 10) {
            $this->db->where('kdppum', $kdppum);
            $data = $this->db->get('spatial.v_batas_prov_new')->row_array();
            // echo $this->db->last_query();
            
        };
        echo json_encode($data);
    }
   
    
    
    public function ssp_paket() {
      
       
        $table = 'spatial.digit_peta_analisis';
        $primaryKey = 'id_form'; //test        
        $kd_prov=@$this->input->post("kd_prov",true);
        $kd_kabkot=@$this->input->post("kd_kabkot",true);
        $tahun_data=@$this->input->post("tahun_data",true);

        $where="";
        if (!empty($kd_prov)) {
            $where .="kdppum = '".$kd_prov."'";
            if (!empty($kd_kabkot)) {
                $where .=" and kdpkab = '".$kd_kabkot."'";
            }     
        } 
        if ($tahun_data != '#') {
            $where .="thndata = '".$tahun_data."'";
        } 
        $columns = array(
            array('db' => 'id_form', 'dt' => 0),
            array('db'=> 'geom', 'dt' => 1),
            array('db'=> 'no_berkas', 'dt' => 2),
            array('db'=> 'lokasi_surat', 'dt' => 3),
            array('db'=> 'tanggal_surat', 'dt' => 4),
            array('db'=> 'nama_pemohon', 'dt' => 5),
            array('db'=> 'jabatan_pemohon', 'dt' => 6),
            array('db'=> 'alamat', 'dt' => 7),
            array('db'=> 'nik', 'dt' => 8),
            array('db'=> 'id_subjek', 'dt' => 9),
            array('db'=> 'luas_tanah', 'dt' => 10),
            array('db'=> 'id_tema', 'dt' => 11),
            array('db'=> 'kd_format', 'dt' => 12),
            array('db'=> 'kdppum', 'dt' => 13),
            array('db'=> 'kdpkab', 'dt' => 14),
            array('db'=> 'kdcpum', 'dt' => 15),
            array('db'=> 'kdepum', 'dt' => 16),
            array('db'=> 'wadmpr', 'dt' => 17),
            array('db'=> 'wadmkk', 'dt' => 18),
            array('db'=> 'wadmkc', 'dt' => 19),
            array('db'=> 'wadmkd', 'dt' => 20),
            array('db'=> 'keperluan', 'dt' => 21),
            array('db'=> 'keterangan_tambahan', 'dt' => 22),
            array('db'=> 'atas_nama', 'dt' => 23),
            array('db'=> 'pelaksana', 'dt' => 24),
            array('db'=> 'nama_tema', 'dt' => 25),
            array('db'=> 'nama_subjek', 'dt' => 26),
            array('db'=> 'keterangan', 'dt' => 27),
            array('db'=> 'is_print', 'dt' => 28),
        );

        

         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);

        
    }
   
    
    public function ssp_paket_hist($id) {
      
       
        $table = 'spatial.v_ptp_kppr_berusaha_hist';
        $primaryKey = 'gid'; //test        
        $where="gid =".$id;
    
        $columns = array(
            array('db' => 'gid', 'dt' => 0),
            array('db' => 'proses', 'dt' => 1),
            array('db' => 'oleh' , 'dt'  =>2) , 
            array('db' => 'waktu' , 'dt'  =>3) , 
            array('db' => 'id_hist' , 'dt'  =>4) , 
        );
         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);
    }
    
    public function ssp_paket_pdf($id) {
      
       
        $table = 'v_print_peta';
        $primaryKey = 'id_peta'; //test        
        $where="id_ptp =".$id;
    
        $columns = array(
            array('db' => 'id_peta', 'dt' => 0),
            array('db' => 'judul', 'dt' => 1),
        );
         datatable_ssp($table, $primaryKey, $columns, $where);
        //  echo json_encode(['data' => array(),'draw' => 0, 'recordsTotal'=>0,'recordsFiltered'=>0]);
    }

    public function ajax_delete($id) {
        $this->db->where('id_form', $id);
        $this->db->delete('spatial.form_petanalisis');
        
        
        // $this->M_model->delete_gis_by_id('dok_pt_tnh_timbul',$arr);
        // $this->M_model->delete_by_id('dok_neraca','kd_kabkot', $id);
        echo json_encode(array("status" => TRUE));
    }

    public function insertShp()
    {

        

        $geom = json_decode($this->input->post('geom', TRUE));
        // $created_by = $this->session->userdata('users')['id_user'];
        $pelaksana = $this->session->userdata('users')['id_user'];
        

        $data = [ 
            'wadmpr' => $this->input->post('wadmpr',true),
            'wadmkk' => $this->input->post('wadmkk',true),
            'lokasi_surat' => $this->input->post('wadmkk',true),
            'wadmkc' => $this->input->post('wadmkc',true), 
            'wadmkd' => $this->input->post('wadmkd',true),  
            'no_berkas' => $this->input->post('no_berkas',true),
            'tanggal_surat' => $this->input->post('tanggal_surat',true), 
            'nama_pemohon' => $this->input->post('nama_pemohon',true),
            'alamat' => $this->input->post('alamat',true), 
            'nik' => $this->input->post('nik',true),
            'jabatan_pemohon' => $this->input->post('jabatan_pemohon',true),
            'id_subjek' => $this->input->post('id_subjek',true),
            'atas_nama' => $this->input->post('atas_nama',true), 
            'luas_tanah' => $this->input->post('luas_tanah',true),
            'kdppum' => $this->input->post('kdppum',true),
            'kdpkab' => $this->input->post('kdpkab',true),
            'kdcpum' => $this->input->post('kdcpum',true),
            'kdepum' => $this->input->post('kdepum',true),
            'id_tema' => $this->input->post('id_tema',true),
            'kd_format' => $this->input->post('kd_format',true),
            'keperluan' => $this->input->post('keperluan',true), 
            'keterangan_tambahan' => $this->input->post('keterangan_tambahan',true),
            'pelaksana' => $pelaksana,
            'created_by' => $pelaksana,
            'created_at' => date('Y-m-d')
             
        ];

        
        
        if ($geom != '' ) {
            $str = '';
            $type = @$geom->geometry->type;
           
            if(!$type == ''){
                if($type == 'Polygon'){
                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->features[0]->geometry->coordinates[0];
                        
                    }
                        $str .="(";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[0].' '.$v[1].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .="),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'Polygon('.$str.')';
                    $data['geom'] = $geom;
                }
            }else{
                $type = @$geom->features[0]->geometry->type;
                if($type == 'Polygon'){
                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->features[0]->geometry->coordinates[0];
                        
                    }
                        $str .="(";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[1].' '.$v[0].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .="),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'Polygon('.$str.')';
                    $data['geom'] = $geom;
                }
            }
            
        }
        
        // echo "<pre>";
        // print_r ($data);
        // echo "</pre>";exit();
        
      
        $gid = $this->input->post('gid', TRUE);
        // if($gid=='' ){
            $ins = $this->db->insert('spatial.form_petanalisis', $data);
            // echo $this->db->last_query();
            
            // echo json_encode(['sts' => 'sukses']);
            // $gid = $this->db->query('SELECT last_value FROM spatial.ptp_kppr_berusaha_gid_seq')->row_array()['last_value'];            
            // $this->save_image($gid);
            echo json_encode(array("status" => TRUE));

    }

    public function save_image($gid) {
        
       
        
        if (is_array($_FILES)) {
            
            
            foreach ($_FILES as $key => $value) {
                if($_FILES[$key]['name'] != ''){

                    $allowed_type = array('image/jpeg'=>1,'image/png'=>1,);
                    $filetype = mime_content_type($_FILES[$key]['tmp_name']);
                    if (@$allowed_type[$filetype]) {
                        $nama_dir = FCPATH . 'uploads/foto_digit/'.date('Ym').'/'.$gid;
                        $nama_file = date('Ymdhis').'_'.$_FILES[$key]['name'];
                        if (is_dir($nama_dir)) {
                            
                        } else {
                            $oldmask = umask(0);
                            mkdir($nama_dir, 0777, true);
                            umask($oldmask);
                        }
                            $sourcePath = $_FILES[$key]['tmp_name'];
                            $up = move_uploaded_file($sourcePath, $nama_dir.'/'.$nama_file);
                            $created_by = $this->session->userdata('users')['id_user'];
                            $data = [ 
                                "gid" => $gid, 
                                "filename" => $nama_file, 
                                "filepath" => 'uploads/foto_digit/'.date('Ym').'/'.$gid, 
                                "created_by" => $created_by,
                                "created_at" => date('Y-m-d H:i:s'),
                                "updated_at" => date('Y-m-d H:i:s'),
                            ];
                            $this->db->insert('dok_ptp_berusaha',$data);
                    }else{
                        echo json_encode(['status' => false ,'sts' => 'fail', 'msg' => 'Type FIle Salah atatu File Rusak!']);
                        exit();
                }
            }
            
            }

        }
            echo json_encode(array("status" => TRUE));
        }
        
        
        
        public function updateShp()
        {

            
            $id = $this->input->post('xid_form', TRUE);
            $geom = json_decode($this->input->post('xgeom', TRUE));
            $created_by = $this->session->userdata('users')['id_user'];
        $data = [
                    'wadmpr' => $this->input->post('xwadmpr',true),
                    'wadmkk' => $this->input->post('xwadmkk',true),
                    'wadmkc' => $this->input->post('xwadmkc',true),
                    'wadmkd' => $this->input->post('xwadmkd',true),
                    'lokasi_surat' => $this->input->post('xwadmkk',true),
                    'no_berkas' => $this->input->post('xno_berkas',true),
                    'tanggal_surat' => $this->input->post('xtanggal_surat',true),
                    'nama_pemohon' => $this->input->post('xnama_pemohon',true),
                    'alamat' => $this->input->post('xalamat',true),
                    'nik' => $this->input->post('xnik',true),
                    'jabatan_pemohon' => $this->input->post('xjabatan_pemohon',true),
                    'id_subjek' => $this->input->post('xid_subjek',true),
                    'atas_nama' => $this->input->post('xatas_nama',true),
                    'luas_tanah' => $this->input->post('xluas_tanah',true),
                    'kdppum' => $this->input->post('xkdppum',true),
                    'kdpkab' => $this->input->post('xkdpkab',true),
                    'kdcpum' => $this->input->post('xkdcpum',true),
                    'kdepum' => $this->input->post('xkdepum',true),
                    'id_tema' => $this->input->post('xid_tema',true),
                    'kd_format' => $this->input->post('xkd_format',true),
                    'keperluan' => $this->input->post('xkeperluan',true), 
                    'keterangan_tambahan' => $this->input->post('xketerangan_tambahan',true),
                    'updated_by' => $created_by   
        ];
     

        if ($geom != '' ) {
            $str = '';
            $type = @$geom->geometry->type;
           
            if(!$type == ''){
                if($type == 'Polygon'){
                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->features[0]->geometry->coordinates[0];
                        
                    }
                        $str .="(";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[0].' '.$v[1].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .="),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'Polygon('.$str.')';
                    $data['geom'] = $geom;
                }
            }else{
                $type = @$geom->features[0]->geometry->type;
                if($type == 'Polygon'){
                    $dataGeom = @$geom->geometry->coordinates[0];
                    if ($dataGeom == '') {
                        $dataGeom = @$geom->features[0]->geometry->coordinates[0];
                        
                    }
                        $str .="(";
                        foreach ($dataGeom as $k => $v) {
                            $str .=' '.$v[0].' '.$v[1].',';
                        }
                        $str = substr($str, 0, -1);
                        $str .="),";
                        
                    $str = substr($str, 0, -1);
                    $geom = 'Polygon('.$str.')';
                    $data['geom'] = $geom;
                }
            }
        }
        
        // echo "<pre>";
        // print_r ($data);
        // echo "</pre>";exit();
        
        if (!empty($data)) {
            echo 'masuk if '.$id;
            $this->db->where('id_form', $id);
            $this->db->update('spatial.form_petanalisis', $data);
            echo $this->db->last_query();
            // $this->db->select('last_value');
            // $last_hist = $this->db->get('spatial.ptp_kppr_berusaha_hist_id_hist_seq')->row_array()['last_value'];
            
            // $this->db->where('id_hist', $last_hist);
            // $this->db->update('spatial.ptp_kppr_berusaha_hist', ['updated_by' => $created_by]);
            // $this->save_image($id);
            // $del = $this->input->post('deleted_foto',TRUE);
            // if (!empty($del)) {
            //     foreach ($del as $key => $value) {
            //         $this->db->where('id_dok', $value);
            //         $this->db->delete('dok_ptp_berusaha');
            //     }
            // }
            
            echo json_encode(['sts' => 'sukses']);

            exit();
        }
        echo $this->db->last_query();
        
        echo json_encode(['sts' => 'sukses']);

    }

    public function uploadShp()
    {
        $layer = explode('&&',$this->input->post('layer', TRUE));
        $tabel='ptp_kppr_berusaha';
        //mencari kolom dari tabel yang sesuai 
        $this->db->where('table_schema', 'spatial');
        $this->db->where('table_name', $tabel);
        $column = $this->db->get('v_column')->result();
        // echo $this->db->last_query();
        
        // echo "<pre>";
        // print_r ($column);
        // echo "</pre>"; exit();
        // mengambil data file upload
        $file = file_get_contents($_FILES['filess']['tmp_name']);
        $exp = explode('.',$_FILES['filess']['name']);
        $name= $exp[0];
        $ext=$exp[1];

        $prov = $this->M_model->getInisialProv($this->input->post('ukd_prov', TRUE));
        // echo $this->db->last_query();
        
        $kdprov = $this->input->post('ukd_prov', TRUE);
        $kdkab = $this->input->post('ukd_kabkot', TRUE);
        $kabkot = $this->M_model->getInisialKabkot($this->input->post('ukd_kabkot', TRUE));
        // print_r($prov);
        $nm_kabkot=str_replace(' ','_',$kabkot['nama_kabkot']);
        $nm_prov=str_replace(' ','_',$prov['nama_prov']);
        $tahun_data = $this->input->post('utahun_data', TRUE);
        
        $sourcePath = $_FILES['filess']['tmp_name'];
        $nama_dir = FCPATH . 'upload_shp/';
        $nama_file=$name.'.'.$ext;
        if (!is_dir($nama_dir)) {
            
            mkdir($nama_dir,0777,true);
        }
        move_uploaded_file($sourcePath, $nama_dir.$nama_file);
        chmod($nama_dir.$nama_file,0777);
        // if (file_exists('upload_shp/bangai_kepu.shp')) {
        $zip = new ZipArchive();
        // $source_file = $_FILES['filess']["tmp_name"];
        // open the zip file to extract
        // echo '';
        if ($zip->open($nama_dir.$nama_file) !== true) {
            echo "Could not unzip file";
            exit;
        }

        // place in the temp folder
        if ($zip->extractTo($nama_dir) !== true) {
            $zip->close();
            echo "Could not extract files to $nama_dir folder";
            exit;
        }
        $zip->close();
        $shpname = $nama_dir.$name.'.shp';
        try {
            // Open Shapefile
            // $Shapefile = new ShapefileReader('upload_shp/kepulauan.shp');
            // $Shapefile = new ShapefileReader($file);
            $Shapefile = new ShapefileReader($nama_dir.$name.'.shp');
            // Read all the records
            $data=[];
            while ($Geometry = $Shapefile->fetchRecord()) {
              
                $isiDatas=[];
                $cekwil=[];
                foreach ($Geometry->getDataArray() as $key => $value) {
                    $k=strtolower($key);
                    $value = str_replace('*' ,0,$value);
                    $isiDatas[$k]=$value;
                    // echo $k.'/'.strtolower($value)."/".$tahun_data."<pre>";
                    if($k=='kdppum' && $value != $kdprov){
                        echo json_encode(['sts' => "gagal", 'msg' => 'Provinsi Tidak Sama Dengan Data yang Dipilih']);
                        exit();
                    }
                    if($k=='kdpkab' && $value != $kdkab){
                        echo json_encode(['sts' => "gagal", 'msg' => 'Kabkupaten/kota Tidak Sama Dengan Data yang Dipilih']);
                        exit();
                     
                    }
                    if($k=='tahun_data' && $value != $tahun_data){
                        echo json_encode(['sts' => "gagal", 'msg' => 'Tahun Tidak Sama Dengan Data yang Dipilih']);
                        exit();
                    }
                }

                foreach ($isiDatas as $k => $v ) {
                        
                    foreach ($column as $k2 => $v2) {
                        
                        //  pencocokan kolom geojson dengan kolom tabel
                        if ($v2->column_name == $k && $k != 'gid') {
                            $isiData[$k]=$v;
                        }
                    }
                }

                $isiData['geom'] = $Geometry->getWKT();
                array_push($data,$isiData);
                $type = explode('(',$Geometry->getWKT());
                $type = $type[0];
            }
            
            
            foreach ($data as $key => $value) {
                unset($value['geom']);
                $this->db->delete("spatial.".$tabel, $value);
            }
            
            $this->db->insert_batch('spatial.'.$tabel, $data);
            // echo $this->db->last_query();
            

            // $data = [
            //     'judul_dok'=>$layer[0],
            //     'nm_dok'=>$layer[0],
            //     'path'=>$nama_dir.$nama_file,
            //     'kdpkab'=>$kdkab,
            //     'id_layer'=>$layer[3],
            //     'tahun_data'=>$tahun_data
            // ];

            // $this->db->insert('dok_ptpil_b',$data);
            foreach (glob($nama_dir.'/'.$name.'*') as $filename) {
                unlink($filename);
            }
            echo json_encode(['sts' => "success"]);
            

        } catch (ShapefileException $e) {
            // Print detailed error information
            // echo "Error Type: " . $e->getErrorType()
            //     . "\nMessage: " . $e->getMessage()
            //     . "\nDetails: " . $e->getDetails();
            // switch ($e->getErrorType()) {
            //     case Shapefile::ERR_GEOM_RING_AREA_TOO_SMALL:
            //     case Shapefile::ERR_GEOM_RING_NOT_ENOUGH_VERTICES:
            //     case Shapefile::ERR_GEOM_POLYGON_OPEN_RING:
                    require_once(FCPATH."env.php");
                    $shpname = str_replace(array(' ','(',')'),array('\ ','\(','\)'),$shpname);
                    $str = 'ogr2ogr -update -append -f "PostgreSQL" PG:"host='.WGI_DB_LRS_HOST.' port='.WGI_DB_LRS_PORT.' user='.WGI_DB_LRS_USER.' dbname='.WGI_DB_LRS_DB.' password='.WGI_DB_LRS_PWD.'" -nln spatial.'.$tabel.' '.$shpname.' -progress -nlt MULTIPOLYGON';
                    $arr = [
                            'kdppum' => $kdprov,
                            'kdpkab' => $kdkab,
                            'tahun_data' =>$tahun_data
                        ];
                    $this->db->delete("spatial.".$tabel, $arr);
                    
                    exec($str,$a,$b);
                    foreach (glob($nama_dir.'/'.$name.'*') as $filename) {
                        unlink($filename);
                    }
                    if (@$a[0] == '0...10...20...30...40...50...60...70...80...90...100 - done.') {
                        echo json_encode(['sts' => "success"]);
    
                    }else{
                        echo json_encode(['sts' => "gagal", 'msg' => 'Gagal Simpan Data']);
                    }
                    
                    // echo "<pre>";
                    // print_r ($a);
                    // echo "</pre>";
                    
                    // echo "<pre>";
                    // print_r ($b);
                    // echo "</pre>";
                    
                    // echo $str;
                    
                    // echo json_encode(['sts' => "gagal", 'msg' => 'Beberapa Type Data Geometry Invalid, Mohon Perbaiki Terlebih Daluhu.']);
                        // exit();
            // }
        }

        
    }

    
    function getFoto($id) {
        $this->db->where('gid', $id);        
        $datas = $this->db->get('dok_ptp_berusaha')->result();
        $html = $this->load->view('peta_analisis/foto_edit', ['data' => $datas], true);
        
        echo json_encode($html);
    }

    function printAnalis($id='') {
        // echo $view;exit();
        $this->db->select('form_petanalisis.*,st_asgeojson(geom) as geom_json');
        $this->db->where('id_form', $id);
        $dataPtp = $this->db->get('spatial.form_petanalisis')->row_array();
        
        $session = $this->session->userdata('users');
        header("Access-Control-Allow-Origin: *");
        $data = array();
        
        $title = "Peta Integrasi Basdat";
        
        $js_file_print = $this->load->view('peta_analisis/js_files', '', true);
        $data = array(/* "modal_filter" => $modal_filter, */

            "title" => $title,
            "jv_script" => $js_file_print,
            "session" => $session,
            'id'=> $id,
            'viewName'=> 'ptp_berusaha',
            'data'=> $dataPtp
        );
    
        
        
        // $this->load->view('index', $data);
        $this->load->view('indexs', $data);

    }

    function uploadPeta() {

        
        // echo "<pre>";
        // print_r ($_FILES);
        // echo "</pre>";
        
        // echo "<pre>";
        // print_r ($_POST);
        // echo "</pre>";exit();
        

        if ($_FILES) {
            // check type fle upload
            $allowed_type = array('image/png'=>1);
            $filetype = mime_content_type($_FILES['zoom']['tmp_name']);

            if (@$allowed_type[$filetype]) {
            }else{
                echo json_encode(['sts' => 'fail', 'msg' => 'Type FIle Salah atatu File Rusak!']);
                exit();
            }
                $date = date('Ymdhis');
                $dir = './uploads/print_peta/'.$date;
                if (!is_dir($dir)) {
                    mkdir($dir, 0777, true); // The third parameter true creates nested directories if needed
                }
                $_FILES['zoom']['name'] = $date.str_replace('image_','',$_FILES['zoom']['name']);
                $config['upload_path']          = './uploads/print_peta/'.$date.'/';
                $config['allowed_types']        = 'png';
                $this->load->library('upload', $config);
                $file1 = $this->upload->data()['file_name'];
                if ( ! $this->upload->do_upload('zoom'))
                {
                    $error = $this->upload->display_errors();
                    
                    echo "<pre>";
                    print_r ($error);
                    echo "</pre>";
                    
					// echo json_encode(['sts' => 'fail','msg' => 'Upload Lampiran Sukses!']);
                }else{
                    $_FILES['lokasi']['name'] = $date.str_replace('image_','',$_FILES['lokasi']['name']);
                    $config['upload_path']          = './uploads/print_peta/'.$date.'/';
                    $config['allowed_types']        = 'png';
                    $this->load->library('upload', $config);
                    $file2 = $this->upload->data()['file_name'];
                    if ( ! $this->upload->do_upload('lokasi'))
                    {
                        $error = $this->upload->display_errors();
                        
                        echo "<pre>";
                        print_r ($error);
                        echo "</pre>";
                        
                        // echo json_encode(['sts' => 'fail','msg' => 'Upload Lampiran Sukses!']);
                    }else{
                        $_FILES['mata_angin']['name'] = $date.str_replace('image_','',$_FILES['mata_angin']['name']);
                        $config['upload_path']          = './uploads/print_peta/'.$date.'/';
                        $config['allowed_types']        = 'png';
                        $this->load->library('upload', $config);
                        $file2 = $this->upload->data()['file_name'];
                        if ( ! $this->upload->do_upload('mata_angin'))
                        {
                            $error = $this->upload->display_errors();
                            
                            echo "<pre>";
                            print_r ($error);
                            echo "</pre>";
                            
                            // echo json_encode(['sts' => 'fail','msg' => 'Upload Lampiran Sukses!']);
                        }else{
                           
                            $view = $this->input->post('viewName',true);
                            $gid = $this->input->post('id_form',true);
                            $jnsPdf = $this->input->post('jns_pdf',true);
                            $this->db->where('jns_pdf', $jnsPdf);
                            $this->db->where('id_form', $gid);
                            $cek = $this->db->get('print_peta_analisis')->result();
                            $tgl = $this->input->post('tanggal',true);
                            $year = date("Y", strtotime($tgl));          
                            $ins = [
                                'tahun_peta' => $year,
                                'file_peta' => $date.'/'.$date.'zoom.png',
                                'file_lokasi' => $date.'/'.$date.'lokasi.png',
                                'file_mata_angin' => $date.'/'.$date.'mata_angin.png',
                                'id_form' => $this->input->post('id_form',true),
                                'dianalisis' => $this->input->post('dianalisis',true),
                                'tanggal' => $this->input->post('tanggal',true),
                                // 'digambar' => $this->input->post('digambar',true),
                                'diperiksa' => $this->input->post('diperiksa',true),
                                'jabatan' => $this->input->post('jabatan',true),
                                'kepala_nama' => $this->input->post('kepala_nama',true),
                                'kepala_nip' => $this->input->post('kepala_nip',true),
                                // 'jns_pdf' => $this->input->post('jns_pdf',true) == '' ? '1' : $this->input->post('jns_pdf',true),
                            ];
                            if(count($cek) == 0){
                                
                                $this->db->insert('print_peta_analisis', $ins);
                                $last_id =  $this->db->insert_id();
                                $legenda = $this->input->post('legenda',true);
                                
                                // s
                                
                                $sumber_peta = $this->input->post('sumber_peta',true);
                                if (count($sumber_peta) > 0 ) {
                                    foreach ($sumber_peta as $key => $value) {
                                        $sp= [
                                            'id_print_peta' => $last_id,
                                            'text'  => $value
                                        ];
                                        $this->db->insert('print_sp_analisis', $sp);
                                        
                                    }
                                }
                                echo $gid;
                            }else{
                                $last_id =  $cek[0]->id_peta;
                                // echo $last_id;exit();
                                $this->db->where('id_form', $last_id);
                                $this->db->update('print_peta_analisis', $ins);

                                $this->db->where('id_print_peta', $last_id);
                                $this->db->delete('print_sp_analisis');
                                $sumber_peta = $this->input->post('sumber_peta',true);
                                if (count($sumber_peta) > 0 ) {
                                    foreach ($sumber_peta as $key => $value) {
                                        $sp= [
                                            'id_print_peta' => $last_id,
                                            'text'  => $value
                                        ];
                                        $this->db->insert('print_sp_analisis', $sp);
                                        
                                    }
                                }
                                echo $gid;
                            }
                        }
                    }
            }

        }
        
       

        return $this->upload->data('file_name');
    }

    function export_pdf_peta($id='1'){
       
        $this->db->where('v_print_analisis.id_form', $id);
        $data = $this->db->get('v_print_analisis')->row_array();

        
        if(empty($data)){
            $pdf = new FPDF('P', 'mm',[215,330]);
            $pdf->SetFillColor(255,255,255);
            $pdf->SetMargins(10, 5, 10,5);
            $pdf->AddPage();
            $pdf->SetFont('Times','',20);
            $pdf->Cell(190,30,'Peta Belum Tersedia, Silahkan Print Peta Terlebih Dahulu.','',1,'C',true);
            $pdf->Output();

        }
        $legenda = $this->db->get('r_legenda')->result();
        $this->db->where('id_print_peta', $data['id_peta']);
        $sumber_peta = $this->db->get('print_sp_analisis')->result();


        foreach ($data as $key => $value) {
            // echo $data[$key];
            if(empty($value)){
                $data[$key] = ' - ';
            }
        }
        $geoJSON = json_decode(@$data['geom'],true);

        $dmsCoordinates = $this->geoJSONToDMS($geoJSON);
        
        $n=0;
        $strDms ='';
        foreach ($dmsCoordinates as $key => $value) {
            $strDms .= $value['longitude'].' '.$value['latitude']."\n";
        }
        
        // echo "<pre>";
        // print_r ($strDms);
        // echo "</pre>";exit();
        
        $strDms = utf8_decode($strDms);

        // $pdf = new FPDF('P', 'mm',[210,297]);
        $pdf = new FPDF('P', 'mm',[215,330]);
        // $pdf = new FPDF('P', 'mm','f4');
        $pdf->isUTF8 = true;
        $pdf->SetMargins(10, 5, 10,5);
        $pdf->AddPage();
        
        // form page
        $pdf->SetFillColor(255,255,255);
        
        $pdf->SetFont('Times','',16);
        $pdf->Cell(10,8,'','',0,'C',true);
        $pdf->Cell(180,8,'KANTOR PERTANAHAN '.strtoupper(@$data['wadmkk']),'',1,'C',true);
        $pdf->Cell(10,8,'','',0,'C',true);
        $pdf->Cell(180,8,'PROVINSI '.strtoupper(@$data['wadmpr']),'',1,'C',true);
        $pdf->SetFont('Times','',12);
        $pdf->Cell(3,5,'','',1,'C',true);
        $pdf->Cell(3,5,'','',0,'C',true);
        $pdf->Cell(180,5,'','T',1,'C',true);
        
        $pdf->Cell(20,5,'Nomor','',0,'L',true);
        $pdf->Cell(90,5,': '.@$data['no_berkas'],'',0,'L',true);
        $pdf->Cell(70,5,$this->camel(@$data['wadmkk']).', '.tanggal_indonesia(@$data['tanggal']),'',1,'L',true);
        $pdf->Cell(20,5,'Lampiran','',0,'L',true);
        $pdf->Cell(90,5,': 1 (Satu) Berkas','',0,'L',true);
        $pdf->Cell(70,5,'Kepada ','',1,'L',true);
        
        $pdf->Cell(20,5,'Perihal','',0,'L',true);
        $pdf->Cell(90,5,': Permohonan Layanan Peta Analisis ','',0,'L',true);
        $pdf->Cell(70,5,'Yth. Kepala Kantor Wilayah BPN ','',1,'L',true);

        $pdf->Cell(22,5,'','',0,'L',true);
        $pdf->Cell(88,5,$this->camel(@$data['nama_tema']),'',0,'L',true);
        $pdf->Cell(70,5,'Provinsi/Kepala Kantor ','',1,'L',true);
        $pdf->Cell(20,5,'','',0,'L',true);
        $pdf->Cell(90,5,'   ','',0,'L',true);
        $pdf->Cell(70,5,'Pertanahan Kabupaten/Kota ','',1,'L',true);
        $pdf->Cell(20,5,'','',0,'L',true);
        $pdf->Cell(89,5,'   ','',0,'L',true);
        $pdf->Cell(70,5,$this->camel(str_replace('KAB.','',$data['wadmkk'])),'',1,'L',true);
        $pdf->Cell(20,5,'','',0,'L',true);

        $pdf->Cell(180,5,'','',1,'C',true);
        $pdf->Cell(110,5,'   ','',0,'L',true);
        $pdf->Cell(70,5,'di','',1,'L',true);
        $pdf->Cell(110,5,'   ','',0,'L',true);
        $pdf->Cell(70,5,'Tempat','B',1,'L',true);
        
        $pdf->SetX($pdf->GetX()+10);
        $pdf->Cell(70,5,'Yang bertandatangan di bawah ini :','',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'1. Nama','',0,'L',true);
        $pdf->Cell(115,5,': '.@$data['nama_pemohon'],'',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'2. Alamat','',0,'L',true);
        $pdf->MultiCell(115,5,': '.@$data['alamat'],'',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'3. NIK','',0,'L',true);
        $pdf->Cell(115,5,': '.@$data['nik'],'',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'3. Bertindak untuk','',0,'L',true);
        $pdf->Cell(115,5,': '.@$data['atas_nama'],'',1,'L',true);
        $pdf->SetX($pdf->GetX()+15);
        $pdf->Cell(48,5,'  dan atas nama','',0,'L',true);
        $pdf->Cell(115,5,' ','',1,'L',true);
        
        $pdf->SetY($pdf->GetY()+5);
        $pdf->SetX($pdf->GetX()+10);
        $pdf->MultiCell(180,5,'dengan ini mengajukan permohonan layanan peta kesesuaian penggunaan tanah terhadap RTRW/ketersediaan tanah','',1,'L',true);
        
        $pdf->SetY($pdf->GetY()+5);
        $pdf->Cell(180,5,'KETERANGAN TENTANG TANAH YANG DIMOHON:','',1,'C',true);
        
        $pdf->SetY($pdf->GetY()+5);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'1. Luas','',0,'L',true);
        $pdf->Cell(115,5,': '.@$data['luas_tanah'].' ha','',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'2. Letak','',1,'L',true);
        
        $pdf->SetX($pdf->GetX()+17.5);
        $pdf->Cell(45.5,5,'Desa / Kelurahan','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['wadmkd']),'',1,'L',true);
        $pdf->SetX($pdf->GetX()+17.5);
        $pdf->Cell(45.5,5,'Kecamatan','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['wadmkc']),'',1,'L',true);
        $pdf->SetX($pdf->GetX()+17.5);
        $pdf->Cell(45.5,5,'Kabupaten / Kota','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['wadmkk']),'',1,'L',true);
        $pdf->SetX($pdf->GetX()+17.5);
        $pdf->Cell(45.5,5,'Koordinat (DMS)','',0,'L',true);
        $pdf->Cell(2,5,': ','',0,'L',true);
        $pdf->MultiCell(114,5,''.$strDms,'',1,'L',true);


        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'3. Tema Analisis','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['nama_tema']),'',1,'L',true);
        
        

        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'4. Format Peta','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['format']),'',1,'L',true);

        $pdf->SetY($pdf->GetY()+5);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->MultiCell(180,5,'Demikian disampaikan untuk menjadi bahan pertimbangan.','',1,'L',true);
        
        $pdf->SetY($pdf->GetY()+5);
        $pdf->SetX($pdf->GetX()+120);
        $pdf->Cell(30,5,'Hormat kami,','',1,'L',true);
        $pdf->SetY($pdf->GetY()+20);
        $pdf->SetX($pdf->GetX()+120);
        $pdf->SetFont('Times','U',12);
        $pdf->Cell(30,5,$this->camel(@$data['nama_pemohon']),'',1,'L',true);
        $pdf->SetFont('Times','',12);
        $pdf->SetY($pdf->GetY()+1);
        $pdf->SetX($pdf->GetX()+120);
        $pdf->Cell(30,5,$this->camel(@$data['jabatan_pemohon']),'',1,'L',true);
        $pdf->Cell(30,5,'Keterangan : ','',1,'L',true);
        $pdf->Cell(30,5,'*) Jika Tersedia','',1,'L',true);
        $pdf->Cell(30,5,'**) Dipilih sesuai layanan yang dimohon.','',1,'L',true);
        $url_logos = 'assets/themes/adminity/images/logo_bpn.png';
        // peta page
            $pdf->Image($url_logos,12, 3, 20, 0);
        $pdf->AddPage();
        
        $pdf->SetFont('Arial','',7);
        $url_logo = 'assets/themes/adminity/images/logo_bpn.png';
        $url_peta = 'uploads/print_peta/'.$data['file_peta'];
        $lokasi = 'uploads/print_peta/'.$data['file_lokasi'];
        $mata_angin = 'uploads/print_peta/'.$data['file_mata_angin'];
        // $legenda4 = 'uploads/legenda/legenda_default_kemampuan_tanah.png';
        $legenda4 = 'uploads/legenda/legend4def.png';
        $legenda4lereng = 'uploads/legenda/legend4lereng.png';
        $legenda4tekstur = 'uploads/legenda/legend4tekstur.png';
        $markRed = 'uploads/print_peta/default/red_location.png';
        // $pdf->SetTextColor(255,255,255);
        
        $pdf->SetFillColor(255,255,255);
        
        $pdf->Cell(20,6,'','TL',0,'C',true);
        $pdf->Cell(45,6,'','TR',0,'C',true);
        $pdf->Cell(65,6,'','LT',0,'C',true);
        $pdf->Cell(65,6,'','TR',1,'C',true);
        
        $str =  'KANTOR PERTANAHAN '.str_replace('KAB.',' KABUPATEN ',@$data['wadmkk']).' PROVINSI '.@$data['wadmpr'];
        $line = count($this->cutTextIntoArray($str, 25));
        $height = $line*3;
        $y = $pdf->GetY();
        $x = $pdf->GetX();
        $pdf->Cell(20,$height,'','L',0,'L',true);
        $pdf->MultiCell(45 , 3,$str, 'R', 'L',0);
        $pdf->SetY($y-3);
        
        $pdf->Cell(65,3,'','LR',0,'L',true);
        $pdf->Cell(130,3,'','LR',1,'L',true);
        $pdf->SetX($x+65);
        $pdf->Cell(25,3,'NAMA PEMOHON','L',0,'L',true);
        $pdf->Cell(105,3,': '.strtoupper(@$data['nama_pemohon']).' bertidak untuk dan atas nama '.strtoupper(@$data['nama_pemohon']),'R',1,'L',true);
        $pdf->SetX($x+65);
        $pdf->Cell(25,3,'NO. BERKAS:','L',0,'L',true);
        $pdf->Cell(105,3,': '.@$data['no_berkas'],'R',1,'L',true);
        $pdf->SetX($x+65);
        $pdf->Cell(25,3,'LUAS:','L',0,'L',true);
        $pdf->Cell(105,3,': '.str_replace(',','.',number_format(round(@$data['luas_tanah'],2))).' Ha','R',1,'L',true);
        $pdf->Cell(65,6,'','LBR',0,'L',true);
        $pdf->Cell(130,6,'','LBR',1,'L',true);

        $pdf->SetFont('Arial','B',10);
        $pdf->Cell(195,3,'','LTR',1,'C',true);
        $pdf->Cell(195,6,'PETA ANALISIS PENATAGUNAAN TANAH ','LR',1,'C',true);
        $pdf->Cell(195,6,'TEMA : '.@$data['nama_tema'],'LR',1,'C',true);
        $pdf->Cell(195,8,'Lokasi : Desa '. $this->camel(@$data['wadmkd']).' Kecamatan '.$this->camel(@$data['wadmkc']).' '.str_replace('Kab.',' Kabupaten ',$this->camel(@$data['wadmkk'])),'LR',1,'C',true);
        $pdf->Cell(195,3,'Tahun '.@$data['tahun_peta'],'LR',1,'C',true);
        $pdf->Cell(195,3,'','LRB',1,'C',true);
        $pdf->Cell(195,160,'','LRBT',1,'C',true);
        
        $pdf->SetFont('Arial','B',8);
        $pdf->Cell(80,1,'','LRT',0,'C',true);
        $pdf->Cell(115,1,'','RT',1,'C',true);
        $pdf->Cell(80,4,'PETUNJUK LOKASI','LR',0,'C',true);
        $pdf->Cell(115,4,'KETERANGAN/LEGENDA','LR',1,'C',true);
        $yPetunjuk = $pdf->GetY();
        $pdf->SetFont('Arial','',8);
        $enter = 0;
        $xLegenda = 0;
        $yLegenda =0;
        $ylereng = 0;
        $legendaYawal=0;
        

        $data['jns_pdf'] = 6;
        if($data['jns_pdf'] == 1 ){
            if(count($legenda) > 0){
                // $pdf->SetX($pdf->GetX()-65);
                foreach ($legenda as $key => $value) {
                    $url = 'uploads/legenda/'.$value->img;
                    
                    if($enter == 0){
                        // $pdf->SetX($pdf->GetX()+65);
                        $pdf->Cell(85,5,'','',0,'L',true);

                        $xx = $pdf->GetX()+5;
                        $yy = $pdf->GetY()+1;
                        $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($pdf->GetX()+15);
                        $pdf->Cell(50,5,$value->text,'',0,'L',true);
                        $enter = 1;
                        $xLegenda = $pdf->GetX();

                    }else{
                        $xx = $pdf->GetX()+5;
                        $yy = $pdf->GetY()+1;
                        $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($pdf->GetX()+15);
                        $pdf->Cell(50,5,$value->text,'',1,'L',true);
                        $enter = 0;
                        $xLegenda = $pdf->GetX();
                        
                    }
                }
                if($yLegenda < $legendaYawal+12){
                    $kurang = ($legendaYawal+12)-$yLegenda;
                    
                    // echo $kurang;exit();
                    // $pdf->SetY($legendaYawal+10);
                    if($xLegenda == 140){
                        $pdf->Cell(65,5,'','R',1,'C',true);
                    }
                    $pdf->Cell(65,$kurang,'','',0,'C',true);
                    $pdf->SetX($pdf->GetX()+125);
                    $pdf->Cell(5,$kurang,'','R',1,'C',true);
                    
                }
            }else{
                $pdf->Cell(65,30,'','LB',0,'C',true);
                $pdf->Cell(65,30,'','RB',1,'C',true);
            }
        
        }else if($data['jns_pdf'] == 2){
            $pdf->SetFont('Arial','',7);

            if(count($legenda) > 0){
                $legendaYawal = $pdf->GetY();
                foreach ($legenda as $key => $value) {
                    $url = 'uploads/legenda/'.$value->img;
                        $pdf->Cell(85,5,'','',0,'L',true);
                        $xx = $pdf->GetX()+5;
                        $yy = $pdf->GetY()+1;
                        $pdf->Image($url,$xx,$yy, 7, 0);
                        $pdf->SetX($pdf->GetX()+15);
                        $pdf->Cell(50,4,$value->text,'',1,'L',true);
                        $yLegenda = $pdf->GetY();

                }
                $pdf->SetY($legendaYawal);
                $xx = $pdf->GetX()+139;
                $pdf->SetX($xx);
                $pdf->Cell(66,3,'Penggunaan Tanah','R',1,'L',true);
                $penggunaan_tanah =[
                    '255 125 125' => 'Permukiman',
                    '200 225 100' => 'Persawahan',
                    '255 200 50' => 'Tegalan/Ladang',
                    '0 150 250' => 'Perairan Darat',

                ];
                foreach ($penggunaan_tanah as $key => $value) {
                        $xx = $pdf->GetX()+140;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);

                        // $pdf->Cell(5,1,'','',1,'L',true);
                        // $pdf->SetX($xx);
                        $rgb = explode(' ',$key);
                        $pdf->SetFillColor($rgb[0],$rgb[1],$rgb[2]);
                        $pdf->Cell(5,3,'','1',0,'L',true);
                        $pdf->SetFillColor(255, 255, 255);
                        $pdf->Cell(60,4,$value,'R',1,'L',true);

                        $xx = $pdf->GetX()+120;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);
                        $pdf->Cell(5,1,'','',0,'L',true);
                        $pdf->Cell(70,1,'','R',1,'L',true);
                        $ylereng = $pdf->GetY();

                }
                if($yLegenda < $legendaYawal+37){
                    $kurang = ($legendaYawal+37)-$yLegenda;
                    
                    // echo $kurang;exit();
                    // $pdf->SetY($legendaYawal+10);
                    if($xLegenda == 140){
                        $pdf->Cell(65,5,'','R',1,'C',true);
                    }
                    $pdf->Cell(65,$kurang,'','LR',0,'C',true);
                    $pdf->SetX($pdf->GetX()+125);
                    $pdf->Cell(5,$kurang,'','R',1,'C',true);
                    
                }

            }else{
                // $pdf->Cell(65,30,'','LB',0,'C',true);
                // $pdf->Cell(65,30,'','RB',1,'C',true);
            }
        }else if($data['jns_pdf'] == 3){
                $pdf->SetFont('Arial','',7);
    
                if(count($legenda) > 0){
                    $legendaYawal = $pdf->GetY();
                    foreach ($legenda as $key => $value) {
                        $url = 'uploads/legenda/'.$value->img;
                            $pdf->Cell(85,5,'','',0,'L',true);
                            $xx = $pdf->GetX()+5;
                            $yy = $pdf->GetY()+1;
                            $pdf->Image($url,$xx,$yy, 7, 0);
                            $pdf->SetX($pdf->GetX()+15);
                            $pdf->Cell(50,4,$value->text,'',1,'L',true);
                            $yLegenda = $pdf->GetY();
    
                    }
                    $pdf->SetY($legendaYawal);
                    $xx = $pdf->GetX()+129;
                    $pdf->SetX($xx);
                    $pdf->Cell(66,3,'Penguasaan Tanah','R',1,'L',true);
                    $penggunaan_tanah =[
                        '253 170 1' => 'Tanah Terdaftar',
                        '104 204 124' => 'Kawasan Hutan',
                        '255 255 255' => 'Hak Atas Tanah Lainnya',
    
                    ];
                    foreach ($penggunaan_tanah as $key => $value) {
                            $xx = $pdf->GetX()+130;
                            $yy = $pdf->GetY()+1;
                            // $pdf->Image($url,$xx,$yy, 10, 0);
                            $pdf->SetX($xx);
    
                            // $pdf->Cell(5,1,'','',1,'L',true);
                            // $pdf->SetX($xx);
                            $rgb = explode(' ',$key);
                            $pdf->SetFillColor($rgb[0],$rgb[1],$rgb[2]);
                            $pdf->Cell(5,3,'','1',0,'L',true);
                            $pdf->SetFillColor(255, 255, 255);
                            $pdf->Cell(60,4,$value,'R',1,'L',true);
    
                            $xx = $pdf->GetX()+120;
                            $yy = $pdf->GetY()+1;
                            // $pdf->Image($url,$xx,$yy, 10, 0);
                            $pdf->SetX($xx);
                            $pdf->Cell(5,1,'','',0,'L',true);
                            $pdf->Cell(70,1,'','R',1,'L',true);
                            $ylereng = $pdf->GetY();
    
                    }
                    if($yLegenda < $legendaYawal+37){
                        $kurang = ($legendaYawal+37)-$yLegenda;
                        
                        // echo $kurang;exit();
                        // $pdf->SetY($legendaYawal+10);
                        if($xLegenda == 140){
                            $pdf->Cell(65,5,'','R',1,'C',true);
                        }
                        $pdf->Cell(65,$kurang,'','LR',0,'C',true);
                        $pdf->SetX($pdf->GetX()+125);
                        $pdf->Cell(5,$kurang,'','R',1,'C',true);
                        
                    }
    
                }else{
                    // $pdf->Cell(65,30,'','LB',0,'C',true);
                    // $pdf->Cell(65,30,'','RB',1,'C',true);
                }
        }else if($data['jns_pdf'] == 4){
            $pdf->SetFont('Arial','',6);

            if(count($legenda) > 0){
                $legendaYawal = $pdf->GetY();
                foreach ($legenda as $key => $value) {
                    $url = 'uploads/legenda/'.$value->img;
                        $pdf->Cell(80,5,'','',0,'L',true);
                        $xx = $pdf->GetX()+5;
                        $yy = $pdf->GetY()+1;
                        $pdf->Image($url,$xx,$yy, 7, 0);
                        $pdf->SetX($pdf->GetX()+15);
                        $pdf->Cell(50,4,$value->text,'',1,'L',true);
                        $yLegenda = $pdf->GetY();

                }
                $pdf->SetY($legendaYawal);
                $xx = $pdf->GetX()+129;
                $pdf->SetX($xx);
                $pdf->Cell(20,3,'Lereng','',1,'L',true);
                $lereng = $this->db->get('simbologi')->result();

                foreach ($lereng as $key => $value) {
                        if($key == 5){
                            break;
                        }
                        $xx = $pdf->GetX()+130;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);
                        $rgb = explode(' ',$value->rgb_fill);
                        $pdf->SetFillColor($rgb[0],$rgb[1],$rgb[2]);
                        $pdf->Cell(5,2.5,'','',0,'L',true);
                        $pdf->SetFillColor(255, 255, 255);
                        $pdf->Cell(50,2.5,$value->jenis_simbologi,'',1,'L',true);

                        $xx = $pdf->GetX()+120;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);
                        $pdf->Cell(5,0.5,'','',0,'L',true);
                        $pdf->Cell(50,0.5,'','',1,'L',true);
                        $yLegenda = $pdf->GetY();

                }

                $dalam = [
                    'A' => '>90 cm',
                    'B' => '60 - 90 cm',
                    'C' => '30 - 60 cm',
                    'D' => '<30 cm',
                ];
                // $pdf->SetY($legendaYawal);
                $xx = $pdf->GetX()+129;
                $pdf->SetX($xx);
                $pdf->Cell(20,3,'Tekstur Tanah','',1,'L',true);
                foreach ($dalam as $key => $value) {
                    $pdf->Cell(75,5,'','LR',0,'L',true);
                    $xx = $pdf->GetX()+55;
                    $yy = $pdf->GetY()+1;
                    // $pdf->Image($url,$xx,$yy, 10, 0);
                    $pdf->SetX($xx);
                    $pdf->Cell(5,3,$key,'RBLT',0,'C',true);
                    $pdf->Cell(30,3,$value,'',1,'L',true);

                    $xx = $pdf->GetX()+120;
                    $yy = $pdf->GetY()+1;
                    // $pdf->Image($url,$xx,$yy, 10, 0);
                    $pdf->SetX($xx);
                    $pdf->Cell(5,1,'','',0,'L',true);
                    $pdf->Cell(50,1,'','',1,'L',true);
                    $yLegenda = $pdf->GetY();

                }


                $tekstur = [
                    '1' => 'Halus',
                    '2' => 'Sedang',
                    '3' => 'Kasar',
                ];
                $pdf->SetY($legendaYawal-3);
                $xx = $pdf->GetX()+159;
                $pdf->SetX($xx);
                $pdf->Cell(20,3,'Kedalaman Efektif','',1,'L',true);
                foreach ($tekstur as $key => $value) {
                    $xx = $pdf->GetX()+160;
                    $yy = $pdf->GetY()+1;
                    // $pdf->Image($url,$xx,$yy, 10, 0);
                    $pdf->SetX($xx);
                    $pdf->Cell(5,3,$key,'RBLT',0,'C',true);
                    $pdf->Cell(30,3,$value,'R',1,'L',true);

                    $xx = $pdf->GetX()+160;
                    $yy = $pdf->GetY()+1;
                    // $pdf->Image($url,$xx,$yy, 10, 0);
                    $pdf->SetX($xx);
                    $pdf->Cell(5,1,'','',0,'L',true);
                    $pdf->Cell(30,1,'','R',1,'L',true);
                    // $yLegenda = $pdf->GetY();

                }
                $drainase = [
                    'a' => 'Tidak Pernah Tergenang',
                    'b' => 'Tergenang Periodik',
                    'c' => 'Tergenang terus -menerus',
                ];
                // $pdf->SetY($legendaYawal);
                $xx = $pdf->GetX()+159;
                $pdf->SetX($xx);
                $pdf->Cell(36,3,'Drainase','R',1,'L',true);
                foreach ($drainase as $key => $value) {
                    $xx = $pdf->GetX()+160;
                    $yy = $pdf->GetY()+1;
                    // $pdf->Image($url,$xx,$yy, 10, 0);
                    $pdf->SetX($xx);
                    $pdf->Cell(5,3,$key,'RBLT',0,'C',true);
                    $pdf->SetFillColor(255, 255, 255);
                    $pdf->Cell(30,3,$value,'R',1,'L',true);

                    $xx = $pdf->GetX()+160;
                    $yy = $pdf->GetY()+1;
                    // $pdf->Image($url,$xx,$yy, 10, 0);
                    $pdf->SetX($xx);
                    $pdf->Cell(5,1,'','',0,'L',true);
                    $pdf->Cell(30,1,'','R',1,'L',true);
                    // $yLegenda = $pdf->GetY();

                }
                $erosi = [
                    'T' => 'Tidak ada erosi',
                    'E' => 'Erosi',
                ];
                // $pdf->SetY($legendaYawal);
                $xx = $pdf->GetX()+159;
                $pdf->SetX($xx);
                $pdf->Cell(36,3,'Erosi','R',1,'L',true);
                foreach ($erosi as $key => $value) {
                    $xx = $pdf->GetX()+160;
                    $yy = $pdf->GetY()+1;
                    // $pdf->Image($url,$xx,$yy, 10, 0);
                    $pdf->SetX($xx);
                    $pdf->Cell(5,3,$key,'RBLT',0,'C',true);
                    $pdf->SetFillColor(255, 255, 255);
                    $pdf->Cell(30,3,$value,'R',1,'L',true);

                    $xx = $pdf->GetX()+160;
                    $yy = $pdf->GetY()+1;
                    // $pdf->Image($url,$xx,$yy, 10, 0);
                    $pdf->SetX($xx);
                    $pdf->Cell(5,1,'','',0,'L',true);
                    $pdf->Cell(30,1,'','R',1,'L',true);
                    $yLegenda = $pdf->GetY();

                }
            }else{
                $pdf->Cell(65,30,'','LB',0,'C',true);
                $pdf->Cell(65,30,'','RB',1,'C',true);
            }
            
        }else if($data['jns_pdf'] == 5){
            $pdf->SetFont('Arial','',7);

            if(count($legenda) > 0){
                $legendaYawal = $pdf->GetY();
                foreach ($legenda as $key => $value) {
                    $url = 'uploads/legenda/'.$value->img;
                        $pdf->Cell(85,5,'','',0,'L',true);
                        $xx = $pdf->GetX()+5;
                        $yy = $pdf->GetY()+1;
                        $pdf->Image($url,$xx,$yy, 7, 0);
                        $pdf->SetX($pdf->GetX()+15);
                        $pdf->Cell(50,4,$value->text,'',1,'L',true);
                        $yLegenda = $pdf->GetY();

                }
                $pdf->SetY($legendaYawal);
                $xx = $pdf->GetX()+139;
                $pdf->SetX($xx);
                $pdf->Cell(76,3,'Rencana Pola Ruang :','R',1,'L',true);
                $pdf->SetX($xx);
                $pdf->Cell(6,3,'Kawasan Lindung','',1,'L',true);
                $kawasan_lindung =[
                    '56 167 0' => 'Hutan Lindung',
                ];
                $xxKW = $xx;
                foreach ($kawasan_lindung as $key => $value) {
                        $xx = $pdf->GetX()+140;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);

                        // $pdf->Cell(5,1,'','',1,'L',true);
                        // $pdf->SetX($xx);
                        $rgb = explode(' ',$key);
                        $pdf->SetFillColor($rgb[0],$rgb[1],$rgb[2]);
                        $pdf->Cell(5,3,'','1',0,'L',true);
                        $pdf->SetFillColor(255, 255, 255);
                        $pdf->Cell(60,4,$value,'R',1,'L',true);

                        $xx = $pdf->GetX()+120;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);
                        $pdf->Cell(5,1,'','',0,'L',true);
                        $pdf->Cell(70,1,'','R',1,'L',true);

                }

                $pdf->SetX($xxKW);
                $pdf->Cell(66,3,'Kawasan Budidaya','R',1,'L',true);
                $kawsasan_budidaya =[
                    '248 129 126' => 'Permukiman',
                    '194 242 135' => 'Sawah Irigasi',
                    '77 230 0' => 'Hutan Produksi',
                    '205 167 101' => 'Sempadan Sungai',
                ];
                foreach ($kawsasan_budidaya as $key => $value) {
                        $xx = $pdf->GetX()+140;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);

                        // $pdf->Cell(5,1,'','',1,'L',true);
                        // $pdf->SetX($xx);
                        $rgb = explode(' ',$key);
                        $pdf->SetFillColor($rgb[0],$rgb[1],$rgb[2]);
                        $pdf->Cell(5,3,'','1',0,'L',true);
                        $pdf->SetFillColor(255, 255, 255);
                        $pdf->Cell(60,4,$value,'R',1,'L',true);

                        $xx = $pdf->GetX()+120;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);
                        $pdf->Cell(5,1,'','',0,'L',true);
                        $pdf->Cell(70,1,'','R',1,'L',true);

                }
                // echo $yLegenda.'/'.($legendaYawal+30);
                // exit();
                if($yLegenda < $legendaYawal+32){
                    $kurang = ($legendaYawal+32)-$yLegenda;
                    
                    // echo $kurang;exit();
                    $pdf->SetY($legendaYawal+28);
                    if($xLegenda == 140){
                        $pdf->Cell(65,5,'','R',1,'C',true);
                    }
                    $pdf->Cell(65,$kurang,'','LR',0,'C',true);
                    $pdf->SetX($pdf->GetX()+125);
                    $pdf->Cell(5,$kurang,'','R',1,'C',true);
                    
                }

            }else{
                // $pdf->Cell(65,30,'','LB',0,'C',true);
                // $pdf->Cell(65,30,'','RB',1,'C',true);
            }
        }else if($data['jns_pdf'] == 6){
                $pdf->SetFont('Arial','',7);
    
                if(count($legenda) > 0){
                    $legendaYawal = $pdf->GetY();
                    foreach ($legenda as $key => $value) {
                        $url = 'uploads/legenda/'.$value->img;
                            $pdf->Cell(85,5,'','',0,'L',true);
                            $xx = $pdf->GetX()+5;
                            $yy = $pdf->GetY()+1;
                            $pdf->Image($url,$xx,$yy, 7, 0);
                            $pdf->SetX($pdf->GetX()+15);
                            $pdf->Cell(50,4,$value->text,'',0,'L',true);
                            $pdf->SetX($pdf->GetX()+60);
                            $pdf->Cell(5,4,'','R',1,'L',true);
                            $yLegenda = $pdf->GetY();
    
                    }
                    $pdf->SetY($legendaYawal);
                    $xx = $pdf->GetX()+129;
                    $pdf->SetX($xx);
                    $pdf->Cell(66,3,'Kesesuaian','R',1,'L',true);
                    $kesesuaian =[
                        '102 255 128' => 'Sesuai',
                        '208 255 115' => 'Cukup Sesuia',
                        '255 255 103' => 'Tidak Sesuai',
                    ];
                    foreach ($kesesuaian as $key => $value) {
                            $xx = $pdf->GetX()+130;
                            $yy = $pdf->GetY()+1;
                            // $pdf->Image($url,$xx,$yy, 10, 0);
                            $pdf->SetX($xx);
                            
                            // $pdf->Cell(5,1,'','',1,'L',true);
                            // $pdf->SetX($xx);
                            $rgb = explode(' ',$key);
                            $pdf->SetFillColor($rgb[0],$rgb[1],$rgb[2]);
                            $pdf->Cell(5,3,'','1',0,'L',true);
                            $pdf->SetFillColor(255, 255, 255);
                            $pdf->Cell(60,4,$value,'R',1,'L',true);
    
                            $xx = $pdf->GetX()+120;
                            $yy = $pdf->GetY()+1;
                            // $pdf->Image($url,$xx,$yy, 10, 0);
                            $pdf->SetX($xx);
                            $pdf->Cell(5,1,'','',0,'L',true);
                            $pdf->Cell(70,1,'','R',1,'L',true);
    
                    }
    
                    if($yLegenda < $legendaYawal+32){
                        $kurang = ($legendaYawal+32)-$yLegenda;
                        
                        // echo $kurang;exit();
                        $pdf->SetY($legendaYawal+28);
                        if($xLegenda == 140){
                            $pdf->Cell(65,5,'','R',1,'C',true);
                        }
                        $pdf->Cell(65,$kurang,'','LR',0,'C',true);
                        $pdf->SetX($pdf->GetX()+125);
                        $pdf->Cell(5,$kurang,'','R',1,'C',true);
                        
                    }
    
                }else{
                    // $pdf->Cell(65,30,'','LB',0,'C',true);
                    // $pdf->Cell(65,30,'','RB',1,'C',true);
                }
        }else if($data['jns_pdf'] == 7){
            $pdf->SetFont('Arial','',7);

            if(count($legenda) > 0){
                $legendaYawal = $pdf->GetY();
                foreach ($legenda as $key => $value) {
                    $url = 'uploads/legenda/'.$value->img;
                        $pdf->Cell(85,5,'','',0,'L',true);
                        $xx = $pdf->GetX()+5;
                        $yy = $pdf->GetY()+1;
                        $pdf->Image($url,$xx,$yy, 7, 0);
                        $pdf->SetX($pdf->GetX()+15);
                        $pdf->Cell(50,4,$value->text,'',0,'L',true);
                        $pdf->SetX($pdf->GetX()+60);
                        $pdf->Cell(5,4,'','R',1,'L',true);
                        $yLegenda = $pdf->GetY();

                }
                $pdf->SetY($legendaYawal);
                $xx = $pdf->GetX()+129;
                $pdf->SetX($xx);
                $pdf->Cell(66,3,'Ketersediaan','R',1,'L',true);
                $ketersediaan =[
                    '140 253 135' => 'Tersedia',
                    '247 191 255' => 'Tersedia Bersyarat',
                    '48 145 1' => 'Tidak Tersedia',
                ];
                foreach ($ketersediaan as $key => $value) {
                        $xx = $pdf->GetX()+130;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);

                        // $pdf->Cell(5,1,'','',1,'L',true);
                        // $pdf->SetX($xx);
                        $rgb = explode(' ',$key);
                        $pdf->SetFillColor($rgb[0],$rgb[1],$rgb[2]);
                        $pdf->Cell(5,3,'','1',0,'L',true);
                        $pdf->SetFillColor(255, 255, 255);
                        $pdf->Cell(60,4,$value,'R',1,'L',true);

                        $xx = $pdf->GetX()+120;
                        $yy = $pdf->GetY()+1;
                        // $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($xx);
                        $pdf->Cell(5,1,'','',0,'L',true);
                        $pdf->Cell(70,1,'','R',1,'L',true);

                }

                if($yLegenda < $legendaYawal+32){
                    $kurang = ($legendaYawal+32)-$yLegenda;
                    
                    // echo $kurang;exit();
                    $pdf->SetY($legendaYawal+28);
                    if($xLegenda == 140){
                        $pdf->Cell(65,5,'','R',1,'C',true);
                    }
                    $pdf->Cell(65,$kurang,'','LR',0,'C',true);
                    $pdf->SetX($pdf->GetX()+125);
                    $pdf->Cell(5,$kurang,'','R',1,'C',true);
                    
                }

            }else{
                // $pdf->Cell(65,30,'','LB',0,'C',true);
                // $pdf->Cell(65,30,'','RB',1,'C',true);
            }
        }else{
            if(count($legenda) > 0){
                // $pdf->SetX($pdf->GetX()-65);
                foreach ($legenda as $key => $value) {
                    $url = 'uploads/legenda/'.$value->img;
                    
                    if($enter == 0){
                        // $pdf->SetX($pdf->GetX()+65);
                        $pdf->Cell(85,5,'','',0,'L',true);

                        $xx = $pdf->GetX()+5;
                        $yy = $pdf->GetY()+1;
                        $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($pdf->GetX()+15);
                        $pdf->Cell(50,5,$value->text,'',0,'',true);
                        $enter = 1;
                        $xLegenda = $pdf->GetX();

                    }else{
                        $xx = $pdf->GetX()+5;
                        $yy = $pdf->GetY()+1;
                        $pdf->Image($url,$xx,$yy, 10, 0);
                        $pdf->SetX($pdf->GetX()+15);
                        $pdf->Cell(50,5,$value->text,'',1,'',true);
                        $enter = 0;
                        $xLegenda = $pdf->GetX();
                        
                    }
                }
                if($yLegenda < $legendaYawal+12){
                    $kurang = ($legendaYawal+12)-$yLegenda;
                    
                    // echo $kurang;exit();
                    // $pdf->SetY($legendaYawal+10);
                    if($xLegenda == 140){
                        $pdf->Cell(95,5,'','R',1,'C',true);
                    }
                    $pdf->Cell(65,$kurang,'','',0,'C',true);
                    $pdf->SetX($pdf->GetX()+125);
                    $pdf->Cell(5,$kurang,'','',1,'C',true);
                    
                }
            }else{
                $pdf->Cell(65,30,'','LB',0,'C',true);
                $pdf->Cell(65,30,'','RB',1,'C',true);
            }
        }
        
        // $pdf->Cell(130,30,'','LBR',1,'C',true);
        // $kt =[
        //     'lokasi' => [227, 76, 230] ,
        //     'tersedia' => [140,253,135] ,
        //     'bersyarat' => [247,191,255] ,
        //     'tidak' => [48,145,1] ,
        // ];
        
        // $pdf->SetX($pdf->GetX()+80);
        // $pdf->Cell(65,5,'','',1,'C',true);
        // $pdf->SetX($pdf->GetX()+90);
        // $pdf->SetFillColor(255,255,255);
        // $pdf->SetDrawColor($kt['lokasi'][0],$kt['lokasi'][1],$kt['lokasi'][2]); // Red color
        
        // $pdf->Cell(5,3,'','1',0,'L',true);
        // $pdf->SetDrawColor(0, 0, 0); // Red color
        // $pdf->SetFillColor(255, 255, 255);
        // $pdf->SetX($pdf->GetX()+2);
        // $pdf->Cell(50,3,'Lokasi Yang Dimohon','',0,'L',true);
        // $pdf->SetFillColor($kt['tersedia'][0],$kt['tersedia'][1],$kt['tersedia'][2]);
        // $pdf->SetDrawColor($kt['tersedia'][0],$kt['tersedia'][1],$kt['tersedia'][2]); // Red color
        // $pdf->Cell(5,3,'','1',0,'L',true);
        // $pdf->SetDrawColor(0, 0, 0); // Red color
        // $pdf->SetFillColor(255, 255, 255);
        // $pdf->Cell(43,3,'Tersedia','R',1,'L',true);
        
        // $pdf->SetX($pdf->GetX()+185);
        // $pdf->Cell(10,1,'','R',1,'L',true);
        // // $pdf->SetY($pdf->GetY()+1);
        
        // $pdf->SetX($pdf->GetX()+90);
        // $pdf->SetFillColor($kt['bersyarat'][0],$kt['bersyarat'][1],$kt['bersyarat'][2]);
        // $pdf->SetDrawColor($kt['bersyarat'][0],$kt['bersyarat'][1],$kt['bersyarat'][2]); // Red color
        // $pdf->Cell(5,3,'','1',0,'L',true);
        // $pdf->SetDrawColor(0, 0, 0); // Red color
        // $pdf->SetFillColor(255, 255, 255);
        // $pdf->SetX($pdf->GetX()+2);
        // $pdf->Cell(50,3,'Tersedia Bersyarat','',0,'L',true);
        // $pdf->SetFillColor($kt['tidak'][0],$kt['tidak'][1],$kt['tidak'][2]);
        // $pdf->SetDrawColor($kt['tidak'][0],$kt['tidak'][1],$kt['tidak'][2]); // Red color
        // $pdf->Cell(5,3,'','1',0,'L',true);
        // $pdf->SetDrawColor(0, 0, 0); // Red color
        // $pdf->SetFillColor(255, 255, 255);
        // $pdf->Cell(43,3,'Tidak Tersedia','R',1,'L',true);
        // $pdf->SetX($pdf->GetX()+90);
        // $pdf->Cell(125,3,'','R',1,'L',true);
        // $pdf->SetX($pdf->GetX()+90);
        // $pdf->MultiCell(105 , 3,@$data['keterangan_peta'], 'R', 'L',0);
      
        $yLegenda = $pdf->GetY();
        $tinggiLegenda = $yLegenda-$yPetunjuk;
        // $tinggiLegenda = $tinggiLegenda <= 18 ? 30 : $tinggiLegenda; 
        
        $pdf->SetY($yLegenda-$tinggiLegenda);
        $pdf->Cell(80,25,'','LR',1,'C',true);
        $pdf->SetFont('Arial','',9);
        $markRedY = $pdf->GetY();
        $pdf->Cell(80,5,'Lokasi Yang Dimohon','LRB',1,'C',true);
        
        $pdf->Cell(80,2,'','LRT',1,'L',true);
        $pdf->Cell(80,3,'Sumber Peta:','LR',1,'L',true);
        if(!empty($sumber_peta) > 0){
            foreach ($sumber_peta as $key => $v) {
                $pdf->Cell(80,3,'- '.$v->text,'LR',1,'L',true);
            }  
            
        }
        $pdf->Cell(80,2,'','LRB',1,'L',true);
        $yDatum = $pdf->GetY();

        // $pdf->SetY($yPetunjuk+40);
        $ttd = [
                '1' => 'a.n Kepala Kantor Wilayah BPN Provinsi '.$this->camel(@$data['wadmpr']),
                '2' => 'a.n Kepala Kantor Pertanahan '.str_replace('Kab.',' Kabupaten ',$this->camel(@$data['wadmkk'])),
                    ];
        $ttd2 = [
                '1' => 'Kepala Bidang Penataan dan Pemberdayaan/ '.str_replace('Kab.',' Kabupaten ',$this->camel(@$data['wadmkk'])),
                '2' => 'Kepala Seksi Penataan dan Pemberdayaan'
                    ];
        $data['jabatan'] = $data['jabatan'] == '-' ? 1 : $data['jabatan'] ;
        $data['jabatan'] = 1 ;
        
        $pdf->SetFont('Arial','',9);
        $pdf->Cell(80,2,'','LRT',0,'L',true);
        $pdf->Cell(115,3,'','TLR',1,'C',true);
        $pdf->Cell(25,4,'Sistem Proyeksi','L',0,'L',true);
        $pdf->Cell(55,4,': Transferse Mecrator','R',0,'L',true);
        $pdf->Cell(115,4,$ttd[$data['jabatan']],'LR',1,'C',true);
        $pdf->Cell(25,4,'Sistem Koordinat','L',0,'L',true);
        $pdf->Cell(55,4,': Universal Transverse Mercator','R',0,'L',true);
        $pdf->Cell(115,4,$ttd2[$data['jabatan']],'LR',1,'C',true);
        $pdf->Cell(25,4,'Datum & Zona','L',0,'L',true);
        $pdf->Cell(55,4,': Universal Transverse Mercator','R',0,'L',true);
        $pdf->Cell(115,4,'','LR',1,'C',true);
        $pdf->Cell(80,2,'','LR',0,'L',true);
        $pdf->Cell(115,3,'','LR',1,'C',true);
        // $yDatum = $pdf->GetY();
        
        
        $pdf->Cell(80,2,'','TRL',0,'L',true);
        $pdf->Cell(115,2,'','LR',1,'L',true);
        $pdf->Cell(25,4,'Tanggal ','L',0,'L',true);
        $pdf->Cell(55,4,': '.tanggal_indonesia($data['tanggal']),'R',0,'L',true);
        $pdf->Cell(115,4,'','LR',1,'C',true);
        $pdf->Cell(25,4,'Dianalisis Oleh ','L',0,'L',true);
        $pdf->Cell(55,4,': '.$data['dianalisis'],'R',0,'L',true);
        $pdf->Cell(115,4,'','LR',1,'C',true);
        $pdf->Cell(25,4,'DIperiksa Oleh ','L',0,'L',true);
        $pdf->Cell(55,4,': '.$data['diperiksa'],'R',0,'L',true);
        $pdf->Cell(115,4,$data['kepala_nama'],'LR',1,'C',true);
        $pdf->Cell(25,4,' ','L',0,'L',true);
        $pdf->Cell(55,4,'','R',0,'L',true);
        $pdf->Cell(115,4,'Nip.'.$data['kepala_nip'],'LR',1,'C',true);
       
        
        $pdf->Cell(80,2,'','L',0,'L',true);
        $pdf->Cell(115,2,' ','LR',1,'C',true);
        $pdf->Cell(195,1,'','T',1,'L',true);
        
        
        
        // line right legenda
        $lineRight = ($yDatum-$yPetunjuk);
        $pdf->SetY($yPetunjuk);
        $pdf->SetX($pdf->GetX()+195);
        $pdf->Cell(1,$lineRight,'','L',1,'L',true);


        
    
        
        $pdf->Image($url_logo,12, 7, 15, 0);
        $pdf->Image($url_peta,20,58, 185, 0);
        $pdf->Image($mata_angin,160,60, 30, 0);
        $pdf->Image($lokasi,20,$yPetunjuk, 60, 0);
        $pdf->Image($markRed,28,$markRedY****, 3, 0);

        if(@$data['jns_pdf'] == 4){
            
            // $pdf->Image($legenda4,80,$yPetunjuk, 40, 0);
            // $pdf->Image($legenda4lereng,130,$yPetunjuk-5, 21, 0);
            // $pdf->Image($legenda4tekstur,160,$yPetunjuk-5, 25, 0);
        }
        // $pdf->Cell(5,10,'','L',0,'L',true);
        // $pdf->Cell(165,10,'','B',0,'L',true);
        // $pdf->Cell(5,10,'','R',1,'L',true);
        
        
        // $pdf->SetFont('Arial','',8);
        // $pdf->Cell(175,10,'','BLRT',1,'L',true);
        
        $pdf->Output();
    }

    
// Function to convert decimal degrees to DMS
    function decimalToDMS($decimal) {
        $degrees = floor($decimal);
        $minutes = floor(($decimal - $degrees) * 60);
        $seconds = ($decimal - $degrees - ($minutes / 60)) * 3600;

        return sprintf('%d° %d\' %.2f"', $degrees, $minutes, $seconds);
    }

    // Function to convert GeoJSON coordinates to DMS
    function geoJSONToDMS($geoJSON) {
        $type = $geoJSON['type'];
        $coordinates = $geoJSON['coordinates'];

        switch ($type) {
            case 'Point':
                $latitude = $coordinates[1];
                $longitude = $coordinates[0];
                return array('latitude' => decimalToDMS($latitude), 'longitude' => decimalToDMS($longitude));
            
            case 'Polygon':
                return $this->geoJSONPolygonToDMS($coordinates);

            case 'MultiPolygon':
                $dmsCoordinates = array();
                foreach ($coordinates as $polygon) {
                    $dmsCoordinates[] = geoJSONPolygonToDMS($polygon);
                }
                return $dmsCoordinates;

            // Add cases for other types if needed

            default:
                return null; // Unsupported type
        }
    }

    // Function to convert GeoJSON polygon coordinates to DMS
    function geoJSONPolygonToDMS($coordinates) {
        $dmsCoordinates = array();

        foreach ($coordinates as $ring) {
            foreach ($ring as $coord) {
                $latitude = $coord[1];
                $longitude = $coord[0];

                $latitudeDMS = $this->decimalToDMS($latitude);
                $longitudeDMS = $this->decimalToDMS($longitude);

                $dmsCoordinates[] = array('latitude' => $latitudeDMS, 'longitude' => $longitudeDMS);
            }
        }

        return $dmsCoordinates;
    }

    function export_pdf_form($id='1',$view){
        $arr = [
            'ptp_berusaha' => 'ptp_kppr_berusaha',
            'ptp_non_berusaha' => 'ptp_kppr_non_berusaha',
            'ptp_stranas' => 'ptp_kppr_stranas',
            'ptp_tnh_timbul' => 'ptp_tnh_timbul',
            'ptp_pk_p3t' => 'ptp_pk_p3t',
            'ptp_p3t' => 'ptp_p3t',
            'ptpil_prov' => 'ptpil_prov',
            'ptpil_prov_tk' => 'ptpil_prov_tk'
        ];
        $this->db->where('v_print_analisis.id_form', $id);
        $data = $this->db->get('v_print_analisis')->row_array();
        $legenda = $this->db->get('r_legenda')->result();
        $this->db->where('id_print_peta', $data['id_peta']);
        $sumber_peta = $this->db->get('print_sp_analisis')->result();


        foreach ($data as $key => $value) {
            // echo $data[$key];
            if(empty($value)){
                $data[$key] = ' - ';
            }
        }
  
        

        // $pdf = new FPDF('P', 'mm',[210,297]);
        // $pdf = new FPDF('P', 'mm',[215,330]);
        $pdf = new FPDF('P', 'mm','a4');
        $pdf->SetMargins(15, 5, 10,5);
        $pdf->AddPage();
        $url_logo = 'assets/themes/adminity/images/logo_bpn.png';
        $url_peta = 'uploads/print_peta/'.$data['file_peta'];
        $lokasi = 'uploads/print_peta/'.$data['file_lokasi'];
        $mata_angin = 'uploads/print_peta/'.$data['file_mata_angin'];
        // $legenda4 = 'uploads/legenda/legenda_default_kemampuan_tanah.png';
        $legenda4 = 'uploads/legenda/legend4def.png';
        $legenda4lereng = 'uploads/legenda/legend4lereng.png';
        $legenda4tekstur = 'uploads/legenda/legend4tekstur.png';
        $markRed = 'uploads/print_peta/default/red_location.png';
        // $pdf->SetTextColor(255,255,255);
        
        $pdf->SetFillColor(255,255,255);
        
        $pdf->SetFont('Times','',12);
        $pdf->Cell(190,30,'Form Layanan Peta Analisis ','',1,'C',true);
        $pdf->Cell(3,5,'','',0,'C',true);
        $pdf->Cell(180,5,'','T',1,'C',true);
        
        $pdf->Cell(20,5,'Nomor','',0,'L',true);
        $pdf->Cell(90,5,': '.@$data['no_berkas'],'',0,'L',true);
        $pdf->Cell(70,5,$this->camel(@$data['wadmkk']).', '.tanggal_indonesia(@$data['tanggal']),'',1,'L',true);
        $pdf->Cell(20,5,'Lampiran','',0,'L',true);
        $pdf->Cell(90,5,': 1 (Satu) Berkas','',0,'L',true);
        $pdf->Cell(70,5,'Kepada ','',1,'L',true);
        
        $pdf->Cell(20,5,'Perihal','',0,'L',true);
        $pdf->Cell(90,5,': Permohonan Layanan Peta Analisis ','',0,'L',true);
        $pdf->Cell(70,5,'Yth. Kepala Kantor Wilayah BPN ','',1,'L',true);

        $pdf->Cell(22,5,'','',0,'L',true);
        $pdf->Cell(88,5,$this->camel(@$data['nama_tema']),'',0,'L',true);
        $pdf->Cell(70,5,'Provinsi/Kepala Kantor ','',1,'L',true);
        $pdf->Cell(20,5,'','',0,'L',true);
        $pdf->Cell(90,5,'   ','',0,'L',true);
        $pdf->Cell(70,5,'Pertanahan Kabupaten/Kota ','',1,'L',true);
        $pdf->Cell(20,5,'','',0,'L',true);
        $pdf->Cell(89,5,'   ','',0,'L',true);
        $pdf->Cell(70,5,$this->camel(str_replace('KAB.','',$data['wadmkk'])),'',1,'L',true);
        $pdf->Cell(20,5,'','',0,'L',true);

        $pdf->Cell(180,5,'','',1,'C',true);
        $pdf->Cell(110,5,'   ','',0,'L',true);
        $pdf->Cell(70,5,'di','',1,'L',true);
        $pdf->Cell(110,5,'   ','',0,'L',true);
        $pdf->Cell(70,5,'Tempat','B',1,'L',true);
        
        $pdf->SetX($pdf->GetX()+10);
        $pdf->Cell(70,5,'Yang bertandatangan di bawah ini :','',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'1. Nama','',0,'L',true);
        $pdf->Cell(115,5,': '.@$data['nama_pemohon'],'',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'2. Alamat','',0,'L',true);
        $pdf->MultiCell(115,5,': '.@$data['alamat'],'',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'3. NIK','',0,'L',true);
        $pdf->Cell(115,5,': '.@$data['nik'],'',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'3. Bertindak untuk','',0,'L',true);
        $pdf->Cell(115,5,': '.@$data['atas_nama'],'',1,'L',true);
        $pdf->SetX($pdf->GetX()+15);
        $pdf->Cell(48,5,'  dan atas nama','',0,'L',true);
        $pdf->Cell(115,5,' ','',1,'L',true);
        
        $pdf->SetY($pdf->GetY()+5);
        $pdf->SetX($pdf->GetX()+10);
        $pdf->MultiCell(180,5,'dengan ini mengajukan permohonan layanan peta kesesuaian penggunaan tanah terhadap RTRW/ketersediaan tanah','',1,'L',true);
        
        $pdf->SetY($pdf->GetY()+5);
        $pdf->Cell(180,5,'KETERANGAN TENTANG TANAH YANG DIMOHON:','',1,'C',true);
        
        $pdf->SetY($pdf->GetY()+5);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'1. Luas','',0,'L',true);
        $pdf->Cell(115,5,': '.@$data['luas_tanah'].' ha','',1,'L',true);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'2. Letak','',1,'L',true);
        
        $pdf->SetX($pdf->GetX()+17.5);
        $pdf->Cell(45.5,5,'Desa / Kelurahan','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['wadmkd']),'',1,'L',true);
        $pdf->SetX($pdf->GetX()+17.5);
        $pdf->Cell(45.5,5,'Kecamatan','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['wadmkc']),'',1,'L',true);
        $pdf->SetX($pdf->GetX()+17.5);
        $pdf->Cell(45.5,5,'Kabupaten / Kota','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['wadmkk']),'',1,'L',true);
        $pdf->SetX($pdf->GetX()+17.5);
        $pdf->Cell(45.5,5,'Koordinat (DMS)','',0,'L',true);
        $pdf->Cell(115,5,': ','',1,'L',true);


        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'3. Tema Analisis','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['nama_tema']),'',1,'L',true);
        
        

        $pdf->SetX($pdf->GetX()+13);
        $pdf->Cell(50,5,'4. Format Peta','',0,'L',true);
        $pdf->Cell(115,5,': '.$this->camel(@$data['format']),'',1,'L',true);

        $pdf->SetY($pdf->GetY()+5);
        $pdf->SetX($pdf->GetX()+13);
        $pdf->MultiCell(180,5,'Demikian disampaikan untuk menjadi bahan pertimbangan.','',1,'L',true);
        
        $pdf->SetY($pdf->GetY()+5);
        $pdf->SetX($pdf->GetX()+120);
        $pdf->Cell(30,5,'Hormat kami,','',1,'L',true);
        $pdf->SetY($pdf->GetY()+20);
        $pdf->SetX($pdf->GetX()+120);
        $pdf->SetFont('Times','U',12);
        $pdf->Cell(30,5,$this->camel(@$data['nama_pemohon']),'',1,'L',true);
        $pdf->SetFont('Times','',12);
        $pdf->SetY($pdf->GetY()+1);
        $pdf->SetX($pdf->GetX()+120);
        $pdf->Cell(30,5,$this->camel(@$data['jabatan_pemohon']),'',1,'L',true);
        $pdf->Cell(30,5,'Keterangan : ','',1,'L',true);
        $pdf->Cell(30,5,'*) Jika Tersedia','',1,'L',true);
        $pdf->Cell(30,5,'**) Dipilih sesuai layanan yang dimohon.','',1,'L',true);
        

        // $pdf->Image($url_logo,12, 7, 15, 0);
        // $pdf->Image($url_peta,20,58, 185, 0);
        // $pdf->Image($mata_angin,160,60, 30, 0);
        $pdf->Output();
    }

    function cutTextIntoArray($text, $maxLength) {
        $segments = array();
        
        while (strlen($text) > $maxLength) {
            // Find the last space within the allowed length
            $lastSpace = strrpos(substr($text, 0, $maxLength), ' ');
    
            if ($lastSpace === false) {
                // No space found within the limit, truncate at the maxLength
                $segment = substr($text, 0, $maxLength);
            } else {
                // Truncate at the last space
                $segment = substr($text, 0, $lastSpace);
            }
    
            $segments[] = $segment;
            $text = substr($text, strlen($segment));
        }
    
        if (!empty($text)) {
            $segments[] = $text; // Add any remaining text
        }
    
        return $segments;
    }

    function camel($str) {
        $str = strtolower($str);
        $exp = explode(' ',$str);
        $ret='';
        foreach ($exp as $key => $value) {
            $ret .= ucfirst($value).' ';
        }
        return $ret;

    }
}