<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Rating extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    

    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $kdMenu = $this->session->userdata('menu');
        
        // echo "<pre>";
        // print_r ($kd_parent);
        // echo "</pre>";exit();
        
        $title = "Rating ";
        $js_file = $this->load->view('rating/js_file', '', true);
        $modal_tambah = $this->load->view('rating/modal_tambah', '', true);
        $modal_edit = $this->load->view('rating/modal_edit', '', true);
        
        $in = ['#','peta2','insight_provinsi','peta_lainnya','dashboard4'];
        $this->db->where_in('url', $in);
        $this->db->order_by('nama_module', 'asc');
        $menus = $this->db->get('aset_module')->result();
        
        
        $group = $this->session->userdata('users')['id_user_group'];
        $this->db->select('kode_module');
        $this->db->where('id_user_group', $group);
        $group_module = $this->db->get('aset_group_modules')->result();
        $groups=[];
        $menu=[];
        foreach ($group_module as $key => $value) {
            array_push($groups,$value->kode_module);          
        }
        foreach ($menus as $k => $v) {
            if (in_array($v->kode_module,$groups)) {
                
                array_push($menu,$v);          
            }
            
            
            
        }
      
  

        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "jv_script" => $js_file,
            "menu" => $menu,
            "kd_module" => $kdMenu
        );
        
        $this->load->view('index', $data);
    }

    function getMenu()  {
        $in = ['#','peta2','insight_provinsi','peta_lainnya','dashboard4/frame'];
        $this->db->where_in('url', $in);
        $this->db->order_by('nama_module');
        $sql = "SELECT 
                case when b.nama_module  is not null then concat(b.nama_module,' - ',a.nama_module)
                else a.nama_module end as module_name
                ,a.*
                FROM aset_module a
                left join aset_module b on b.kode_module =a.parent
                WHERE a.url IN('#', 'peta2', 'insight_provinsi', 'peta_lainnya', 'dashboard4/frame')
                ORDER BY module_name;";
        $menu = $this->db->query($sql)->result();
        
        
        echo json_encode($menu);
    }

    public function save_form() {
        $param = $this->input->post('formData', TRUE);
        
   
        
        $this->wgisitia->handle_removed($param);
        $id_user=$this->session->users['id_user'];
        
        $data_detail = [ 
            "saran" => $this->input->post("formData")["saran"] ,
            "review" => $this->input->post("formData")["review"] ,
            "rating_index" => $this->input->post("formData")["star"], 
            "kd_module" => $this->input->post("formData")["kd_module"], 
            "created_at" => date('Y-m-d'), 
            "created_by" => $id_user
        ];
        $this->db->insert('r_review', $data_detail);

        echo json_encode(array("status" => TRUE));
    }

    function get_command($kd_module='') {

        if($kd_module == '00'){
            $this->db->select('rating_index');
            // $this->db->group_by('kd_module');
            $datas['data'] = $this->db->get('r_review')->result();
        }else{

            $this->db->where('kd_module', $kd_module);
            $this->db->select('r_review.*,aset_users.nama');
            $this->db->join('aset_users', 'aset_users.id_user = r_review.created_by', 'left');
            $datas['data'] = $this->db->get('r_review')->result();
        }
        

        
     
        $avg=0;
        $count=0;
        $ratings=[
            1=>0,
            2=>0,
            3=>0,
            4=>0,
            5=>0
        ];
        $datas['json_data']=json_encode($datas['data']);
        if(!empty($datas['data'])){
            
            foreach ($datas['data'] as $key => $value) {
                $avg = $avg+$value->rating_index;
                $count++;
                $ratings[round($value->rating_index)]++;
            }
            $datas['rating']=$ratings;
            $avg = round($avg/$count,1);
        }else{
            
            
            
            $datas['rating']=$ratings;
        }
        $datas['count'] = $count;
        if($kd_module == '00'){
            $list= '';
            // $this->db->select('AVG(rating_index) as avg');
            // $avg = $this->db->get('r_review')->row_array();
            
            // echo "<pre>";
            // print_r ($avg);
            // echo "</pre>";
            $list = $this->load->view('list_global', $datas, true);
        }else{
            
            $list = $this->load->view('list', $datas, true);
        }
        
            echo json_encode(['data' => $list, 'avg' => @$avg,'rating' => @$ratings ]);
        
        
    }


    function update_form() {
        // $this->wgisitia->handle_removed($param);
        $param = $this->input->post('formData', false);
        
        // echo "<pre>";
        // print_r ($this->input->post('formData', FALSE));
        // echo "</pre>";
        // strip_tags($_POST[''], '<p><a>');
        // exit();
        
        
        $data_detail = [ 
            "id_kategori" => $this->input->post("formData")["id_kategori"] ,
            "url" => $this->input->post("formData")["url"] 
        ];

        $this->db->where('id', $param["id"]);
        $this->db->update('vidio', $data_detail);

        echo json_encode(array("status" => TRUE,"data"=>$param["id"]));
    }

    
    public function ajax_delete($id) {

        $this->M_model->delete_by_id('rating','id', $id);
        echo json_encode(array("status" => TRUE));
    }

    public function ssp_paket() {
      
        $table = 'v_vidio';
        $primaryKey = 'id'; //test        
         
        $columns = array(
            array('db' => 'id', 'dt' => 0),
            array('db' => 'url', 'dt' => 1),
            array('db' => 'id_kategori', 'dt' => 2),
            array('db' => 'kategori', 'dt' => 3),
        );
       

        datatable_ssp($table, $primaryKey, $columns);

        
    }


}