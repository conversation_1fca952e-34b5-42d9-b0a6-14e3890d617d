
<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var xhrdata = null;
    var table = null;

    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";

    function clear_input() {
        $("#frm-tambah :input").val("");      
    }
   
    var load=0;
    function listing() {
            
        var kd_module = $('#imenu').val()
        $.get("<?php echo base_url(); ?>" + "rating/get_command/"+kd_module, function(data, status){
            data = JSON.parse(data)
            // console.log(data);
            $('.list_command').html(data.data);
            $('#avg').html(data.avg);
            $('#star_avg').val(data.avg);
            $('#star_avg').rating();
            $('#star').rating();
        });
    }

    
 


    function simpanForm() {
        
            if ($( "#review" ).val() == '' ) {
                $( "#review" ).focus()
                return false;
            }

            var xobj_usulan = {
                "star" : $( "#star" ).val(),
                "saran" : $( "#saran" ).val(),
                "review" : $( "#review" ).val(),
                "kd_module" : $( "#imenu" ).val(),
                "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"
            };

            
            var url = "<?php echo base_url(); ?>" + "rating/save_form";
            var params = {"formData": xobj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                
            Swal.fire(
                            'Sukses!',
                            'Data Tersimpan!',
                            'success'
                        )
                        listing()
                            $('.tArea').val('')
                
            })
            .fail(function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Tambah Data Gagal!',
                })
                
            });
        
    }

    function updateForm(){
        var id = $("#xid").val();
        var data_selected = xhrdata.data.filter(x => x[0] == id)[0];

        if($( "#frm-edit" ).valid() === true){

            var objmasterdetail = {
                    "id": id,
                    "id_kategori" : $( "#xid_kategori" ).val(),
                    "url" : $( "#xurl" ).val(),
                    "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"


            };
        //  console.log(objmasterdetail);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("rating/update_form") ?>";
            $.post(url, params).done(function (data) {
                    Swal.fire(
                                'Sukses!',
                                'Data Tersimpan!',
                                'success'
                            )
                            listing()
                            $('.tArea').val('')
            })
            .fail(function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Update Data Gagal!',
                })
            });
        }else{
            Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Tambah Data Gagal!silahkan lihat informasi pada setiap field isian untuk memeriksa kesalahan entri data anda',
                })
        $(".error").css("color","red");
        }
    }
    function refreshSelectpicker() {
        $('select').selectpicker('refresh')
    }

    $(document).ready(function () {
        setTimeout(function() {
            // $('select').selectpicker('refresh')
            // listing();
            
            
            // $('#imenu').select2();
            listing()
        }, 1000);

        
        $.ajax({
            url: '<?=base_url()?>rating/getMenu',
            method: 'GET',
            success: function(data) {
                data = JSON.parse(data)
                var options = data.map(function(item) {
                    return '<option value="' + item.kode_module + '">' + item.module_name + '</option>';
                });
                options.unshift('<option value="00">Global</option>');
                // return '<option value="00">Global</option>';
                console.log(options);
                var kdModule = $('#kdModule').val()
                console.log(kdModule) 
                $('#imenu').empty();
                $('#imenu').append(options);
                if (kdModule) {
                    $('#imenu').val(kdModule);
                }
                $('#imenu').selectpicker('refresh');
            },
            error: function(xhr, status, error) {
                console.error('Error fetching data:', error);
            }
        });
        // refreshSelectpicker()
        
        $('.numberonly').keypress(function (e) {    
            var charCode = (e.which) ? e.which : event.keyCode    

            if (String.fromCharCode(charCode).match(/[^0-9]/g))    

                return false;                        

        }); 
        $('.float-number').keypress(function(event) {
            if ((event.which != 46 || $(this).val().indexOf('.') != -1) && (event.which < 48 || event.which > 57)) {
                event.preventDefault();
            }
        });





    });


</script>
