<div class="row">
      <div class="col-md-1"></div>
      <div class="col-md-2" id="avgDiv" style="text-align: center;background-color: rgb(217 221 227);padding: 5px;border-radius: 10px;">
          <label style="display: block; margin: 0 auto;">Rata-ra rating pengguna</label>
              <h1 id="avg"></h1>
          <br/>
          <input id="star_avg" disabled value="5" type="text" class="rating_review" data-theme="krajee-fas" data-min=0 data-max=5 data-step=0.2 data-size="sm" required title="">
      </div>
      <div class="col-md-8" id="barDiv" style="text-align: center;background-color: rgb(217 221 227);padding: 10px;border-radius: 10px;margin-left:10px">
        <div class="container">
          <?php
            $maxValue = $count;
            $color = ['','red','yellow','orange','green','blue'];
            foreach ($rating as $key => $bar) {
              if($maxValue == 0){
                $width = 1;
              }else{
                $width = ($bar / $maxValue) * 100;
              }
              
              echo '<div class="row">';
              echo '<div class="col-sm-1 progress-label "><label style="float:right">' . $key . '</label></div>';
              echo '<div class="col-sm-10">';
              echo '<div class="progress-wrapper">';
              echo '<div class="progress">';
              echo '<div class="progress-bar" role="progressbar" style="width: ' . $width . '%;background-color:'.$color[$key].'" aria-valuenow="' . $bar . '" aria-valuemin="0" aria-valuemax="' . $maxValue . '"></div>';
              echo '</div>';
              echo '</div>';
              echo '</div>';
              echo '</div>';
            }
            ?>
        </div>
      </div>
    </div>
      &nbsp;
      <hr>
</div>