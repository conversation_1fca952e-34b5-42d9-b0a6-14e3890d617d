<div class="row">
      <div class="col-md-1"></div>
      <div class="col-md-2" id="avgDiv" style="text-align: center;background-color: rgb(217 221 227);padding: 5px;border-radius: 10px;">
          <label style="display: block; margin: 0 auto;">Rata-ra rating pengguna</label>
              <h1 id="avg"></h1>
          <br/>
          <input id="star_avg" disabled value="5" type="text" class="rating_review" data-theme="krajee-fas" data-min=0 data-max=5 data-step=0.2 data-size="sm" required title="">
      </div>
      <div class="col-md-8" id="barDiv" style="text-align: center;background-color: rgb(217 221 227);padding: 10px;border-radius: 10px;margin-left:10px">
        <div class="container">
          <?php
            $maxValue = count($rating);
            $color = ['','red','yellow','orange','green','blue'];
            foreach ($rating as $key => $bar) {
              if($maxValue == 0){
                $width = 1;
              }else{
                $width = ($bar / $maxValue) * 100;
              }
              echo '<div class="row">';
              echo '<div class="col-sm-1 progress-label "><label style="float:right">' . $key . '</label></div>';
              echo '<div class="col-sm-10">';
              echo '<div class="progress-wrapper">';
              echo '<div class="progress">';
              echo '<div class="progress-bar" role="progressbar" style="width: ' . $width . '%;background-color:'.$color[$key].'" aria-valuenow="' . $bar . '" aria-valuemin="0" aria-valuemax="' . $maxValue . '"></div>';
              echo '</div>';
              echo '</div>';
              echo '</div>';
              echo '</div>';
            }
            ?>
        </div>
      </div>
    </div>
      &nbsp;
      <hr>
      &nbsp;
      <form id="frm-tambah" method="post">
        <div class="card-block">
          <div class="row" style="">
            <div class="col-md-1"></div>
            <div class="col-md-11"><b>Ingin Memberkan Rating ??</b></div>
            <div class="col-md-1"></div>
            <div class="col-md-11">
                <div class="form-group">
                    <input id="star" value="5" type="text" class="rating_review" data-theme="krajee-fas" data-min=0.1 data-max=5 data-step=0.2 data-size="lg" required title="">
                </div>
            </div>
            <div class="col-md-1"></div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="review">Review</label>
                    <textarea name="review" id="review" required class="form-control tArea" cols="30" rows="5"></textarea>
                </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                  <label for="saran">Saran</label>
                  <textarea name="saran" id="saran" class="form-control tArea" cols="30" rows="5"></textarea>
                </div>
            </div>
            <div class="col-md-3"></div>
            <div class="col-md-1"></div>
            <div class="col-md-1">
                <div class="form-group">
                  <label for="saran" style="color:white">.</label></br>
                  <button type="button" style="padding:10px" onclick="simpanForm()" class="btn btn-primary btn-xl">Submit</button>&nbsp;
              </div>
            </div>
          </div>
          <hr>
        </form>
        <div class="container" id="data-container">
          <div class="col-md-1"></div>
            <div class="col-md-11"><b>Hasil Review</b></div>
            &nbsp;
        <?php
    
    foreach ($data as $key => $data) { ?> 
    <!-- <div id="html_list" style="display:none"> -->
      <div class="row class_row" id="id_row<?=$key?>" style="display:none">
        <div class="col-md-2" style="text-align: center;margin-top:10px">
            <div style=" margin: 0 auto;">
                <svg xmlns="http://www.w3.org/2000/svg" width="70px" height="70px" viewBox="0 0 24 24" fill="none">
                    <path opacity="0.5" d="M14 4H10C6.22876 4 4.34315 4 3.17157 5.17157C2 6.34315 2 8.22876 2 12C2 15.7712 2 17.6569 3.17157 18.8284C4.34315 20 6.22876 20 10 20H14C17.7712 20 19.6569 20 20.8284 18.8284C22 17.6569 22 15.7712 22 12C22 8.22876 22 6.34315 20.8284 5.17157C19.6569 4 17.7712 4 14 4Z" fill="#1C274C"/>
                    <path d="M13.25 9C13.25 8.58579 13.5858 8.25 14 8.25H19C19.4142 8.25 19.75 8.58579 19.75 9C19.75 9.41421 19.4142 9.75 19 9.75H14C13.5858 9.75 13.25 9.41421 13.25 9Z" fill="#1C274C"/>
                    <path d="M14.25 12C14.25 11.5858 14.5858 11.25 15 11.25H19C19.4142 11.25 19.75 11.5858 19.75 12C19.75 12.4142 19.4142 12.75 19 12.75H15C14.5858 12.75 14.25 12.4142 14.25 12Z" fill="#1C274C"/>
                    <path d="M15.25 15C15.25 14.5858 15.5858 14.25 16 14.25H19C19.4142 14.25 19.75 14.5858 19.75 15C19.75 15.4142 19.4142 15.75 19 15.75H16C15.5858 15.75 15.25 15.4142 15.25 15Z" fill="#1C274C"/>
                    <path d="M9 11C10.1046 11 11 10.1046 11 9C11 7.89543 10.1046 7 9 7C7.89543 7 7 7.89543 7 9C7 10.1046 7.89543 11 9 11Z" fill="#1C274C"/>
                    <path d="M9 17C13 17 13 16.1046 13 15C13 13.8954 11.2091 13 9 13C6.79086 13 5 13.8954 5 15C5 16.1046 5 17 9 17Z" fill="#1C274C"/>
                </svg>
            </div>
            <br/>
            <a href="#" style="display: block; margin: 0 auto;"><?=$data->nama?></a>
            <label style="display: block; margin: 0 auto;"><?=tanggal_indonesia($data->created_at)?></label>
        </div>
        <div class="col-md-8">
            <div>
                <input id="rating<?=$key?>" disabled value="<?=$data->rating_index?>" type="text" class="rating" data-theme="krajee-fas" data-min=0 data-max=5 data-step=0.2 data-size="sm" required title="">
                <p><b><?=$data->review?></b></p>
                <p><?=$data->saran?></p>
            </div>
        </div>
        <hr style="margin-left:20px;margin-right:20px">
      </div>
  <!-- </div> -->


<?php }?> 

        </div>
        
        <div class="pagination" id="pagination-container" style="display: flex; justify-content: center">
          <!-- Pagination links will be dynamically loaded here -->
        </div>


        <textarea name="" style="display:none" id="json_data" class="form-control" cols="30" rows="10"><?=$json_data  ?></textarea>
<script>

    var data = JSON.parse($('#json_data').val()); // Assuming $data is your array of data
    var itemsPerPage = 10;
    var totalPages = Math.ceil(data.length / itemsPerPage);
      
    $(document).ready(function(){
        $('.rating').rating();
        // Pagination click event
        $('#pagination-container').on('click', '.page-link', function() {
            var page = $(this).data('page');
            displayData(page);
            console.log(page);
        });
        displayData(1);
        displayPagination();

    });

    function displayData(page) {

        var startIndex = (page - 1) * itemsPerPage;
        var endIndex = Math.min(startIndex + itemsPerPage, data.length);

        $('.class_row').css('display','none');

        for (var i = startIndex; i < endIndex; i++) {
        var val = data[i];
        // Your HTML for displaying data goes here
        
        $('#id_row'+i).css('display','flex');
        console.log(i)
        }


    }
    function displayPagination() {

        var paginationHtml = '';
        for (var i = 1; i <= totalPages; i++) {
        paginationHtml += '<button type="button" class="page-link btn-default btn-xl" data-page="' + i + '">' + i + '</button>';
        }
        $('#pagination-container').html(paginationHtml);
    }

   


</script>
