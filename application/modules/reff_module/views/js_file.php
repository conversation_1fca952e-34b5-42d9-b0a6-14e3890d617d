t<script>
    var xhrdata = null;
    var table = null;
    function clear_input() {
        $("#formData :input").val("");
    }

    function listing() {
        table = $('#table_id').DataTable({
            "draw": 0,
            "columnDefs": [{"orderable": true, "targets": [0, 1, 2, 3, 4]}],
            //"columns": [
            //{ "name": "kd_prov_irmsv3" },
            //{ "name": "kd_prov_rkakl" },
            //{ "name": "nama_dapil" },
            //{ "name": "kd_dapil" }
            //],
            "order": [[0, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>/reff_module/ssp"},
            "aoColumnDefs": [
                {
                    "aTargets": [4],
                    "mRender": function (data, type, row) {

                        var id = row[4];


                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "<i class='fa fa-pencil'>",
                            "</i>",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "<i class='fa fa-trash'>",
                            "</i>",
                            "</button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fast backward ui icon'></i>",
                    "last": "<i class='fast forward ui icon'></i>",
                    "next": "<i class='step forward ui icon'></i>",
                    "previous": "<i class='step backward ui icon'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();

            console.log(xhrdata);
        });
        //});


    }


    function dtEditRow(id) {

       //console.log(xhrdata);

        var data_selected = xhrdata.data.filter(x => x[4] == id)[0];  //46
        var waydata = {
            kode_module: data_selected[0],
            nama_module: data_selected[1],
            parent: data_selected[5],
            url: data_selected[3],
            id: data_selected[4]
        }

        way.set('formData', waydata);


        $('#modalTitle').text('Edit Data');
        $('#modeform').val('edit');
        $('#modal-tambah').modal('show');
    }


    function dtTambahRow() {
      $('#frm-tambah')[0].reset();

        $('#modalTitle').text('Tambah ');
        $('#modeform').val('tambah');
        $('#modal-tambah').modal('show');
    }


    function dtDeleteRow(id) {
        console.log('deleting ' + id);

        url = "<?php echo base_url();?>/reff_module/deleteform";

        console.log(url);

        var r = confirm("Data yang anda pilih akan kami hapus!, apakah anda yakin mau melanjutkan");
        if (r == true) {
            $.post(url, {id: id}).done(function (data) {
//                console.log(data);

                table.ajax.reload();
                // clear_input();

            })
                    .fail(function () {
                        alert("error");
                    })
                    .always(function () {
                        alert("finished");
                    });
        }


    }


    function simpanForm() {
        var mode = $('#modeform').val();
        var serializeData = way.get('formData');
        if(typeof(serializeData.kode_module)=="")
        {
           serializeData.kode_module=="NULL"; 
        }
        console.log(serializeData);
        if (mode == 'tambah') {
            url = "<?php echo base_url(); ?>reff_module/addform";

        } else if (mode == 'edit') {
            url = "<?php echo base_url(); ?>reff_module/editform";
        }

        var params = {"formData": serializeData};
        // way.set('formData', serializeData);        
        $.post(url, params).done(function (data) {
//                    console.log(data)
            table.ajax.reload();

            alert(data);
        });
//                .fail(function () {
//                    alert("error");
//                })
//                .always(function () {
//                    // alert("finished");
//                });


    }

    $(document).ready(function () {
        listing();
        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        //bind_modul();
        //bind_level();
        initCombobox('kode_module', 31);
//        $("#yes").click(function(){
//        $("#zz").show("");
//        $("#yy").hide("");
//        });
//                
//        $("#no").click(function(){
//        $("#zz").hide("");
//        $("#yy").show("");
//        });
    });



</script>