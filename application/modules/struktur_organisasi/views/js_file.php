<script>
   

    function updateForm(){
        event.preventDefault()
        var kontak = $('#summernote').summernote('code');
        // kontak = btoa(kontak)
        var objmasterdetail = {
            
            "kontak" : kontak, 
            "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"
            
            
        };
         console.log(objmasterdetail);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("struktur_organisasi/update_form") ?>";
            $.post(url, params).done(function (data) {
                // alert('Data Berubah')
            })
            .fail(function () {
            alert("error");
            });
     
    }

    function dtUpload() {
        
        $("#modal-download").modal("show");
    }

    $(document).ready(function () {
              
        $('#submit').submit(function (e) {
            e.preventDefault();
            var file = new FormData;
            $.ajax({
                url: '<?php echo base_url(); ?>struktur_organisasi/up',
                type: "post",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                    data = JSON.parse(data)
                    console.log(data.name)
                    $("#imgs").attr("src","<?=base_url()?>assets/landingpage/assets/img/"+data.name);
                    // alert('Upload Sukses')
                    Swal.fire(
                            'Berhasil!',
                            'Upload Gambar Berhasil.',
                            'success'
                        )
                    $("#modal-download").modal("hide");

                }
            });
        }); 
    });

    

</script>