<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Struktur_organisasi extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "Edit Struktur Organisasi";
        $js_file = $this->load->view('struktur_organisasi/js_file', '', true);
        $modal_download = $this->load->view('struktur_organisasi/modal_download', '', true);
        $kontak = $this->db->get('landing_page')->row_array();
        
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "jv_script" => $js_file,
            "modal_download" => $modal_download,
            "data" => $kontak
        );
        
        $this->load->view('index', $data);
    }

    

    function update_form() {
        // $this->wgisitia->handle_removed($param);
        $param = $this->input->post('formData', TRUE);
        
        // echo "<pre>";
        // print_r ($_POST);
        // echo "</pre>";exit();
        
        
        
        $data_detail = [ 
            "kontak" => $this->input->post("formData")["kontak"], 
        ];

        $this->db->where('id', 1);
        $this->db->update('landing_page', $data_detail);
        echo 'ok';

        echo json_encode(array("status" => TRUE));
    }

    public function up() {

        
        // echo "<pre>";
        // print_r ($_FILES['filess']['name']);
        // echo "</pre>";
        
        if (is_array($_FILES)) {
            

            
            $nama_dir = FCPATH . 'uploads/landingpage/';
            $nama_file = str_replace('','_',$_FILES['filess']['name']);



            if (is_dir($nama_dir)) {

            } else {
                $oldmask = umask(0);
                mkdir($nama_dir, 0777, true);
                umask($oldmask);
            }



            
            foreach (glob($nama_dir.'/'.$nama_file) as $filename) {
                unlink($nama_dir.'/'.$nama_file);

            }
            
            // if (is_uploaded_file($_FILES['filess']['tmp_name'])) {
                $sourcePath = $_FILES['filess']['tmp_name'];

                $up = move_uploaded_file($sourcePath, $nama_dir.'/'.$nama_file);
             
            
                $data_detail = [ 
                    "struktur" => $nama_file, 
                ];
        
                $this->db->where('id', 1);
                $this->db->update('landing_page', $data_detail);
                $data=['name'=>$nama_file];
                echo json_encode($data);
        }
    }


}
