<script>

var geojson_edit = null
L<PERSON>TileLayer.BetterWMS = L.TileLayer.WMS.extend({
  
  onAdd: function (map) {
    // Triggered when the layer is added to a map.
    //   Register a click listener, then do all the upstream WMS things
    L.TileLayer.WMS.prototype.onAdd.call(this, map);
    map.on('click', this.getFeatureInfo, this);
    // map.on('click', this.consoles, this);
  },
  
  onRemove: function (map) {
    // Triggered when the layer is removed from a map.
    //   Unregister a click listener, then do all the upstream WMS things
    L.TileLayer.WMS.prototype.onRemove.call(this, map);
    map.off('click', this.getFeatureInfo, this);
  },
  
  getFeatureInfo: function (evt) {
    // Make an AJAX request to the server and hope for the best
    // console.log(evt)
    
    var url = this.getFeatureInfoUrl(evt.latlng),
        showResults = L.Util.bind(this.showGetFeatureInfo, this);
    $.ajax({
      url: url,
      success: function (data, status, xhr) {
        if (data.numberReturned == 1) {
          
          var gid = data.features[0].properties.gid
          $('#idDelete').val(gid)
          $('.leaflet-delete-div').css('display','block')

          if (data == geojson_edit) {
            return false
          };
         
          geojson_edit = data
          var pol = data.features[0].geometry.coordinates[0]
          drawnItems.clearLayers()
          var arr=[]
            $.each(pol, function(key, value) {
                        // console.log(  value[0] );
                        const temp = value[0];
                        value[0] = value[1];
                        value[1] = temp;
                        arr.push(value)
                    });
            var poly = L.polygon(arr,{
                        title: 'test',
                        fillColor: '#5893e0',
                        fillOpacity: 0.5,
                        weight: 1,
                        color: 'blue',
                        opacity: 0.5,
                        fill: true,
                    }).addTo(drawnItems);
            poly.bindPopup(<?=$popup?>);
            poly.on('popupopen', function () {
              showPopup()
              $('#geom').val(JSON.stringify(data))

            });
            initCombobox('kd_prov', 19);
            $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                  refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
            });
            
            $('.modal-bodyp').slimScroll({
                height: '250px'
            });
            console.log(geojson_edit)
       
        }
      },
      error: function (xhr, status, error) {
        showResults(error);  
      }
    });
  },
  
  getFeatureInfoUrl: function (latlng) {
    // Construct a GetFeatureInfo request URL given a point
    var point = this._map.latLngToContainerPoint(latlng, this._map.getZoom()),
        size = this._map.getSize(),
        
        params = {
          request: 'GetFeatureInfo',
          service: 'WMS',
          srs: 'EPSG:4326',
          styles: this.wmsParams.styles,
          transparent: this.wmsParams.transparent,
          version: this.wmsParams.version,      
          format: this.wmsParams.format,
          bbox: this._map.getBounds().toBBoxString(),
          height: size.y,
          width: size.x,
          buffer:5,
          fields:this.wmsParams.outFields,
          // height: 101,
          // width: 101,
          // x:50,
          // y:50,
          layers: this.wmsParams.layers,
          query_layers: this.wmsParams.layers,
          // info_format: 'text/html'
          info_format: 'application/json',
         //bgcolor: '#FFFFF'
          //env: 'backgroundColor:#FFFFFF'
        };
    
    params[params.version === '1.3.0' ? 'i' : 'x'] = point.x;
    params[params.version === '1.3.0' ? 'j' : 'y'] = point.y;
    
    return this._url + L.Util.getParamString(params, this._url, true);
  },
  
  showGetFeatureInfo: function (err, latlng, content) {

  function addThousandsSeparator(x) {
      try {
          return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      } catch (err) {
          return (x)
      }
  }
  
  function round(value, decimals) {
      try {
          var num = parseFloat(value).toFixed(0);
          if (decimals > 0) {
              num = Number(Math.round(value + 'e' + decimals) + 'e-' + decimals);
          }
          return num;
      } catch (err) {
          return value;
      }
  }

    function formatValue(value, field) {
      // Given a value and a field definition, apply the appropriate formatting
      // Used in the Query Widget and Identify functions
  
      try {
          if (field.decimals !== undefined) {
              value = round(value, field.decimals);
              
          }
          if (field.date !== undefined && value !== null) {
              try {
                  var theDate = new Date(value);
                  var theYear = theDate.year();
                  var theMonth = theDate.month() + 1;
                  if (theMonth.toString().length === 1) {
                      theMonth = "0" + theMonth;
                  }
                  var theDay = theDate.date();
                  if (theDay.toString().length === 1) {
                      theDay = "0" + theDay;
                  }
  
                  value = theYear + "/" + theMonth + "/" + theDay;
              } catch (err) {
                  console.log("Please ensure that Moment.js has been included");
              }
  
          }
          if (field.thousands !== undefined) {
              value = addThousandsSeparator(value);
          }
          if (field.prefix !== undefined) {
              value = field.prefix + value;
          }
      } catch (err) {
          console.log("There was a problem applying field formatting");
      }
  
      return (value);
  }

    function formatPopup(key, val, outFields) {
      // Given an input field name and value, return the appropriately formatted values
      for (var i = 0; i < outFields.length; i++) {
          var field = outFields[i];
          if (field.name !== undefined && field.name === key) {
              if (field.alias !== undefined) {
                  key = field.alias;
              }
              if (field.hidden === undefined || field.hidden === false) {
                  val = formatValue(val, field);
              } else {
                  key = null;
              }
              break;
          }
      }
      return [key, val];
  }


      if (content.features.length > 0) {
        var layerConfig = this.options;

        var output = '<h6><i>'+layerConfig.title+'<i></h6>'
        output += '<table class="table table-condensed">';
        
        for (key in content.features[0].properties) {
            
            var setOutFields = [
              "gid",
              "id",
              "objectid", 
              "objid", 
              "objek",
              "namobj",
              "remark",
              "kdpbps",
              "fcode",
              "uupp",
              "srs_id",
              "lcode",
              "metadata",
              "kdebps",
              "kdepum",
              "kdcbps",
              "kdcbps",
              "kdcpum",
              "kdbbps",
              "kdbpum",
              "kdbpum",
              "wiadkd",
              "wiadkc",
              "wiadkk",
              "wiadpr",
              "shape_leng", 
              "shape_area", 
              "wacname", 
              "wakname", 
              "wapname", 
              "wadname",
              "objtype",
              "objyear",
              "kdpkab",
              "kdppum",
              "pola_ruang",
              "kws_hutan",
              "pt",
              "nama_pul_1",
              "nama_pul_2",
              "nama_pul_3",
              "verified",
              "ket_rtrw",
              "verified_b",
              "tipadm",
              "gname_rek",
              "glabel",
              "fitcode",
              "idsn",
              "gcode50",
              "gname50",
              "gcode100",
              "gcode25",
              "kode",
              "gname25",
              "layer",
              "elevation",
              "gqname25",
              "id_masterdata",
              "hcode",
              "toponimi",
              "gm_type",
              "kkcode",
              "kfcode",
              "kscode",
              "kkname",
              "kfname",
              "ocode",
              "oname_rek",
              "kode_pt",
              "qcode100",
              "qcode25",
              "qname50",
              "qcode50",
              "qlabel",
              "qname_rek",
              //"qname100",
              "qcode100",
              "ktrsddlmta",
              "wcode",
              "wname_rek",
              "wlabel",
              "id_masterd",
              "su",
             "tglterbit", 
             "tglakhir", 
             "guna_usaha", 
             "est_prior", 
             "sumber", 
             "coding", 
             "kom_utama", 
             "kom_lain", 
             "produksi", 
             "penguasaan", 
             "jaraktanam", 
             "pkomoditas", 
             "jenistanah", 
             "jnspetani", 
             "jpts", 
             "itm", 
             "konflik", 
             "hat_ket", 
             "rd", 
             "rd_s", 
             "rd_p", 
             "ap", 
             "ap_s", 
             "lp2b", 
             "lp2b_s", 
             "ppb", 
             "ppb_s", 
             "kh", 
             "kh_s", 
             "a", 
             "b", 
             "c", 
             "tp_c", 
             "k_c", 
             "ocr", 
             "oca",
             "v_reclass",
             "tipologi",
             "hat",
             "q_reclass",
             "w_reclass",
             "w_s",
             "oname_rek", 
             "wname_rek",  
             "oname20", 
             "qname20", 
             "wname20",
             "wadmkd",
             "oname10",
             "tkt_kritis",
             "tk_kritis",
             "b_code",
             "u_code",
             "u_label",
             "x_code",
             "x_label",
             "d_code",
             "d_label",
             "e_code",
             "e_label",
             "qname5", 
             "qname10", 
             "qname25", 
             "esri_oid",
             "__gid",
             "orde01",
             "orde02",
             "orde03",
             "orde04",
             "orde04",
             "kkop_1",
             "kp2b_2",
             "krb_03",
             "ket_obj",
             "esri_oid",
             "psnid",
             "pmnid",
             "pfnid",
             "ptnid",
             "kode_kbli",
             //"kbli",
             "x",
             "y",
             //"luasha",
             //"kaw_hutan",
             "vnamew",
             "vnameo",
             "gq_rek",
             "gq",
             "glable",
             "perubahan",
             "qlable",
             "qcode20",
             "gname20",
             "gcode20",
             "qcode10",
             //"gname10",
             "gcode10",
             "gcode",
             "qcode",
             "vname_rek",
             "kdpkec",
             "koordinat",
             //"luas",
             //"gname100",
             "kesesuaian",
             "sedia_tnah",
             "hgu",
             "komoditas",
             "alamat_ptk",
             "luas_ptp",
             "luas_ilok",
             //"luas_m2",
             "prof_ktp",
             "prof_riil",
             "ppkt",
             "mark",
             "status",
             "rtrw",
             "nama_perus",
             "no_ptp"

             ];

            if(layerConfig.title === 'Tanah Kritis' || layerConfig.title === 'Monitoring PPT')  { setOutFields.push("oname19","qname19","wname19") };
            //if(layerConfig.title === 'NPGT Kabupaten/Kota')  { setOutFields.push("gname100","wadmkc") };
            //if(layerConfig.title === 'Pertimbangan Teknis Pertanahan PK P3T')  { setOutFields.push("luas_ha") };
            if(layerConfig.title === 'NPGT Provinsi' || layerConfig.title === 'Pertimbangan Teknis Pertanahan Izin Lokasi Provinsi- AOI' || layerConfig.title === 'NPGT Kecamatan Penggunaan Terakhir (Q)' || layerConfig.title === 'IP4T' || layerConfig.title === 'Pertimbangan Teknis Pertanahan Izin Lokasi Provinsi - POI' || layerConfig.title ==='Tanah Negara Bekas Hak' || layerConfig.title === 'Tanah Negara Bekas Kawasan' || layerConfig.title === 'Tanah Kritis')  { setOutFields.push("qname100") };
            if(layerConfig.title === 'NPGT Kabupaten/Kota')  { setOutFields.push("qname100", "gname100") };
            if(layerConfig.title === 'NPGT Perkebunan')  { setOutFields.push("luasha") };
            if(layerConfig.title === 'Pertimbangan Teknis Pertanahan PK P3T'|| layerConfig.title === 'Pertimbangan Teknis Pertanahan KKPR Berusaha' || layerConfig.title === 'Pertimbangan Teknis Pertanahan KKPR Non Berusaha' || layerConfig.title === 'Pertimbangan Teknis Pertanahan Tanah Timbul' || layerConfig.title === 'Pertimbangan Teknis Pertanahan KKPR Stranas' || layerConfig.title === 'WP3WT Penataan Tanah Timbul')  { setOutFields.push("luas_m2") };

            if (setOutFields.indexOf(key) < 0) {
               
                  var val = content.features[0].properties[key];

                // Use the field alias if supplied in the outFields parameter
               if (layerConfig.outFields !== undefined) {
                   var outFields = layerConfig.outFields;
                    var outputs = formatPopup(key, val, outFields);
                    key = outputs[0];
                    val = outputs[1];
                }
                if (key !== null) {
                    if(val == null) val = '-';
                    output += "<tr><td><b>" + key + "</b></td><td>:</td><td>" + val + "</td></tr>";
                }
            }
        }
        output += "</table>";


      } else if(content.features === undefined) {
        console.log('Not Support');

      }

      L.popup({ maxWidth: "auto", maxHeight: 200})
      .setLatLng(latlng)
      .setContent(output)
      .openOn(this._map);

      

  }
});

L.tileLayer.betterWms = function (url, options) {
  return new L.TileLayer.BetterWMS(url, options);  
};

</script>