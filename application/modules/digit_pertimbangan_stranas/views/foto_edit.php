<?php foreach ($data as $key => $value): ?>
	
    <div class="col-md-2">
        <img style="width:100%" src="<?=base_url().$value->filepath.'/'.$value->filename?>" alt="">
    </div>
    <div class="col-md-1">
        <input type="checkbox" class="btn-check img-check" name="deleted_foto[]" value="<?=$value->id_dok?>" id="btn-check<?=$key?>" autocomplete="off">
        <label class="btn btn-danger" id="btn-check<?=$key?>-lbl" for="btn-check<?=$key?>">Hapus</label>
    </div>
    
<?php endforeach ?>

<script>
    $(document).ready(function(){
        $('.img-check').on('change', function(){
            var id = '#'+$(this).attr('id')+'-lbl'; // Retrieve ID of the changed checkbox
            if($(this).is(':checked')) {
                
                $(id).html('Batal Hapus')
                $(id).removeClass('btn-danger')
                $(id).addClass('btn-primary')
            } else {
                $(id).html('Hapus')
                $(id).removeClass('btn-primary')
                $(id).addClass('btn-danger')
            }
        });
    });

    var xnumb=1;
    function xaddFotos(params) {
        var str = '<div class="col-md-10 appendFoto'+xnumb+'" >'+
            '<input id="foto'+xnumb+'" name="foto'+xnumb+'"  type="file" placeholder="" class=" form-control input-md ">'+
        '</div>'+
        '<div class="col-md-2 appendFoto'+xnumb+'">'+
            '<button type="button" style="height:100%" onclick="removeFoto(\'appendFoto'+xnumb+'\')" class="btn btn-danger"><i class="fa fa-minus fa-2xl" aria-hidden="true"></i></button>'+
        '</div>'
        $('#xdivFoto').append(str)
        xnumb++
    }
    function removeFoto(v) {
        $('.'+v).remove()
    }
</script>