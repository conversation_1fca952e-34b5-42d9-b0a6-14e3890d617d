<div class="modal " id="modDigitasi" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Digitasi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" id="frm-tambah">
                    <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>"
                        value="<?=$this->security->get_csrf_hash();?>" style="display: none">
                    <input type="hidden" id="idDelete" name="idDelete">
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-xs-12">
                            <div class="card" id="divMap" style="">
                                <!-- <button type="button" onclick="calculateArea()">get</button> -->
                                <div id="map2" style="height: 80vh;width: 100%"></div>
                            </div>
                        </div>
                    </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <!-- <button type="submit" class="btn btn-primary">Save changes</button> -->
                </form>
            </div>
        </div>
    </div>
</div>


<script>
$(document).ready(function() {
    $('.leaflet-popup-scrolled').slimScroll({
        height: '250px'
    });
    $('#luas_m2').on('input', function() {
        var inputValue = $(this).val();
        var numericValue = inputValue.replace(/[^0-9]/g, ''); // Remove non-numeric characters
        var formattedValue = numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.'); // Format with commas
        $(this).val(formattedValue); // Set the formatted value to the input
    });
    $('#frm-tambah').submit(function(e) {
        e.preventDefault();
        // getPolygon()
        var selectedOption = $("#kd_prov option:selected");
        $('#wadmpr').val(selectedOption.text());
        selectedOption = $("#kd_kabkot option:selected");
        $('#wadmkk').val(selectedOption.text());
        // return false
        var file = new FormData(this);
        $.ajax({
            // url: '<?php echo base_url(); ?>digit_pertimbangan_stranas/up',
            url: '<?php echo base_url(); ?>digit_pertimbangan_stranas/insertShp',
            type: "post",
            data: file,
            processData: false,
            contentType: false,
            cache: false,
            async: false,
            success: function(data) {
                // $("select").val('--pilih--');
                // $("select").selectpicker("refresh");
                data = JSON.parse(data)
                console.log(data.sts)
                if (data.sts == 'gagal') {
                    swal.close()
                    Swal.fire(
                        'Gagal!',
                        data.msg,
                        'error'
                    )
                } else {

                    $("#filess").val('')
                    var table = $('#dt-server-processing').DataTable();
                    table.ajax.reload();
                    swal.close()
                    Swal.fire(
                        'Sukses!',
                        'Data Tersimpan!',
                        'success'
                    )
                    map.closePopup()
                    drawnItems.clearLayers()
                    ptpStranas()

                }
            },
            error: function(jqXHR, exception) {
                // console.log(jqXHR);
                swal.close()
                Swal.fire(
                    'Gagal!',
                    'Data Gagal Tersimpan!',
                    'error'
                )
            }
        });
    });
});


var map
var drawnItems = new L.FeatureGroup();
var drawnPolygons
var geojsonBound;
var dataGeom
var role = "<?=$this->session->users['id_user_group_real']?>";
var kdpkab = "<?php echo $this->session->users['kd_kabkot'];?>";
var kdppum = "<?php echo $this->session->users['kd_prov'];?>";
$('#modDigitasi').on('shown.bs.modal', function(e) {

    $('#geom').val('')
    var mapContainer = document.getElementById('map2');

    if (mapContainer && mapContainer.classList.contains('leaflet-container')) {} else {
        map = L.map('map2').setView([-2.7521401146517785, 116.07226320582281], 5);
        var tombol_upload = L.control({
            position: 'topright'
        });
        tombol_upload.onAdd = function(map) {
            var buttonDiv = L.DomUtil.create('div', 'custom-button');
            buttonDiv.innerHTML =
                '<button type="button" onclick="uploadShp()" class="" style="border: none;border-radius: 2px;background-color:#fff;" id="custom-button">Upload SHP</button>';
            return buttonDiv;
        };
        tombol_upload.addTo(map);
        L.Control.geocoder({
            position: 'topleft'
        }).addTo(map);
        osmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');
        var gl = L.mapboxGL({
            style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
        }).addTo(map)
        googleHybrid = L.tileLayer('http://{s}.google.com/vt?lyrs=s,h&x={x}&y={y}&z={z}', {
            maxZoom: 20,
            subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
        });
        L.control.layers({
            "MapBox": gl.addTo(map),
            "OSM": osmLayer,
            "Google Hybrid": googleHybrid,
        }, {

        }, {
            position: 'topright',
            collapsed: false
        }).addTo(map);
        var lay = '<hr><div class="form-check">' +
            '<input class="form-check-input" onchange="ptpStranas()" type="checkbox" value="" id="Stranas">' +
            '<label class="form-check-label"  for="Stranas">' +
            'PTP P/RKKPR Kegiatan Stranas' +
            '</label>' +
            '</div>'


        $('.leaflet-control-layers').append(lay)

        map.addLayer(drawnItems);

        var drawControl = new L.Control.Draw({
            draw: {
                polygon: true,
                polyline: false,
                rectangle: true,
                circle: false,
                marker: false,
            },
            edit: {
                featureGroup: drawnItems,
                edit: true,
                remove: true
            }
        });
        map.addControl(drawControl);
        map.on('baselayerchange', function(event) {
            ptpStranas()
        });
        map.on('draw:edited', function(e) {
            var layers = e.layers;
            layers.eachLayer(function(layer) {
                // Handle the edited layer, you can access its coordinates with layer.getLatLngs()
                var editedPolygonCoordinates = layer.getLatLngs();
                layer.bindPopup(<?=$popup?>).openPopup();
                var shapes = layer.toGeoJSON();
                $('#geom').val(JSON.stringify(shapes))
                showPopup()
            });

            // console.log(e.layers)
            // console.log(geojson_edit)
        });


        map.on('draw:created', function(e) {
            var layer = e.layer;
            if (role == 7 || role == 8 || role == 9 || role == 10) {
                if (isPolygonWithinOuter(layer)) {
                    map.addLayer(layer);
                } else {
                    Swal.fire(
                        'Gagal!',
                        'Tidak Bisa Melebihi Wilayah yang ditentukan.',
                        'error'
                    );
                    map.removeLayer(layer);
                    return false
                }
            }
            drawnItems.addLayer(layer);



            layer.bindPopup(<?=$popup?>).openPopup();
            var coordinates = layer.getLatLngs()[0];
            var arr = []
            $.each(coordinates, function(index, value) {
                arr.push([value.lat, value.lng])
            });
            var area = L.GeometryUtil.geodesicArea(coordinates);
            $('#luasm2').val(area.toFixed(3))
            $('#luasha').val((area / 10000).toFixed(3))
            var shapes = layer.toGeoJSON();
            $('#geom').val(JSON.stringify(shapes))
            $('.modal-bodyp').slimScroll({
                height: '250px'
            });
            layer.on('popupopen', function() {
                $('.modal-bodyp').slimScroll({
                    height: '250px'
                });
            });

            function initComboboxSelect(divname, table, id, text, val_id) {
                url = WGI_APP_BASE_URL + "lookup/fieldlookSelect/" + table + "/" + id + "/" + val_id +
                    '/' + text;
                wgiAjaxCache(url, function(ajaxdata) {
                    jdata = JSON.parse(ajaxdata);
                    $('#' + divname).empty();
                    $.each(jdata, function(i, el) {
                        $('#' + divname).append(new Option(el.text, el[id]));
                    });
                    $('#' + divname).selectpicker('refresh')

                });
            }

            if (role == 7 || role == 8) {
                initComboboxSelect('kd_prov', 'aset_r_provinsi', 'kd_prov', 'nama_prov', kdppum);
                initComboboxSelect('kd_kabkot', 'aset_r_kabkota', 'kd_kabkot', 'nama_kabkot', kdpkab);
            } else {
                if (role == 9 || role == 10) {

                    initComboboxSelect('kd_prov', 'aset_r_provinsi', 'kd_prov', 'nama_prov', kdppum);
                    refreshSelectboot2('kd_kabkot', 20, 'kd_prov', kdppum);
                } else {
                    initCombobox('kd_prov', 19);
                    $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue,
                        oldValue) {
                        refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
                    });
                }
            }
        });
        L.Control.Button = L.Control.extend({
            options: {
                position: 'topleft'
            },
            onAdd: function(map) {

                var container = L.DomUtil.create('div', 'leaflet-bar leaflet-delete-div');
                var button = L.DomUtil.create('a', 'leaflet-delete-button', container);
                button.innerHTML = '<i class="fa fa-trash" aria-hidden="true"></i>'
                var button2 = L.DomUtil.create('a', 'leaflet-delete-button', container);
                button2.innerHTML = '<i class="fa fa-remove" aria-hidden="true"></i>'
                L.DomEvent.disableClickPropagation(button);
                L.DomEvent.disableClickPropagation(button2);
                L.DomEvent.on(button, 'click', function() {
                    var id = $('#idDelete').val()

                    if (id.length > 0) {
                        dtDeleteRowDigit(id)
                    }


                });
                L.DomEvent.on(button2, 'click', function() {
                    $('.leaflet-delete-div').css('display', 'none')
                    drawnItems.clearLayers();
                    geojson_edit = null
                });
                button.title = "Hapus Geometry";
                button2.title = "Cancel";

                return container;
            },
            onRemove: function(map) {},
        });
        var control = new L.Control.Button()
        control.addTo(map);
        L.control.zoomBox().addTo(map);
        L.control.scale().addTo(map);
        $('.leaflet-delete-div').css('display', 'none')
        if (role == 7 || role == 8 || role == 9 || role == 10) {
            fetch('<?php echo base_url();?>digit_pertimbangan_berusaha/getBoundary/')
                .then(response => {
                    // Check if the response is successful (status code in the range 200-299)
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    // Parse the response as JSON
                    return response.json();
                })
                .then(data => {
                    // Handle the data
                    dataGeom = JSON.parse(data.geom)
                    console.log('Data:', dataGeom);
                    geojsonBound = L.geoJSON(dataGeom, {
                        style: {
                            fillColor: 'transparent',
                            color: 'red',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.5
                        }
                    });

                    geojsonBound.addTo(map);
                    map.setMaxBounds(geojsonBound.getBounds());
                    map.on('drag', function() {
                        map.panInsideBounds(geojsonBound.getBounds(), {
                            animate: false
                        });
                    });
                    map.setMinZoom(map.getBoundsZoom(geojsonBound.getBounds()));


                })
                .catch(error => {
                    // Handle errors
                    console.error('Error:', error);
                });
        }

    }


})


function isPolygonWithinOuter(polygon) {
    var drawnPolygonGeoJSON = polygon.toGeoJSON();
    var isInside = turf.booleanWithin(drawnPolygonGeoJSON, dataGeom);
    return isInside;
}

function showPopup() {
    if (geojson_edit != null) {
        var datas = geojson_edit.features[0].properties

        function script1() {
            initCombobox('kd_prov', 19);

            $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
            });
            refreshSelectboot2('kd_kabkot', 20, 'kd_prov', datas.kdppum);
            console.log(1)
        }

        function script2() {
            console.log(2)
            console.log(datas.kdppum)
            console.log(datas.kdpkab)
            setTimeout(function() {
                $('#kd_prov').val(datas.kdppum).selectpicker('refresh')
                $('#kd_kabkot').val(datas.kdpkab).selectpicker('refresh')
            }, 500);
        }
        $.when(script1()).then(script2());

        $('.modal-bodyp').slimScroll({
            height: '250px'
        });

        $('#tahun_data').val(datas.tahun_data).selectpicker('refresh');
        $('#gid').val(datas.gid);
        $('#kdppum').val(datas.kdppum)
        $('#kdpkab').val(datas.kdpkab)
        $('#land_use').val(datas.land_use)
        $('#penguasaan').val(datas.penguasaan)
        $('#tata_ruang').val(datas.tata_ruang)
        $('#kesesuain').val(datas.kesesuain)
        $('#ketersedia').val(datas.ketersedia)
        $('#x').val(datas.x)
        $('#y').val(datas.y)
        $('#luasha').val(datas.luasha)
        $('#pemohon').val(datas.pemohon)
        $('#jns_ptp').val(datas.jns_ptp)
        $('#nomor_ptp').val(datas.nomor_ptp)
        $('#tgl_ptp').val(datas.tgl_ptp)
        $('#luas_m2').val(datas.luas_m2)
        $('#rencana_ke').val(datas.rencana_ke)
        $('#hasil_ptp').val(datas.hasil_ptp)
        $('#lokasi').val(datas.lokasi)
        $('#nama_perus').val(datas.nama_perus)
        $('#nib').val(datas.nib)
        $('#kode_kbli').val(datas.kode_kbli)
        $('#kbli').val(datas.kbli)
    }
}



function getPolygons(params) {
    var layers = L.PM.Utils.findLayers(map);
    var polygonsArray = [];
    var polygons = layers
    // Loop through the drawn polygons and convert each one to GeoJSON Polygon
    for (var i = 0; i < polygons.length; i++) {
        var latlngs = layers[i].getLatLngs()[0];
        // var lu=L.GeometryUtil.geodesicArea(layers[i].getLatLngs());
        var coordinates = [];
        // console.log(layers[i].getLatLngs()[0])
        for (var j = 0; j < latlngs.length; j++) {
            coordinates.push(latlngs[j].lng + ' ' + latlngs[j].lat);
        }

        // Push the first point again to close the polygon
        coordinates.push(coordinates[0]);

        polygonsArray.push([coordinates]);
    }
    // Create a GeoJSON MultiPolygon from the individual polygons

    multiPolygon = 'MULTIPOLYGON ' + JSON.stringify(polygonsArray).replace(/\[/g, '(').replace(/\]/g, ')').replace(
        /\"/g, '')
    $('#geom').val(multiPolygon)
}

function getMultiPolygonGeoJSON(polygons) {
    return false
    // Create an array to store the individual polygons
    var polygonsArray = [];

    // Loop through the drawn polygons and convert each one to GeoJSON Polygon
    for (var i = 0; i < polygons.length; i++) {
        var latlngs = polygons[i].latlngs[0];
        var coordinates = [];

        // Convert the latlngs to GeoJSON coordinates
        for (var j = 0; j < latlngs.length; j++) {
            coordinates.push(latlngs[j].lng + ' ' + latlngs[j].lat);
        }

        // Push the first point again to close the polygon
        coordinates.push(coordinates[0]);

        polygonsArray.push([coordinates]);
    }

    // Create a GeoJSON MultiPolygon from the individual polygons
    var geojson = {
        type: 'MultiPolygon',
        coordinates: polygonsArray,
    };

    return polygonsArray;
}

function getPolygon() {
    var layers = L.PM.Utils.findLayers(map);
    var group = L.featureGroup();
    layers.forEach((layer) => {
        group.addLayer(layer);
        var latt = layer.getLatLngs()[0]
        // var areas = L.GeometryUtil.geodesicArea(latt)
    });
    shapes = group.toGeoJSON();
    $('#geom').val(JSON.stringify(shapes))
    // console.log(shapes)
}

function clearAllLayer() {
    map.eachLayer(function(layer) {
        if (layer._path != null) {
            layer.remove()
        }
    });
}

function tes(params) {
    // alert('okay')
}
var ptp_kppr_stranas
var rtrw
var kemampuan_tanah
var wp3wt

<?php
            require_once(FCPATH."env.php");
            echo 'var WGI_APP_BASE_URL = "'.WGI_APP_BASE_URL.'"; ';
            echo 'var WGI_NODE_API_URL = "'.WGI_NODE_API_URL.'"; ';
            echo 'var WGI_APP_GEOSERVER_URL = "'.WGI_APP_GEOSERVER_URL.'"; ';

        ?>
var url = WGI_APP_GEOSERVER_URL + 'pgt/wms';
var gsAuthKey = WGI_APP_GEOSERVER_AUTHKEY;

function ptpStranas() {
    // alert('okay')
    if ($('#Stranas').prop('checked')) {
        console.log('load')
        if (map.hasLayer(ptp_kppr_stranas)) {
            map.removeLayer(ptp_kppr_stranas);
        };
        map.closePopup()
        ptp_kppr_stranas = L.tileLayer.betterWms(url, {
            layers: 'pgt:spatial_ptp_kppr_stranas',
            styles: 'pgt:spatial_ptp_kppr_stranas',
            transparent: true,
            format: 'image/png8',
            title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
            onEachFeature: function(feature, layer) {
                layer.on({
                    click: closePopup() // Attach the custom click function to each feature
                });
            },
            authkey: gsAuthKey
        });
        ptp_kppr_stranas.addTo(map)
        ptp_kppr_stranas.bringToFront();
    } else {
        if (map.hasLayer(ptp_kppr_stranas)) {
            map.removeLayer(ptp_kppr_stranas);
        };
    }
}


function kkemampuan_tanah() {
    // alert('okay')
    if ($('#kemampuanTanah').prop('checked')) {
        if (map.hasLayer(kemampuan_tanah)) {
            map.removeLayer(kemampuan_tanah);
        };
        map.closePopup()
        kemampuan_tanah = L.tileLayer.betterWms(url, {
            layers: 'pgt:spatial_kemampuan_tanah',
            styles: 'pgt:spatial_kemampuan_tanah',
            transparent: true,
            format: 'image/png8',
            title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
            cache: false,
            onEachFeature: function(feature, layer) {
                layer.on({
                    click: closePopup() // Attach the custom click function to each feature
                });
            },
            authkey: gsAuthKey
        });
        kemampuan_tanah.addTo(map)
        kemampuan_tanah.bringToFront();
    } else {
        console.log('g load')
        if (map.hasLayer(kemampuan_tanah)) {
            console.log('masuk if')
            map.removeLayer(kemampuan_tanah);
        };
    }
}



function rrtrw() {
    // alert('okay')
    if ($('#rtrw').prop('checked')) {
        console.log('load')
        if (map.hasLayer(rtrw)) {
            map.removeLayer(rtrw);
        };
        map.closePopup()
        rtrw = L.tileLayer.betterWms(url, {
            layers: 'pgt:spatial_rtrw',
            styles: 'pgt:spatial_rtrw',
            transparent: true,
            format: 'image/png8',
            title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
            cache: false,
            onEachFeature: function(feature, layer) {
                layer.on({
                    click: closePopup() // Attach the custom click function to each feature
                });
            },
            authkey: gsAuthKey
        });
        rtrw.addTo(map)
        rtrw.bringToFront();
    } else {
        if (map.hasLayer(rtrw)) {
            map.removeLayer(rtrw);
        };
    }
}

function wwp3wt() {
    // alert('okay')
    if ($('#wp3wt').prop('checked')) {
        console.log('load')
        if (map.hasLayer(wp3wt)) {
            map.removeLayer(wp3wt);
        };
        map.closePopup()
        wp3wt = L.tileLayer.betterWms(url, {
            layers: 'pgt:spatial_wp3wt_penataan_pesisir',
            styles: 'pgt:spatial_wp3wt_penataan_pesisir',
            transparent: true,
            format: 'image/png8',
            title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
            cache: false,
            onEachFeature: function(feature, layer) {
                layer.on({
                    click: closePopup() // Attach the custom click function to each feature
                });
            },
            authkey: gsAuthKey
        });
        wp3wt.addTo(map)
        wp3wt.bringToFront();
    } else {
        if (map.hasLayer(wp3wt)) {
            map.removeLayer(wp3wt);
        };
    }
}

function closePopup() {
    map.closePopup()
}

function numericPopup(v) {
    var id = v.attr('id')
    var inputValue = $('#' + id).val();
    var numericValue = inputValue.replace(/[^0-9]/g, ''); // Remove non-numeric characters
    var formattedValue = numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.'); // Format with commas
    $('#' + id).val(formattedValue); // Set the formatted value to the input
}
// =================================================
function uploadShp() {
   if (role == 7 || role == 8) {
        initComboboxSelectx('ukd_prov', 'aset_r_provinsi', 'kd_prov', 'nama_prov', kdppum);
        initComboboxSelectx('ukd_kabkot', 'aset_r_kabkota', 'kd_kabkot', 'nama_kabkot', kdpkab);
    } else {
        if (role == 9 || role == 10) {

            initComboboxSelectx('ukd_prov', 'aset_r_provinsi', 'kd_prov', 'nama_prov', kdppum);
            refreshSelectboot2('ukd_kabkot', 20, 'kd_prov', kdppum);
        } else {
            initCombobox('kd_prov', 19);
            $("#ukd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('ukd_kabkot', 20, 'kd_prov', this.value);
            });
        }
    }
    menu = $("#alias_menu").val();
    initComboboxTematik('layer', 22, menu);
    $('#modal-download').modal('show')
}

function initComboboxSelectx(divname, table, id, text, val_id) {
    url = WGI_APP_BASE_URL + "lookup/fieldlookSelect/" + table + "/" + id + "/" + val_id + '/' + text;
    wgiAjaxCache(url, function(ajaxdata) {
        jdata = JSON.parse(ajaxdata);
        $('#' + divname).empty();
        $.each(jdata, function(i, el) {
            $('#' + divname).append(new Option(el.text, el[id]));
        });
        $('#' + divname).selectpicker('refresh')
    });
}
</script>



<?php echo $js_wms?>