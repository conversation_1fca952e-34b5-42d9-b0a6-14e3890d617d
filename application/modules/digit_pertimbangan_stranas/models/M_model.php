<?php (defined('BASEPATH')) OR exit('No direct script access allowed');

class M_model extends MY_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }
	function search($title){
		$this->db->like('nmakun', $title , 'both');
		$this->db->order_by('nmakun', 'ASC');
		return $this->db->get('v_akun_bm')->result();
	}
    /*
     * To change this license header, choose License Headers in Project Properties.
     * To change this template file, choose Tools | Templates
     * and open the template in the editor.
     */

    public function get_by_id($table, $field, $id) {
        $this->db->from($table);
        $this->db->where($field, $id);
        $query = $this->db->get();

//echo $this->db->last_query();

        return $query->row();
    }

    public function save($table, $data) {
        $this->db->insert($table, $data);
     //   echo $this->db->last_query();
        return $this->db->insert_id();
    }

    public function cek_id($table, $data) {
       $query = $this->db->get_where($table, $data);
       
//       echo $this->db->last_query();
       
       //$row = $query->row();
        
//        $query = $this->db->query("SELECT `AUTO_INCREMENT` AS id
//                                    FROM INFORMATION_SCHEMA.TABLES
//                                    WHERE TABLE_SCHEMA = 'dbeplanningv3'
//                                    AND TABLE_NAME = '$table'");
//        $row = $query->row();

        return $query->row();
    }

    public function update($table, $where, $data) {
        $this->db->update($table, $where, $data);
        return $this->db->affected_rows();
    }

    public function delete_by_id($table, $field, $id) {
        $this->db->where($field, $id);
        $this->db->delete($table);
       $res = $this->db->affected_rows();
        return $res;
    }

    public function getInisialProv($id = null)
    {
        $this->db->select('inisial,nama_prov');
        $this->db->where('kd_prov', $id);
        $data = $this->db->get('aset_r_provinsi');
        
        return $data->row_array();
    }

    public function getInisialKabkot($id = null)
    {
        $this->db->select('inisial,nama_kabkot');
        $this->db->where('kd_kabkot', $id);
        $data = $this->db->get('aset_r_kabkota');
        
        return $data->row_array();
    }
    public function delete_gis_by_id($table, $param) {
        $this->db->delete($table,$param);
        $res = $this->db->affected_rows();
        return $res;
    }

    public function getAttachmen($id = null,$menu)
    {
        $this->db->select('vt.nama_layer,vt.id_layer,dn.nm_dok,dn.nm_dok,dn.id_dokpttnh,dn.txtidtahap61
        ,dn.path');
                            
        $this->db->where('vt.url', $menu);
        $this->db->join('dok_pt_tnh_timbul dn', 'dn.id_layer=vt.id_layer and dn.txtidtahap61 ='.$id, 'left');
        $this->db->order_by('vt.id_layer', 'asc');
        
        $data = $this->db->get('v_tematik vt');

        return $data->result();
    }

    public function getPathById($id)
    {
        $this->db->select('path');
        $this->db->where('id_dokpttnh', $id);
        $data = $this->db->get('dok_ptpil');
        return $data->row_array();
    }

}
