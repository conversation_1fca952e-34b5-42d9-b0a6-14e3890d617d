<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<link rel="stylesheet" href="<? echo base_url();?>assets/js/pgt-maps/leaflet.css" />
<link rel="stylesheet" href="<? echo base_url();?>assets/js/pgt-maps/L.Control.Layers.Tree.css" />
<link href="<?php echo base_url(); ?>assets/peta/select2/select2.min.css" rel="stylesheet" />
<link href="<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.css" rel="stylesheet" />
<link href="<?php echo base_url(); ?>assets/peta/leaflet-betterscale-master/L.Control.BetterScale.css" rel="stylesheet" />
<link rel="stylesheet" href="<?php //echo base_ url(); ?>assets/peta/leaflet-draw/css/L.Control.ZoomBox.min.css">
<link rel="stylesheet" href="<?php echo base_url(); ?>node_modules/leaflet-control-geocoder/dist/Control.Geocoder.css" />


<style>



#map{
    /* position: absolute; */
    top: 0;
    left: 0;
    /* width: 100%; */
    /* height: 900px; */
    min-height: 93vh;
    width: calc(100% + 80px); 
    height: calc(100% + 80px); 
    margin: -40px;
    
}

#map2{
    /* position: absolute; */
    top: 0;
    left: 0;
    width: 100%;
    /* height: 900px; */
    min-height: 600px;
    display: none;
}

.leaflet-grab {
   cursor: auto;
}
.leaflet-dragging .leaflet-grab{
   cursor: move;
}

.leaflet-control-layers-separator{
    display:block!important
}

.opt_select{
  color:black!important;
}

.main-body .page-wrapper{
  /* padding:0!important; */
}
body {
            max-height: 400px; /* Set your desired maximum height */
            overflow-y: auto; /* Enable vertical scrolling if content exceeds the height */
        }
/* Make sure the size of the image fits perfectly into the container */

</style>

<!-- <h3><a href="../"><big>◄</big> Leaflet Panel Layers</a></h3>
<h4> Groups Example: multiple groups of layers</h4>
<br /> -->
	<!-- <div id="sidebar-panel" class="sidebar collapsed"> -->
        <!-- Nav tabs -->
        <!-- <div class="sidebar-tabs"> -->
            <!-- <ul role="tablist"> -->
                <!-- <li><a href="#dashboard" role="tab"><i class="fa fa-bars"></i></a></li> -->
                <!-- <li><a href="#informasi" role="tab"><i class="fa fa-user"></i></a></li> -->
                <!-- <li class="disabled"><a href="#messages" role="tab"><i class="fa fa-envelope"></i></a></li>
                <li><a href="https://github.com/Turbo87/sidebar-v2" role="tab" target="_blank"><i class="fa fa-github"></i></a></li> -->
            <!-- </ul> -->

            <!-- <ul role="tablist">
                <li><a href="#settings" role="tab"><i class="fa fa-gear"></i></a></li>
            </ul> -->
        <!-- </div> -->

        <!-- Tab panes -->
        <!-- <div class="sidebar-content"> -->
			<!-- <div class="sidebar-pane" id="dashboard"> -->
                <!-- <h1 class="sidebar-header">Dashboard<span class="sidebar-close"><i class="fa fa-caret-left"></i></span></h1> -->
            <!-- </div> -->
            <!-- <div class="sidebar-pane" id="informasi"> -->
                <!-- <h1 class="sidebar-header"> -->
                    <!-- Informasi -->
                    <!-- <span class="sidebar-close"><i class="fa fa-caret-left"></i></span> -->
                <!-- </h1> -->

                <!-- <p id="infoobjek"></p> -->
            <!-- </div> -->

            <!-- <div class="sidebar-pane" id="messages">
                <h1 class="sidebar-header">Messages<span class="sidebar-close"><i class="fa fa-caret-left"></i></span></h1>
            </div>

            <div class="sidebar-pane" id="settings">
                <h1 class="sidebar-header">Settings<span class="sidebar-close"><i class="fa fa-caret-left"></i></span></h1>
            </div> -->
        <!-- </div> -->
    <!-- </div> -->
    <div>
        <style>
          .center-div {
    display: flex;
    justify-content: center;
    align-items: center;
    /* border: 1px solid black; */
}

.center-div img {
    max-width: 100%; /* Ensure the image doesn't exceed the container width */
    max-height: 100%; /* Ensure the image doesn't exceed the container height */
}
        </style>
      
      <!-- <div id="mymap" style="width: 100%; height: 400px;"></div> -->
      <div id="map">
        </div>
         <div id="div-mataangin" style="width:200px;display:none">
            <div class="center-div" style="">
              <img src="<?=base_url('uploads/print_peta/default/mata_angin.png')?>" alt="">
              
            </div>
            <div>
              <center style="display:block;margin-bottom:10px">Skala 1 : <span id="span_skala"></span></center>
              <center><div id="scales" style=""></div></center>
            </div>
        </div> 
        
        <div id="map2"></div>

  <style>
    .containers {
      max-height: 100px;
      overflow-y: auto;
      border: 1px solid #ccc;
      /* margin-top:130px */
      border-color:transparent;
    }
    .containers img {
      /* width: 80%; */
      height: auto;
    }
    .slimScrollDiv{
      height:auto!important;
    }
    .slimScrollBar{
      height:20px!important;
    }
  </style>





          
        <input type="hidden" name="imageChange" id="imageChange">
  <!-- <iframe src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d2027928.7027891946!2d113.58324044999999!3d-6.914709!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sid!2sid!4v1697119110698!5m2!1sid!2sid" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe> -->
  <!-- <button onclick="captures()">Tombol</button> -->
  </div>
  
    <div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="imageModalLabel"></h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          
          <div class="modal-body">
            <form id="uploadForm" class="form-horizontal" >
            <fieldset class="border rounded-3 p-3">
           <legend class="float-none w-auto px-3">Peta</legend>
           <img id="capturedImage" src="" alt="Captured Image" class="img-fluid">
           <img id="captured-image" src="" style="width:200px;display:none" alt="Captured Image">
           <img id="captured-mataangin" src="" style="width:170px;;display:none" alt="Captured Image">

           <!-- <button type="button" style="float:right" class="btn btn-primary" onclick="crop()" id="cropButton">Crop</button> -->
          
          </fieldset>
            <fieldset class="border rounded-3 p-3">
           <legend class="float-none w-auto px-3">Isian</legend>
          
            <div class="form-group">
              <label class="col-md-12 control-label" for="id_ptp">Nama Pemohon</label>
              <div class="col-md-12">
                <select id="id_ptp" name="id_ptp"  class="bootstrap-select form-control" data-live-search="true">
                  <!-- <option value="#">Pilih</option> -->
                </select>
                
              </div>
            </div>
          
          
            <div class="form-group">
              <label class="col-md-12 control-label" for="legenda">Legenda</label>  
              <div class="col-md-12">

                <select id="legenda" name="legenda"  class="bootstrap-select form-control" data-live-search="true"> -->
                </select>

             
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-12 control-label" for="ditinjau">Ditinjau Oleh</label>
              <div class="col-md-12">
              <input id="ditinjau" name="ditinjau"   type="text" placeholder="" class="form-control input-md numberonly" >

              </div>
            </div>
            <div class="form-group">
              <label class="col-md-12 control-label" for="tanggal">Tanggal</label>
              <div class="col-md-12">
              <input id="tanggal" name="tanggal"   type="date" placeholder="" class="form-control input-md numberonly" >

              </div>
            </div>
            <div class="form-group">
              <label class="col-md-12 control-label" for="digambar">Digambar Oleh</label>
              <div class="col-md-12">
              <input id="digambar" name="digambar"   type="text" placeholder="" class="form-control input-md numberonly" >

              </div>
            </div>
            <div class="form-group">
              <label class="col-md-12 control-label" for="diperiksa">Diperiksa Oleh</label>
              <div class="col-md-12">
              <input id="diperiksa" name="diperiksa"   type="text" placeholder="" class="form-control input-md numberonly" >

              </div>
            </div>
          </fieldset>

          <fieldset class="border rounded-3 p-3">
           <legend class="float-none w-auto px-3">Sumber Peta</legend>
        
            <div class="form-group div_sumber_peta">
              <label class="col-md-10 control-label" for="sumber_peta">Sumber Peta</label>
              <div class="row">

                <div class="col-md-10">
                  <input id="sumber_peta" name="sumber_peta[]"   type="text" placeholder="" class="form-control input-md numberonly" >
                </div>
                <button type="button" onclick="addSumber()" class="col-md-2 btn btn-sm btn-primary">Tambah</button>
              </div>
            </div>
          </fieldset>
          <fieldset class="border rounded-3 p-3">
           <legend class="float-none w-auto px-3"></legend>
        
            <div class="form-group">
              <label class="col-md-10 control-label" for="tanggal_surat">Tanggal Surat</label>
                <div class="col-md-10">
                  <input id="tanggal_surat" name="tanggal_surat"   type="date" placeholder="" class="form-control input-md numberonly" >
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-10 control-label" for="kepala_nama">Nama Kepala</label>
                <div class="col-md-10">
                  <input id="kepala_nama" name="kepala_nama"   type="text" placeholder="" class="form-control input-md numberonly" >
              </div>
            </div>
            <div class="form-group">
              <label class="col-md-10 control-label" for="kepala_nip">NIP Kepala</label>
                <div class="col-md-10">
                  <input id="kepala_nip" name="kepala_nip"   type="text" placeholder="" class="form-control input-md numberonly" >
              </div>
            </div>
          </fieldset>
        </div>

          
          <div class="modal-footer">
            <button type="button"  class="btn btn-success" onclick="sub()" id="">Submit</button>
            <button type="button" class="btn btn-secondary" onclick="($('#imageModal').modal('hide'))" data-dismiss="modal">Close</button>
            <!-- <button type="button" class="btn btn-primary" id="saveImageButton">Save Image</button> -->
          </div>
        </form>
        </div>
      </div>
    </div>
    <!-- <div id="a"> <h1>ini tes</h1></div> -->
    <!-- <button id="capture-button" class="btn btn-primary">Capture Map</button> -->
 

	<?php //echo $js_wms; ?>
	<?php echo $jv_script; ?>
	<?php //echo $print_peta; ?>
	<?php //echo $easy_print; ?>
<!-- <div id="copy"><a href="https://opengeo.tech/">Labs</a> &bull; <a rel="author" href="https://opengeo.tech/stefano-cudini/">Stefano Cudini</a></div> -->

<!-- <a href="https://github.com/stefanocudini/leaflet-panel-layers"><img id="ribbon" src="https://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png" alt="Fork me on GitHub"></a> -->
<script>
  
  // var img_blob
  
  $(document).ready(function() {
    
    // initComboboxPeta('legenda', 27)
    // $('#imageModal').on('show.bs.modal', function () {
    //     console.log('modal show')
    //     initComboboxPemohon('id_ptp', 26)

    //     var elementToCapture = document.getElementById("map2");
    //         setTimeout(function() {
    //             elementToCapture.style.display = "block";
    //             map2.invalidateSize();
    //             setTimeout(function() {
    //                   domtoimage.toPng(elementToCapture)
    //                     .then(function (dataUrl) {
    //                         var img = document.getElementById("captured-image");
    //                         img.src = dataUrl;
    //                     })
    //                     .catch(function (error) {
    //                         console.error("Error capturing element:", error);
    //                     })
    //                     .finally(function () {
    //                         // Hide the div again after capture
    //                         elementToCapture.style.display = "none";
    //                     });
    //             }, 2000);
    //         }, 10);           
    // });
    function crop(params) {
      cropper = new Cropper(document.getElementById('capturedImage'), {
                aspectRatio: 19 / 17,
                viewMode: 10, // Crop box can cover the whole preview
                guides: true,
                autoCropArea: 1,
                background: false,
                movable: false,
                zoomable: false,
                rotatable: false,
                scalable: false,
            });
            const croppedCanvas = cropper.getCroppedCanvas();
            console.log(croppedCanvas)
    }
      

    $('#uploadForm').submit(function (e) {
      e.preventDefault();

      var formData = new FormData(this);
          // Get the cropped canvas as a Blob
          
          // cropper.getCroppedCanvas().toBlob(function (blob) {
              // Create FormData object
              // var formData = new FormData();

              // Append the cropped image Blob to the FormData
              // blobs = $('#imageChange').val();
              // console.log(blobs)
              // console.log(blob)

              // var imageElement = document.getElementById("captured-image");
              // var canvas = document.createElement("canvas");
              // var ctx = canvas.getContext("2d");
              // canvas.width = imageElement.width;
              // canvas.height = imageElement.height;
              // ctx.drawImage(imageElement, 0, 0, imageElement.width, imageElement.height);
              // var dataURL = canvas.toDataURL("image/png");

              // // Append the data URL as a Blob to the FormData
              // var blob = dataURLtoBlob(dataURL);
              // formData.append("image", blob, "image.png");

              // Now you can send the FormData to the server or perform other actions

          // Function to convert a data URL to a Blob
          


                // formData.append('croppedImage', $('#imageChange').val(), 'cropped_image.png');

                formData.append("<?php echo $this->security->get_csrf_token_name(); ?>", "<?php echo $this->security->get_csrf_hash(); ?>");
              
                
                $.ajax({
                    url: '<?=base_url()?>'+'peta2/uploadPeta',
                    type: "post",
                    data: formData,
                    processData: false,
                    contentType: false,
                    cache: false,
                    async: false,
                    success: function (data) {
                        console.log(data)
                        var urlToOpen = "<?php echo base_url(); ?>peta2/export_pdf_peta/"+data;
                        window.open(urlToOpen, '_blank');
                        
                    },error: function (jqXHR, exception) {
                        
                    }
                });

            });
        // });

        


    });

    function dataURLtoBlob(dataURL) {
              var arr = dataURL.split(",");
              var mime = arr[0].match(/:(.*?);/)[1];
              var bstr = atob(arr[1]);
              var n = bstr.length;
              var u8arr = new Uint8Array(n);

              while (n--) {
                  u8arr[n] = bstr.charCodeAt(n);
              }

              return new Blob([u8arr], { type: mime });
          }

  
// Custom template function


function sub() {
  var formData = new FormData($('#uploadForm')[0]);
          var imgElement = document.getElementById('capturedImage');
          var imageUrl = imgElement.src;
          var img_zoom 
          fetch(imageUrl)
            .then(response => response.blob())
            .then(blob => {
              console.log(blob)
              formData.append('zoom', blob, 'image_zoom.png');
              var imgElement = document.getElementById('captured-image');
              var imageUrl = imgElement.src;
              var img_lokasi 
              fetch(imageUrl)
              .then(response => response.blob())
                .then(blob => {
                  formData.append('lokasi', blob, 'image_lokasi.png');
                    var imgElement = document.getElementById('captured-mataangin');
                    var imageUrl = imgElement.src;
                    var img_lokasi 
                    fetch(imageUrl)
                    .then(response => response.blob())
                    .then(blob => {
                      formData.append('mata_angin', blob, 'mata_angin.png');
                      formData.append("<?php echo $this->security->get_csrf_token_name(); ?>", "<?php echo $this->security->get_csrf_hash(); ?>");
              
                
                          $.ajax({
                              url: '<?=base_url()?>'+'peta2/uploadPeta',
                              type: "post",
                              data: formData,
                              processData: false,
                              contentType: false,
                              cache: false,
                              async: false,
                              success: function (data) {
                                  console.log(data)
                                  var urlToOpen = "<?php echo base_url(); ?>peta2/export_pdf_peta/"+data;
                                  window.open(urlToOpen, '_blank');
                                  
                              },error: function (jqXHR, exception) {
                                  
                              }
                          });
                        })
                    .catch(error => {
                      console.error('Error fetching and converting image to Blob:', error);
                    });
                  })
                .catch(error => {
                  console.error('Error fetching and converting image to Blob:', error);
                });
              })
            .catch(error => {
              console.error('Error fetching and converting image to Blob:', error);
            });

  
}

var no_sumber = 1
function addSumber() {
  
  var str = '<div id="sumber'+no_sumber+'" class="row"><div  class="col-md-10">'+
    '<input id="sumber_peta'+no_sumber+'" name="sumber_peta[]"   type="text" placeholder="" class="form-control input-md numberonly" >'+
  '</div>'+
  '<button type="button" onclick="delSumber(\'sumber'+no_sumber+'\')" class="col-md-2 btn btn-sm btn-danger">Hapus</button></div>'
  $('.div_sumber_peta').append(str)
}

function delSumber(v) {
  $('#'+v).remove()
}

function crop() {
  cropper.getCroppedCanvas()
}
</script>
