<script type="text/javascript" src="https://cdn.rawgit.com/ashl1/datatables-rowsgroup/fbd569b8768155c7a9a62568e66a64115887d7d0/dataTables.rowsGroup.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/v/dt/dt-1.10.12/datatables.min.js"></script>
<!-- <script src="/path/to/dist/row-merge-bundle.min.js"></script> -->
<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var xhrdata = null;
    var table = null;

    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";

    function clear_input() {
        $("#frm-tambah :input").val("");      
    }

    function removeSelectpicker(v='') {
        var groupFilter = $('#'+v);
        groupFilter.selectpicker('val', '');
        groupFilter.find('option').remove();
        groupFilter.selectpicker("refresh");
        
        // var cek = $('#'+v).val();
        // if(cek !=null && cek != '' ){
        //     var itemSelectorOption = $('#kd_prov option:selected');
        //     itemSelectorOption.remove(); 
        //     $('#'+v).selectpicker('refresh');
        // }
        // alert(cek)
    }

    function listing() {
        console.log('ok')
        table = $('#dt-server-processing').DataTable({
            "draw": 0,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>neraca_pgt_kabupaten/ssp_paket","data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>",
                d.kd_kabkot = $('#ikd_kabkot').val();
                
            }},
            "columnDefs": [
                {
                    "aTargets": [0],
                    "mRender": function (data, type, full,meta) {
                        // console.log(full)
                        return meta.row + meta.settings._iDisplayStart + 1;
                    }
                },
                {
                    "name":'prov',
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
                    
                },
                {
                    "aTargets": [2],
                    "mRender": function (data, type, full) {
                        return full[2];
                    }
                },
                {
                    "aTargets": [3],
                    "mRender": function (data, type, full) {
                        return full[3];
                    }
                },
                {
                    "aTargets": [4],
                    "mRender": function (data, type, full) {
                        return full[4];
                    }
                },{
                    "aTargets": [5],
                    "mRender": function (data, type, full) {
                        return full[5];
                    }
                },
                // {
                //     "aTargets": [5],
                //     "mRender": function (data, type, full) {
                //         return full[1];
                //     }
                // },
                // {
                //     "aTargets": [6],
                //     "mRender": function (data, type, full) {
                //         return full[3];
                //     }
                // },
                // {
                //     "aTargets": [7],
                //     "mRender": function (data, type, full) {
                //         return full[9];
                //     }
                // },
                // {
                //     "aTargets": [7],
                //     "mRender": function (data, type, full) {
                //         return full[];
                //     }
                // },
                // {
                //     "aTargets": [9],
                //     "mRender": function (data, type, full) {
                //         return full[10];
                //     }
                // },
                {
                    "aTargets": [6],
                    "mRender": function (data, type, row) {
                        var id = row[0];
                        var kd_prov = row[8];
                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "Edit",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "Hapus",
                            "</button>",
                            "<button onclick= dtUpload(" + id + ","+kd_prov+") class='btn btn-warning btn-xs'>",
                            "Upload",
                            "</button>",
                            //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                // "paginate": {
                //     "first": "<i class='fast backward ui icon'></i>",
                //     "last": "<i class='fast forward ui icon'></i>",
                //     "next": "<i class='step forward ui icon'></i>",
                //     "previous": "<i class='step backward ui icon'></i>"
                // },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function (e) {
            xhrdata = table.ajax.json();
        });
        //});
    }


    function dtDeleteRow(id) {
        if (confirm('Yakin untuk menghapus data ini?'))
        {

        // var url = "<?php echo base_url(); ?>" + "pemegang_saham/ajax_delete/" + id + "/" + yearnow;
        var url = "<?php echo base_url(); ?>" + "neraca_pgt_kabupaten/ajax_delete/" + id;
        var params = {"formData":{}, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
        $.post(url, params)
                .done(function (data) {

                var table = $('#dt-server-processing').DataTable();
                table.ajax.reload();
                })
                .fail(function () {
                alert("error");
                })
        }
    }


 

    function dtEditRow(id) {
      
      function start(callback) { 
          $("#modal-edit").modal("show");
          
          data_selected = xhrdata.data.filter(x => x[0] == id)[0];
          
          $("#xkd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', this.value);
                console.log(this.value)
            });
  
          $('#xid').val(id);
      }
      

      function a(){
          if(role == 1){ 
              initCombobox('xkd_prov', 19);
              initCombobox('xkd_status', 22);
            //   initCombobox('xkd_kabkot', 20);
            // console.log(data_selected[8]);
              refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', data_selected[8]);
            //   refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', data_selected[8]);

          }else{
              // refreshCombobox4('xid_ruas', 0, 'bujt', data_selected[20]);

          }

      };

      function b(){


          $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
            // console.log(data_selected[1])
              $('#xkd_prov').val(data_selected[8]).selectpicker('refresh');      
              $('#xkd_kabkot').val(data_selected[7]).selectpicker('refresh');      
              $('#xkd_status').val(data_selected[6]).selectpicker('refresh');      
          });

          
          $(".decformat").keyup(function (event) {
              if (event.which >= 37 && event.which <= 40)
                  return;
              // format number
              $(this).val(function (index, value) {
              return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
              });
          });

         

          $('#xtahun_data').val(data_selected[3]);
          $('#xluas').val(data_selected[5]);
      }

       $.when($.ajax(start())).then(a()).then(b());
    }

    var menu='';
    var id_neraca;
    function dtUpload(id,kdProv) {
        menu = $("#alias_menu").val();
        id_neraca = id;
        // alert(menu)
        $("#id_up").val(id);
        $("#kd_prov_up").val(kdProv);
        // initCombobox('layer', 22);
        initComboboxTematik('layer',22,menu);


        var tab = $('#table_id2').DataTable();
        tab.destroy()
        $('#table_id2 td').empty();
        listing_attachment2(id);
        $("#modal-download").modal("show");
    }
    function refreshSelectpicker() {
        removeSelectpicker('kd_kabkot')
        removeSelectpicker('layer')
        $('select').selectpicker('refresh')
    }
   
    function dtTambahRow() {

        // const myTimeout = setTimeout(refreshSelectpicker, 100);
        $('#modal-tambah').modal('show');

        // $('#frm-tambah').trigger('reset');
            clear_input()
            // removeSelectpicker()
            // $('#kd_prov').text('Pilih')
            initCombobox('kd_prov', 19);
            //    initCombobox('kd_kabkot', 20);
            initCombobox('kd_status', 22);
            // $('#kd_prov').selectpicker('refresh')


            $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
            });

            
            

        $(".decformat").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;
            // format number
            $(this).val(function (index, value) {
            return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            });
        });


        
       

    }

    function simpanForma() {

        if($( "#frm-tambah" ).valid() === true){
            var form = $('form')[0]; // You need to use standard javascript object here
            var formData = new FormData(form);
            
            // var formData = new FormData(document.getElementById("frm-tambah"));
            
            var fileInput = document.getElementById("petaA");
            var xobj_usulan = {
                "kd_kabkot" : $( "#kd_kabkot" ).val(),
                "kd_prov" : $( "#kd_prov" ).val(), 
                "kd_status" : $( "#kd_status" ).val(), 
                "tahun_data" : $( "#tahun_data" ).val(), 
                "luas" : $( "#luas" ).val(), 
                "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>",

            };

            formData.append("<?php echo $this->security->get_csrf_token_name(); ?>","<?php echo $this->security->get_csrf_hash(); ?>")
            
            
            var url = "<?php echo base_url(); ?>" + "neraca_pgt_kabupaten/save_form";
            var params = {"formData": xobj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                
            
            var table = $('#dt-server-processing').DataTable();
            table.ajax.reload();
            $('#modal-tambah').modal('hide');

            })
            .fail(function () {
            alert("error");
            });
            

        } else {
            alert("Data gagal disimpan, harap periksa informasi di setiap field isian");
            $(".error").css("color","red");
        }
    }


    function updateForm(){
        var id = $("#xid").val();
        var data_selected = xhrdata.data.filter(x => x[0] == id)[0];

        if($( "#frm-edit" ).valid() === true){

            var objmasterdetail = {
                    "id": id,
                    "kd_kabkot" : $( "#xkd_kabkot" ).val(),
                    "kd_prov" : $( "#xkd_prov" ).val(), 
                    "kd_status" : $( "#xkd_status" ).val(), 
                    "tahun_data" : $( "#xtahun_data" ).val(), 
                    "luas" : $( "#xluas" ).val(), 
                    "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"


            };
        //  console.log(objmasterdetail);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("neraca_pgt_kabupaten/update_form") ?>";
            $.post(url, params).done(function (data) {
                if (data == '1'){
                    alert('Data Sudah Ada');
                } else {
                    var table = $('#dt-server-processing').DataTable();
                    table.ajax.reload();
                    $('#modal-edit').modal('hide');
                }
            })
            .fail(function () {
            alert("error");
            });
        }else{
            alert("Data gagal diupdate, silahkan lihat informasi pada setiap field isian untuk memeriksa kesalahan entri data anda");
        $(".error").css("color","red");
        }
    }

    function convertTo(datastring) {
        var str = datastring;
        var s1 = str.replace('{','[');
            
        return eval(s1.replace('}',']'));
    }
    
    function bind_combo_induk(thang, selval){
        var data = get_induk(thang);
        var objthang = JSON.parse(data);
        $("#kdinduk").empty();
        $('#kdinduk').append("<option value='' >" + "--Pilih--" + "</option>");
        for(var i=0; i<= objthang.length-1; i++){
            $("#kdinduk").append("<option value="+objthang[i].kdsatker+">"+objthang[i].nmsatker+"</option>");
        }
        if (selval){
            $("#kdinduk").val(selval);
        }
    }
    
    function get_induk(thang){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('neraca_pgt_kabupaten/get_induk/') ?>" + thang,
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }




    
    function dtdetail(id){
        //console.log(xhrdata);

        var data_selected = xhrdata.data.filter(x => x[9] === id)[0];  //46

        var waydata = {
            thang: $("#fthang").val(),
            ibukota: data_selected[2],
            kd_prov:data_selected[0],
            kd_prov_bps: data_selected[3],
            kd_prov_irmsv3: data_selected[4],
            kd_prov_krisna:data_selected[5],
            kd_prov_rams: data_selected[6],
            kd_prov_rkakl: data_selected[7],
            kd_prov_sipro: data_selected[8],
            nama_prov: data_selected[1]

        }

        way.set('formData', waydata);
        $('#modalTitle').text('Detail');
//        $('#modeform').val('edit');
        $("input").prop('disabled', true);
	$("#hid").hide("slow");
	$("#hida").hide("slow");
        $('#modal-tambah').modal('show');
    }




   

    function close_alert() {
        $("#alert_information").css({display: "none"});
    }
    
    function bind_combo_thang(selval){
        var data = get_thang();
        var objthang = JSON.parse(data);
        for(var i=0; i<= objthang.length-1; i++){
            $("#fthang").append("<option value="+objthang[i].thang+">"+objthang[i].uraian+"</option>");
        }
        if (selval){
            $("#fthang").val(selval);
        }
    }
    
    function get_thang(){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('neraca_pgt_kabupaten/get_thang') ?>",
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    



    var table_attachment = null;
    function listing_attachment2(id) {
        
        const myOpts = document.getElementById('layer').options;

        $(myOpts).each(function(k,v)
        {
            // Add $(this).val() to your list
            console.log(k+v)
        });

        table_attachment = $('#table_id2').DataTable({

            "draw": 0,
            "columnDefs": [{"orderable": true, "targets": [0]}],
            "order": [[0, "desc"]], 
            "processing": true,
            "serverSide": true,
            "ajax": {
                type: "POST",
                url: "<?php echo base_url(); ?>neraca_pgt_kabupaten/ssp_attachment_file",
                data: function (d) {


                    d.id = id;
                    // d.role = role;
                    d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                }
            },
            "columnDefs": [
                { 
                    "aTargets": [0],
                    "mRender": function (data, type, full) {
                        
                        return full[0];
                        // alert("ASd")
                    } 
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        
                        return full[3];
                    }
                },
                
                {
                    "aTargets": [2],
                    "mRender": function (data, type, full) {
                        
                        return full[4];
                    }
                },
                // {
                // "aTargets": [2],
                // "mRender": function (data, type, row) {
                //     var id = row[0];
                //     var kd_prov = row[8];
                //     var html_button = [
                //         "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                //         "Edit",
                //         "</button>",
                //         "<button onclick= hapus_lampiran('" + id + "') class='btn btn-danger btn-xs'>",
                //         "Hapus",
                //         "</button>",
                //         "<button onclick= dtUpload(" + id + ","+kd_prov+") class='btn btn-warning btn-xs'>",
                //         "Upload",
                //         "</button>",
                //         //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                //     ].join("\n");
                //     return html_button;
                //     }
                // }
                
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                "paginate": {
                    "first": "<i class='fast backward ui icon'></i>",
                    "last": "<i class='fast forward ui icon'></i>",
                    "next": "<i class='step forward ui icon'></i>",
                    "previous": "<i class='step backward ui icon'></i>"
                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });
        table_attachment.on('xhr', function () {
            xhrdata1 = table_attachment.ajax.json();
            console.log(xhrdata1);
        });

        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
    }

    // }


        function get_extentsion_file(file) {
    var extension = file.substr((file.lastIndexOf('.') + 1));
    switch (extension) {
    case 'jpg':
            case 'png':
            case 'PNG':
            case 'jpeg':
            case 'gif':
            case 'JPG':
            return 'feather icon-image'; // There's was a typo in the example where
    break; // the alert ended with pdf instead of gif.
    case 'zip':
            case 'rar':
            //alert('was zip rar');
            return 'feather icon-archive';
            break;
    case 'pdf':
        return 'feather icon-file-text';
    case 'xlsx':
        return 'feather icon-file-text';
    break;
    default:
    return 'feather icon-file-text';

    }
    }

    function hapus_lampiran(id,column) {
        // alert(id+column)
        // alert('ok')
        // return false;
        if (confirm('Yakin untuk menghapus data ini?'))
        {

            var url = "<?php echo base_url(); ?>" + "neraca_pgt_kabupaten/hps_lampiran/" + id+"/"+column;
            var params = {"formData": {}, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params)
                    .done(function (data) {
                        // var tlist_paket = $('#tlist_paket').DataTable();
                        // tlist_paket.ajax.reload()
                        var tab = $('#table_id2').DataTable();
                        // tab.ajax.reload();
                        
                        tab.clear()
                        tab.destroy()
                        listing_attachment2(id_neraca);
                    })
                    .fail(function () {
                        alert("error");
                    })
        }
    }
    

    $(document).ready(function () {
        // bind_combo_thang(thangs);
            listing();
            // MergeGridCells();
            // SummerizeTable()
            $('.numberonly').keypress(function (e) {    
                var charCode = (e.which) ? e.which : event.keyCode    

                if (String.fromCharCode(charCode).match(/[^0-9]/g))    

                    return false;                        

            }); 

            $('.float-number').keypress(function(event) {
                if ((event.which != 46 || $(this).val().indexOf('.') != -1) && (event.which < 48 || event.which > 57)) {
                    event.preventDefault();
                }
            });

            $('.form-control-file').change(function(){
                var file = this.files[0];
                // name = file.name;
                size = file.size;
                type = file.type;
                console.log(file);
            });

            // $('#id_bujt, #id_ruas, #id_jnsdana, #id_refbank, #id_pt').select2({
            //     dropdownParent: $('#modal-tambah'),
            //     // width: 'resolve',
            //     theme: 'classic'
            // });

            // $('#xid_bujt, #xid_ruas, #xid_jnsdana, #xid_refbank, #xid_pt').select2({
            //     dropdownParent: $('#modal-edit'),
            //     theme: 'classic',
            // });

            // $('#xid_refbank, #xid_pt').select2({
            //     tags: true
            // });
            $('#submit').submit(function (e) {
            e.preventDefault();
            var file = new FormData;
            $.ajax({
                url: '<?php echo base_url(); ?>neraca_pgt_kabupaten/up',
                type: "post",
                data: new FormData(this),
                processData: false,
                contentType: false,
                cache: false,
                async: false,
                success: function (data) {
                    // $("#judul").val('')
                    $("select").val('--pilih--');
                    $("select").selectpicker("refresh");
                    // refreshSelectpicker();
                    
                    // setTimeout(function() {
                        
                    //     initComboboxTematik('layer',22,menu);
                    //     $('select').selectpicker('refresh');
                    //     console.log('ok')
                    // }, 1000);

                    $("#filess").val('')
                    // $("#kate").val("#");
                    var tab = $('#table_id2').DataTable();
                    tab.ajax.reload();
                    // tab.clear()
                    // tab.destroy()
                    // listing_attachment2(id_neraca);

                    // tlist_paket.ajax.reload();
                }
            });
        });

            $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker(); 
                
            }); 
            
            
            $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker();
            });
            $('body div#modal-download.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker();
            });
            
          
 
        // $('#dt-server-processing tbody').on('click', 'tr', function () {
        //     var data = table.row(this).data();
        //     // alert('You clicked on ' + data[0] + "'s row");
        //     var tr = $(this).closest('tr');
        //     var row = table.row(tr);
        //     // console.log(data)
        //     // alert(data)
        //     if (row.child.isShown()) {
        //         // This row is already open - close it
        //         row.child.hide();
        //         tr.removeClass('shown');
        //     } else {
        //         // Open this row
        //         row.child(format(row.data())).show();
        //         tr.addClass('shown');
        //     }
        //     // counter++;
        // });

            
            
            
        });

        function format(d) {
	    return (
	        '<table cellpadding="5" cellspacing="0" width="100%" border="0" style="padding-left:50px;">' +
	        '<tr>' +
	        '<td width="10%">Propinsi</td>' +
	        '<td>:' +
	        d[1] +
	        '</td>' +
	        '<td width="10%">Luas</td>' +
	        '<td>:' +
	        d[5] +
	        '</td>' +
	        '</tr>' +
	        
            '<tr>' +
	        '<td>Kabupaten</td>' +
	        '<td>:' +
	        d[2] +
	        '</td>' +
	        '<td>Tahun</td>' +
	        '<td>:' +
	        d[3 ] +
	        '</td>' +
	        '</tr>' +
            
            '<tr>' +
	        '<td>Status</td>' +
	        '<td>:' +
	        d[4] +
	        '</td>' +
	        '<td>ID</td>' +
	        '<td>:' +
	        d[6] +
	        '</td>' +
	        '</tr>' +

            '<tr>' +
	        '<td>KD Provinsi</td>' +
	        '<td>:' +
	        d[8] +
	        '</td>' +
	        '<td>KD Kabkota</td>' +
	        '<td>:' +
	        d[7] +
	        '</td>' +
	        '</tr>' 
            
	    );
	}


    function MergeGridCells() {
        // alert('me')
    var dimension_cells = new Array();
    var dimension_col = null;
    var columnCount = $("#dt-server-processing tr:first th").length;
    for (dimension_col = 0; dimension_col < columnCount; dimension_col++) {
        // first_instance holds the first instance of identical td
        var first_instance = null;
        var rowspan = 1;
        // iterate through rows
        $("#dt-server-processing").find('tr').each(function () {

            // find the td of the correct column (determined by the dimension_col set above)
            var dimension_td = $(this).find('td:nth-child(' + dimension_col + ')');

            if (first_instance == null) {
                // must be the first row
                first_instance = dimension_td;
            } else if (dimension_td.text() == first_instance.text()) {
                // the current td is identical to the previous
                // remove the current td
                dimension_td.remove();
                ++rowspan;
                // increment the rowspan attribute of the first instance
                first_instance.attr('rowspan', rowspan);
            } else {
                // this cell is different from the last
                first_instance = dimension_td;
                rowspan = 1;
            }
        });
    }
}
function SummerizeTable() {
    console.log('ser')
    $('#dt-server-processing').each(function() {
        $('#dt-server-processin').find('td').each(function() {
            console.log(this)
        var $this = $(this);
        var col = $this.index();
        var html = $this.html();
        var row = $(this).parent()[0].rowIndex; 
        var span = 1;
        var cell_above = $($this.parent().prev().children()[col]);

        // look for cells one above another with the same text
        while (cell_above.html() === html) { // if the text is the same
            span += 1; // increase the span
            cell_above_old = cell_above; // store this cell
            cell_above = $(cell_above.parent().prev().children()[col]); // and go to the next cell above
        }

        // if there are at least two columns with the same value, 
        // set a new span to the first and hide the other
        if (span > 1) {
            // console.log(span);
            $(cell_above_old).attr('rowspan', span);
            $this.hide();
        }
        
        });
    });
}


    function initComboboxTematik(divname, refindex,parwhere,$rev = 1) {


        var url = null;
        if ($rev === 1) {
            url = WGI_APP_BASE_URL + "lookup/fieldlooktematik/" + refindex+"/"+parwhere;
        } else {
            url = WGI_APP_BASE_URL + "lookup/fieldlooktematik/" + refindex +"/"+parwhere +"/" + 0;
        }
        // return false;
        wgiAjaxCache(url, function(ajaxdata) {
            jdata = JSON.parse(ajaxdata);
            $('#' + divname).empty();
            $('#' + divname).append(new Option("--Pilih--", ""));
            $.each(jdata, function(i, el) {
                $('#' + divname).append(new Option(el.nama_layer,el.nama_layer+'/'+el.no));
            });
            $('#' + divname).selectpicker('refresh')

        });
    }


</script>

