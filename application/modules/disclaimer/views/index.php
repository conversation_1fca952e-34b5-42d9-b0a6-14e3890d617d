<!-- <script src="<?=base_url()?>assets/ckeditor/ckeditor.js"></script>
<script src="<?=base_url()?>assets/ckeditor/samples/js/sample.js"></script>
<link rel="stylesheet" href="<?=base_url()?>assets/ckeditor/samples/css/samples.css">
<link rel="stylesheet" href="<?=base_url()?>assets/ckeditor/samples/toolbarconfigurator/lib/codemirror/neo.css"> -->

<link rel="stylesheet" href="<?php echo base_url() ?>assets/summernote/summernote.css">
<script src="<?php echo base_url() ?>assets/summernote/summernote.js"></script>


<!-- <script type="text/javascript" src="//code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" />
    <script type="text/javascript" src="cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script> -->

    <!-- <link href="summernote-bs5.css" rel="stylesheet">
    <script src="summernote-bs5.js"></script> -->

    <style>
        .note-popover.popover{
            display:none;
        }
    </style>
    <style>
  .modal {
  text-align: center;
  padding: 0!important;
}

.modal:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -4px;
}

.modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}
.modal-backdrop {
     background-color: rgba(0,0,0,.0001) !important;
}
</style>
<div class="page-body">
    
            <!-- Button trigger modal -->
<!-- <button type="button" class="btn btn-primary" onclick="show()" data-toggle="modal" data-target="#exampleModal">
  Launch demo modal
</button> -->

<!-- Modal -->
<div class="modal fade" style="background-color: rgba(0,0,0,.0001) !important;" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Modal title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
      <div class="text-center">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary">Save changes</button>
      </div>
    </div>
  </div>
</div>
    <div class="card">
    <?php 

?>
        <div class="card-header">
            <h5><?=$title;?></h5>
        </div>
        <div classc="container" style="pading:20px">

        <button title="Edit" id="btnEdit" class="btn btn-warning waves-effect waves-light " style="margin-right:10px;margin-bottom:10px;float:right;border-radius:10%;padding :10px" onclick="edit()"><i class="fa-regular fa-3x fa-pen-to-square"></i></button>
        <button title="Simpan" id="btnSimpan" class="btn btn-primary waves-effect waves-light " style="margin-right:10px;margin-bottom:10px;float:right;display:none;border-radius:10%;padding :10px" onclick="updateForm()"><i class="fa-regular fa-4x fa-floppy-disk"></i></button>
            
        </div>
        <form class="form-horizontal" id="frm-edit">
            <!-- <div class="card-block">
                <div id="editor">
                      <?=$data['disclaimer']?>  
                </div>
            </div> -->
            <div class="card border" id="divShow" style="margin:20px;border-radius:10px;padding:10px">

                <?=$data['disclaimer']?>
            </div>
            <div class="container" id="divSummernote" style="display:none;margin:10px">

                <textarea id="summernote" name="editordata"><?=$data['disclaimer']?> </textarea>
            </div>
        </form>

    </div>
    
</div>   




<?php echo $jv_script; ?>

<script>
        function show() {
    $('#exampleModal').modal('show')
}
      </script>
    <script>
        
	// initSample();
</script>