<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>

<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/leaflet-draw/css/leaflet.css" />
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/geoman/leaflet-geoman.css" /> 
<link href='<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.css' rel='stylesheet' />
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/leaflet-draw/css/leaflet.draw.css" />
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/leaflet-draw/css/L.Control.ZoomBox.min.css">

<script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/libs/leaflet/leaflet.js"></script>
<script src="<?php echo base_url(); ?>assets/peta/geoman/leaflet-geoman.min.js"></script>  
<script src='<?php echo base_url(); ?>assets/peta/mapbox/mapbox-gl.js'></script>
<script src="<?php echo base_url(); ?>assets/peta/mapbox/leaflet-mapbox-gl.js"></script>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/leaflet-draw/js/L.Control.ZoomBox.min.js"></script>
<script type="text/javascript" src="<?php echo base_url(); ?>assets/peta/leaflet-draw/js/leaflet.draw.js"></script>
<link rel="stylesheet" href="<?php echo base_url(); ?>assets/peta/geocoder/control.geocoder.css" />
<script src="<?php echo base_url(); ?>assets/peta/geocoder/control.geocoder.js"></script>     
<script src="<?php echo base_url(); ?>assets/peta/turf/turf.js"></script>
<link>

<style>

.leaflet-popup{
    width:400px;
    height:300px;
}

.leaflet-popup-content-wrapper{
    width: 90%!important;
}
.leaflet-popup-content{
    width:100% !important;
}

.modal-bodyp{
    max-height:400px;
    width:90%;
}

</style>

 <div class="page-body">
    <!-- Server Side Processing table start -->
    <div class="card">
        <div class="card-header">
            <h5><?=$title;?></h5>
        </div>
        <div class="card-block">
            
        <div class="row">    
                <div class="col-sm-6">
                    <!-- <button type="button" style="float:left!important" class=" btn btn-primary waves-effect waves-light add" onclick="dtUpload();">Tambah Neraca Provinsi -->
                    <button type="button" style="float:left!important" class=" btn btn-primary waves-effect waves-light add" onclick="digitasi();">Digitasi
                </div>
                <div class="col-sm-6 d-flex align-items-end justify-content-end" style="">
                    <!-- <button style="margin-left:3px;border-radius:20%;color:blue;border-color:blue;" onclick="downloadCaraUpload()" title="Petunjuk Upload" class=""><i class="fa-regular fa-2x fa-file-pdf"></i></button> -->
                    <!-- <button style="margin-left:3px;border-radius:20%;color:red;border-color:red" title="Dokumen " class=""onclick="viewDokumen()"><i class="fa-regular fa-2x fa-file-pdf"></i></button> -->
                </div>
                    <!-- <div class="col-md-2 " style="margin-top:20px;position: relative;">
                    </div> -->
                    <div class="col-md-4" style="margin-top:20px;display:inline">
                        <label style="font-size:15pt;">Provinsi</label>
                        <select id="ikd_prov" onchange="filterChange()" name="ikd_prov" class="bootstrap-select form-control" data-live-search="true">
                        <!-- <option value="#">Pilih</option> -->
                        </select>
                    </div>
                    <div class="col-md-4" style="margin-top:20px">
                        <label style="font-size:15pt">Kabupaten/Kota</label>
                        <select id="ikd_kabkot"  onchange="filterChange()" name="ikd_kabkot" class="bootstrap-select form-control" data-live-search="true">
                        <!-- <option value="#">Pilih</option> -->
                        </select>
                    </div>
                    <div class="col-md-4" style="margin-top:20px">
                        <label style="font-size:15pt">Tahun Data</label>
                        <select id="itahun_data"  onchange="filterChange()" name="itahun_data" class="bootstrap-select form-control" data-live-search="true">
                        <option value="#">Semua Tahun</option>
                        <option value="">Tidak Ada Tahun</option>
                        <?php 
                            $now = date('Y');
                            for ($i=$now; $i >= 1990 ; $i--) { 
                                echo '<option value="'.$i.'">'.$i.'</option>';
                            }
                        ?>
                        </select>
                    </div>
                    
                   
            </div>
            <br/>
            <div>
                
            </div>
            <div class="dt-responsive table-responsive">
                <table id="dt-server-processing" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Kabupaten/Kota</th>
                            <th>Pemohon</th>
                            <th>Nomor PTP</th>
                            <th>Tanggal PTP</th>
                            <th>Luas m2</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
    <!-- Server Side Processing table end -->
</div>   
<input type="hidden" name="alias_menu" id="alias_menu" value="<?php echo $menu?>">

<?php echo $jv_script; ?>
<?php echo $modal_digitasi; ?> 
<?php echo $modal_digitasi_edit; ?> 
<?php echo $modal_download; ?> 
<?php echo $modal_history; ?> 
