


<div class="modal " id="modDigitasiEdit" tabindex="-1">
  <div class="modal-dialog modal-fullscreen">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Digitasi Edit</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal" id="frm-edit">
            <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
            <input type="hidden" name="xgid" id="xgid">
            <input type="hidden" name="xwadmpr" id="xwadmpr">
            <input type="hidden" name="xwadmkk" id="xwadmkk">
            <div class="row">
                <div class="col-sm-12 col-md-12 col-xs-12">
                    <div class="card" id="xdivMap" style="">
                    <!-- <button type="button" onclick="clearAllLayer()">clear</button> -->
                        <div id="xmap2" style="height: 80vh;width: 100%"></div>
                    </div>
                </div>
             </div>
            <div class="row">
                <div class="form-group col-md-6 ">
                    <label class="col-md-12 control-label"  for="xkd_prov">Provinsi</label>
                    <div class="col-md-12">
                    <select id="xkd_prov" name="xkd_prov" onchange="" class="bootstrap-select form-control" data-live-search="true">
                    </select>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-12 control-label" for="xkd_kabkot">Kab/Kota</label>
                    <div class="col-md-12">
                        <select id="xkd_kabkot" name="xkd_kabkot" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                    </div>
                </div>
                <div class="form-group col-md-6 ">
                    <label class="col-md-12 control-label" for="xthndata">Tahun Data</label>  
                    <div class="col-md-12">
                        <select id="xthndata"   name="xthndata" class="bootstrap-select form-control" data-live-search="true">
                        <?php 
                            $now = date('Y');
                            for ($i=$now; $i >= 1990 ; $i--) { 
                                echo '<option value="'.$i.'">'.$i.'</option>';
                            }
                        ?>
                        </select>
                    </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xlokasi">Lokasi</label>  
                        <div class="col-md-12">
                        <input id="xlokasi" name="xlokasi" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xpemohon">Pemohon</label>  
                        <div class="col-md-12">
                        <input id="xpemohon" name="xpemohon" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xnamprh">Nama Perus</label>  
                        <div class="col-md-12">
                        <input id="xnamprh" name="xnamprh" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xalamat_perusahaan">Alamat Perusahaan</label>  
                        <div class="col-md-12">
                        <input id="xalamat_perusahaan" name="xalamat_perusahaan" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xrcnkeg">Rencana Kegiatan</label>  
                        <div class="col-md-12">
                        <input id="xrcnkeg" name="xrcnkeg" type="text" placeholder="" class="form-control input-md numeric">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xnib">NIB</label>  
                        <div class="col-md-12">
                        <input id="xnib" name="xnib" type="text" placeholder="" class="form-control input-md numeric">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xkdkbli">Kode KBLI</label>  
                        <div class="col-md-12">
                        <input id="xkdkbli" name="xkdkbli" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xkbli">KBLI</label>  
                        <div class="col-md-12">
                        <input id="xkbli" name="xkbli" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xjptp">Jenis PTP</label>  
                        <div class="col-md-12">
                        <input id="xjptp" name="xjptp" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xnoptp">Nomor PTP</label>  
                        <div class="col-md-12">
                        <input id="xnoptp" name="xnoptp" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xtglptp">Tanggal PTP</label>  
                        <div class="col-md-12">
                        <input id="xtglptp" name="xtglptp" type="date" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xhslptp">Hasil PTP</label>  
                        <div class="col-md-12">
                        <!-- <input id="xhslptp" name="xhslptp" type="text" placeholder="" class="form-control input-md "> -->
                        <select id="xhslptp" name="xhslptp" onchange="" class="bootstrap-select form-control" data-live-search="true">
                            <option value="">Tidak Ada hasil</option>
                            <option value="Sesuai">Sesuai</option>
                            <option value="Tidak Sesuai">Tidak Sesuai</option>
                            <option value="Sesuai Bersyarat">Sesuai Bersyarat</option>
                        </select>
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xluas_ha">Luas ha</label>  
                        <div class="col-md-12">
                        <input id="xluas_ha" name="xluas_ha" oninput="numericPopup($(this))" type="text" placeholder="" class="numeric form-control input-md ">
                        </div>
                    </div>
                        <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xluasm2">luas m2</label>  
                        <div class="col-md-12">
                        <input id="xluasm2" name="xluasm2" oninput="numericPopup($(this))" type="text" placeholder="" class="numeric form-control input-md ">
                        </div>
                    </div>
                    <div class="form-group col-md-6 ">
                        <label class="col-md-12 control-label" for="xkbli">Foto Baru</label>  
                        <div class="row" id="xdivFoto">
                            <div class="col-md-10">
                                <input id="xfoto" name="xfoto"  type="file" placeholder="" class=" form-control input-md ">
                            </div>
                            <div class="col-md-2">
                                <button type="button" style="height:100%" onclick="xaddFotos()" class="btn btn-success"><i class="fa fa-plus fa-2xl" aria-hidden="true"></i></button>
                            </div>
                        </div>
                    </div>
                    <div  class="form-group col-md-12 row">
                        <label class="col-md-12 control-label" for="">Foto</label>  
                        <div class="row" id="div_foto">
                            
                        </div>
                    </div>
                    <div class="form-group col-md-6">
                        <label class="col-md-12 control-label" for="xluas"></label>  
                        <div class="col-md-12">
                        <input id="xgeom" name="xgeom" type="hidden" placeholder="" class="form-control input-md float-number">
                        <input id="xgeom_json" name="xgeom_json" type="text" placeholder="" class="form-control input-md float-number">
                        
                        </div>
                    </div>
                    
            </div>
            
            </div>
            <div class="modal-footer">
                <button type="button" id="xbtn-close" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="submit" onclick="$('#frm-edit').submit()" class="btn btn-primary">Save changes</button>
            </form>
            </div>
    </div>
  </div>
</div>


<script>
    $(document).ready(function () {
     
        $('#frm-edit').submit(function (e) {
            e.preventDefault();
            getPolygons()
            var selectedOption = $("#xkd_prov option:selected");
            $('#xwadmpr').val(selectedOption.text());
             selectedOption = $("#xkd_kabkot option:selected");
            $('#xwadmkk').val(selectedOption.text());
            var file = new FormData(this);
            $.ajax({
                    // url: '<?php echo base_url(); ?>digit_pertimbangan_psrp_tnh_timbul/up',
                    url: '<?php echo base_url(); ?>digit_pertimbangan_non_berusaha/updateShp',
                    type: "post",
                    data: file,
                    processData: false,
                    contentType: false,
                    cache: false,
                    async: false,
                    success: function (data) {
                        // $("select").val('--pilih--');
                        // $("select").selectpicker("refresh");
                        data=JSON.parse(data)
                        console.log(data.sts)
                        if(data.sts=='gagal'){
                            swal.close()
                            Swal.fire(
                                'Gagal!',
                                data.msg,
                                'error'
                            )
                        }else{

                            $("#filess").val('')
                            var tab = $('#dt-server-processing').DataTable();
                            tab.ajax.reload();
                            $('#dt-server-processing_processing').css('display','none')
                            swal.close()
                            Swal.fire(
                                'Sukses!',
                                'Data Tersimpan!',
                                'success'
                            )
                            $('#xbtn-close').click()

                        }
                        
                    },error: function (jqXHR, exception) {
                        // console.log(jqXHR);
                        swal.close()
                        Swal.fire(
                            'Gagal!',
                            'Data Gagal Tersimpan!',
                            'error'
                        )
                    }
                });
        });
    });
    var xmap
    var drawnPolygons
    var drawnItems 
    var dataGeojson
    var multiPolygonLayer
    var drawControl
    var geojsonBounds;
    var dataGeom
    var isMulti = 0;
    var xrole = "<?=$this->session->users['id_user_group_real']?>";
    var xkdpkab = "<?php echo $this->session->users['kd_kabkot'];?>";
    var xkdppum = "<?php echo $this->session->users['kd_prov'];?>";
    $('#modDigitasiEdit').on('shown.bs.modal', function (e) {
        var gid = $('#xgid').val()
        var table = 'dok_ptp_tnh_timbul'
        
    
         var mapContainer = document.getElementById('xmap2');
        if (mapContainer && mapContainer.classList.contains('leaflet-container')) {

            var dataGeojson = JSON.parse($('#xgeom_json').val())
            if (dataGeojson.type === 'MultiPolygon') {
                var polygonCoordinates = dataGeojson.coordinates[0][0]; // Extract coordinates
                 dataGeojson = {
                "type": "Polygon",
                "coordinates": [polygonCoordinates]
                };
                isMulti = 1;
            }else{
                isMulti = 0;
            }
            xmap.eachLayer(function(layer){
                if (layer._path != null) {
                    layer.remove()
                }
            });
            drawnItems = L.geoJSON(dataGeojson);
            xmap.addLayer(drawnItems);
            setTimeout(() => {
                xmap.fitBounds(drawnItems.getBounds());
                
            }, 1000);
            xmap.removeControl(drawControl);
            drawControl = new L.Control.Draw({
                draw: false,
                edit: {
                    featureGroup: drawnItems,
                    edit: true,
                    remove: true
                }
            });
            xmap.addControl(drawControl);
        
            xmap.on('draw:edited', function (e) {
                var layers = L.PM.Utils.findLayers(xmap);
                var lay = e.layers
                lay.eachLayer(function (layer) {
                    var editedLayerBounds = layer.getBounds();
                    if (xrole == 7 || xrole == 8 || xrole == 9 || xrole == 10) {
                        if (!isPolygonWithinOuter(layer)) {
                            Swal.fire(
                                'Gagal!',
                                'Tidak Bisa Melebihi Wilayah yang ditentukan.',
                                'error'
                            );
                            drawnItems.clearLayers();
                            xmap.removeControl(drawControl);
                            drawnItems = L.geoJSON(dataGeojson);
                            xmap.addLayer(drawnItems);
                            drawControl = new L.Control.Draw({
                                draw: false,
                                edit: {
                                    featureGroup: drawnItems,
                                    edit: true,
                                    remove: true
                                }
                            });
                            xmap.addControl(drawControl);
                            return false;
                        }   
                    }
                });
                var coordinates = layers[0].getLatLngs()[0];
                var arr =[]
                $.each(coordinates, function(index, value) {
                    arr.push([value.lat,value.lng])
                });
                var area = L.GeometryUtil.geodesicArea(coordinates);
                $('#xluasm2').val(area)
                $('#xluas_ha').val(area/10000)
                console.log(coordinates)
                console.log(area)
            });
            if (xrole == 7 || xrole == 8 || xrole == 9 || xrole == 10) {
                fetch('<?php echo base_url();?>digit_pertimbangan_berusaha/getBoundary/')
                .then(response => {
                    if (!response.ok) {
                    throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Handle the data
                    dataGeom = JSON.parse(data.geom)
                    geojsonBounds = L.geoJSON(dataGeom, {
                            style: {
                            fillColor: 'transparent',  
                            color: 'red',      
                            weight: 2,         
                            opacity: 1,        
                            fillOpacity: 0.5   
                        }
                    });
                    
                    geojsonBounds.addTo(xmap);
                    xmap.setMaxBounds(geojsonBounds.getBounds());
                    xmap.on('drag', function() {
                        xmap.panInsideBounds(geojsonBounds.getBounds(), { animate: false });
                    });
                    xmap.setMinZoom(xmap.getBoundsZoom(geojsonBounds.getBounds()));
                    
                    console.log(geojsonBounds)
                    
                })
                .catch(error => {
                    // Handle errors
                    console.error('Error:', error);
                });
            }

        }else{
            
            xmap = L.map('xmap2').setView([-2.7521401146517785, 116.07226320582281], 5);
        //   map = L.map('map2').setView([51.505, -0.09], 13);
            // L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
            //     maxZoom: 19,
            //     attribution: '© OpenStreetMap'
            // }).addTo(xmap);

            var xgl = L.mapboxGL({
                style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
            });
            var xosmLayer = new L.TileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(xmap);

            var xgoogleHybrid = L.tileLayer('http://{s}.google.com/vt?lyrs=s,h&x={x}&y={y}&z={z}', {
                subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
            });
            // xmap.pm.addControls({  
            //     position: 'topleft',  
            //     drawCircleMarker: false,
            //     rotateMode: false,
            //     drawCircle:false,
            //     drawText:false,
            //     cutPolygon:false,
            //     drawPolygon:true,
            //     drawMarker:false,
            //     drawPolyline:false,
            //     drawRectangle:true,
            //     dragMode:false,

            // }); 

           
            drawnPolygons = [];
            // xmap.removeLayer(multiPolygonLayer);
            var dataGeojson = JSON.parse($('#xgeom_json').val())
            if (dataGeojson.type === 'MultiPolygon') {
                var polygonCoordinates = dataGeojson.coordinates[0][0]; // Extract coordinates
                 dataGeojson = {
                "type": "Polygon",
                "coordinates": [polygonCoordinates]
                };
                isMulti = 1;
            }else{
                isMulti = 0;
            }
            drawnItems = L.geoJSON(dataGeojson);
            xmap.addLayer(drawnItems);
            setTimeout(() => {
                xmap.fitBounds(drawnItems.getBounds());
                
            }, 100);

            drawControl = new L.Control.Draw({
                draw: false,
                edit: {
                    featureGroup: drawnItems,
                    edit: true,
                    remove: true
                }
            });
            xmap.addControl(drawControl);
        
            xmap.on('draw:edited', function (e) {
                var layers = L.PM.Utils.findLayers(xmap);
                var lay = e.layers;
                lay.eachLayer(function (layer) {
                    var editedLayerBounds = layer.getBounds();
                    if (xrole == 7 || xrole == 8 || xrole == 9 || xrole == 10) {
                        if (!isPolygonWithinOuter(layer)) {
                            Swal.fire(
                                'Gagal!',
                                'Tidak Bisa Melebihi Wilayah yang ditentukan.',
                                'error'
                            );
                            drawnItems.clearLayers();
                            xmap.removeControl(drawControl);
                            drawnItems = L.geoJSON(dataGeojson);
                            xmap.addLayer(drawnItems);
                            drawControl = new L.Control.Draw({
                                draw: false,
                                edit: {
                                    featureGroup: drawnItems,
                                    edit: true,
                                    remove: true
                                }
                            });
                            xmap.addControl(drawControl);
                            return false;
                        }   
                    }
                });
                var coordinates = layers[0].getLatLngs()[0];
                var arr =[]
                $.each(coordinates, function(index, value) {
                    arr.push([value.lat,value.lng])
                });
                var area = L.GeometryUtil.geodesicArea(coordinates);
                $('#xluasm2').val(area)
                $('#xluas_ha').val(area/10000)
                console.log(coordinates)
                console.log(area)
            });
            if (xrole == 7 || xrole == 8 || xrole == 9 || xrole == 10) {
                fetch('<?php echo base_url();?>digit_pertimbangan_berusaha/getBoundary/')
                .then(response => {
            // Check if the response is successful (status code in the range 200-299)
                    if (!response.ok) {
                    throw new Error('Network response was not ok');
                    }
                    // Parse the response as JSON
                    return response.json();
                })
                .then(data => {
                    // Handle the data
                    dataGeom = JSON.parse(data.geom)
                    geojsonBounds = L.geoJSON(dataGeom, {
                            style: {
                            fillColor: 'transparent',  
                            color: 'red',      
                            weight: 2,         
                            opacity: 1,        
                            fillOpacity: 0.5   
                        }
                    });
                    
                    geojsonBounds.addTo(xmap);
                    xmap.setMaxBounds(geojsonBounds.getBounds());
                    xmap.on('drag', function() {
                        xmap.panInsideBounds(geojsonBounds.getBounds(), { animate: false });
                    });
                    xmap.setMinZoom(xmap.getBoundsZoom(geojsonBounds.getBounds()));
                    
                    console.log(geojsonBounds)
                    
                })
                .catch(error => {
                    // Handle errors
                    console.error('Error:', error);
                });
            }
            
        }
       
    
    })  

    function isPolygonWithinOuter(polygon) {
        var drawnPolygonGeoJSON = polygon.toGeoJSON();
        var isInside = turf.booleanWithin(drawnPolygonGeoJSON, dataGeom);
        return isInside;
    }
    function clearAllLayer() {
        xmap.eachLayer(function(layer){
            if (layer._path != null) {
                layer.remove()
            }
        });
    }

    function getPolygons() {
        var layers = L.PM.Utils.findLayers(xmap);
        var group = L.featureGroup();
        // layers.forEach((layer)=>{
            group.addLayer(layers[0]);
        // });
        shapes = group.toGeoJSON();
        if (isMulti == 1) {
            shapes = {
                "type": "MultiPolygon",
                "coordinates": [[
                    shapes.features[0].geometry.coordinates[0]  // Wrap the coordinates in an additional set of brackets
                ]]
            };
        }
        $('#xgeom').val(JSON.stringify(shapes))
        
    }
</script>