<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Page_not_found extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();


        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));


        $this->load->database();
        $this->load->helper('dtssp');

    }
    
    public function index() {
        

        header("Access-Control-Allow-Origin: *");
 
        
        $this->load->view('index');
    }

    


}
