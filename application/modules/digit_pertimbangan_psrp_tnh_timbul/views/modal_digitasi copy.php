
<div class="modal " id="modDigitasi" tabindex="-1">
  <div class="modal-dialog modal-fullscreen">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Digitasi</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form class="form-horizontal" id="frm-tambah">
            <input type="hidden" name="<?=$this->security->get_csrf_token_name();?>" value="<?=$this->security->get_csrf_hash();?>" style="display: none">
            <input type="hidden" id="idDelete" name="idDelete">
            <div class="row">
                <div class="col-sm-12 col-md-12 col-xs-12">
                    <div class="card" id="divMap" style="">
                    <!-- <button type="button" onclick="calculateArea()">get</button> -->
                        <div id="map2" style="height: 80vh;width: 100%"></div>
                    </div>
                </div>
             </div>
            <!-- <div id="divRow" class="row">
                <div class="form-group col-md-6">
                    <label class="col-md-12 control-label"  for="kd_prov">Provinsi</label>
                    <div class="col-md-12">
                    <select id="kd_prov" name="kd_prov" onchange="prov_change()" class="bootstrap-select form-control" data-live-search="true">
                    </select>
                    </div>
                </div>
                <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="kd_kabkot">Kab/Kota</label>
                    <div class="col-md-12">
                        <select id="kd_kabkot" name="kd_kabkot" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="kd_bidang">Kode Bidang</label>  
                    <div class="col-md-12">
                    <input id="kd_bidang" name="kd_bidang" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="pemilik">Pemilik</label>  
                    <div class="col-md-12">
                    <input id="pemilik" name="pemilik" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="sk">SK</label>  
                    <div class="col-md-12">
                    <input id="sk" name="sk" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="tanggal_sk">Tanggal SK</label>  
                    <div class="col-md-12">
                    <input id="tanggal_sk" name="tanggal_sk" type="date" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="nib">NIB</label>  
                    <div class="col-md-12">
                    <input id="nib" name="nib" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="kriteria">kriteria</label>  
                    <div class="col-md-12">
                    <input id="kriteria" name="kriteria" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="ket">Keterangan</label>  
                    <div class="col-md-12">
                    <input id="ket" name="ket" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="kelayakan">Kelayakan</label>  
                    <div class="col-md-12">
                    <input id="kelayakan" name="kelayakan" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="aprogram">Aprogram</label>  
                    <div class="col-md-12">
                    <input id="aprogram" name="aprogram" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="komoditas">Komoditas</label>  
                    <div class="col-md-12">
                    <input id="komoditas" name="komoditas" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="kluster">Kluster</label>  
                    <div class="col-md-12">
                    <input id="kluster" name="kluster" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="penggarap">Penggarap</label>  
                    <div class="col-md-12">
                    <input id="penggarap" name="penggarap" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="prof_ktp">Prof  KTP</label>  
                    <div class="col-md-12">
                    <input id="prof_ktp" name="prof_ktp" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="prof_riil">Prof Riil</label>  
                    <div class="col-md-12">
                    <input id="prof_riil" name="prof_riil" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="jml_kk">Jumlah KK</label>  
                    <div class="col-md-12">
                    <input id="jml_kk" name="jml_kk" type="text" placeholder="" class="numeric form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="pokgar">POKGAR</label>  
                    <div class="col-md-12">
                    <input id="pokgar" name="pokgar" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="pemohon">Pemohon</label>  
                    <div class="col-md-12">
                    <input id="pemohon" name="pemohon" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="bdn_hukum">Badan Hukum</label>  
                    <div class="col-md-12">
                    <input id="bdn_hukum" name="bdn_hukum" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="rncana_keg">Rencana Kegiatan</label>  
                    <div class="col-md-12">
                    <input id="rncana_keg" name="rncana_keg" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="nomor_ptp">Nomor PTP</label>  
                    <div class="col-md-12">
                    <input id="nomor_ptp" name="nomor_ptp" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="luas_ha">Luas HA</label>  
                    <div class="col-md-12">
                    <input id="luas_ha" name="luas_ha" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="luas_m2">luas m2</label>  
                    <div class="col-md-12">
                    <input id="luas_m2" name="luas_m2" type="text" placeholder="" class="numeric form-control input-md ">
                    </div>
                </div>
                    
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="jenis_ptp">Jenis PTP</label>  
                    <div class="col-md-12">
                    <input id="jenis_ptp" name="jenis_ptp" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="tgl_ptp">Tanggal PTP</label>  
                    <div class="col-md-12">
                    <input id="tgl_ptp" name="tgl_ptp" type="date" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="hasil_ptp">Hasil PTP</label>  
                    <div class="col-md-12">
                    <input id="hasil_ptp" name="hasil_ptp" type="text" placeholder="" class="form-control input-md ">
                    </div>
                </div>
                    <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="tahun_data">tahun_data</label>  
                    <div class="col-md-12">
                        <select id="tahun_data"   name="tahun_data" class="bootstrap-select form-control" data-live-search="true">
                        <?php 
                            $now = date('Y');
                            for ($i=$now; $i >= 1990 ; $i--) { 
                                echo '<option value="'.$i.'">'.$i.'</option>';
                            }
                        ?>
                        </select>
                    </div>
                </div>
                <div class="form-group col-md-6">
                    <label class="col-md-12 control-label" for="luas"></label>  
                    <div class="col-md-12">
                    <input id="geom" name="geom" type="hidden" placeholder="" class="form-control input-md float-number">
                    
                    </div>
                </div>
            </div> -->
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <!-- <button type="submit" class="btn btn-primary">Save changes</button> -->
        </form>
       </div>
    </div>
  </div>
</div>


<script>
      
    $(document).ready(function () {
        $('.leaflet-popup-scrolled').slimScroll({
            height: '250px'
        });
        $('#luas_m2').on('input', function() {
            var inputValue = $(this).val();
            var numericValue = inputValue.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            var formattedValue = numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.'); // Format with commas
            $(this).val(formattedValue); // Set the formatted value to the input
        });
        $('#frm-tambah').submit(function (e) {
            e.preventDefault();
            // getPolygon()
            var selectedOption = $("#kd_prov option:selected");
            $('#wadmpr').val(selectedOption.text());
                selectedOption = $("#kd_kabkot option:selected");
            $('#wadmkk').val(selectedOption.text());
            // return false
            var file = new FormData(this);
            $.ajax({
                    // url: '<?php echo base_url(); ?>digit_pertimbangan_psrp_tnh_timbul/up',
                    url: '<?php echo base_url(); ?>digit_pertimbangan_psrp_tnh_timbul/insertShp',
                    type: "post",
                    data: file,
                    processData: false,
                    contentType: false,
                    cache: false,
                    async: false,
                    success: function (data) {
                        // $("select").val('--pilih--');
                        // $("select").selectpicker("refresh");
                        data=JSON.parse(data)
                        console.log(data.sts)
                        if(data.sts=='gagal'){
                            swal.close()
                            Swal.fire(
                                'Gagal!',
                                data.msg,
                                'error'
                            )
                        }else{
    
                            $("#filess").val('')
                            var table = $('#dt-server-processing').DataTable();
                            table.ajax.reload();
                            swal.close()
                            Swal.fire(
                                'Sukses!',
                                'Data Tersimpan!',
                                'success'
                            )
                            map.closePopup()
                            // clearAllLayer()
                            // drawnItems
                            var drawnLayers = map.pm.getGeomanDrawLayers();
                            var drawnLayer = drawnLayers[0];
                            map.removeLayer(drawnLayer);
                            var polygonLayer = drawnLayer
                            polygonLayer.addTo(map);
                            if ($('#tnhTimbul').prop('checked')) {
                                ptpTnhTimbul()
                            }
                            drawnItems.clearLayers();
                        }
                    },error: function (jqXHR, exception) {
                        // console.log(jqXHR);
                        swal.close()
                        Swal.fire(
                            'Gagal!',
                            'Data Gagal Tersimpan!',
                            'error'
                        )
                    }
                });
        });
    });

    function numericPopup(v) {
        var id = v.attr('id')
        var inputValue = $('#'+id).val();
            var numericValue = inputValue.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            $('#'+id).val(formattedValue); // Set the formatted value to the input
    }

    var map
    var drawnPolygons
    var drawnItems 
    $('#modDigitasi').on('shown.bs.modal', function (e) {
        
        $('#geom').val('')
         var mapContainer = document.getElementById('map2');

        if (mapContainer && mapContainer.classList.contains('leaflet-container')) {
            drawnItems.clearLayers();
        }else{
            map = L.map('map2').setView([-2.7521401146517785, 116.07226320582281], 5);
            var gl = L.mapboxGL({
                style: 'https://api.maptiler.com/maps/topo/style.json?key=KSsNNpzquIVkk0KWNz4s'
            }).addTo(map)
           
             L.control.layers({
                "MapBox": gl.addTo(map)
            }, {
                
            }, {
                position: 'topright',
                collapsed: false
            }).addTo(map);
            var lay = '<hr><div class="form-check">'+
                        '<input class="form-check-input" onchange="ptpTnhTimbul()" type="checkbox" value="" id="tnhTimbul">'+
                        '<label class="form-check-label"  for="tnhTimbul">'+
                            'Tanah Timbul'+
                        '</label>'+
                       '</div>'

            $('.leaflet-control-layers').append(lay) 

           
            map.pm.addControls({  
                position: 'topleft',  
                drawCircleMarker: false,
                rotateMode: false,
                drawCircle:false,
                drawText:false,
                cutPolygon:false,
                drawPolygon:true,
                drawMarker:false,
                drawPolyline:false,
                drawRectangle:true,
                dragMode:false,

            }); 

        
            drawnPolygons = [];
            drawnItems = new L.FeatureGroup().addTo(map);
            L.Control.Button = L.Control.extend({
                options: {
                    position: 'topleft'
                },
                onAdd: function (map) {
                    
                    var container = L.DomUtil.create('div', 'leaflet-bar leaflet-delete-div');
                    var button = L.DomUtil.create('a', 'leaflet-delete-button', container);
                    button.innerHTML = '<i class="fa fa-trash" aria-hidden="true"></i>'
                    var button2 = L.DomUtil.create('a', 'leaflet-delete-button', container);
                    button2.innerHTML = '<i class="fa fa-remove" aria-hidden="true"></i>'
                    L.DomEvent.disableClickPropagation(button);
                    L.DomEvent.disableClickPropagation(button2);
                    L.DomEvent.on(button, 'click', function(){
                        var id =$('#idDelete').val()
                        console.log(id)
                        if(id.length > 0){
                            dtDeleteRowDigit(id)
                            
                        }
                    });
                    L.DomEvent.on(button2, 'click', function(){
                        $('.leaflet-delete-div').css('display','none')
                        drawnItems.clearLayers();
                    });
                    container.title = "Hapus Geometry";

                    return container;
                },
                onRemove: function(map) {},
            });
            var control = new L.Control.Button()
            control.addTo(map);
            $('.leaflet-delete-div').css('display','none')

      

            map.on('pm:create', function (e) {
                var layer = e.layer;
                var latlngs = layer.getLatLngs();
                // drawnItems.addLayer(layer);
                // Generate a unique ID for the new polygon
                // var newPolygonId = Date.now();

                // Save the new polygon along with its ID to the array
                // drawnPolygons.push({ id: newPolygonId, latlngs: latlngs });

                // Get the GeoJSON of all drawn polygons (including the edited ones)
                var geojson = getMultiPolygonGeoJSON(drawnPolygons);

                layer.bindPopup(<?=$popup?>).openPopup();
                initCombobox('kd_prov', 19);
                $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                     refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
                });
                var shapes = layer.toGeoJSON();
                $('#geom').val(JSON.stringify(shapes))
                $('.modal-bodyp').slimScroll({
                    height: '250px'
                });
                map.on('pm:edit', (e) => {
                    alert('okay')
                });
                
            });

            

            
            // map.on('pm:update', (e) => {
            //     alert('okay update')
            //     });

           
        }

    
    })  

    

    function addPopup(layer) {
        
        layer.bindPopup(content).openPopup();
    }
  

    function getPolygons(params) {
        var layers = L.PM.Utils.findLayers(map);
        var polygonsArray = [];
        var polygons = layers
            // Loop through the drawn polygons and convert each one to GeoJSON Polygon
        for (var i = 0; i < polygons.length; i++) {
            var latlngs = layers[i].getLatLngs()[0];
            // var lu=L.GeometryUtil.geodesicArea(layers[i].getLatLngs());
            var coordinates = [];
            // console.log(layers[i].getLatLngs()[0])
            for (var j = 0; j < latlngs.length; j++) {
                coordinates.push(latlngs[j].lng+' '+latlngs[j].lat);
            }

            // Push the first point again to close the polygon
            coordinates.push(coordinates[0]);

            polygonsArray.push([coordinates]);
        }
        // Create a GeoJSON MultiPolygon from the individual polygons
        
        multiPolygon = 'MULTIPOLYGON '+JSON.stringify(polygonsArray).replace(/\[/g, '(').replace(/\]/g, ')').replace(/\"/g, '')
        $('#geom').val(multiPolygon)
    }

    function getMultiPolygonGeoJSON(polygons) {
        return false
    // Create an array to store the individual polygons
    var polygonsArray = [];

    // Loop through the drawn polygons and convert each one to GeoJSON Polygon
    for (var i = 0; i < polygons.length; i++) {
        var latlngs = polygons[i].latlngs[0];
        var coordinates = [];

        // Convert the latlngs to GeoJSON coordinates
        for (var j = 0; j < latlngs.length; j++) {
            coordinates.push(latlngs[j].lng+' '+latlngs[j].lat);
        }

        // Push the first point again to close the polygon
        coordinates.push(coordinates[0]);

        polygonsArray.push([coordinates]);
    }

    // Create a GeoJSON MultiPolygon from the individual polygons
    var geojson = {
        type: 'MultiPolygon',
        coordinates: polygonsArray,
    };

    return polygonsArray;
    }

    function getPolygon() {
        var layers = L.PM.Utils.findLayers(map);
        var group = L.featureGroup();
        layers.forEach((layer)=>{
            group.addLayer(layer);
            var latt = layer.getLatLngs()[0]
            // var areas = L.GeometryUtil.geodesicArea(latt)
        });
        shapes = group.toGeoJSON();
        $('#geom').val(JSON.stringify(shapes))
        // console.log(shapes)
    }

    function clearAllLayer() {
        map.eachLayer(function(layer){
            if (layer._path != null) {
                layer.remove()
            }
        });
    }

    function tes(params) {
        // alert('okay')
    }
    var ptp_tnh_timbul
    var url = 'http://************:4980/geoserver/pgt/wms';

    function ptpTnhTimbul() {
        // alert('okay')
        if ($('#tnhTimbul').prop('checked')) {
            if (map.hasLayer(ptp_tnh_timbul)) {
                map.removeLayer(ptp_tnh_timbul);
            };
            map.closePopup()
            ptp_tnh_timbul = L.tileLayer.betterWms(url, {
                layers: 'pgt:ptp_tnh_timbul',
                styles: 'pgt:ptp_tnh_timbul',
                transparent: true,
                format: 'image/png8',
                title: 'Pertimbangan Teknis Pertanahan Tanah Timbul',
                onEachFeature: function(feature, layer) {
                    layer.on({
                        click:  closePopup()// Attach the custom click function to each feature
                    });
                }
            });
            ptp_tnh_timbul.addTo(map)
        }else{
            if (map.hasLayer(ptp_tnh_timbul)) {
                map.removeLayer(ptp_tnh_timbul);
            };
        }
    }

    function closePopup() {
        map.closePopup()
    }

    // =================================================
   
</script>



<?php echo $js_wms?>