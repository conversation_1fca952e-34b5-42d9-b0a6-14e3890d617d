
<script type="text/javascript">

    $(document).ready(function () {


        var table = $('#table_div').DataTable({
            "draw": 0,
            "columnDefs": [
                {
                    "orderable": true, 
                    "targets": [___FIELD_NUMBER___]
                }
            ],
            "order": [[0, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", 
                url: "///___CONTROLLER_FOLDER___/ssp"},
            },
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "Memuat...",
                "processing": "Memroses...",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data",
//                "paginate": {
//                    "first": "<i class='fast backward ui icon'></i>",
//                    "last": "<i class='fast forward ui icon'></i>",
//                    "next": "<i class='step forward ui icon'></i>",
//                    "previous": "<i class='step backward ui icon'></i>"
//                },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });


//        table.on('xhr', function () {
//            xhrdata = table.ajax.json();
//            console.log(xhrdata);
//        });


        $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");

    });



</script>
