!function(D,j,R,z){"use strict";j=void 0!==j&&j.Math==Math?j:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),D.fn.sidebar=function(C){var k,e=D(this),w=D(j),T=D(R),x=D("html"),S=D("head"),A=e.selector||"",F=(new Date).getTime(),O=[],P=C,E="string"==typeof P,H=[].slice.call(arguments,1),M=j.requestAnimationFrame||j.mozRequestAnimationFrame||j.webkitRequestAnimationFrame||j.msRequestAnimationFrame||function(e){setTimeout(e,0)};return e.each(function(){var s,a,e,i,l,c,d=D.isPlainObject(C)?D.extend(!0,{},D.fn.sidebar.settings,C):D.extend({},D.fn.sidebar.settings),n=d.selector,r=d.className,t=d.namespace,o=d.regExp,u=d.error,f="."+t,b="module-"+t,h=D(this),m=D(d.context),g=h.children(n.sidebar),v=(m.children(n.fixed),m.children(n.pusher)),p=this,y=h.data(b);c={initialize:function(){c.debug("Initializing sidebar",C),c.create.id(),l=c.get.transitionEvent(),d.delaySetup?M(c.setup.layout):c.setup.layout(),M(function(){c.setup.cache()}),c.instantiate()},instantiate:function(){c.verbose("Storing instance of module",c),y=c,h.data(b,c)},create:{id:function(){e=(Math.random().toString(16)+"000000000").substr(2,8),a="."+e,c.verbose("Creating unique id for element",e)}},destroy:function(){c.verbose("Destroying previous module for",h),h.off(f).removeData(b),c.is.ios()&&c.remove.ios(),m.off(a),w.off(a),T.off(a)},event:{clickaway:function(e){var i=0<v.find(e.target).length||v.is(e.target),n=m.is(e.target);i&&(c.verbose("User clicked on dimmed page"),c.hide()),n&&(c.verbose("User clicked on dimmable context (scaled out page)"),c.hide())},touch:function(e){},containScroll:function(e){p.scrollTop<=0&&(p.scrollTop=1),p.scrollTop+p.offsetHeight>=p.scrollHeight&&(p.scrollTop=p.scrollHeight-p.offsetHeight-1)},scroll:function(e){0===D(e.target).closest(n.sidebar).length&&e.preventDefault()}},bind:{clickaway:function(){c.verbose("Adding clickaway events to context",m),d.closable&&m.on("click"+a,c.event.clickaway).on("touchend"+a,c.event.clickaway)},scrollLock:function(){d.scrollLock&&(c.debug("Disabling page scroll"),w.on("DOMMouseScroll"+a,c.event.scroll)),c.verbose("Adding events to contain sidebar scroll"),T.on("touchmove"+a,c.event.touch),h.on("scroll"+f,c.event.containScroll)}},unbind:{clickaway:function(){c.verbose("Removing clickaway events from context",m),m.off(a)},scrollLock:function(){c.verbose("Removing scroll lock from page"),T.off(a),w.off(a),h.off("scroll"+f)}},add:{inlineCSS:function(){var e,i=c.cache.width||h.outerWidth(),n=c.cache.height||h.outerHeight(),t=c.is.rtl(),o=c.get.direction(),r={left:i,right:-i,top:n,bottom:-n};t&&(c.verbose("RTL detected, flipping widths"),r.left=-i,r.right=i),e="<style>","left"===o||"right"===o?(c.debug("Adding CSS rules for animation distance",i),e+=" .ui.visible."+o+".sidebar ~ .fixed, .ui.visible."+o+".sidebar ~ .pusher {   -webkit-transform: translate3d("+r[o]+"px, 0, 0);           transform: translate3d("+r[o]+"px, 0, 0); }"):"top"!==o&&"bottom"!=o||(e+=" .ui.visible."+o+".sidebar ~ .fixed, .ui.visible."+o+".sidebar ~ .pusher {   -webkit-transform: translate3d(0, "+r[o]+"px, 0);           transform: translate3d(0, "+r[o]+"px, 0); }"),c.is.ie()&&("left"===o||"right"===o?(c.debug("Adding CSS rules for animation distance",i),e+=" body.pushable > .ui.visible."+o+".sidebar ~ .pusher:after {   -webkit-transform: translate3d("+r[o]+"px, 0, 0);           transform: translate3d("+r[o]+"px, 0, 0); }"):"top"!==o&&"bottom"!=o||(e+=" body.pushable > .ui.visible."+o+".sidebar ~ .pusher:after {   -webkit-transform: translate3d(0, "+r[o]+"px, 0);           transform: translate3d(0, "+r[o]+"px, 0); }"),e+=" body.pushable > .ui.visible.left.sidebar ~ .ui.visible.right.sidebar ~ .pusher:after, body.pushable > .ui.visible.right.sidebar ~ .ui.visible.left.sidebar ~ .pusher:after {   -webkit-transform: translate3d(0px, 0, 0);           transform: translate3d(0px, 0, 0); }"),s=D(e+="</style>").appendTo(S),c.debug("Adding sizing css to head",s)}},refresh:function(){c.verbose("Refreshing selector cache"),m=D(d.context),g=m.children(n.sidebar),v=m.children(n.pusher),m.children(n.fixed),c.clear.cache()},refreshSidebars:function(){c.verbose("Refreshing other sidebars"),g=m.children(n.sidebar)},repaint:function(){c.verbose("Forcing repaint event"),p.style.display="none";p.offsetHeight;p.scrollTop=p.scrollTop,p.style.display=""},setup:{cache:function(){c.cache={width:h.outerWidth(),height:h.outerHeight(),rtl:"rtl"==h.css("direction")}},layout:function(){0===m.children(n.pusher).length&&(c.debug("Adding wrapper element for sidebar"),c.error(u.pusher),v=D('<div class="pusher" />'),m.children().not(n.omitted).not(g).wrapAll(v),c.refresh()),0!==h.nextAll(n.pusher).length&&h.nextAll(n.pusher)[0]===v[0]||(c.debug("Moved sidebar to correct parent element"),c.error(u.movedSidebar,p),h.detach().prependTo(m),c.refresh()),c.clear.cache(),c.set.pushable(),c.set.direction()}},attachEvents:function(e,i){var n=D(e);i=D.isFunction(c[i])?c[i]:c.toggle,0<n.length?(c.debug("Attaching sidebar events to element",e,i),n.on("click"+f,i)):c.error(u.notFound,e)},show:function(e){if(e=D.isFunction(e)?e:function(){},c.is.hidden()){if(c.refreshSidebars(),d.overlay&&(c.error(u.overlay),d.transition="overlay"),c.refresh(),c.othersActive())if(c.debug("Other sidebars currently visible"),d.exclusive){if("overlay"!=d.transition)return void c.hideOthers(c.show);c.hideOthers()}else d.transition="overlay";c.pushPage(function(){e.call(p),d.onShow.call(p)}),d.onChange.call(p),d.onVisible.call(p)}else c.debug("Sidebar is already visible")},hide:function(e){e=D.isFunction(e)?e:function(){},(c.is.visible()||c.is.animating())&&(c.debug("Hiding sidebar",e),c.refreshSidebars(),c.pullPage(function(){e.call(p),d.onHidden.call(p)}),d.onChange.call(p),d.onHide.call(p))},othersAnimating:function(){return 0<g.not(h).filter("."+r.animating).length},othersVisible:function(){return 0<g.not(h).filter("."+r.visible).length},othersActive:function(){return c.othersVisible()||c.othersAnimating()},hideOthers:function(e){var i=g.not(h).filter("."+r.visible),n=i.length,t=0;e=e||function(){},i.sidebar("hide",function(){++t==n&&e()})},toggle:function(){c.verbose("Determining toggled direction"),c.is.hidden()?c.show():c.hide()},pushPage:function(i){var e,n,t,o=c.get.transition(),r="overlay"===o||c.othersActive()?h:v;i=D.isFunction(i)?i:function(){},"scale down"==d.transition&&c.scrollToTop(),c.set.transition(o),c.repaint(),e=function(){c.bind.clickaway(),c.add.inlineCSS(),c.set.animating(),c.set.visible()},n=function(){c.set.dimmed()},t=function(e){e.target==r[0]&&(r.off(l+a,t),c.remove.animating(),c.bind.scrollLock(),i.call(p))},r.off(l+a),r.on(l+a,t),M(e),d.dimPage&&!c.othersVisible()&&M(n)},pullPage:function(i){var e,n,t=c.get.transition(),o="overlay"==t||c.othersActive()?h:v;i=D.isFunction(i)?i:function(){},c.verbose("Removing context push state",c.get.direction()),c.unbind.clickaway(),c.unbind.scrollLock(),e=function(){c.set.transition(t),c.set.animating(),c.remove.visible(),d.dimPage&&!c.othersVisible()&&v.removeClass(r.dimmed)},n=function(e){e.target==o[0]&&(o.off(l+a,n),c.remove.animating(),c.remove.transition(),c.remove.inlineCSS(),("scale down"==t||d.returnScroll&&c.is.mobile())&&c.scrollBack(),i.call(p))},o.off(l+a),o.on(l+a,n),M(e)},scrollToTop:function(){c.verbose("Scrolling to top of page to avoid animation issues"),i=D(j).scrollTop(),h.scrollTop(0),j.scrollTo(0,0)},scrollBack:function(){c.verbose("Scrolling back to original page position"),j.scrollTo(0,i)},clear:{cache:function(){c.verbose("Clearing cached dimensions"),c.cache={}}},set:{ios:function(){x.addClass(r.ios)},pushed:function(){m.addClass(r.pushed)},pushable:function(){m.addClass(r.pushable)},dimmed:function(){v.addClass(r.dimmed)},active:function(){h.addClass(r.active)},animating:function(){h.addClass(r.animating)},transition:function(e){e=e||c.get.transition(),h.addClass(e)},direction:function(e){e=e||c.get.direction(),h.addClass(r[e])},visible:function(){h.addClass(r.visible)},overlay:function(){h.addClass(r.overlay)}},remove:{inlineCSS:function(){c.debug("Removing inline css styles",s),s&&0<s.length&&s.remove()},ios:function(){x.removeClass(r.ios)},pushed:function(){m.removeClass(r.pushed)},pushable:function(){m.removeClass(r.pushable)},active:function(){h.removeClass(r.active)},animating:function(){h.removeClass(r.animating)},transition:function(e){e=e||c.get.transition(),h.removeClass(e)},direction:function(e){e=e||c.get.direction(),h.removeClass(r[e])},visible:function(){h.removeClass(r.visible)},overlay:function(){h.removeClass(r.overlay)}},get:{direction:function(){return h.hasClass(r.top)?r.top:h.hasClass(r.right)?r.right:h.hasClass(r.bottom)?r.bottom:r.left},transition:function(){var e,i=c.get.direction();return e=c.is.mobile()?"auto"==d.mobileTransition?d.defaultTransition.mobile[i]:d.mobileTransition:"auto"==d.transition?d.defaultTransition.computer[i]:d.transition,c.verbose("Determined transition",e),e},transitionEvent:function(){var e,i=R.createElement("element"),n={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in n)if(i.style[e]!==z)return n[e]}},is:{ie:function(){return!j.ActiveXObject&&"ActiveXObject"in j||"ActiveXObject"in j},ios:function(){var e=navigator.userAgent,i=e.match(o.ios),n=e.match(o.mobileChrome);return!(!i||n)&&(c.verbose("Browser was found to be iOS",e),!0)},mobile:function(){var e=navigator.userAgent;return e.match(o.mobile)?(c.verbose("Browser was found to be mobile",e),!0):(c.verbose("Browser is not mobile, using regular transition",e),!1)},hidden:function(){return!c.is.visible()},visible:function(){return h.hasClass(r.visible)},open:function(){return c.is.visible()},closed:function(){return c.is.hidden()},vertical:function(){return h.hasClass(r.top)},animating:function(){return m.hasClass(r.animating)},rtl:function(){return c.cache.rtl===z&&(c.cache.rtl="rtl"==h.css("direction")),c.cache.rtl}},setting:function(e,i){if(c.debug("Changing setting",e,i),D.isPlainObject(e))D.extend(!0,d,e);else{if(i===z)return d[e];D.isPlainObject(d[e])?D.extend(!0,d[e],i):d[e]=i}},internal:function(e,i){if(D.isPlainObject(e))D.extend(!0,c,e);else{if(i===z)return c[e];c[e]=i}},debug:function(){!d.silent&&d.debug&&(d.performance?c.performance.log(arguments):(c.debug=Function.prototype.bind.call(console.info,console,d.name+":"),c.debug.apply(console,arguments)))},verbose:function(){!d.silent&&d.verbose&&d.debug&&(d.performance?c.performance.log(arguments):(c.verbose=Function.prototype.bind.call(console.info,console,d.name+":"),c.verbose.apply(console,arguments)))},error:function(){d.silent||(c.error=Function.prototype.bind.call(console.error,console,d.name+":"),c.error.apply(console,arguments))},performance:{log:function(e){var i,n;d.performance&&(n=(i=(new Date).getTime())-(F||i),F=i,O.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:p,"Execution Time":n})),clearTimeout(c.performance.timer),c.performance.timer=setTimeout(c.performance.display,500)},display:function(){var e=d.name+":",n=0;F=!1,clearTimeout(c.performance.timer),D.each(O,function(e,i){n+=i["Execution Time"]}),e+=" "+n+"ms",A&&(e+=" '"+A+"'"),(console.group!==z||console.table!==z)&&0<O.length&&(console.groupCollapsed(e),console.table?console.table(O):D.each(O,function(e,i){console.log(i.Name+": "+i["Execution Time"]+"ms")}),console.groupEnd()),O=[]}},invoke:function(t,e,i){var o,r,n,s=y;return e=e||H,i=p||i,"string"==typeof t&&s!==z&&(t=t.split(/[\. ]/),o=t.length-1,D.each(t,function(e,i){var n=e!=o?i+t[e+1].charAt(0).toUpperCase()+t[e+1].slice(1):t;if(D.isPlainObject(s[n])&&e!=o)s=s[n];else{if(s[n]!==z)return r=s[n],!1;if(!D.isPlainObject(s[i])||e==o)return s[i]!==z?r=s[i]:c.error(u.method,t),!1;s=s[i]}})),D.isFunction(r)?n=r.apply(i,e):r!==z&&(n=r),D.isArray(k)?k.push(n):k!==z?k=[k,n]:n!==z&&(k=n),r}},E?(y===z&&c.initialize(),c.invoke(P)):(y!==z&&c.invoke("destroy"),c.initialize())}),k!==z?k:this},D.fn.sidebar.settings={name:"Sidebar",namespace:"sidebar",silent:!1,debug:!1,verbose:!1,performance:!0,transition:"auto",mobileTransition:"auto",defaultTransition:{computer:{left:"uncover",right:"uncover",top:"overlay",bottom:"overlay"},mobile:{left:"uncover",right:"uncover",top:"overlay",bottom:"overlay"}},context:"body",exclusive:!1,closable:!0,dimPage:!0,scrollLock:!1,returnScroll:!1,delaySetup:!1,duration:500,onChange:function(){},onShow:function(){},onHide:function(){},onHidden:function(){},onVisible:function(){},className:{active:"active",animating:"animating",dimmed:"dimmed",ios:"ios",pushable:"pushable",pushed:"pushed",right:"right",top:"top",left:"left",bottom:"bottom",visible:"visible"},selector:{fixed:".fixed",omitted:"script, link, style, .ui.modal, .ui.dimmer, .ui.nag, .ui.fixed",pusher:".pusher",sidebar:".ui.sidebar"},regExp:{ios:/(iPad|iPhone|iPod)/g,mobileChrome:/(CriOS)/g,mobile:/Mobile|iP(hone|od|ad)|Android|BlackBerry|IEMobile|Kindle|NetFront|Silk-Accelerated|(hpw|web)OS|Fennec|Minimo|Opera M(obi|ini)|Blazer|Dolfin|Dolphin|Skyfire|Zune/g},error:{method:"The method you called is not defined.",pusher:"Had to add pusher element. For optimal performance make sure body content is inside a pusher element",movedSidebar:"Had to move sidebar. For optimal performance make sure sidebar and pusher are direct children of your body tag",overlay:"The overlay setting is no longer supported, use animation: overlay",notFound:"There were no elements that matched the specified selector"}}}(jQuery,window,document);