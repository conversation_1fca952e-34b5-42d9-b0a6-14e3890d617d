!function(H,e,T,Z){"use strict";e=void 0!==e&&e.Math==Math?e:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),H.fn.shape=function(v){var b,x=H(this),y=(H("body"),(new Date).getTime()),S=[],w=v,C="string"==typeof w,W=[].slice.call(arguments,1),F=e.requestAnimationFrame||e.mozRequestAnimationFrame||e.webkitRequestAnimationFrame||e.msRequestAnimationFrame||function(e){setTimeout(e,0)};return x.each(function(){var n,a,o,t=x.selector||"",s=H.isPlainObject(v)?H.extend(!0,{},H.fn.shape.settings,v):H.extend({},H.fn.shape.settings),e=s.namespace,r=s.selector,i=s.error,l=s.className,d="."+e,u="module-"+e,c=H(this),g=c.find(r.sides),f=c.find(r.side),m=!1,h=this,p=c.data(u);o={initialize:function(){o.verbose("Initializing module for",h),o.set.defaultSide(),o.instantiate()},instantiate:function(){o.verbose("Storing instance of module",o),p=o,c.data(u,p)},destroy:function(){o.verbose("Destroying previous module for",h),c.removeData(u).off(d)},refresh:function(){o.verbose("Refreshing selector cache for",h),c=H(h),g=H(this).find(r.shape),f=H(this).find(r.side)},repaint:function(){o.verbose("Forcing repaint event");(g[0]||T.createElement("div")).offsetWidth},animate:function(e,t){o.verbose("Animating box with properties",e),t=t||function(e){o.verbose("Executing animation callback"),e!==Z&&e.stopPropagation(),o.reset(),o.set.active()},s.beforeChange.call(a[0]),o.get.transitionEvent()?(o.verbose("Starting CSS animation"),c.addClass(l.animating),g.css(e).one(o.get.transitionEvent(),t),o.set.duration(s.duration),F(function(){c.addClass(l.animating),n.addClass(l.hidden)})):t()},queue:function(e){o.debug("Queueing animation of",e),g.one(o.get.transitionEvent(),function(){o.debug("Executing queued animation"),setTimeout(function(){c.shape(e)},0)})},reset:function(){o.verbose("Animating states reset"),c.removeClass(l.animating).attr("style","").removeAttr("style"),g.attr("style","").removeAttr("style"),f.attr("style","").removeAttr("style").removeClass(l.hidden),a.removeClass(l.animating).attr("style","").removeAttr("style")},is:{complete:function(){return f.filter("."+l.active)[0]==a[0]},animating:function(){return c.hasClass(l.animating)}},set:{defaultSide:function(){n=c.find("."+s.className.active),a=0<n.next(r.side).length?n.next(r.side):c.find(r.side).first(),m=!1,o.verbose("Active side set to",n),o.verbose("Next side set to",a)},duration:function(e){e="number"==typeof(e=e||s.duration)?e+"ms":e,o.verbose("Setting animation duration",e),(s.duration||0===s.duration)&&g.add(f).css({"-webkit-transition-duration":e,"-moz-transition-duration":e,"-ms-transition-duration":e,"-o-transition-duration":e,"transition-duration":e})},currentStageSize:function(){var e=c.find("."+s.className.active),t=e.outerWidth(!0),i=e.outerHeight(!0);c.css({width:t,height:i})},stageSize:function(){var e=c.clone().addClass(l.loading),t=e.find("."+s.className.active),i=m?e.find(r.side).eq(m):0<t.next(r.side).length?t.next(r.side):e.find(r.side).first(),n="next"==s.width?i.outerWidth(!0):"initial"==s.width?c.width():s.width,a="next"==s.height?i.outerHeight(!0):"initial"==s.height?c.height():s.height;t.removeClass(l.active),i.addClass(l.active),e.insertAfter(c),e.remove(),"auto"!=s.width&&(c.css("width",n+s.jitter),o.verbose("Specifying width during animation",n)),"auto"!=s.height&&(c.css("height",a+s.jitter),o.verbose("Specifying height during animation",a))},nextSide:function(e){m=e,a=f.filter(e),m=f.index(a),0===a.length&&(o.set.defaultSide(),o.error(i.side)),o.verbose("Next side manually set to",a)},active:function(){o.verbose("Setting new side to active",a),f.removeClass(l.active),a.addClass(l.active),s.onChange.call(a[0]),o.set.defaultSide()}},flip:{up:function(){if(!o.is.complete()||o.is.animating()||s.allowRepeats)if(o.is.animating())o.queue("flip up");else{o.debug("Flipping up",a);var e=o.get.transform.up();o.set.stageSize(),o.stage.above(),o.animate(e)}else o.debug("Side already visible",a)},down:function(){if(!o.is.complete()||o.is.animating()||s.allowRepeats)if(o.is.animating())o.queue("flip down");else{o.debug("Flipping down",a);var e=o.get.transform.down();o.set.stageSize(),o.stage.below(),o.animate(e)}else o.debug("Side already visible",a)},left:function(){if(!o.is.complete()||o.is.animating()||s.allowRepeats)if(o.is.animating())o.queue("flip left");else{o.debug("Flipping left",a);var e=o.get.transform.left();o.set.stageSize(),o.stage.left(),o.animate(e)}else o.debug("Side already visible",a)},right:function(){if(!o.is.complete()||o.is.animating()||s.allowRepeats)if(o.is.animating())o.queue("flip right");else{o.debug("Flipping right",a);var e=o.get.transform.right();o.set.stageSize(),o.stage.right(),o.animate(e)}else o.debug("Side already visible",a)},over:function(){!o.is.complete()||o.is.animating()||s.allowRepeats?o.is.animating()?o.queue("flip over"):(o.debug("Flipping over",a),o.set.stageSize(),o.stage.behind(),o.animate(o.get.transform.over())):o.debug("Side already visible",a)},back:function(){!o.is.complete()||o.is.animating()||s.allowRepeats?o.is.animating()?o.queue("flip back"):(o.debug("Flipping back",a),o.set.stageSize(),o.stage.behind(),o.animate(o.get.transform.back())):o.debug("Side already visible",a)}},get:{transform:{up:function(){return{transform:"translateY("+-(n.outerHeight(!0)-a.outerHeight(!0))/2+"px) translateZ("+-n.outerHeight(!0)/2+"px) rotateX(-90deg)"}},down:function(){return{transform:"translateY("+-(n.outerHeight(!0)-a.outerHeight(!0))/2+"px) translateZ("+-n.outerHeight(!0)/2+"px) rotateX(90deg)"}},left:function(){return{transform:"translateX("+-(n.outerWidth(!0)-a.outerWidth(!0))/2+"px) translateZ("+-n.outerWidth(!0)/2+"px) rotateY(90deg)"}},right:function(){return{transform:"translateX("+-(n.outerWidth(!0)-a.outerWidth(!0))/2+"px) translateZ("+-n.outerWidth(!0)/2+"px) rotateY(-90deg)"}},over:function(){return{transform:"translateX("+-(n.outerWidth(!0)-a.outerWidth(!0))/2+"px) rotateY(180deg)"}},back:function(){return{transform:"translateX("+-(n.outerWidth(!0)-a.outerWidth(!0))/2+"px) rotateY(-180deg)"}}},transitionEvent:function(){var e,t=T.createElement("element"),i={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(e in i)if(t.style[e]!==Z)return i[e]},nextSide:function(){return 0<n.next(r.side).length?n.next(r.side):c.find(r.side).first()}},stage:{above:function(){var e={origin:(n.outerHeight(!0)-a.outerHeight(!0))/2,depth:{active:a.outerHeight(!0)/2,next:n.outerHeight(!0)/2}};o.verbose("Setting the initial animation position as above",a,e),g.css({transform:"translateZ(-"+e.depth.active+"px)"}),n.css({transform:"rotateY(0deg) translateZ("+e.depth.active+"px)"}),a.addClass(l.animating).css({top:e.origin+"px",transform:"rotateX(90deg) translateZ("+e.depth.next+"px)"})},below:function(){var e={origin:(n.outerHeight(!0)-a.outerHeight(!0))/2,depth:{active:a.outerHeight(!0)/2,next:n.outerHeight(!0)/2}};o.verbose("Setting the initial animation position as below",a,e),g.css({transform:"translateZ(-"+e.depth.active+"px)"}),n.css({transform:"rotateY(0deg) translateZ("+e.depth.active+"px)"}),a.addClass(l.animating).css({top:e.origin+"px",transform:"rotateX(-90deg) translateZ("+e.depth.next+"px)"})},left:function(){var e=n.outerWidth(!0),t=a.outerWidth(!0),i={origin:(e-t)/2,depth:{active:t/2,next:e/2}};o.verbose("Setting the initial animation position as left",a,i),g.css({transform:"translateZ(-"+i.depth.active+"px)"}),n.css({transform:"rotateY(0deg) translateZ("+i.depth.active+"px)"}),a.addClass(l.animating).css({left:i.origin+"px",transform:"rotateY(-90deg) translateZ("+i.depth.next+"px)"})},right:function(){var e=n.outerWidth(!0),t=a.outerWidth(!0),i={origin:(e-t)/2,depth:{active:t/2,next:e/2}};o.verbose("Setting the initial animation position as left",a,i),g.css({transform:"translateZ(-"+i.depth.active+"px)"}),n.css({transform:"rotateY(0deg) translateZ("+i.depth.active+"px)"}),a.addClass(l.animating).css({left:i.origin+"px",transform:"rotateY(90deg) translateZ("+i.depth.next+"px)"})},behind:function(){var e=n.outerWidth(!0),t=a.outerWidth(!0),i={origin:(e-t)/2,depth:{active:t/2,next:e/2}};o.verbose("Setting the initial animation position as behind",a,i),n.css({transform:"rotateY(0deg)"}),a.addClass(l.animating).css({left:i.origin+"px",transform:"rotateY(-180deg)"})}},setting:function(e,t){if(o.debug("Changing setting",e,t),H.isPlainObject(e))H.extend(!0,s,e);else{if(t===Z)return s[e];H.isPlainObject(s[e])?H.extend(!0,s[e],t):s[e]=t}},internal:function(e,t){if(H.isPlainObject(e))H.extend(!0,o,e);else{if(t===Z)return o[e];o[e]=t}},debug:function(){!s.silent&&s.debug&&(s.performance?o.performance.log(arguments):(o.debug=Function.prototype.bind.call(console.info,console,s.name+":"),o.debug.apply(console,arguments)))},verbose:function(){!s.silent&&s.verbose&&s.debug&&(s.performance?o.performance.log(arguments):(o.verbose=Function.prototype.bind.call(console.info,console,s.name+":"),o.verbose.apply(console,arguments)))},error:function(){s.silent||(o.error=Function.prototype.bind.call(console.error,console,s.name+":"),o.error.apply(console,arguments))},performance:{log:function(e){var t,i;s.performance&&(i=(t=(new Date).getTime())-(y||t),y=t,S.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:h,"Execution Time":i})),clearTimeout(o.performance.timer),o.performance.timer=setTimeout(o.performance.display,500)},display:function(){var e=s.name+":",i=0;y=!1,clearTimeout(o.performance.timer),H.each(S,function(e,t){i+=t["Execution Time"]}),e+=" "+i+"ms",t&&(e+=" '"+t+"'"),1<x.length&&(e+=" ("+x.length+")"),(console.group!==Z||console.table!==Z)&&0<S.length&&(console.groupCollapsed(e),console.table?console.table(S):H.each(S,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),S=[]}},invoke:function(n,e,t){var a,o,i,s=p;return e=e||W,t=h||t,"string"==typeof n&&s!==Z&&(n=n.split(/[\. ]/),a=n.length-1,H.each(n,function(e,t){var i=e!=a?t+n[e+1].charAt(0).toUpperCase()+n[e+1].slice(1):n;if(H.isPlainObject(s[i])&&e!=a)s=s[i];else{if(s[i]!==Z)return o=s[i],!1;if(!H.isPlainObject(s[t])||e==a)return s[t]!==Z&&(o=s[t]),!1;s=s[t]}})),H.isFunction(o)?i=o.apply(t,e):o!==Z&&(i=o),H.isArray(b)?b.push(i):b!==Z?b=[b,i]:i!==Z&&(b=i),o}},C?(p===Z&&o.initialize(),o.invoke(w)):(p!==Z&&p.invoke("destroy"),o.initialize())}),b!==Z?b:this},H.fn.shape.settings={name:"Shape",silent:!1,debug:!1,verbose:!1,jitter:0,performance:!0,namespace:"shape",width:"initial",height:"initial",beforeChange:function(){},onChange:function(){},allowRepeats:!1,duration:!1,error:{side:"You tried to switch to a side that does not exist.",method:"The method you called is not defined"},className:{animating:"animating",hidden:"hidden",loading:"loading",active:"active"},selector:{sides:".sides",side:".side"}}}(jQuery,window,document);