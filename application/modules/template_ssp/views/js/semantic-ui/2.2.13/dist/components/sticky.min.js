!function(T,w,B,P){"use strict";w=void 0!==w&&w.Math==Math?w:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),T.fn.sticky=function(b){var v,e=T(this),x=e.selector||"",C=(new Date).getTime(),S=[],y=b,k="string"==typeof y,z=[].slice.call(arguments,1);return e.each(function(){var n,i,e,t,m,u=T.isPlainObject(b)?T.extend(!0,{},T.fn.sticky.settings,b):T.extend({},T.fn.sticky.settings),o=u.className,s=u.namespace,r=u.error,c="."+s,l="module-"+s,a=T(this),f=T(w),d=T(u.scrollContext),h=(a.selector,a.data(l)),g=w.requestAnimationFrame||w.mozRequestAnimationFrame||w.webkitRequestAnimationFrame||w.msRequestAnimationFrame||function(e){setTimeout(e,0)},p=this;m={initialize:function(){m.determineContainer(),m.determineContext(),m.verbose("Initializing sticky",u,n),m.save.positions(),m.checkErrors(),m.bind.events(),u.observeChanges&&m.observeChanges(),m.instantiate()},instantiate:function(){m.verbose("Storing instance of module",m),h=m,a.data(l,m)},destroy:function(){m.verbose("Destroying previous instance"),m.reset(),e&&e.disconnect(),t&&t.disconnect(),f.off("load"+c,m.event.load).off("resize"+c,m.event.resize),d.off("scrollchange"+c,m.event.scrollchange),a.removeData(l)},observeChanges:function(){"MutationObserver"in w&&(e=new MutationObserver(m.event.documentChanged),t=new MutationObserver(m.event.changed),e.observe(B,{childList:!0,subtree:!0}),t.observe(p,{childList:!0,subtree:!0}),t.observe(i[0],{childList:!0,subtree:!0}),m.debug("Setting up mutation observer",t))},determineContainer:function(){n=u.container?T(u.container):a.offsetParent()},determineContext:function(){0!==(i=u.context?T(u.context):n).length||m.error(r.invalidContext,u.context,a)},checkErrors:function(){if(m.is.hidden()&&m.error(r.visible,a),m.cache.element.height>m.cache.context.height)return m.reset(),void m.error(r.elementSize,a)},bind:{events:function(){f.on("load"+c,m.event.load).on("resize"+c,m.event.resize),d.off("scroll"+c).on("scroll"+c,m.event.scroll).on("scrollchange"+c,m.event.scrollchange)}},event:{changed:function(e){clearTimeout(m.timer),m.timer=setTimeout(function(){m.verbose("DOM tree modified, updating sticky menu",e),m.refresh()},100)},documentChanged:function(e){[].forEach.call(e,function(e){e.removedNodes&&[].forEach.call(e.removedNodes,function(e){(e==p||0<T(e).find(p).length)&&(m.debug("Element removed from DOM, tearing down events"),m.destroy())})})},load:function(){m.verbose("Page contents finished loading"),g(m.refresh)},resize:function(){m.verbose("Window resized"),g(m.refresh)},scroll:function(){g(function(){d.triggerHandler("scrollchange"+c,d.scrollTop())})},scrollchange:function(e,t){m.stick(t),u.onScroll.call(p)}},refresh:function(e){m.reset(),u.context||m.determineContext(),e&&m.determineContainer(),m.save.positions(),m.stick(),u.onReposition.call(p)},supports:{sticky:function(){var e=T("<div/>");e[0];return e.addClass(o.supported),e.css("position").match("sticky")}},save:{lastScroll:function(e){m.lastScroll=e},elementScroll:function(e){m.elementScroll=e},positions:function(){var e={height:d.height()},t={margin:{top:parseInt(a.css("margin-top"),10),bottom:parseInt(a.css("margin-bottom"),10)},offset:a.offset(),width:a.outerWidth(),height:a.outerHeight()},o={offset:i.offset(),height:i.outerHeight()};n.outerHeight();m.is.standardScroll()||(m.debug("Non-standard scroll. Removing scroll offset from element offset"),e.top=d.scrollTop(),e.left=d.scrollLeft(),t.offset.top+=e.top,o.offset.top+=e.top,t.offset.left+=e.left,o.offset.left+=e.left),m.cache={fits:t.height+u.offset<=e.height,sameHeight:t.height==o.height,scrollContext:{height:e.height},element:{margin:t.margin,top:t.offset.top-t.margin.top,left:t.offset.left,width:t.width,height:t.height,bottom:t.offset.top+t.height},context:{top:o.offset.top,height:o.height,bottom:o.offset.top+o.height}},m.set.containerSize(),m.stick(),m.debug("Caching element positions",m.cache)}},get:{direction:function(e){var t="down";return e=e||d.scrollTop(),m.lastScroll!==P&&(m.lastScroll<e?t="down":m.lastScroll>e&&(t="up")),t},scrollChange:function(e){return e=e||d.scrollTop(),m.lastScroll?e-m.lastScroll:0},currentElementScroll:function(){return m.elementScroll?m.elementScroll:m.is.top()?Math.abs(parseInt(a.css("top"),10))||0:Math.abs(parseInt(a.css("bottom"),10))||0},elementScroll:function(e){e=e||d.scrollTop();var t=m.cache.element,o=m.cache.scrollContext,n=m.get.scrollChange(e),i=t.height-o.height+u.offset,s=m.get.currentElementScroll(),r=s+n;return s=m.cache.fits||r<0?0:i<r?i:r}},remove:{lastScroll:function(){delete m.lastScroll},elementScroll:function(e){delete m.elementScroll},minimumSize:function(){n.css("min-height","")},offset:function(){a.css("margin-top","")}},set:{offset:function(){m.verbose("Setting offset on element",u.offset),a.css("margin-top",u.offset)},containerSize:function(){var e=n.get(0).tagName;"HTML"===e||"body"==e?m.determineContainer():Math.abs(n.outerHeight()-m.cache.context.height)>u.jitter&&(m.debug("Context has padding, specifying exact height for container",m.cache.context.height),n.css({height:m.cache.context.height}))},minimumSize:function(){var e=m.cache.element;n.css("min-height",e.height)},scroll:function(e){m.debug("Setting scroll on element",e),m.elementScroll!=e&&(m.is.top()&&a.css("bottom","").css("top",-e),m.is.bottom()&&a.css("top","").css("bottom",e))},size:function(){0!==m.cache.element.height&&0!==m.cache.element.width&&(p.style.setProperty("width",m.cache.element.width+"px","important"),p.style.setProperty("height",m.cache.element.height+"px","important"))}},is:{standardScroll:function(){return d[0]==w},top:function(){return a.hasClass(o.top)},bottom:function(){return a.hasClass(o.bottom)},initialPosition:function(){return!m.is.fixed()&&!m.is.bound()},hidden:function(){return!a.is(":visible")},bound:function(){return a.hasClass(o.bound)},fixed:function(){return a.hasClass(o.fixed)}},stick:function(e){var t=e||d.scrollTop(),o=m.cache,n=o.fits,i=o.sameHeight,s=o.element,r=o.scrollContext,c=o.context,l=m.is.bottom()&&u.pushing?u.bottomOffset:u.offset,a=(e={top:t+l,bottom:t+l+r.height},m.get.direction(e.top),n?0:m.get.elementScroll(e.top)),f=!n;0!==s.height&&!i&&(m.is.initialPosition()?e.top>=c.bottom?(m.debug("Initial element position is bottom of container"),m.bindBottom()):e.top>s.top&&(s.height+e.top-a>=c.bottom?(m.debug("Initial element position is bottom of container"),m.bindBottom()):(m.debug("Initial element position is fixed"),m.fixTop())):m.is.fixed()?m.is.top()?e.top<=s.top?(m.debug("Fixed element reached top of container"),m.setInitialPosition()):s.height+e.top-a>=c.bottom?(m.debug("Fixed element reached bottom of container"),m.bindBottom()):f&&(m.set.scroll(a),m.save.lastScroll(e.top),m.save.elementScroll(a)):m.is.bottom()&&(e.bottom-s.height<=s.top?(m.debug("Bottom fixed rail has reached top of container"),m.setInitialPosition()):e.bottom>=c.bottom?(m.debug("Bottom fixed rail has reached bottom of container"),m.bindBottom()):f&&(m.set.scroll(a),m.save.lastScroll(e.top),m.save.elementScroll(a))):m.is.bottom()&&(e.top<=s.top?(m.debug("Jumped from bottom fixed to top fixed, most likely used home/end button"),m.setInitialPosition()):u.pushing?m.is.bound()&&e.bottom<=c.bottom&&(m.debug("Fixing bottom attached element to bottom of browser."),m.fixBottom()):m.is.bound()&&e.top<=c.bottom-s.height&&(m.debug("Fixing bottom attached element to top of browser."),m.fixTop())))},bindTop:function(){m.debug("Binding element to top of parent container"),m.remove.offset(),a.css({left:"",top:"",marginBottom:""}).removeClass(o.fixed).removeClass(o.bottom).addClass(o.bound).addClass(o.top),u.onTop.call(p),u.onUnstick.call(p)},bindBottom:function(){m.debug("Binding element to bottom of parent container"),m.remove.offset(),a.css({left:"",top:""}).removeClass(o.fixed).removeClass(o.top).addClass(o.bound).addClass(o.bottom),u.onBottom.call(p),u.onUnstick.call(p)},setInitialPosition:function(){m.debug("Returning to initial position"),m.unfix(),m.unbind()},fixTop:function(){m.debug("Fixing element to top of page"),u.setSize&&m.set.size(),m.set.minimumSize(),m.set.offset(),a.css({left:m.cache.element.left,bottom:"",marginBottom:""}).removeClass(o.bound).removeClass(o.bottom).addClass(o.fixed).addClass(o.top),u.onStick.call(p)},fixBottom:function(){m.debug("Sticking element to bottom of page"),u.setSize&&m.set.size(),m.set.minimumSize(),m.set.offset(),a.css({left:m.cache.element.left,bottom:"",marginBottom:""}).removeClass(o.bound).removeClass(o.top).addClass(o.fixed).addClass(o.bottom),u.onStick.call(p)},unbind:function(){m.is.bound()&&(m.debug("Removing container bound position on element"),m.remove.offset(),a.removeClass(o.bound).removeClass(o.top).removeClass(o.bottom))},unfix:function(){m.is.fixed()&&(m.debug("Removing fixed position on element"),m.remove.minimumSize(),m.remove.offset(),a.removeClass(o.fixed).removeClass(o.top).removeClass(o.bottom),u.onUnstick.call(p))},reset:function(){m.debug("Resetting elements position"),m.unbind(),m.unfix(),m.resetCSS(),m.remove.offset(),m.remove.lastScroll()},resetCSS:function(){a.css({width:"",height:""}),n.css({height:""})},setting:function(e,t){if(T.isPlainObject(e))T.extend(!0,u,e);else{if(t===P)return u[e];u[e]=t}},internal:function(e,t){if(T.isPlainObject(e))T.extend(!0,m,e);else{if(t===P)return m[e];m[e]=t}},debug:function(){!u.silent&&u.debug&&(u.performance?m.performance.log(arguments):(m.debug=Function.prototype.bind.call(console.info,console,u.name+":"),m.debug.apply(console,arguments)))},verbose:function(){!u.silent&&u.verbose&&u.debug&&(u.performance?m.performance.log(arguments):(m.verbose=Function.prototype.bind.call(console.info,console,u.name+":"),m.verbose.apply(console,arguments)))},error:function(){u.silent||(m.error=Function.prototype.bind.call(console.error,console,u.name+":"),m.error.apply(console,arguments))},performance:{log:function(e){var t,o;u.performance&&(o=(t=(new Date).getTime())-(C||t),C=t,S.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:p,"Execution Time":o})),clearTimeout(m.performance.timer),m.performance.timer=setTimeout(m.performance.display,0)},display:function(){var e=u.name+":",o=0;C=!1,clearTimeout(m.performance.timer),T.each(S,function(e,t){o+=t["Execution Time"]}),e+=" "+o+"ms",x&&(e+=" '"+x+"'"),(console.group!==P||console.table!==P)&&0<S.length&&(console.groupCollapsed(e),console.table?console.table(S):T.each(S,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),S=[]}},invoke:function(n,e,t){var i,s,o,r=h;return e=e||z,t=p||t,"string"==typeof n&&r!==P&&(n=n.split(/[\. ]/),i=n.length-1,T.each(n,function(e,t){var o=e!=i?t+n[e+1].charAt(0).toUpperCase()+n[e+1].slice(1):n;if(T.isPlainObject(r[o])&&e!=i)r=r[o];else{if(r[o]!==P)return s=r[o],!1;if(!T.isPlainObject(r[t])||e==i)return r[t]!==P&&(s=r[t]),!1;r=r[t]}})),T.isFunction(s)?o=s.apply(t,e):s!==P&&(o=s),T.isArray(v)?v.push(o):v!==P?v=[v,o]:o!==P&&(v=o),s}},k?(h===P&&m.initialize(),m.invoke(y)):(h!==P&&h.invoke("destroy"),m.initialize())}),v!==P?v:this},T.fn.sticky.settings={name:"Sticky",namespace:"sticky",silent:!1,debug:!1,verbose:!0,performance:!0,pushing:!1,context:!1,container:!1,scrollContext:w,offset:0,bottomOffset:0,jitter:5,setSize:!0,observeChanges:!1,onReposition:function(){},onScroll:function(){},onStick:function(){},onUnstick:function(){},onTop:function(){},onBottom:function(){},error:{container:"Sticky element must be inside a relative container",visible:"Element is hidden, you must call refresh after element becomes visible. Use silent setting to surpress this warning in production.",method:"The method you called is not defined.",invalidContext:"Context specified does not exist",elementSize:"Sticky element is larger than its container, cannot create sticky."},className:{bound:"bound",fixed:"fixed",supported:"native",top:"top",bottom:"bottom"}}}(jQuery,window,document);