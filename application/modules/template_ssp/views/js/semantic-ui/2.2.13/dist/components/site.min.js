!function(b,p,v,h){b.site=b.fn.site=function(e){var a,c,i=(new Date).getTime(),t=[],n=e,o="string"==typeof n,l=[].slice.call(arguments,1),u=b.isPlainObject(e)?b.extend(!0,{},b.site.settings,e):b.extend({},b.site.settings),s=u.namespace,m=u.error,r="module-"+s,d=b(v),g=this,f=d.data(r);return a={initialize:function(){a.instantiate()},instantiate:function(){a.verbose("Storing instance of site",a),f=a,d.data(r,a)},normalize:function(){a.fix.console(),a.fix.requestAnimationFrame()},fix:{console:function(){a.debug("Normalizing window.console"),console!==h&&console.log!==h||(a.verbose("Console not available, normalizing events"),a.disable.console()),void 0!==console.group&&void 0!==console.groupEnd&&void 0!==console.groupCollapsed||(a.verbose("Console group not available, normalizing events"),p.console.group=function(){},p.console.groupEnd=function(){},p.console.groupCollapsed=function(){}),void 0===console.markTimeline&&(a.verbose("Mark timeline not available, normalizing events"),p.console.markTimeline=function(){})},consoleClear:function(){a.debug("Disabling programmatic console clearing"),p.console.clear=function(){}},requestAnimationFrame:function(){a.debug("Normalizing requestAnimationFrame"),p.requestAnimationFrame===h&&(a.debug("RequestAnimationFrame not available, normalizing event"),p.requestAnimationFrame=p.requestAnimationFrame||p.mozRequestAnimationFrame||p.webkitRequestAnimationFrame||p.msRequestAnimationFrame||function(e){setTimeout(e,0)})}},moduleExists:function(e){return b.fn[e]!==h&&b.fn[e].settings!==h},enabled:{modules:function(e){var o=[];return e=e||u.modules,b.each(e,function(e,n){a.moduleExists(n)&&o.push(n)}),o}},disabled:{modules:function(e){var o=[];return e=e||u.modules,b.each(e,function(e,n){a.moduleExists(n)||o.push(n)}),o}},change:{setting:function(t,s,e,r){e="string"==typeof e?"all"===e?u.modules:[e]:e||u.modules,r=r===h||r,b.each(e,function(e,n){var o,i=!a.moduleExists(n)||(b.fn[n].settings.namespace||!1);a.moduleExists(n)&&(a.verbose("Changing default setting",t,s,n),b.fn[n].settings[t]=s,r&&i&&0<(o=b(":data(module-"+i+")")).length&&(a.verbose("Modifying existing settings",o),o[n]("setting",t,s)))})},settings:function(i,e,t){e="string"==typeof e?[e]:e||u.modules,t=t===h||t,b.each(e,function(e,n){var o;a.moduleExists(n)&&(a.verbose("Changing default setting",i,n),b.extend(!0,b.fn[n].settings,i),t&&s&&0<(o=b(":data(module-"+s+")")).length&&(a.verbose("Modifying existing settings",o),o[n]("setting",i)))})}},enable:{console:function(){a.console(!0)},debug:function(e,n){e=e||u.modules,a.debug("Enabling debug for modules",e),a.change.setting("debug",!0,e,n)},verbose:function(e,n){e=e||u.modules,a.debug("Enabling verbose debug for modules",e),a.change.setting("verbose",!0,e,n)}},disable:{console:function(){a.console(!1)},debug:function(e,n){e=e||u.modules,a.debug("Disabling debug for modules",e),a.change.setting("debug",!1,e,n)},verbose:function(e,n){e=e||u.modules,a.debug("Disabling verbose debug for modules",e),a.change.setting("verbose",!1,e,n)}},console:function(e){if(e){if(f.cache.console===h)return void a.error(m.console);a.debug("Restoring console function"),p.console=f.cache.console}else a.debug("Disabling console function"),f.cache.console=p.console,p.console={clear:function(){},error:function(){},group:function(){},groupCollapsed:function(){},groupEnd:function(){},info:function(){},log:function(){},markTimeline:function(){},warn:function(){}}},destroy:function(){a.verbose("Destroying previous site for",d),d.removeData(r)},cache:{},setting:function(e,n){if(b.isPlainObject(e))b.extend(!0,u,e);else{if(n===h)return u[e];u[e]=n}},internal:function(e,n){if(b.isPlainObject(e))b.extend(!0,a,e);else{if(n===h)return a[e];a[e]=n}},debug:function(){u.debug&&(u.performance?a.performance.log(arguments):(a.debug=Function.prototype.bind.call(console.info,console,u.name+":"),a.debug.apply(console,arguments)))},verbose:function(){u.verbose&&u.debug&&(u.performance?a.performance.log(arguments):(a.verbose=Function.prototype.bind.call(console.info,console,u.name+":"),a.verbose.apply(console,arguments)))},error:function(){a.error=Function.prototype.bind.call(console.error,console,u.name+":"),a.error.apply(console,arguments)},performance:{log:function(e){var n,o;u.performance&&(o=(n=(new Date).getTime())-(i||n),i=n,t.push({Element:g,Name:e[0],Arguments:[].slice.call(e,1)||"","Execution Time":o})),clearTimeout(a.performance.timer),a.performance.timer=setTimeout(a.performance.display,500)},display:function(){var e=u.name+":",o=0;i=!1,clearTimeout(a.performance.timer),b.each(t,function(e,n){o+=n["Execution Time"]}),e+=" "+o+"ms",(console.group!==h||console.table!==h)&&0<t.length&&(console.groupCollapsed(e),console.table?console.table(t):b.each(t,function(e,n){console.log(n.Name+": "+n["Execution Time"]+"ms")}),console.groupEnd()),t=[]}},invoke:function(i,e,n){var t,s,o,r=f;return e=e||l,n=g||n,"string"==typeof i&&r!==h&&(i=i.split(/[\. ]/),t=i.length-1,b.each(i,function(e,n){var o=e!=t?n+i[e+1].charAt(0).toUpperCase()+i[e+1].slice(1):i;if(b.isPlainObject(r[o])&&e!=t)r=r[o];else{if(r[o]!==h)return s=r[o],!1;if(!b.isPlainObject(r[n])||e==t)return r[n]!==h?s=r[n]:a.error(m.method,i),!1;r=r[n]}})),b.isFunction(s)?o=s.apply(n,e):s!==h&&(o=s),b.isArray(c)?c.push(o):c!==h?c=[c,o]:o!==h&&(c=o),s}},o?(f===h&&a.initialize(),a.invoke(n)):(f!==h&&a.destroy(),a.initialize()),c!==h?c:this},b.site.settings={name:"Site",namespace:"site",error:{console:"Console cannot be restored, most likely it was overwritten outside of module",method:"The method you called is not defined."},debug:!1,verbose:!1,performance:!0,modules:["accordion","api","checkbox","dimmer","dropdown","embed","form","modal","nag","popup","rating","shape","sidebar","state","sticky","tab","transition","visit","visibility"],siteNamespace:"site",namespaceStub:{cache:{},config:{},sections:{},section:{},utilities:{}}},b.extend(b.expr[":"],{data:b.expr.createPseudo?b.expr.createPseudo(function(n){return function(e){return!!b.data(e,n)}}):function(e,n,o){return!!b.data(e,o[3])}})}(jQuery,window,document);