!function(A,E,T,D){"use strict";E=void 0!==E&&E.Math==Math?E:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")(),A.fn.search=function(R){var C,w=A(this),x=w.selector||"",F=(new Date).getTime(),j=[],k=R,q="string"==typeof k,S=[].slice.call(arguments,1);return A(this).each(function(){var f,u=A.isPlainObject(R)?A.extend(!0,{},A.fn.search.settings,R):A.extend({},A.fn.search.settings),g=u.className,l=u.metadata,c=u.regExp,i=u.fields,h=u.selector,d=u.error,e=u.namespace,n="."+e,t=e+"-module",p=A(this),m=p.find(h.prompt),s=p.find(h.searchButton),r=p.find(h.results),a=p.find(h.result),v=(p.find(h.category),this),o=p.data(t),b=!1,y=!1;f={initialize:function(){f.verbose("Initializing module"),f.determine.searchFields(),f.bind.events(),f.set.type(),f.create.results(),f.instantiate()},instantiate:function(){f.verbose("Storing instance of module",f),o=f,p.data(t,f)},destroy:function(){f.verbose("Destroying instance"),p.off(n).removeData(t)},refresh:function(){f.debug("Refreshing selector cache"),m=p.find(h.prompt),s=p.find(h.searchButton),p.find(h.category),r=p.find(h.results),a=p.find(h.result)},refreshResults:function(){r=p.find(h.results),a=p.find(h.result)},bind:{events:function(){f.verbose("Binding events to search"),u.automatic&&(p.on(f.get.inputEvent()+n,h.prompt,f.event.input),m.attr("autocomplete","off")),p.on("focus"+n,h.prompt,f.event.focus).on("blur"+n,h.prompt,f.event.blur).on("keydown"+n,h.prompt,f.handleKeyboard).on("click"+n,h.searchButton,f.query).on("mousedown"+n,h.results,f.event.result.mousedown).on("mouseup"+n,h.results,f.event.result.mouseup).on("click"+n,h.result,f.event.result.click)}},determine:{searchFields:function(){R&&R.searchFields!==D&&(u.searchFields=R.searchFields)}},event:{input:function(){u.searchDelay?(clearTimeout(f.timer),f.timer=setTimeout(function(){f.is.focused()&&f.query()},u.searchDelay)):f.query()},focus:function(){f.set.focus(),u.searchOnFocus&&f.has.minimumCharacters()&&f.query(function(){f.can.show()&&f.showResults()})},blur:function(e){var t=T.activeElement===this,s=function(){f.cancel.query(),f.remove.focus(),f.timer=setTimeout(f.hideResults,u.hideDelay)};t||(y=!1,f.resultsClicked?(f.debug("Determining if user action caused search to close"),p.one("click.close"+n,h.results,function(e){f.is.inMessage(e)||b?m.focus():(b=!1,f.is.animating()||f.is.hidden()||s())})):(f.debug("Input blurred without user action, closing results"),s()))},result:{mousedown:function(){f.resultsClicked=!0},mouseup:function(){f.resultsClicked=!1},click:function(e){f.debug("Search result selected");var t=A(this),s=t.find(h.title).eq(0),n=t.is("a[href]")?t:t.find("a[href]").eq(0),r=n.attr("href")||!1,i=n.attr("target")||!1,a=(s.html(),0<s.length&&s.text()),o=f.get.results(),c=t.data(l.result)||f.get.result(a,o);if(A.isFunction(u.onSelect)&&!1===u.onSelect.call(v,c,o))return f.debug("Custom onSelect callback cancelled default select action"),void(b=!0);f.hideResults(),a&&f.set.value(a),r&&(f.verbose("Opening search link found in result",n),"_blank"==i||e.ctrlKey?E.open(r):E.location.href=r)}}},handleKeyboard:function(e){var t,s=p.find(h.result),n=p.find(h.category),r=s.filter("."+g.active),i=s.index(r),a=s.length,o=0<r.length,c=e.which,u=13,l=38,d=40;if(c==27&&(f.verbose("Escape key pressed, blurring search field"),f.hideResults(),y=!0),f.is.visible())if(c==u){if(f.verbose("Enter key pressed, selecting active result"),0<s.filter("."+g.active).length)return f.event.result.click.call(s.filter("."+g.active),e),e.preventDefault(),!1}else c==l&&o?(f.verbose("Up key pressed, changing active result"),t=i-1<0?i:i-1,n.removeClass(g.active),s.removeClass(g.active).eq(t).addClass(g.active).closest(n).addClass(g.active),e.preventDefault()):c==d&&(f.verbose("Down key pressed, changing active result"),t=a<=i+1?i:i+1,n.removeClass(g.active),s.removeClass(g.active).eq(t).addClass(g.active).closest(n).addClass(g.active),e.preventDefault());else c==u&&(f.verbose("Enter key pressed, executing query"),f.query(),f.set.buttonPressed(),m.one("keyup",f.remove.buttonFocus))},setup:{api:function(t,s){var e={debug:u.debug,on:!1,cache:!0,action:"search",urlData:{query:t},onSuccess:function(e){f.parse.response.call(v,e,t),s()},onFailure:function(){f.displayMessage(d.serverError),s()},onAbort:function(e){},onError:f.error};A.extend(!0,e,u.apiSettings),f.verbose("Setting up API request",e),p.api(e)}},can:{useAPI:function(){return A.fn.api!==D},show:function(){return f.is.focused()&&!f.is.visible()&&!f.is.empty()},transition:function(){return u.transition&&A.fn.transition!==D&&p.transition("is supported")}},is:{animating:function(){return r.hasClass(g.animating)},hidden:function(){return r.hasClass(g.hidden)},inMessage:function(e){if(e.target){var t=A(e.target);return A.contains(T.documentElement,e.target)&&0<t.closest(h.message).length}},empty:function(){return""===r.html()},visible:function(){return 0<r.filter(":visible").length},focused:function(){return 0<m.filter(":focus").length}},get:{inputEvent:function(){var e=m[0];return e!==D&&e.oninput!==D?"input":e!==D&&e.onpropertychange!==D?"propertychange":"keyup"},value:function(){return m.val()},results:function(){return p.data(l.results)},result:function(s,e){var n=["title","id"],r=!1;return s=s!==D?s:f.get.value(),e=e!==D?e:f.get.results(),"category"===u.type?(f.debug("Finding result that matches",s),A.each(e,function(e,t){if(A.isArray(t.results)&&(r=f.search.object(s,t.results,n)[0]))return!1})):(f.debug("Finding result in results object",s),r=f.search.object(s,e,n)[0]),r||!1}},select:{firstResult:function(){f.verbose("Selecting first result"),a.first().addClass(g.active)}},set:{focus:function(){p.addClass(g.focus)},loading:function(){p.addClass(g.loading)},value:function(e){f.verbose("Setting search input value",e),m.val(e)},type:function(e){e=e||u.type,"category"==u.type&&p.addClass(u.type)},buttonPressed:function(){s.addClass(g.pressed)}},remove:{loading:function(){p.removeClass(g.loading)},focus:function(){p.removeClass(g.focus)},buttonPressed:function(){s.removeClass(g.pressed)}},query:function(e){e=A.isFunction(e)?e:function(){};var t=f.get.value(),s=f.read.cache(t);e=e||function(){},f.has.minimumCharacters()?(s?(f.debug("Reading result from cache",t),f.save.results(s.results),f.addResults(s.html),f.inject.id(s.results),e()):(f.debug("Querying for",t),A.isPlainObject(u.source)||A.isArray(u.source)?(f.search.local(t),e()):f.can.useAPI()?f.search.remote(t,e):(f.error(d.source),e())),u.onSearchQuery.call(v,t)):f.hideResults()},search:{local:function(e){var t,s=f.search.object(e,u.content);f.set.loading(),f.save.results(s),f.debug("Returned local search results",s),t=f.generateResults({results:s}),f.remove.loading(),f.addResults(t),f.inject.id(s),f.write.cache(e,{html:t,results:s})},remote:function(e,t){t=A.isFunction(t)?t:function(){},p.api("is loading")&&p.api("abort"),f.setup.api(e,t),p.api("query")},object:function(n,t,e){var r=[],i=[],s=n.toString().replace(c.escape,"\\$&"),a=new RegExp(c.beginsWith+s,"i"),o=function(e,t){var s=-1==A.inArray(t,r),n=-1==A.inArray(t,i);s&&n&&e.push(t)};return t=t||u.source,e=e!==D?e:u.searchFields,A.isArray(e)||(e=[e]),t===D||!1===t?(f.error(d.source),[]):(A.each(e,function(e,s){A.each(t,function(e,t){"string"==typeof t[s]&&(-1!==t[s].search(a)?o(r,t):u.searchFullText&&f.fuzzySearch(n,t[s])&&o(i,t))})}),A.merge(r,i))}},fuzzySearch:function(e,t){var s=t.length,n=e.length;if("string"!=typeof e)return!1;if(e=e.toLowerCase(),t=t.toLowerCase(),s<n)return!1;if(n===s)return e===t;e:for(var r=0,i=0;r<n;r++){for(var a=e.charCodeAt(r);i<s;)if(t.charCodeAt(i++)===a)continue e;return!1}return!0},parse:{response:function(e,t){var s=f.generateResults(e);f.verbose("Parsing server response",e),e!==D&&t!==D&&e[i.results]!==D&&(f.addResults(s),f.inject.id(e[i.results]),f.write.cache(t,{html:s,results:e[i.results]}),f.save.results(e[i.results]))}},cancel:{query:function(){f.can.useAPI()&&p.api("abort")}},has:{minimumCharacters:function(){return f.get.value().length>=u.minCharacters},results:function(){return 0!==r.length&&""!=r.html()}},clear:{cache:function(e){var t=p.data(l.cache);e?e&&t&&t[e]&&(f.debug("Removing value from cache",e),delete t[e],p.data(l.cache,t)):(f.debug("Clearing cache",e),p.removeData(l.cache))}},read:{cache:function(e){var t=p.data(l.cache);return!!u.cache&&(f.verbose("Checking cache for generated html for query",e),"object"==typeof t&&t[e]!==D&&t[e])}},create:{id:function(e,t){var s,n=e+1;return t!==D?(s=String.fromCharCode(97+t)+n,f.verbose("Creating category result id",s)):(s=n,f.verbose("Creating result id",s)),s},results:function(){0===r.length&&(r=A("<div />").addClass(g.results).appendTo(p))}},inject:{result:function(e,t,s){f.verbose("Injecting result into results");var n=s!==D?r.children().eq(s).children(h.result).eq(t):r.children(h.result).eq(t);f.verbose("Injecting results metadata",n),n.data(l.result,e)},id:function(n){f.debug("Injecting unique ids into results");var r=0,i=0;return"category"===u.type?A.each(n,function(e,n){i=0,A.each(n.results,function(e,t){var s=n.results[e];s.id===D&&(s.id=f.create.id(i,r)),f.inject.result(s,i,r),i++}),r++}):A.each(n,function(e,t){var s=n[e];s.id===D&&(s.id=f.create.id(i)),f.inject.result(s,i),i++}),n}},save:{results:function(e){f.verbose("Saving current search results to metadata",e),p.data(l.results,e)}},write:{cache:function(e,t){var s=p.data(l.cache)!==D?p.data(l.cache):{};u.cache&&(f.verbose("Writing generated html to cache",e,t),s[e]=t,p.data(l.cache,s))}},addResults:function(e){if(A.isFunction(u.onResultsAdd)&&!1===u.onResultsAdd.call(r,e))return f.debug("onResultsAdd callback cancelled default action"),!1;e?(r.html(e),f.refreshResults(),u.selectFirstResult&&f.select.firstResult(),f.showResults()):f.hideResults(function(){r.empty()})},showResults:function(e){e=A.isFunction(e)?e:function(){},y||!f.is.visible()&&f.has.results()&&(f.can.transition()?(f.debug("Showing results with css animations"),r.transition({animation:u.transition+" in",debug:u.debug,verbose:u.verbose,duration:u.duration,onComplete:function(){e()},queue:!0})):(f.debug("Showing results with javascript"),r.stop().fadeIn(u.duration,u.easing)),u.onResultsOpen.call(r))},hideResults:function(e){e=A.isFunction(e)?e:function(){},f.is.visible()&&(f.can.transition()?(f.debug("Hiding results with css animations"),r.transition({animation:u.transition+" out",debug:u.debug,verbose:u.verbose,duration:u.duration,onComplete:function(){e()},queue:!0})):(f.debug("Hiding results with javascript"),r.stop().fadeOut(u.duration,u.easing)),u.onResultsClose.call(r))},generateResults:function(e){f.debug("Generating html from response",e);var t=u.templates[u.type],s=A.isPlainObject(e[i.results])&&!A.isEmptyObject(e[i.results]),n=A.isArray(e[i.results])&&0<e[i.results].length,r="";return s||n?(0<u.maxResults&&(s?"standard"==u.type&&f.error(d.maxResults):e[i.results]=e[i.results].slice(0,u.maxResults)),A.isFunction(t)?r=t(e,i):f.error(d.noTemplate,!1)):u.showNoResults&&(r=f.displayMessage(d.noResults,"empty")),u.onResults.call(v,e),r},displayMessage:function(e,t){return t=t||"standard",f.debug("Displaying message",e,t),f.addResults(u.templates.message(e,t)),u.templates.message(e,t)},setting:function(e,t){if(A.isPlainObject(e))A.extend(!0,u,e);else{if(t===D)return u[e];u[e]=t}},internal:function(e,t){if(A.isPlainObject(e))A.extend(!0,f,e);else{if(t===D)return f[e];f[e]=t}},debug:function(){!u.silent&&u.debug&&(u.performance?f.performance.log(arguments):(f.debug=Function.prototype.bind.call(console.info,console,u.name+":"),f.debug.apply(console,arguments)))},verbose:function(){!u.silent&&u.verbose&&u.debug&&(u.performance?f.performance.log(arguments):(f.verbose=Function.prototype.bind.call(console.info,console,u.name+":"),f.verbose.apply(console,arguments)))},error:function(){u.silent||(f.error=Function.prototype.bind.call(console.error,console,u.name+":"),f.error.apply(console,arguments))},performance:{log:function(e){var t,s;u.performance&&(s=(t=(new Date).getTime())-(F||t),F=t,j.push({Name:e[0],Arguments:[].slice.call(e,1)||"",Element:v,"Execution Time":s})),clearTimeout(f.performance.timer),f.performance.timer=setTimeout(f.performance.display,500)},display:function(){var e=u.name+":",s=0;F=!1,clearTimeout(f.performance.timer),A.each(j,function(e,t){s+=t["Execution Time"]}),e+=" "+s+"ms",x&&(e+=" '"+x+"'"),1<w.length&&(e+=" ("+w.length+")"),(console.group!==D||console.table!==D)&&0<j.length&&(console.groupCollapsed(e),console.table?console.table(j):A.each(j,function(e,t){console.log(t.Name+": "+t["Execution Time"]+"ms")}),console.groupEnd()),j=[]}},invoke:function(n,e,t){var r,i,s,a=o;return e=e||S,t=v||t,"string"==typeof n&&a!==D&&(n=n.split(/[\. ]/),r=n.length-1,A.each(n,function(e,t){var s=e!=r?t+n[e+1].charAt(0).toUpperCase()+n[e+1].slice(1):n;if(A.isPlainObject(a[s])&&e!=r)a=a[s];else{if(a[s]!==D)return i=a[s],!1;if(!A.isPlainObject(a[t])||e==r)return a[t]!==D&&(i=a[t]),!1;a=a[t]}})),A.isFunction(i)?s=i.apply(t,e):i!==D&&(s=i),A.isArray(C)?C.push(s):C!==D?C=[C,s]:s!==D&&(C=s),i}},q?(o===D&&f.initialize(),f.invoke(k)):(o!==D&&o.invoke("destroy"),f.initialize())}),C!==D?C:this},A.fn.search.settings={name:"Search",namespace:"search",silent:!1,debug:!1,verbose:!1,performance:!0,type:"standard",minCharacters:1,selectFirstResult:!1,apiSettings:!1,source:!1,searchOnFocus:!0,searchFields:["title","description"],displayField:"",searchFullText:!0,automatic:!0,hideDelay:0,searchDelay:200,maxResults:7,cache:!0,showNoResults:!0,transition:"scale",duration:200,easing:"easeOutExpo",onSelect:!1,onResultsAdd:!1,onSearchQuery:function(e){},onResults:function(e){},onResultsOpen:function(){},onResultsClose:function(){},className:{animating:"animating",active:"active",empty:"empty",focus:"focus",hidden:"hidden",loading:"loading",results:"results",pressed:"down"},error:{source:"Cannot search. No source used, and Semantic API module was not included",noResults:"Your search returned no results",logging:"Error in debug logging, exiting.",noEndpoint:"No search endpoint was specified",noTemplate:"A valid template name was not specified.",serverError:"There was an issue querying the server.",maxResults:"Results must be an array to use maxResults setting",method:"The method you called is not defined."},metadata:{cache:"cache",results:"results",result:"result"},regExp:{escape:/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,beginsWith:"(?:s|^)"},fields:{categories:"results",categoryName:"name",categoryResults:"results",description:"description",image:"image",price:"price",results:"results",title:"title",url:"url",action:"action",actionText:"text",actionURL:"url"},selector:{prompt:".prompt",searchButton:".search.button",results:".results",message:".results > .message",category:".category",result:".result",title:".title, .name"},templates:{escape:function(e){var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};return/[&<>"'`]/.test(e)?e.replace(/[&<>"'`]/g,function(e){return t[e]}):e},message:function(e,t){var s="";return e!==D&&t!==D&&(s+='<div class="message '+t+'">',s+="empty"==t?'<div class="header">No Results</div class="header"><div class="description">'+e+'</div class="description">':' <div class="description">'+e+"</div>",s+="</div>"),s},category:function(e,s){var n="";A.fn.search.settings.templates.escape;return e[s.categoryResults]!==D&&(A.each(e[s.categoryResults],function(e,t){t[s.results]!==D&&0<t.results.length&&(n+='<div class="category">',t[s.categoryName]!==D&&(n+='<div class="name">'+t[s.categoryName]+"</div>"),A.each(t.results,function(e,t){t[s.url]?n+='<a class="result" href="'+t[s.url]+'">':n+='<a class="result">',t[s.image]!==D&&(n+='<div class="image"> <img src="'+t[s.image]+'"></div>'),n+='<div class="content">',t[s.price]!==D&&(n+='<div class="price">'+t[s.price]+"</div>"),t[s.title]!==D&&(n+='<div class="title">'+t[s.title]+"</div>"),t[s.description]!==D&&(n+='<div class="description">'+t[s.description]+"</div>"),n+="</div>",n+="</a>"}),n+="</div>")}),e[s.action]&&(n+='<a href="'+e[s.action][s.actionURL]+'" class="action">'+e[s.action][s.actionText]+"</a>"),n)},standard:function(e,s){var n="";return e[s.results]!==D&&(A.each(e[s.results],function(e,t){t[s.url]?n+='<a class="result" href="'+t[s.url]+'">':n+='<a class="result">',t[s.image]!==D&&(n+='<div class="image"> <img src="'+t[s.image]+'"></div>'),n+='<div class="content">',t[s.price]!==D&&(n+='<div class="price">'+t[s.price]+"</div>"),t[s.title]!==D&&(n+='<div class="title">'+t[s.title]+"</div>"),t[s.description]!==D&&(n+='<div class="description">'+t[s.description]+"</div>"),n+="</div>",n+="</a>"}),e[s.action]&&(n+='<a href="'+e[s.action][s.actionURL]+'" class="action">'+e[s.action][s.actionText]+"</a>"),n)}}}}(jQuery,window,document);