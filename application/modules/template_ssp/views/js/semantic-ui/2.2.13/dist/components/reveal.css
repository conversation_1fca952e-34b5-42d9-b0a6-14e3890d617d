/*!
 * # Semantic UI 2.2.13 - Reveal
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Reveal
*******************************/

.ui.reveal {
  display: inherit;
  position: relative !important;
  font-size: 0em !important;
}
.ui.reveal > .visible.content {
  position: absolute !important;
  top: 0em !important;
  left: 0em !important;
  z-index: 3 !important;
  -webkit-transition: all 0.5s ease 0.1s;
  transition: all 0.5s ease 0.1s;
}
.ui.reveal > .hidden.content {
  position: relative !important;
  z-index: 2 !important;
}

/* Make sure hovered element is on top of other reveal */
.ui.active.reveal .visible.content,
.ui.reveal:hover .visible.content {
  z-index: 4 !important;
}


/*******************************
              Types
*******************************/


/*--------------
      Slide
---------------*/

.ui.slide.reveal {
  position: relative !important;
  overflow: hidden !important;
  white-space: nowrap;
}
.ui.slide.reveal > .content {
  display: block;
  width: 100%;
  float: left;
  margin: 0em;
  -webkit-transition: -webkit-transform 0.5s ease 0.1s;
  transition: -webkit-transform 0.5s ease 0.1s;
  transition: transform 0.5s ease 0.1s;
  transition: transform 0.5s ease 0.1s, -webkit-transform 0.5s ease 0.1s;
}
.ui.slide.reveal > .visible.content {
  position: relative !important;
}
.ui.slide.reveal > .hidden.content {
  position: absolute !important;
  left: 0% !important;
  width: 100% !important;
  -webkit-transform: translateX(100%) !important;
          transform: translateX(100%) !important;
}
.ui.slide.active.reveal > .visible.content,
.ui.slide.reveal:hover > .visible.content {
  -webkit-transform: translateX(-100%) !important;
          transform: translateX(-100%) !important;
}
.ui.slide.active.reveal > .hidden.content,
.ui.slide.reveal:hover > .hidden.content {
  -webkit-transform: translateX(0%) !important;
          transform: translateX(0%) !important;
}
.ui.slide.right.reveal > .visible.content {
  -webkit-transform: translateX(0%) !important;
          transform: translateX(0%) !important;
}
.ui.slide.right.reveal > .hidden.content {
  -webkit-transform: translateX(-100%) !important;
          transform: translateX(-100%) !important;
}
.ui.slide.right.active.reveal > .visible.content,
.ui.slide.right.reveal:hover > .visible.content {
  -webkit-transform: translateX(100%) !important;
          transform: translateX(100%) !important;
}
.ui.slide.right.active.reveal > .hidden.content,
.ui.slide.right.reveal:hover > .hidden.content {
  -webkit-transform: translateX(0%) !important;
          transform: translateX(0%) !important;
}
.ui.slide.up.reveal > .hidden.content {
  -webkit-transform: translateY(100%) !important;
          transform: translateY(100%) !important;
}
.ui.slide.up.active.reveal > .visible.content,
.ui.slide.up.reveal:hover > .visible.content {
  -webkit-transform: translateY(-100%) !important;
          transform: translateY(-100%) !important;
}
.ui.slide.up.active.reveal > .hidden.content,
.ui.slide.up.reveal:hover > .hidden.content {
  -webkit-transform: translateY(0%) !important;
          transform: translateY(0%) !important;
}
.ui.slide.down.reveal > .hidden.content {
  -webkit-transform: translateY(-100%) !important;
          transform: translateY(-100%) !important;
}
.ui.slide.down.active.reveal > .visible.content,
.ui.slide.down.reveal:hover > .visible.content {
  -webkit-transform: translateY(100%) !important;
          transform: translateY(100%) !important;
}
.ui.slide.down.active.reveal > .hidden.content,
.ui.slide.down.reveal:hover > .hidden.content {
  -webkit-transform: translateY(0%) !important;
          transform: translateY(0%) !important;
}

/*--------------
      Fade
---------------*/

.ui.fade.reveal > .visible.content {
  opacity: 1;
}
.ui.fade.active.reveal > .visible.content,
.ui.fade.reveal:hover > .visible.content {
  opacity: 0;
}

/*--------------
      Move
---------------*/

.ui.move.reveal {
  position: relative !important;
  overflow: hidden !important;
  white-space: nowrap;
}
.ui.move.reveal > .content {
  display: block;
  float: left;
  margin: 0em;
  -webkit-transition: -webkit-transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1) 0.1s;
  transition: -webkit-transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1) 0.1s;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1) 0.1s;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1) 0.1s, -webkit-transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1) 0.1s;
}
.ui.move.reveal > .visible.content {
  position: relative !important;
}
.ui.move.reveal > .hidden.content {
  position: absolute !important;
  left: 0% !important;
  width: 100% !important;
}
.ui.move.active.reveal > .visible.content,
.ui.move.reveal:hover > .visible.content {
  -webkit-transform: translateX(-100%) !important;
          transform: translateX(-100%) !important;
}
.ui.move.right.active.reveal > .visible.content,
.ui.move.right.reveal:hover > .visible.content {
  -webkit-transform: translateX(100%) !important;
          transform: translateX(100%) !important;
}
.ui.move.up.active.reveal > .visible.content,
.ui.move.up.reveal:hover > .visible.content {
  -webkit-transform: translateY(-100%) !important;
          transform: translateY(-100%) !important;
}
.ui.move.down.active.reveal > .visible.content,
.ui.move.down.reveal:hover > .visible.content {
  -webkit-transform: translateY(100%) !important;
          transform: translateY(100%) !important;
}

/*--------------
     Rotate
---------------*/

.ui.rotate.reveal > .visible.content {
  -webkit-transition-duration: 0.5s;
          transition-duration: 0.5s;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
}
.ui.rotate.reveal > .visible.content,
.ui.rotate.right.reveal > .visible.content {
  -webkit-transform-origin: bottom right;
          transform-origin: bottom right;
}
.ui.rotate.active.reveal > .visible.content,
.ui.rotate.reveal:hover > .visible.content,
.ui.rotate.right.active.reveal > .visible.content,
.ui.rotate.right.reveal:hover > .visible.content {
  -webkit-transform: rotate(110deg);
          transform: rotate(110deg);
}
.ui.rotate.left.reveal > .visible.content {
  -webkit-transform-origin: bottom left;
          transform-origin: bottom left;
}
.ui.rotate.left.active.reveal > .visible.content,
.ui.rotate.left.reveal:hover > .visible.content {
  -webkit-transform: rotate(-110deg);
          transform: rotate(-110deg);
}


/*******************************
              States
*******************************/

.ui.disabled.reveal:hover > .visible.visible.content {
  position: static !important;
  display: block !important;
  opacity: 1 !important;
  top: 0 !important;
  left: 0 !important;
  right: auto !important;
  bottom: auto !important;
  -webkit-transform: none !important;
          transform: none !important;
}
.ui.disabled.reveal:hover > .hidden.hidden.content {
  display: none !important;
}


/*******************************
           Variations
*******************************/


/*--------------
     Visible
---------------*/

.ui.visible.reveal {
  overflow: visible;
}

/*--------------
     Instant
---------------*/

.ui.instant.reveal > .content {
  -webkit-transition-delay: 0s !important;
          transition-delay: 0s !important;
}

/*--------------
     Sizing
---------------*/

.ui.reveal > .content {
  font-size: 1rem !important;
}


/*******************************
         Theme Overrides
*******************************/



/*******************************
         Site Overrides
*******************************/

