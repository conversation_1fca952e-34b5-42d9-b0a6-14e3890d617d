/*!
 * # Semantic UI 2.2.13 - Step
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Plural
*******************************/

.ui.steps {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  margin: 1em 0em;
  background: '';
  -webkit-box-shadow: none;
          box-shadow: none;
  line-height: 1.14285714em;
  border-radius: 0.28571429rem;
  border: 1px solid rgba(34, 36, 38, 0.15);
}

/* First Steps */
.ui.steps:first-child {
  margin-top: 0em;
}

/* Last Steps */
.ui.steps:last-child {
  margin-bottom: 0em;
}


/*******************************
           Singular
*******************************/

.ui.steps .step {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
      -ms-flex: 1 0 auto;
          flex: 1 0 auto;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  vertical-align: middle;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0em 0em;
  padding: 1.14285714em 2em;
  background: #FFFFFF;
  color: rgba(0, 0, 0, 0.87);
  -webkit-box-shadow: none;
          box-shadow: none;
  border-radius: 0em;
  border: none;
  border-right: 1px solid rgba(34, 36, 38, 0.15);
  -webkit-transition: background-color 0.1s ease, opacity 0.1s ease, color 0.1s ease, -webkit-box-shadow 0.1s ease;
  transition: background-color 0.1s ease, opacity 0.1s ease, color 0.1s ease, -webkit-box-shadow 0.1s ease;
  transition: background-color 0.1s ease, opacity 0.1s ease, color 0.1s ease, box-shadow 0.1s ease;
  transition: background-color 0.1s ease, opacity 0.1s ease, color 0.1s ease, box-shadow 0.1s ease, -webkit-box-shadow 0.1s ease;
}

/* Arrow */
.ui.steps .step:after {
  display: none;
  position: absolute;
  z-index: 2;
  content: '';
  top: 50%;
  right: 0%;
  border: medium none;
  background-color: #FFFFFF;
  width: 1.14285714em;
  height: 1.14285714em;
  border-style: solid;
  border-color: rgba(34, 36, 38, 0.15);
  border-width: 0px 1px 1px 0px;
  -webkit-transition: background-color 0.1s ease, opacity 0.1s ease, color 0.1s ease, -webkit-box-shadow 0.1s ease;
  transition: background-color 0.1s ease, opacity 0.1s ease, color 0.1s ease, -webkit-box-shadow 0.1s ease;
  transition: background-color 0.1s ease, opacity 0.1s ease, color 0.1s ease, box-shadow 0.1s ease;
  transition: background-color 0.1s ease, opacity 0.1s ease, color 0.1s ease, box-shadow 0.1s ease, -webkit-box-shadow 0.1s ease;
  -webkit-transform: translateY(-50%) translateX(50%) rotate(-45deg);
          transform: translateY(-50%) translateX(50%) rotate(-45deg);
}

/* First Step */
.ui.steps .step:first-child {
  padding-left: 2em;
  border-radius: 0.28571429rem 0em 0em 0.28571429rem;
}

/* Last Step */
.ui.steps .step:last-child {
  border-radius: 0em 0.28571429rem 0.28571429rem 0em;
}
.ui.steps .step:last-child {
  border-right: none;
  margin-right: 0em;
}

/* Only Step */
.ui.steps .step:only-child {
  border-radius: 0.28571429rem;
}


/*******************************
            Content
*******************************/


/* Title */
.ui.steps .step .title {
  font-family: 'Lato', 'Helvetica Neue', Arial, Helvetica, sans-serif;
  font-size: 1.14285714em;
  font-weight: bold;
}
.ui.steps .step > .title {
  width: 100%;
}

/* Description */
.ui.steps .step .description {
  font-weight: normal;
  font-size: 0.92857143em;
  color: rgba(0, 0, 0, 0.87);
}
.ui.steps .step > .description {
  width: 100%;
}
.ui.steps .step .title ~ .description {
  margin-top: 0.25em;
}

/* Icon */
.ui.steps .step > .icon {
  line-height: 1;
  font-size: 2.5em;
  margin: 0em 1rem 0em 0em;
}
.ui.steps .step > .icon,
.ui.steps .step > .icon ~ .content {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 1 auto;
          flex: 0 1 auto;
  -ms-flex-item-align: middle;
      align-self: middle;
}
.ui.steps .step > .icon ~ .content {
  -webkit-box-flex: 1 0 auto;
      -ms-flex-positive: 1 0 auto;
          flex-grow: 1 0 auto;
}

/* Horizontal Icon */
.ui.steps:not(.vertical) .step > .icon {
  width: auto;
}

/* Link */
.ui.steps .link.step,
.ui.steps a.step {
  cursor: pointer;
}


/*******************************
            Types
*******************************/


/*--------------
     Ordered
---------------*/

.ui.ordered.steps {
  counter-reset: ordered;
}
.ui.ordered.steps .step:before {
  display: block;
  position: static;
  text-align: center;
  content: counters(ordered, ".");
  -ms-flex-item-align: middle;
      align-self: middle;
  margin-right: 1rem;
  font-size: 2.5em;
  counter-increment: ordered;
  font-family: inherit;
  font-weight: bold;
}
.ui.ordered.steps .step > * {
  display: block;
  -ms-flex-item-align: middle;
      align-self: middle;
}

/*--------------
    Vertical
---------------*/

.ui.vertical.steps {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  overflow: visible;
}
.ui.vertical.steps .step {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  border-radius: 0em;
  padding: 1.14285714em 2em;
  border-right: none;
  border-bottom: 1px solid rgba(34, 36, 38, 0.15);
}
.ui.vertical.steps .step:first-child {
  padding: 1.14285714em 2em;
  border-radius: 0.28571429rem 0.28571429rem 0em 0em;
}
.ui.vertical.steps .step:last-child {
  border-bottom: none;
  border-radius: 0em 0em 0.28571429rem 0.28571429rem;
}
.ui.vertical.steps .step:only-child {
  border-radius: 0.28571429rem;
}

/* Arrow */
.ui.vertical.steps .step:after {
  display: none;
}
.ui.vertical.steps .step:after {
  top: 50%;
  right: 0%;
  border-width: 0px 1px 1px 0px;
}
.ui.vertical.steps .step:after {
  display: none;
}
.ui.vertical.steps .active.step:after {
  display: block;
}
.ui.vertical.steps .step:last-child:after {
  display: none;
}
.ui.vertical.steps .active.step:last-child:after {
  display: block;
}

/*---------------
    Responsive
----------------*/


/* Mobile (Default) */
@media only screen and (max-width: 767px) {
  .ui.steps:not(.unstackable) {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    overflow: visible;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .ui.steps:not(.unstackable) .step {
    width: 100% !important;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    border-radius: 0em;
    padding: 1.14285714em 2em;
  }
  .ui.steps:not(.unstackable) .step:first-child {
    padding: 1.14285714em 2em;
    border-radius: 0.28571429rem 0.28571429rem 0em 0em;
  }
  .ui.steps:not(.unstackable) .step:last-child {
    border-radius: 0em 0em 0.28571429rem 0.28571429rem;
  }
  
/* Arrow */
  .ui.steps:not(.unstackable) .step:after {
    display: none !important;
  }
  
/* Content */
  .ui.steps:not(.unstackable) .step .content {
    text-align: center;
  }
  
/* Icon */
  .ui.steps:not(.unstackable) .step > .icon,
  .ui.ordered.steps:not(.unstackable) .step:before {
    margin: 0em 0em 1rem 0em;
  }
}


/*******************************
             States
*******************************/


/* Link Hover */
.ui.steps .link.step:hover::after,
.ui.steps .link.step:hover,
.ui.steps a.step:hover::after,
.ui.steps a.step:hover {
  background: #F9FAFB;
  color: rgba(0, 0, 0, 0.8);
}

/* Link Down */
.ui.steps .link.step:active::after,
.ui.steps .link.step:active,
.ui.steps a.step:active::after,
.ui.steps a.step:active {
  background: #F3F4F5;
  color: rgba(0, 0, 0, 0.9);
}

/* Active */
.ui.steps .step.active {
  cursor: auto;
  background: #F3F4F5;
}
.ui.steps .step.active:after {
  background: #F3F4F5;
}
.ui.steps .step.active .title {
  color: #4183C4;
}
.ui.ordered.steps .step.active:before,
.ui.steps .active.step .icon {
  color: rgba(0, 0, 0, 0.85);
}

/* Active Arrow */
.ui.steps .step:after {
  display: block;
}
.ui.steps .active.step:after {
  display: block;
}
.ui.steps .step:last-child:after {
  display: none;
}
.ui.steps .active.step:last-child:after {
  display: none;
}

/* Active Hover */
.ui.steps .link.active.step:hover::after,
.ui.steps .link.active.step:hover,
.ui.steps a.active.step:hover::after,
.ui.steps a.active.step:hover {
  cursor: pointer;
  background: #DCDDDE;
  color: rgba(0, 0, 0, 0.87);
}

/* Completed */
.ui.steps .step.completed > .icon:before,
.ui.ordered.steps .step.completed:before {
  color: #21BA45;
}

/* Disabled */
.ui.steps .disabled.step {
  cursor: auto;
  background: #FFFFFF;
  pointer-events: none;
}
.ui.steps .disabled.step,
.ui.steps .disabled.step .title,
.ui.steps .disabled.step .description {
  color: rgba(40, 40, 40, 0.3);
}
.ui.steps .disabled.step:after {
  background: #FFFFFF;
}


/*******************************
           Variations
*******************************/


/*--------------
   Stackable
---------------*/


/* Tablet Or Below */
@media only screen and (max-width: 991px) {
  .ui[class*="tablet stackable"].steps {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    overflow: visible;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  
/* Steps */
  .ui[class*="tablet stackable"].steps .step {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    border-radius: 0em;
    padding: 1.14285714em 2em;
  }
  .ui[class*="tablet stackable"].steps .step:first-child {
    padding: 1.14285714em 2em;
    border-radius: 0.28571429rem 0.28571429rem 0em 0em;
  }
  .ui[class*="tablet stackable"].steps .step:last-child {
    border-radius: 0em 0em 0.28571429rem 0.28571429rem;
  }
  
/* Arrow */
  .ui[class*="tablet stackable"].steps .step:after {
    display: none !important;
  }
  
/* Content */
  .ui[class*="tablet stackable"].steps .step .content {
    text-align: center;
  }
  
/* Icon */
  .ui[class*="tablet stackable"].steps .step > .icon,
  .ui[class*="tablet stackable"].ordered.steps .step:before {
    margin: 0em 0em 1rem 0em;
  }
}

/*--------------
      Fluid
---------------*/


/* Fluid */
.ui.fluid.steps {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

/*--------------
    Attached
---------------*/


/* Top */
.ui.attached.steps {
  width: calc(100% +  2px ) !important;
  margin: 0em -1px 0;
  max-width: calc(100% +  2px );
  border-radius: 0.28571429rem 0.28571429rem 0em 0em;
}
.ui.attached.steps .step:first-child {
  border-radius: 0.28571429rem 0em 0em 0em;
}
.ui.attached.steps .step:last-child {
  border-radius: 0em 0.28571429rem 0em 0em;
}

/* Bottom */
.ui.bottom.attached.steps {
  margin: 0 -1px 0em;
  border-radius: 0em 0em 0.28571429rem 0.28571429rem;
}
.ui.bottom.attached.steps .step:first-child {
  border-radius: 0em 0em 0em 0.28571429rem;
}
.ui.bottom.attached.steps .step:last-child {
  border-radius: 0em 0em 0.28571429rem 0em;
}

/*-------------------
    Evenly Divided
--------------------*/

.ui.one.steps,
.ui.two.steps,
.ui.three.steps,
.ui.four.steps,
.ui.five.steps,
.ui.six.steps,
.ui.seven.steps,
.ui.eight.steps {
  width: 100%;
}
.ui.one.steps > .step,
.ui.two.steps > .step,
.ui.three.steps > .step,
.ui.four.steps > .step,
.ui.five.steps > .step,
.ui.six.steps > .step,
.ui.seven.steps > .step,
.ui.eight.steps > .step {
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}
.ui.one.steps > .step {
  width: 100%;
}
.ui.two.steps > .step {
  width: 50%;
}
.ui.three.steps > .step {
  width: 33.333%;
}
.ui.four.steps > .step {
  width: 25%;
}
.ui.five.steps > .step {
  width: 20%;
}
.ui.six.steps > .step {
  width: 16.666%;
}
.ui.seven.steps > .step {
  width: 14.285%;
}
.ui.eight.steps > .step {
  width: 12.500%;
}

/*-------------------
       Sizes
--------------------*/

.ui.mini.steps .step,
.ui.mini.step {
  font-size: 0.78571429rem;
}
.ui.tiny.steps .step,
.ui.tiny.step {
  font-size: 0.85714286rem;
}
.ui.small.steps .step,
.ui.small.step {
  font-size: 0.92857143rem;
}
.ui.steps .step,
.ui.step {
  font-size: 1rem;
}
.ui.large.steps .step,
.ui.large.step {
  font-size: 1.14285714rem;
}
.ui.big.steps .step,
.ui.big.step {
  font-size: 1.28571429rem;
}
.ui.huge.steps .step,
.ui.huge.step {
  font-size: 1.42857143rem;
}
.ui.massive.steps .step,
.ui.massive.step {
  font-size: 1.71428571rem;
}


/*******************************
         Theme Overrides
*******************************/

@font-face {
  font-family: 'Step';
  src: url(data:application/x-font-ttf;charset=utf-8;;base64,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) format('truetype'), url(data:application/font-woff;charset=utf-8;base64,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) format('woff');
}
.ui.steps .step.completed > .icon:before,
.ui.ordered.steps .step.completed:before {
  font-family: 'Step';
  content: '\e800';
  
/* '' */
}


/*******************************
         Site Overrides
*******************************/

