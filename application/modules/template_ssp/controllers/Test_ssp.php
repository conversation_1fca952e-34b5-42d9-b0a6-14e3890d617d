<?php

class ___CONTROLLER_CLASS__ extends MY_Controller {

    public function __construct() {
        header("Access-Control-Allow-Origin: *");

        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        
        $this->load->helper('dtssp');
    }

    
    public function index() {
        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "___TITLE___";
        $data['title'] = $title;
        
        $js_file = $this->load->view('___CONTROLLER_FOLDER___/js_file', '', true);
        $this->template->set('title', $title);
        $this->template->set('jv_script', $js_file);
        $this->template->load('default_layout', 'contents', 'index', $data);
    }


    public function ssp() {
        $table = '___TABLE_NAME___';
        $primaryKey = '___PRIMARY___KEY___';

        $columns = array(
            /*
            $columns = array(
                array('db' => 'first_name', 'dt' => 0),
                array('db' => 'last_name', 'dt' => 1),
                array('db' => 'position', 'dt' => 2),
                array('db' => 'office', 'dt' => 3),
                array(
                    'db' => 'start_date',
                    'dt' => 4,
                    'formatter' => function( $d, $row ) {
                        return date('jS M y', strtotime($d));
                    }
                ),
                array(
                    'db' => 'salary',
                    'dt' => 5,
                    'formatter' => function( $d, $row ) {
                        return '$' . number_format($d);
                    }
                )
            );
             */
        );

        //filter statement ini harusnya ambil dari data session (misalnya user_id), 
        //jangan dikirim sebagai parameter ke sini
        datatable_ssp($table, $primaryKey, $columns, "___FILTER___");
    }
     

}

?>
