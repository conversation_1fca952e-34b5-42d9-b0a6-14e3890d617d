<?php (defined('BASEPATH')) OR exit('No direct script access allowed');

class M_model extends MY_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
        $this->gis = $this->load->database('gis', TRUE);

    }
	function search($title){
		$this->db->like('nmakun', $title , 'both');
		$this->db->order_by('nmakun', 'ASC');
		return $this->db->get('v_akun_bm')->result();
	}
    /*
     * To change this license header, choose License Headers in Project Properties.
     * To change this template file, choose Tools | Templates
     * and open the template in the editor.
     */

    public function get_by_id($table, $field, $id) {
        $this->db->from($table);
        $this->db->where($field, $id);
        $query = $this->db->get();

//echo $this->db->last_query();

        return $query->row();
    }

    public function save($table, $data) {
        $this->db->insert($table, $data);
     //   echo $this->db->last_query();
        return $this->db->insert_id();
    }

    public function cek_id($table, $data) {
       $query = $this->db->get_where($table, $data);
       
//       echo $this->db->last_query();
       
       //$row = $query->row();
        
//        $query = $this->db->query("SELECT `AUTO_INCREMENT` AS id
//                                    FROM INFORMATION_SCHEMA.TABLES
//                                    WHERE TABLE_SCHEMA = 'dbeplanningv3'
//                                    AND TABLE_NAME = '$table'");
//        $row = $query->row();

        return $query->row();
    }

    public function update($table, $where, $data) {
        $this->db->update($table, $where, $data);
        return $this->db->affected_rows();
    }

    public function delete_by_id($table, $field, $id) {
        $this->db->where($field, $id);
        $this->db->delete($table);
       $res = $this->db->affected_rows();
        return $res;
    }


    
    public function getInisialProv($id = null)
    {
        $this->db->select('inisial');
        $this->db->where('kd_prov', $id);
        $data = $this->db->get('aset_r_provinsi');
        
        return $data->row_array();
    }

    public function getInisialKabkot($id = null)
    {
        $this->db->select('inisial');
        $this->db->where('kd_kabkot', $id);
        $data = $this->db->get('aset_r_kabkota');
        
        return $data->row_array();
    }

    public function getAttachmen($id = null,$menu)
    {
        $this->db->select('rl.nama_layer, rl.id_layer,dn.nm_dok,dn.id_dokwp3wt,dn.txtidtahap61
                            ,dn.path');
        $this->db->where('rmp.alias', $menu);
        $this->db->join('r_layer rl', 'rl.id_layer = rrl.id_layer', 'left');
        $this->db->join('r_mod_peta rmp', 'rmp.kd_module = rrl.kd_module', 'left');
        $this->db->join('dok_wp3wt dn', 'dn.id_layer=rl.id_layer and dn.txtidtahap61 ='.$id, 'left');
        $this->db->order_by('rl.id_layer', 'asc');
        
        $data = $this->db->get('r_rmap_layer rrl');

        return $data->result();
    }

    public function getPathById($id)
    {
        $this->db->select('path');
        $this->db->where('id_dokwp3wt', $id);
        $data = $this->db->get('dok_wp3wt');
        return $data->row_array();
    }

    public function pfn($file)
    {
        // return $file;
        
        // echo "<pre>";
        // print_r ($file);
        // echo "</pre>";exit();
                
       $type = $file->features[0]->geometry->type;
       $geo = [];
       $ins=[];
            foreach ($file->features as $key => $value) {

                // if($value->properties->Tahun_Data != ''
                    // && $value->properties->Provcode != ''    
                    // && $value->properties->Kabucode != ''    
                    // && $value->properties->Kecacode != ''    
                    // && $value->properties->Provname != ''    
                    // && $value->properties->Kabuname != ''    
                    // && $value->properties->Kecaname != ''    
                // ){

                    $str = 'SRID=3395;'. $type.' Z(((';
                    $str2 =  '[[';
                    $ar =[];
                    foreach ($value->geometry->coordinates[0][0] as $key2 => $value2) {
                        $str .=@$value2[0].' '.@$value2[1].' '.@$value2[2].', ';
                    };
                    $str = mb_substr($str, 0, -1);
                    $str = mb_substr($str, 0, -1);
                    $str.=')))';

                    $a=[];
                    $g=[];
                    // $a['Tahun_Data']=$value->properties->Tahun_Data;
                    $a['OBJECTID']=$value->properties->OBJECTID;
                    $a['ObjID_']=$value->properties->ObjID_;
                    $a['ObjType_']=$value->properties->ObjType_;
                    $a['ObjYear_']=$value->properties->ObjYear_;
                    $a['WapName_']=$value->properties->WapName_;
                    $a['WakName_']=$value->properties->WakName_;
                    $a['WacName_']=$value->properties->WacName_;
                    $a['PfnID_']=$value->properties->PfnID_;
                    $a['PfnObjName_']=$value->properties->PfnObjName_;
                    $a['Luas_Area_']=$value->properties->Luas_Area_;
                    $a['PfnRemarks_']=$value->properties->PfnRemarks_;
                    $a['WadName_']=$value->properties->WadName_;
                    $a['PsnId_']=$value->properties->PsnId_;
                    $a['PsnObjname_']=$value->properties->PsnObjname_;
                    $a['PsnRemarks_']=$value->properties->PsnRemarks_;
                    $a['SHAPE_Area']=$value->properties->SHAPE_Area;
                    $a['SHAPE_Length']=$value->properties->SHAPE_Length;
                    $a['geom']=$str; 
                    // $g['type']=$type; 
                    // $g['coordinates']=$str2; 
                    // $g=json_encode($g);
                    // $g['coordinates']=$ar; 
                    // $g = json_encode($g);
                    // $sel = $this->db->query('SELECT ST_AsText(ST_GeomFromGeoJSON(\''.$g.'\')) As wkt')->result();
                    // $sel ='SELECT ST_AsText(ST_GeomFromGeoJSON(\''.$g.'\')) As wkt';
                    // echo $sel;
                    // $ori='SELECT ST_AsText(ST_GeomFromGeoJSON(\'{"type":"LineString","coordinates":[[1,2,3],[4,5,6],[7,8,9]]}\')) As wkt; ';
                    
                    
                    // echo $ori."\n".$sel;
                    
                    // array_push($geo,$g);
                    array_push($ins,$a);
                // }
            }

            
            
            foreach ($ins as $key => $value) {
                
                $where = [
                            "OBJECTID"=>$value['OBJECTID'],
                            "ObjID_"=>$value['ObjID_'],
                            "ObjType_"=>$value['ObjType_'],
                            "ObjYear_"=>$value['ObjYear_'],
                            "WapName_"=>$value['WapName_'],
                            "WakName_"=>$value['WakName_'],
                            "WacName_"=>$value['WacName_'],
                            "PfnID_"=>$value['PfnID_'],
                            "PfnObjName_"=>$value['PfnObjName_'],
                            "Luas_Area_"=>$value['Luas_Area_'],
                            "PfnRemarks_"=>$value['PfnRemarks_'],
                            "WadName_"=>$value['WadName_'],
                            "PsnId_"=>$value['PsnId_'],
                            "PsnObjname_"=>$value['PsnObjname_'],
                            "PsnRemarks_"=>$value['PsnRemarks_'],
                            "SHAPE_Area"=>$value['SHAPE_Area'],
                            "SHAPE_Length"=>$value['SHAPE_Length'],
                        ];
                        
                $this->gis->select('id');
                $this->gis->where($where);
                $cek = $this->gis->get('"wp3wt_pfn"')->result();
                echo $this->gis->last_query();
                
                echo "<pre>";
                print_r ($cek);
                echo "</pre>";exit();
                
                if (!empty($cek)) {
                    foreach ($cek as $key => $value) {
                        // $this->db->delete('"wp3wt_pfn"', array('id' => $value->id));
                        

                    }
                }
                
            }


            echo "<pre>";
            print_r ($ins);
            echo "</pre>";exit();
            
            return $this->db->insert_batch('wp3wt_pfn', $ins);
            // $this->db->insert('"npgt_kabkot_a"', $ins[0]);

    }


}
