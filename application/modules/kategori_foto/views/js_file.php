<script>
    var thangs = "<?php echo $this->session->konfig_tahun_ang;?>";
    var xhrdata = null;
    var table = null;

    var role = "<?php echo $this->session->users['id_user_group_real']; ?>";
    var roledesc = "<?php echo $this->session->users['role']; ?>";

    function clear_input() {
        $("#frm-tambah :input").val("");      
    }
   
    function listing() {
        table = $('#dt-server-processing').DataTable({
            "draw": 0,
            // "columnDefs": [{"orderable": true, "targets": [0, 1,2]}],
            "order": [[1, "asc"]],
            "processing": true,
            "serverSide": true,
            "ajax": { type: "POST", url: "<?php echo base_url();?>kategori_foto/ssp_paket","data": function ( d ) {
                d.<?php echo $this->security->get_csrf_token_name();?> = "<?php echo $this->security->get_csrf_hash();?>"
              
            }},
            "columnDefs": [
                {
                    "aTargets": [0],
                    "mRender": function (data, type, full,meta) {
                        // console.log(full)
                        return meta.row + meta.settings._iDisplayStart + 1;
                    }
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        return full[1];
                    }
                },
                {
                    "aTargets": [2],
                    "mRender": function (data, type, row) {
                        var id = row[0];
                        var html_button = [
                            "<button onclick= dtEditRow('" + id + "') class='btn btn-primary btn-xs'>",
                            "Edit",
                            "</button>",
                            "<button onclick= dtDeleteRow('" + id + "') class='btn btn-danger btn-xs'>",
                            "Hapus",
                            "</button>",
                            // "<button onclick= dtUpload(" + id + ","+kd_prov+","+tahun+") class='btn btn-warning btn-xs'>",

                            // "<button onclick= dtUpload(" + id + ") class='btn btn-warning btn-xs'>",
                            // "Upload",
                            // "</button>",
                            //"<button onclick= dtdetail('" + id + "') class='btn btn-warning btn-xs '><i class='fa fa-search'></i></button>"
                        ].join("\n");
                        return html_button;
                    }
                }
            ],
            "language": {
                "decimal": "",
                "emptyTable": "Data tidak ditemukan",
                "info": "Data _START_ s/d _END_ dari _TOTAL_",
                "infoEmpty": "Tidak ada data",
                "infoFiltered": "(tersaring dari _MAX_)",
                "infoPostFix": "",
                "thousands": ",",
                "lengthMenu": "_MENU_  data per halaman",
                "loadingRecords": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "processing": "'<img src='<?php echo base_url(); ?>assets/img/loader.gif'>'",
                "search": "Cari:",
                "zeroRecords": "Tidak ada data ditemukan",
                // "paginate": {
                //     "first": "<i class='fast backward ui icon'></i>",
                //     "last": "<i class='fast forward ui icon'></i>",
                //     "next": "<i class='step forward ui icon'></i>",
                //     "previous": "<i class='step backward ui icon'></i>"
                // },
                "aria": {
                    "sortAscending": ": aktifkan untuk mengurutkan naik",
                    "sortDescending": ": aktifkan untuk mengurutkan turun"
                }
            }
        });

        table.on('xhr', function () {
            xhrdata = table.ajax.json();
            //console.log(xhrdata);
        });
        //});
    }

    function dtDeleteRow(id,tahun) {
        // alert(tahun)
        Swal.fire({
        title: 'Apakah Anda Yakin?',
        text: "Data Ini Akan Dihapus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya!',
        cancelButtonText: 'Batal Hapus!',
        }).then((result) => {
            if (result.isConfirmed) {
                
        // var url = "<?php echo base_url(); ?>" + "pemegang_saham/ajax_delete/" + id + "/" + yearnow;
            var url = "<?php echo base_url(); ?>" + "kategori_foto/ajax_delete/" + id+'/'+tahun;
            var params = {"formData":{}, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params)
                .done(function (data) {
                    Swal.fire(
                            'Data Terhapus!',
                            'Data Berhasil dihapus.',
                            'success'
                        )
                var table = $('#dt-server-processing').DataTable();
                table.ajax.reload();
                })
                .fail(function () {
                    Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Hapus Data Gagal!',
                            })
                })
            }
        })
        
    }


 

    function dtEditRow(id) {
      
      function start(callback) { 
          $("#modal-edit").modal("show");
          
          data_selected = xhrdata.data.filter(x => x[0] == id)[0];
          
          $("#xkd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', this.value);
                console.log(this.value)
            });
  
          $('#xid').val(id);
      }
      

      function a(){
          if(role == 1){ 
            //   initCombobox('xkd_prov', 19);
            //   initCombobox('xkd_status', 22);
            //   initCombobox('xkd_kabkot', 20);
            // console.log(data_selected[8]);
            //   refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', data_selected[8]);
            //   refreshSelectboot2('xkd_kabkot', 20, 'kd_prov', data_selected[8]);

          }else{
              // refreshCombobox4('xid_ruas', 0, 'bujt', data_selected[20]);

          }

      };

      function b(){


          $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
            console.log(data_selected[9])
              $('#xkd_prov').val(data_selected[8]).selectpicker('refresh');      
              $('#xkd_kabkot').val(data_selected[7]).selectpicker('refresh');      
              $('#xkd_status').val(data_selected[5]).selectpicker('refresh');      
          });

          
          $(".decformat").keyup(function (event) {
              if (event.which >= 37 && event.which <= 40)
                  return;
              // format number
              $(this).val(function (index, value) {
              return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
              });
          });

         

        //   $('#xtahun_data').val(data_selected[3]);
          $('#xkategori').val(data_selected[1]);
      }

       $.when($.ajax(start())).then(a()).then(b());
    }

    function refreshSelectpicker() {
        // removeSelectpicker('kd_kabkot')
        $('select').selectpicker('refresh')
    }
   
    function dtTambahRow() {
        const myTimeout = setTimeout(refreshSelectpicker, 100);
        $('#modal-tambah').modal('show');
        // $('#frm-tambah').trigger('reset');
            clear_input()
           initCombobox('kd_prov', 19);
           initCombobox('kd_kabkot', 20);
           initCombobox('kd_status', 22);


            $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
            });

        $(".decformat").keyup(function (event) {
            if (event.which >= 37 && event.which <= 40)
                return;
            // format number
            $(this).val(function (index, value) {
            return value.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            });
        });

        
       

    }

    function simpanForm() {

        if($( "#frm-tambah" ).valid() === true){
            var xobj_usulan = {
                "kategori" : $( "#kategori" ).val(),
                "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"
            };

            
            var url = "<?php echo base_url(); ?>" + "kategori_foto/save_form";
            var params = {"formData": xobj_usulan, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            $.post(url, params).done(function (data) {
                
            Swal.fire(
                            'Sukses!',
                            'Data Tersimpan!',
                            'success'
                        )
            var table = $('#dt-server-processing').DataTable();
            table.ajax.reload();
            $('#modal-tambah').modal('hide');
                
            })
            .fail(function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Tambah Data Gagal!',
                })
                
            });
        } else {
            Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Tambah Data Gagal!Data gagal disimpan, harap periksa informasi di setiap field isian',
                })
            $(".error").css("color","red");
        }
    }

    function updateForm(){
        var id = $("#xid").val();
        var data_selected = xhrdata.data.filter(x => x[0] == id)[0];

        if($( "#frm-edit" ).valid() === true){

            var objmasterdetail = {
                    "id": id,
                    "kategori" : $( "#xkategori" ).val(),
                    "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"


            };
        //  console.log(objmasterdetail);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("kategori_foto/update_form") ?>";
            $.post(url, params).done(function (data) {
                if (data == '1'){
                    alert('Data Sudah Ada');
                } else {
                    Swal.fire(
                                'Sukses!',
                                'Data Tersimpan!',
                                'success'
                            )
                    var table = $('#dt-server-processing').DataTable();
                    table.ajax.reload();
                    $('#modal-edit').modal('hide');
                }
            })
            .fail(function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Update Data Gagal!',
                })
            });
        }else{
            Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Tambah Data Gagal!silahkan lihat informasi pada setiap field isian untuk memeriksa kesalahan entri data anda',
                })
        $(".error").css("color","red");
        }
    }

    function convertTo(datastring) {
        var str = datastring;
        var s1 = str.replace('{','[');
            
        return eval(s1.replace('}',']'));
    }
    
    function bind_combo_induk(thang, selval){
        var data = get_induk(thang);
        var objthang = JSON.parse(data);
        $("#kdinduk").empty();
        $('#kdinduk').append("<option value='' >" + "--Pilih--" + "</option>");
        for(var i=0; i<= objthang.length-1; i++){
            $("#kdinduk").append("<option value="+objthang[i].kdsatker+">"+objthang[i].nmsatker+"</option>");
        }
        if (selval){
            $("#kdinduk").val(selval);
        }
    }
    
    function get_induk(thang){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('kategori_foto/get_induk/') ?>" + thang,
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }




    
    function dtdetail(id){
        //console.log(xhrdata);

        var data_selected = xhrdata.data.filter(x => x[9] === id)[0];  //46

        var waydata = {
            thang: $("#fthang").val(),
            ibukota: data_selected[2],
            kd_prov:data_selected[0],
            kd_prov_bps: data_selected[3],
            kd_prov_irmsv3: data_selected[4],
            kd_prov_krisna:data_selected[5],
            kd_prov_rams: data_selected[6],
            kd_prov_rkakl: data_selected[7],
            kd_prov_sipro: data_selected[8],
            nama_prov: data_selected[1]

        }

        way.set('formData', waydata);
        $('#modalTitle').text('Detail');
//        $('#modeform').val('edit');
        $("input").prop('disabled', true);
	$("#hid").hide("slow");
	$("#hida").hide("slow");
        $('#modal-tambah').modal('show');
    }




   

    function close_alert() {
        $("#alert_information").css({display: "none"});
    }
    
    function bind_combo_thang(selval){
        var data = get_thang();
        var objthang = JSON.parse(data);
        for(var i=0; i<= objthang.length-1; i++){
            $("#fthang").append("<option value="+objthang[i].thang+">"+objthang[i].uraian+"</option>");
        }
        if (selval){
            $("#fthang").val(selval);
        }
    }
    
    function get_thang(){
        var x = null;
        $.ajax({
            type: "GET",
            async:false,
            url : "<?php echo base_url('kategori_foto/get_thang') ?>",
            success: function(response){
                x = response;
            },
            failure: function(errMsg) {
                alert(errMsg);
            }
        });
        return x;
    }
    var menu='';
    var id_neraca;
    function dtUpload(id) {
        menu = $("#alias_menu").val();
        id_neraca = id;
        // $("#id_up").val(id);
        // initCombobox('layer', 22);
        // $('#kd_prov').selectpicker('refresh')
        $("#kd_prov").removeClass("bootstrap-select")
        $("#kd_kabkot").removeClass("bootstrap-select")
        $('#kd_kabkot').empty()
        const myTimeout = setTimeout(refreshSelectpicker, 100);
        initComboboxTematik('layer',22,menu);
        initCombobox('kd_prov', 19);
        

        $("#kd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
            refreshSelectboot2('kd_kabkot', 20, 'kd_prov', this.value);
        });

        // var tab = $('#table_id2').DataTable();
        // tab.destroy()
        // $('#table_id2 td').empty();
        // listing_attachment2(id);
        $("#modal-download").modal("show");
    }



    var table_attachment = null;
    function listing_attachment2(id) {

        // var data_selected = jalanjson.data.filter(x => x[0] == id)[0];
     //   var xiduser = role;
        // var xthang = data_selected[1];
        // if ($.fn.dataTable.isDataTable('#table_id2')) {

        //     table_attachment = $('#table_id2').DataTable();
        // } else {

            table_attachment = $('#table_id2').DataTable({

                "draw": 0,
                "columnDefs": [{"orderable": true, "targets": [0]}],
                "order": [[0, "desc"]], 
                "processing": true,
                "serverSide": true,
                "ajax": {
                    type: "POST",
                    url: "<?php echo base_url(); ?>kategori_foto/ssp_attachment_file",
                    data: function (d) {
                        d.id = id;
                       d.menu = menu;
                        d.<?php echo $this->security->get_csrf_token_name(); ?> = "<?php echo $this->security->get_csrf_hash(); ?>";
                    }
                },
                "columnDefs": [
                    { 
                    "aTargets": [0],
                    "mRender": function (data, type, full) {
                        
                        return full[0];
                        // alert("ASd")
                    } 
                },
                {
                    "aTargets": [1],
                    "mRender": function (data, type, full) {
                        
                        return full[1];
                    }
                },
                
                {
                    "aTargets": [2],
                    "mRender": function (data, type, full) {
                        
                        return full[2];
                    }
                }, 
                {
                    "aTargets": [3],
                    "mRender": function (data, type, full) {
                        
                        return full[3];
                    }
                },
                    // { 
                    //     "aTargets": [0],
                    //     "mRender": function (data, type, full) {
                    //         var ico_class = get_extentsion_file(full[0]);
                    //         var html_icon = "<i class='" + ico_class + "' style='color:maroon;'></i>&nbsp";
                    //         var subs_img=full[0].substr(0,6);
                    //         if(ico_class == "feather icon-image"){
                    //             html_icon="<a target='_blank' class='fancybox' rel='group' href='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'><img class='img-responsive' style='width:40%;' src='<?php echo base_url();?>uploads/"+subs_img+"/"+full[0]+"'></a>";
                    //         }
                    //         return html_icon+"<br>"+full[3];
                    //         // alert("ASd")
                    //     } 
                    // },
                    // {
                    //     "aTargets": [1],
                    //     "mRender": function (data, type, full) {
                    //         console.log("full attachment");
                    //         console.log(full);
                    //         var data_full_attachment = full[0];
                    //         var dire = data_full_attachment.substr(0, 6);
                    //         var html_button = [
                    //             "<a target='_blank' class='btn btn-primary' href='<?php echo base_url(); ?>uploads/" + dire + "/" + data_full_attachment + "'><i class='feather icon-download'></i></a>",
                    //             "<a target='_blank' class='btn btn-danger' onclick=hapus_lampiran('" + full[1] + "')> <i class='feather icon-trash-2'></i></a>",
                    //         ].join("\n");
                    //         return html_button;
                    //     }
                    // }
                ],
                "language": {
                    "decimal": "",
                    "emptyTable": "Data tidak ditemukan",
                    "info": "Data _START_ s/d _END_ dari _TOTAL_",
                    "infoEmpty": "Tidak ada data",
                    "infoFiltered": "(tersaring dari _MAX_)",
                    "infoPostFix": "",
                    "thousands": ",",
                    "lengthMenu": "_MENU_  data per halaman",
                    "loadingRecords": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "processing": "<img src='<?php echo base_url(); ?>assets/img/loader.gif'>",
                    "search": "Cari:",
                    "zeroRecords": "Tidak ada data ditemukan",
                    "paginate": {
                        "first": "<i class='fast backward ui icon'></i>",
                        "last": "<i class='fast forward ui icon'></i>",
                        "next": "<i class='step forward ui icon'></i>",
                        "previous": "<i class='step backward ui icon'></i>"
                    },
                    "aria": {
                        "sortAscending": ": aktifkan untuk mengurutkan naik",
                        "sortDescending": ": aktifkan untuk mengurutkan turun"
                    }
                }
            });
            table_attachment.on('xhr', function () {
                xhrdata1 = table_attachment.ajax.json();
                console.log(xhrdata1);
            });

            $(".display").addClass("table table-bordered table-striped js-dataTable-full-pagination");
        }

    // }



    function hapus_lampiran(id) {
        Swal.fire({
        title: 'Apakah Anda Yakin?',
        text: "Data Ini Akan Dihapus!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Ya!',
        cancelButtonText: 'Batal Hapus!',
        }).then((result) => {
            if (result.isConfirmed) {
                
        
                var url = "<?php echo base_url(); ?>" + "kategori_foto/hps_lampiran/" + id;
                var params = {"formData": {}, "<?php echo $this->security->get_csrf_token_name(); ?>": "<?php echo $this->security->get_csrf_hash(); ?>"};
                $.post(url, params)
                        .done(function (data) {
                            Swal.fire(
                            'Data Terhapus!',
                            'Data Berhasil dihapus.',
                            'success'
                        )

                            var tlist_paket = $('#tlist_paket').DataTable();
                            tlist_paket.ajax.reload()
                            var tab = $('#table_id2').DataTable();
                            tab.ajax.reload();
                            var table = $('#dt-server-processing').DataTable();
                            table.ajax.reload();
                            ;
                        })
                        .fail(function () {
                            
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: 'Hapus Data Gagal!',
                            })
                        })
            }
        })
        
    }
    

    $(document).ready(function () {
        // bind_combo_thang(thangs);
            listing();
            $('.numberonly').keypress(function (e) {    
                var charCode = (e.which) ? e.which : event.keyCode    

                if (String.fromCharCode(charCode).match(/[^0-9]/g))    

                    return false;                        

            }); 
            $('.float-number').keypress(function(event) {
                if ((event.which != 46 || $(this).val().indexOf('.') != -1) && (event.which < 48 || event.which > 57)) {
                    event.preventDefault();
                }
            });

            // $('#id_bujt, #id_ruas, #id_jnsdana, #id_refbank, #id_pt').select2({
            //     dropdownParent: $('#modal-tambah'),
            //     // width: 'resolve',
            //     theme: 'classic'
            // });

            // $('#xid_bujt, #xid_ruas, #xid_jnsdana, #xid_refbank, #xid_pt').select2({
            //     dropdownParent: $('#modal-edit'),
            //     theme: 'classic',
            // });

            // $('#xid_refbank, #xid_pt').select2({
            //     tags: true
            // });

            $("#filess").change(function () {
                var fileExtension = ['zip'];
                if ($.inArray($(this).val().split('.').pop().toLowerCase(), fileExtension) == -1) {
                    Swal.fire(
                            'Gagal!',
                            'File harus berformat ZIP!',
                            'error'
                        )
                    $(this).val('');
                }
                
            });

            $('#submit').submit(function (e) {
            e.preventDefault();
            Swal.fire({
                    title: 'Loading...',
                    html: 'Memproses Data',
                    allowOutsideClick: true,
                    didOpen: () => {
                        Swal.showLoading()
                    }
            })
            var file = new FormData(this);
            setTimeout(function() {
                $.ajax({
                    // url: '<?php echo base_url(); ?>kategori_foto/up',
                    url: '<?php echo base_url(); ?>kategori_foto/insertShp',
                    type: "post",
                    data: file,
                    processData: false,
                    contentType: false,
                    cache: false,
                    async: false,
                    success: function (data) {
                        // $("select").val('--pilih--');
                        // $("select").selectpicker("refresh");
                        data=JSON.parse(data)
                        console.log(data.sts)
                        if(data.sts=='gagal'){
                            swal.close()
                            Swal.fire(
                                'Gagal!',
                                data.msg,
                                'error'
                            )
                        }else{

                            $("#filess").val('')
                            var tab = $('#table_id2').DataTable();
                            tab.ajax.reload();
                            var table = $('#dt-server-processing').DataTable();
                            table.ajax.reload();
                            swal.close()
                            Swal.fire(
                                'Sukses!',
                                'Data Tersimpan!',
                                'success'
                            )
                        }
                        
                    },error: function (jqXHR, exception) {
                        // console.log(jqXHR);
                        swal.close()
                        Swal.fire(
                            'Gagal!',
                            'Data Gagal Tersimpan!',
                            'error'
                        )
                    }
                });
            }, 100);
        });

            $('body div#modal-tambah.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker(); 
            });


            $('body div#modal-edit.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker();
            });

            $('body div#modal-download.modal').one('shown.bs.modal', function (e) { 
                $(this).find('div.modal-content select').selectpicker();
            });
            
           
            initCombobox('ikd_prov', 19);
            setTimeout(loadData,500);
            $("#ikd_prov").on("changed.bs.select", function(e, clickedIndex, newValue, oldValue) {
                refreshSelectboot2('ikd_kabkot', 20, 'kd_prov', this.value);
            });




        });

        function loadData(params) {
            $('#ikd_prov').selectpicker();
            $('#ikd_kabkot').selectpicker();
            $('#filterLayer').selectpicker();
            
        }

        function filterChange(val) {
            loadData()
            $('#dt-server-processing').dataTable().fnClearTable();
            $('#dt-server-processing').dataTable().fnDestroy();
            listing()
        }



        function initComboboxTematik(divname, refindex,parwhere,$rev = 1) {


            var url = null;
            if ($rev === 1) {
                url = WGI_APP_BASE_URL + "lookup/fieldlooktematik/" + refindex+"/"+parwhere;
            } else {
                url = WGI_APP_BASE_URL + "lookup/fieldlooktematik/" + refindex +"/"+parwhere +"/" + 0;
            }
                // return false;
                wgiAjaxCache(url, function(ajaxdata) {
                    jdata = JSON.parse(ajaxdata);
                    $('#' + divname).empty();
                    // $('#' + divname).append(new Option("--Pilih--", ""));
                    $.each(jdata, function(i, el) {
                        $('#' + divname).append(new Option(el.nama_layer,el.nama_layer+'&&'+el.no+'&&'+el.alias+'&&'+el.id_layer));
                    });
                    $('#' + divname).selectpicker('refresh')

                });
            }


</script>