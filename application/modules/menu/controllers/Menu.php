<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Menu extends CI_Controller {

    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));
        $this->load->library('upload');
        $this->load->helper('download');
        //$this->load->library('upload', $config);
        //$this->upload->initialize($config);
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        $this->load->database();
        $this->load->helper('dtssp'); //datatable server side processing helper
    }

    public function index() {
        $data = array();



        $group = $this->session->users['id_user_group'];
        $armenu = $this->db->where(array('id_user_group' => $group, 'parent' => NULL))->get('v_group_module')->result_array();
        $data['dmenu'] = "<ul class='nav-main'>";
        foreach ($armenu as $menu) {
            $submenu = $this->db->where(array('id_user_group' => $group, 'parent' => $menu['kode_module']))->get('v_group_module')->result_array();
            if (count($submenu) == 0) {
                $data['dmenu'] .= "<li> <a href='" . base_url('/') . $menu['url'] . "'><i class='si si-speedometer'></i><span class='sidebar-mini-hide'>" . $menu['nama_module'] . "</span></a></li>";
            } else {
                $data['dmenu'] .= "<a class='nav-submenu' data-toggle='nav-submenu' href='" . base_url('/') . $menu['url'] . "'><i class='si si-badge'></i><span class='sidebar-mini-hide'>" . $menu['nama_module'] . "</span></a>";
            }
            $data['dmenu'] .= "<ul>";
            foreach ($submenu as $x) {
                $data['dmenu'] .= "<li><a href='" . base_url('/') . $x['url'] . "'>" . $x['nama_module'] . "</span></a> </li>";
            }
            $data['dmenu'] .= "</ul>";
            $data['dmenu'] .= "</li>";
        }
        $data['dmenu'] .= "</ul>";


        $this->template->load('default_layout', 'contents' , 'index', $data);
       
    }

}
