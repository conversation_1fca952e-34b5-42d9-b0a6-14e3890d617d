<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Fileupload extends MY_Controller {

    var $data;

    public function __construct() {
        parent::__construct();
        $this->load->helper(array('form', 'url', 'file'));

        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->data = array("id_usulan" => $this->input->get("id_usulan"),
            "id_user" => $this->input->get("id_user"),
            "thang" => $this->input->get("thang")
        );
        $ci = & get_instance();
        $ci->load->database();
    }

    public function index() {
        $data = $this->data;
        //print_r($data);

        $this->load->view('upload/modal-upload', $data);
    }

    public function do_upload($id_user = "", $id_usulan = "", $thang = "") {



        //echo $id_user."--".$id_usulan."--".$thang;

        $upload_path_url = base_url() . 'uploads/';

        $config['upload_path'] = FCPATH . 'uploads/';
        $config['allowed_types'] = 'jpg|jpeg|png|gif|pdf|docx|doc|xls|xlsx|rar|zip|ppt|pptx';
        $config['max_size'] = '60000';

        $this->load->library('upload', $config);

        if (!$this->upload->do_upload()) {

            $url = "http://localhost:12892/usulan/dpr/attachmentusulandpr/" . $id_usulan;
            $json = $this->get_data_module($url);

            $decoded = json_decode($json);
            $datas = $decoded->data;

            $foundFiles = array();
            $f = 0;
            foreach ($datas as $row) {
                $fileName = $row->usulan_attachment;
                $existingFile = get_file_info($config['upload_path'] . $fileName);
                $pathinfo = pathinfo($fileName);
                $fileext = $pathinfo['extension'];

                //echo $fileext;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
                if ($fileName != 'thumbs') {//Skip over thumbs directory
                    //set the data for the json array   
                    $foundFiles[$f]['name'] = $fileName;
                    $foundFiles[$f]['size'] = $existingFile['size'];
                    // $foundFiles[$f]['ext'] = $pathinfo['extension'];
                    $foundFiles[$f]['url'] = $upload_path_url . $fileName;
                    if ($fileext == 'pdf' || $fileext == 'doc' || $fileext == 'xls' || $fileext == 'ppt' || $fileext == 'docx' || $fileext == 'xlsx' || $fileext == 'pptx' || $fileext == 'rar' || $fileext == 'zip') {
                        $thumbURL = $upload_path_url . 'thumbs/' . $fileext . '.png';
                    } else {
                        $thumbURL = $upload_path_url . 'thumbs/' . $fileName;
                    }
                    $foundFiles[$f]['thumbnailUrl'] = $thumbURL;
                    $foundFiles[$f]['deleteUrl'] = base_url() . '/uploadpemda/fileupload/deleteImage/' . $id_user . '/' . $id_usulan . '/' . $thang . '/' . $fileName . '/' . $fileext;
                    $foundFiles[$f]['deleteType'] = 'DELETE';
                    $foundFiles[$f]['error'] = null;

                    $f++;
                }

                //print_r($foundFiles);
            }
            $this->output->set_content_type('application/json')->set_output(json_encode(array('files' => $foundFiles)));
        } else {



            $headname = explode('.', $_FILES["userfile"]["name"])[0];
            $path_parts = pathinfo($_FILES["userfile"]["name"]);
            $data = $this->upload->data();

            $newfile_name = preg_replace('/[^A-Za-z0-9.]/', "", $headname);
            $namabaru = $newfile_name . '_' . round(microtime(true) * 100) . '.' . $path_parts['extension'];

            rename($data['full_path'], $data['file_path'] . $namabaru);

            /*
             * Array
              (
              [file_name] => png1.jpg
              [file_type] => image/jpeg
              [file_path] => /home/<USER>/public_html/uploads/
              [full_path] => /home/<USER>/public_html/uploads/png1.jpg
              [raw_name] => png1
              [orig_name] => png.jpg
              [client_name] => png.jpg
              [file_ext] => .jpg
              [file_size] => 456.93
              [is_image] => 1
              [image_width] => 1198
              [image_height] => 1166
              [image_type] => jpeg
              [image_size_str] => width="1198" height="1166"
              )
             */
            // to re-size for thumbnail images un-comment and set path here and in json array
            $config = array();
            $config['file_name'] = $namabaru;
            $config['image_library'] = 'gd2';
            $config['source_image'] = $data['file_path'] . $namabaru;
            $config['create_thumb'] = TRUE;
            $config['new_image'] = $data['file_path'] . 'thumbs/';
            $config['maintain_ratio'] = TRUE;
            $config['thumb_marker'] = '';
            $config['width'] = 75;
            $config['height'] = 50;
            $this->load->library('image_lib', $config);
            $this->image_lib->resize();

            //set the data for the json array
            $info = new StdClass;
            $info->name = $namabaru;
            $info->size = $data['file_size'] * 1024;
            $info->type = $data['file_type'];
            $info->url = $upload_path_url . $info->name;
            // I set this to original file since I did not create thumbs.  change to thumbnail directory if you do = $upload_path_url .'/thumbs' .$data['file_name']
            $fileext = $path_parts['extension'];

            if ($fileext == 'pdf' || $fileext == 'doc' || $fileext == 'xls' || $fileext == 'ppt' || $fileext == 'docx' || $fileext == 'xlsx' || $fileext == 'pptx' || $fileext == 'rar' || $fileext == 'zip') {
                $thumbURL = $upload_path_url . 'thumbs/' . $fileext . '.png';
            } else {
                $thumbURL = $upload_path_url . 'thumbs/' . $info->name;
            }
            $info->thumbnailUrl = $thumbURL;
            $info->deleteUrl = base_url() . '/uploadpemda/fileupload/deleteImage/' . $id_user . '/' . $id_usulan . '/' . $thang . '/' . $info->name . '/' . $fileext;
            $info->deleteType = 'DELETE';
            $info->error = null;

            $files[] = $info;

            if (!empty($info->name)) {
                $data_raw['usulan_attachment'] = $info->name;
                $data_raw['id_user'] = $id_user;
                $data_raw['id_usulan'] = $id_usulan;
                $data_raw['thang'] = $thang;
            }

            $this->insert_module('http://localhost:12892/usulan/attachment/add', $data_raw);



            //print_r($files); die();
            //this is why we put this in the constants to pass only json data
            if (IS_AJAX) {
                echo json_encode(array("files" => $files));
                //this has to be the only data returned or you will get an error.
                //if you don't give this a json array it will give you a Empty file upload result error
                //it you set this without the if(IS_AJAX)...else... you get ERROR:TRUE (my experience anyway)
                // so that this will still work if javascript is not enabled
            } else {
                $file_data['upload_data'] = $this->upload->data();
                echo "Upload success";
                //$this->load->view('fileupload/upload_success', $file_data);
            }
        }
    }

    public function deleteImage($id_user, $id_usulan, $thang, $file, $fileext) {//gets the job done but you might want to add error checking and security
        $success = unlink(FCPATH . 'uploads/' . $file);

        $success = $this->db->delete('usulan_paket_attachment_dpr', array('usulan_attachment' => $file,
            'id_usulan' => $id_usulan));





        if ($fileext == 'pdf' || $fileext == 'doc' || $fileext == 'xls' || $fileext == 'ppt' || $fileext == 'docx' || $fileext == 'xlsx' || $fileext == 'pptx' || $fileext == 'rar' || $fileext == 'zip') {
            $success = '';
        } else {
            $success = unlink(FCPATH . 'uploads/thumbs/' . $file);
        }

        //info to see if it is doing what it is supposed to
        $info = new StdClass;
        $info->sucess = $success;
        $info->path = base_url() . 'uploads/' . $file;
        $info->file = is_file(FCPATH . 'uploads/' . $file);

        if (!empty($file)) {
            $data_raw['id_user'] = $id_user;
            $data_raw['id_usulan'] = $id_usulan;
            $data_raw['thang'] = $thang;
            $data_raw['usulan_attachment'] = $file;
        }

        $this->insert_module('http://localhost:12892/usulan/attachment/erase', $data_raw);

        if (IS_AJAX) {
            //I don't think it matters if this is set but good for error checking in the console/firebug
            echo json_encode(array($info));
        } else {
            //here you will need to decide what you want to show for a successful delete        
            $file_data['delete_data'] = $file;
            //$this->load->view('fileupload/delete_success', $file_data);
            echo "Delete success";
        }
    }

    // public function getfileupload($id)
    // {
    //      $url="http://localhost:12892/usulan/dpr/attachmentusulandpr/".$id;
    //      echo $this->get_data_module($url);
    // }
}
