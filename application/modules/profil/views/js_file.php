<script>
   

    function updateForm(){
        event.preventDefault()
        var profil = $('#summernote').summernote('code');
        // profil = btoa(profil)
        // profil= profil.replace('xss="removed"', '');
        // console.log(profil)
        // return false;
        var objmasterdetail = {
            
            "profil" : profil, 
            "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"
            
            
        };
         console.log(objmasterdetail);
            var params = {"formData": objmasterdetail, "<?php echo $this->security->get_csrf_token_name(); ?>":"<?php echo $this->security->get_csrf_hash(); ?>"};
            var url = "<?php echo base_url("profil/update_form") ?>";
            $.post(url, params).done(function (data) {
                Swal.fire(
                            'Data Berubah!',
                            'Data Berhasil diubah.',
                            'success'
                        )
                data=JSON.parse(data)
                $('#divShow').html(data.data)
                $('#summernote').html(data.data)
                // console.log(data.data)
                $('#btnEdit').css('display','block')
                $('#divShow').css('display','block')
                $('#btnSimpan').css('display','none')
                $('#divSummernote').css('display','none')
            })
            .fail(function () {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Data Gagal Diupdate!',
                })
            });
     
    }

    function edit() {
        $('#btnEdit').css('display','none')
        $('#divShow').css('display','none')
        $('#btnSimpan').css('display','block')
        $('#divSummernote').css('display','block')
    }


    $(document).ready(function() {
        $('#summernote').summernote({
            
        });
    });

    

</script>