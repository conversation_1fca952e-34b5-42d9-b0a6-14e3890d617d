<!-- <script src="<?=base_url()?>assets/ckeditor/ckeditor.js"></script>
<script src="<?=base_url()?>assets/ckeditor/samples/js/sample.js"></script>
<link rel="stylesheet" href="<?=base_url()?>assets/ckeditor/samples/css/samples.css">
<link rel="stylesheet" href="<?=base_url()?>assets/ckeditor/samples/toolbarconfigurator/lib/codemirror/neo.css"> -->

<link rel="stylesheet" href="<?php echo base_url() ?>assets/summernote/summernote.css">
<script src="<?php echo base_url() ?>assets/summernote/summernote.js"></script>


<!-- <script type="text/javascript" src="//code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" />
    <script type="text/javascript" src="cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script> -->

    <!-- <link href="summernote-bs5.css" rel="stylesheet">
    <script src="summernote-bs5.js"></script> -->

    <style>
        .note-popover.popover{
            display:none;
        }
    </style>
<div class="page-body">
    
    <div class="card">
    <?php 

?>
        <div class="card-header">

            <h5><?=$title;?></h5>
        </div>
        <div>

        <button id="btnEdit" title="Edit" id="btnEdit" class="btn btn-warning waves-effect waves-light " style="margin-right:10px;margin-bottom:10px;float:right;border-radius:10%;padding :10px" onclick="edit()"><i class="fa-regular fa-3x fa-pen-to-square"></i></button>
        <button id="btnSimpan" title="Simpan" id="btnSimpan" class="btn btn-primary waves-effect waves-light " style="margin-right:10px;margin-bottom:10px;float:right;display:none;border-radius:10%;padding :10px" onclick="updateForm()"><i class="fa-regular fa-4x fa-floppy-disk"></i></button>
        </div>
          
        <form class="form-horizontal" id="frm-edit">
            <!-- <div class="card-block">
                <div id="editor">
                      <?=$data['profil']?>  
                </div>
            </div> -->
            <div class="card border" id="divShow" style="margin:20px;border-radius:10px;padding:10px">
                <?=$data['profil']?>
            </div>
            <div class="container" id="divSummernote" style="display:none">

                <textarea id="summernote" name="editordata"><?=$data['profil']?> </textarea>
            </div>
        </form>

    </div>
    
</div>   




<?php echo $jv_script; ?>


    <script>
        
	// initSample();
</script>