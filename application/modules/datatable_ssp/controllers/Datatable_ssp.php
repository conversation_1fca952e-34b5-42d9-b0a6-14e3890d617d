<?php

class Datatable_ssp extends MY_Controller {

    public function __construct() {
        header("Access-Control-Allow-Origin: *");

        parent::__construct();
        $this->load->helper('url');
        $this->load->library('session');
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }
        
        $this->load->helper('dtssp');
    }

    
    public function index($id_ssp) {
        
        
        // DB table to use
        $table = 'datatables_demo';

        // Table's primary key
        $primaryKey = 'id';

        // Array of database columns which should be read and sent back to DataTables.
        // The `db` parameter represents the column name in the database, while the `dt`
        // parameter represents the DataTables column identifier. In this case simple
        // indexes
        $columns = array(
            array('db' => 'first_name', 'dt' => 0),
            array('db' => 'last_name', 'dt' => 1),
            array('db' => 'position', 'dt' => 2),
            array('db' => 'office', 'dt' => 3),
            array(
                'db' => 'start_date',
                'dt' => 4,
                'formatter' => function( $d, $row ) {
                    return date('jS M y', strtotime($d));
                }
            ),
            array(
                'db' => 'salary',
                'dt' => 5,
                'formatter' => function( $d, $row ) {
                    return '$' . number_format($d);
                }
            )
        );


        datatable_ssp($table, $primaryKey, $columns);        
        
    }

    
}
  