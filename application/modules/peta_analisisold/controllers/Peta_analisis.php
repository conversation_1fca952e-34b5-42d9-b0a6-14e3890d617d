<?php

defined('BASEPATH') OR exit('No direct script access allowed');

include_once APPPATH . "/vendor/autoload.php";
require_once(APPPATH . "third_party/PFBC/Form.php");

use \diversen\gps;

class Peta_analisis extends MY_Controller {

    /**
     * Index Page for this controller.
     *
     * Maps to the following URL
     * 		http://example.com//welcome
     * 	- or -
     * 		http://example.com//welcome/index
     * 	- or -
     * Since this controller is set as the default controller in
     * config/routes.php, it's displayed at http://example.com/
     *
     * So any other public methods not prefixed with an underscore will
     * map to //welcome/<method_name>
     * @see https://codeigniter.com/user_guide/general/urls.html
     */
    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

        if( !isset($_SERVER['HTTP_REFERER'])) {
            redirect('page_not_found');
        }
        if ($this->session->has_userdata('users') == false) {
            redirect('login');
        }

        $this->load->database();
        $this->load->model('M_model');
        $this->load->helper('dtssp');

        $this->load->library('wgisitia');
        if (!$this->wgisitia->isallow($this->uri->uri_string()))
            header('location:' . base_url());
    }
    
    public function index($tahaps = '', $idpak = '') {
        if ($tahaps == '') {
            $tap = $this->session->konfig_kd_tahapan;
        } else {
            $tap = $tahaps;
        }

        header("Access-Control-Allow-Origin: *");
        $data = array();

        $title = "Peta Analisis";
        $js_file = $this->load->view('peta_analisis/js_file', '', true);
        $kontak = $this->db->get('landing_page')->row_array();
        $modal_tambah = $this->load->view('peta_analisis/modal_tambah', '', true);
        $modal_edit = $this->load->view('peta_analisis/modal_edit', '', true);
        
        //   --> explode uri ambil namamodule sebagai key ke tabel r_mod_peta
        $data = array(/* "modal_filter" => $modal_filter, */
            
            "title" => $title,
            "modal_tambah" => $modal_tambah,
            "modal_edit" => $modal_edit,
            "jv_script" => $js_file,
            "data" => $kontak
        );
        
        $this->load->view('index', $data);
    }

    public function save_form() {

        $role = $this->session->userdata('users')['id_user_group'];
        $pelaksana ='';
        if ($role == '7' || $role == '8') {
            $pelaksana = 'Kantor Pertanahan';
        }elseif ($role == '9' || $role == '10') {
            $pelaksana = 'Kantor Wilayah BPN';
        }
        

        $data = [ 
            'wadmpr' => $this->input->post('wadmpr',true),
            'wadmkk' => $this->input->post('wadmkk',true),
            'lokasi_surat' => $this->input->post('wadmkk',true),
            'wadmkc' => $this->input->post('wadmkc',true), 
            'wadmkd' => $this->input->post('wadmkd',true),  
            'no_berkas' => $this->input->post('no_berkas',true),
            'tanggal_surat' => $this->input->post('tanggal_surat',true), 
            'nama_pemohon' => $this->input->post('nama_pemohon',true),
            'alamat' => $this->input->post('alamat',true), 
            'nik' => $this->input->post('nik',true),
            'jabatan_pemohon' => $this->input->post('jabatan_pemohon',true),
            'id_subjek' => $this->input->post('id_subjek',true),
            'atas_nama' => $this->input->post('atas_nama',true), 
            'luas_tanah' => $this->input->post('luas_tanah',true),
            'kdppum' => $this->input->post('kdppum',true),
            'kdpkab' => $this->input->post('kdpkab',true),
            'kdcpum' => $this->input->post('kdcpum',true),
            'kdepum' => $this->input->post('kdepum',true),
            'id_tema' => $this->input->post('id_tema',true),
            'kd_format' => $this->input->post('kd_format',true),
            'keperluan' => $this->input->post('keperluan',true), 
            'keterangan_tambahan' => $this->input->post('keterangan_tambahan',true),
            'pelaksana' => $pelaksana
             
        ];

        $ins = $this->db->insert('form_petanalisis', $data);
        if ($ins) {
            
            echo json_encode(['sts' => "sukses", 'msg' => 'Data Tersimpan!']);
        }else{
            
            echo json_encode(['sts' => "gagal", 'msg' => 'gagal SImpan Data!']);
        }

    }


    function update_form() {
        // $this->wgisitia->handle_removed($param);
        $param = $this->input->post('formData', false);
        
        // echo "<pre>";
        // print_r ($this->input->post('formData', FALSE));
        // echo "</pre>";
        // strip_tags($_POST[''], '<p><a>');
        // exit();
        
        
        $data_detail = [ 
            "id_kategori" => $this->input->post("formData")["id_kategori"] ,
            "url" => $this->input->post("formData")["url"] 
        ];

        $this->db->where('id', $param["id"]);
        $this->db->update('vidio', $data_detail);

        echo json_encode(array("status" => TRUE,"data"=>$param["id"]));
    }

    
    public function ajax_delete($id) {

        $this->M_model->delete_by_id('peta_analisis','id', $id);
        echo json_encode(array("status" => TRUE));
    }

    public function ssp_paket() {
      
        $table = 'v_print_analisis';
        $primaryKey = 'id_form';         
         
        $columns = array(
            array('db' => 'id_form', 'dt' => 0),
            array('db' => 'no_berkas', 'dt' => 1),
            array('db' => 'lokasi_surat', 'dt' => 2),
            array('db' => 'tanggal_surat', 'dt' => 3),
            array('db' => 'nama_pemohon', 'dt' => 4),
            array('db' => 'jabatan_pemohon', 'dt' => 5),
            array('db' => 'alamat', 'dt' => 6),
            array('db' => 'nik', 'dt' => 7),
            array('db' => 'id_subjek', 'dt' => 8),
            array('db' => 'luas_tanah', 'dt' => 9),
            array('db' => 'id_tema', 'dt' => 10),
            array('db' => 'kd_format', 'dt' => 11),
            array('db' => 'kdppum', 'dt' => 12),
            array('db' => 'kdpkab', 'dt' => 13),
            array('db' => 'kdcpum', 'dt' => 14),
            array('db' => 'kdepum', 'dt' => 15),
            array('db' => 'wadmpr', 'dt' => 16),
            array('db' => 'wadmkk', 'dt' => 17),
            array('db' => 'wadmkc', 'dt' => 18),
            array('db' => 'wadmkd', 'dt' => 19),
            array('db' => 'keperluan', 'dt' => 20),
            array('db' => 'keterangan_tambahan', 'dt' => 21),
            array('db' => 'atas_nama', 'dt' => 22),
            array('db' => 'pelaksana', 'dt' => 23),
            array('db' => 'format', 'dt' => 24),
            array('db' => 'nama_subjek','dt' => 25)
        );
       

        datatable_ssp($table, $primaryKey, $columns);

        
    }


}
