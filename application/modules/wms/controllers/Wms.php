<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Wms extends MY_Controller {

    public function __construct() {
        // created the construct so that the helpers, libraries, models can be loaded all through this controller
        parent::__construct();

        $this->load->helper('url');
        $this->load->library('session');
        $this->load->helper(array('form', 'url', 'file'));

       

        $this->load->database();
        // // $this->load->model('M_model');
        // $this->load->helper('dtssp');

    }


    private function as_int($s) {
        return preg_replace("/[^0-9]/", "",$s);
    }

    private function as_double($s) {
        return preg_replace('#[^0-9\.-]#', '', $s);
        // return preg_replace("/[^0-9]./", "",$s); //ini ngaco :D
    }

    private function abox($bbox) {
        $ret = explode('~', $bbox);
        if (sizeof($ret) === 4) {
            $ret[0] = floatval($this->as_double($ret[0]));
            $ret[1] = floatval($this->as_double($ret[1]));
            $ret[2] = floatval($this->as_double($ret[2]));
            $ret[3] = floatval($this->as_double($ret[3]));
            return $ret;
        }
        return null;        
    }

 
    //$arr => original array
    //$set => array containing old keys as keys and new keys as values
    function recursive_change_key($arr, $set) {
        if (is_array($arr) && is_array($set)) {
    		$newArr = array();
    		foreach ($arr as $k => $v) {
    		    $key = array_key_exists( $k, $set) ? $set[$k] : $k;
    		    $newArr[$key] = is_array($v) ? recursive_change_key($v, $set) : $v;
    		}
    		return $newArr;
    	}
    	return $arr;    
    }

    // function change_keys($array,$chgkey) {
    //     // print_r($chgkey);
    //     // //$array[0][$new]=$array[0][$old];
    //     // unset($array[0][$old]);

    //     return $array;

    // }

    public function outFields() {
           

        return $arrFields;
    }
 




    public function mobile_info($layer, $lon, $lat) {

            // $abox = $this->abox($bbox);
            // var_dump($abox);
            //echo $abox;
            // if (!$abox) { echo '{"data": null}'; return; }
            //if ($abox[0] > $abox[2]) { echo '{"data": null}'; return; }

           //$bbox = trim(str_replace('~', '%2C', $bbox));

           
            
        //http://103.6.53.254:4980/geoserver/pgt/wms?REQUEST=GetFeatureInfo&SERVICE=WMS&SRS=EPSG%3A4326&STYLES=&TRANSPARENT=true&VERSION=1.1.1&FORMAT=image%2Fpng8&BBOX=107.44628906250001%2C-2.9101249120129014%2C118.87207031250001%2C3.6778915094650726&HEIGHT=600&WIDTH=1040&LAYERS=pgt%3Awp3wt_penataan_perbatasan&QUERY_LAYERS=pgt%3Awp3wt_penataan_perbatasan&INFO_FORMAT=application%2Fjson&X=471&Y=201
            
            // $bbox = ($lon - 0.0000000001) . ',' . ($lat - 0.0000000001) . ',' . ($lon + 0.0000000001) . ',' . ($lat + 0.0000000001);

            $bbox = ($lon - 0.01) . ',' . ($lat - 0.01) . ',' . ($lon + 0.01) . ',' . ($lat + 0.01);

           // echo $bbox; die();
            $height = 256;
            $width = 256;
            $x = 128;
            $y = 128;

            $url  = 'http://localhost/geoserver/pgt/wms?REQUEST=GetFeatureInfo';
            $url .= '&SERVICE=WMS';
            $url .= '&SRS=EPSG%3A4326';
            // $url .= '&STYLES=pgt%3A'.$layer;
            $url .= '&STYLES=';
            $url .= '&TRANSPARENT=true';
            $url .= '&VERSION=1.1.1';
            $url .= '&FORMAT=image%2Fpng8';        
            $url .= '&BBOX='.$bbox;
            $url .= '&HEIGHT='.$height;
            $url .= '&WIDTH='.$width;
            $url .= '&LAYERS='.$layer;
            $url .= '&QUERY_LAYERS='.$layer;
            $url .= '&INFO_FORMAT=application%2Fjson';
            $url .= '&X='.$x;
            $url .= '&Y='.$y;
            $url .= '&FEATURE_COUNT=50';
            $url .= '&BUFFER=10';
            //$url .= '&FIELDS='

           $data = file_get_contents($url);


        //    echo($data); die();


           $arrData = json_decode($data);

           //print_r($arrData); die();

          //if() echo $data; die();

        //    if(empty($arrData)) {
        //      $jsonData = [];
        //    } else {
        //      $jsonData = $arrData;
        //    }  
           
          //print_r($jsonData);
          

          //$data = str_replace(array("\t","\n"), "", $jsonData);


         //echo json_encode($data); die();

          //echo $json; die();

         //print_r($jsonData->features[0]->properties);  die();

        // print_r($arrData->features); die();

         

         if(empty($arrData->features)) {
            echo '{"data":null}'; return;
         } else {
            $arr = array();
            foreach($arrData->features[0]->properties as $key => $val)
            {
               
                $arr[$key] = $val;

            }

            $arrFilter = [
            "gid",
            "id",
            "objectid", 
            "objid", 
            "objek",
            "namobj",
            "remark",
            "kdpbps",
            "fcode",
            "uupp",
            "srs_id",
            "lcode",
            "metadata",
            "kdebps",
            "kdepum",
            "kdcbps",
            "kdcbps",
            "kdcpum",
            "kdbbps",
            "kdbpum",
            "kdbpum",
            "wiadkd",
            "wiadkc",
            "wiadkk",
            "wiadpr",
            "shape_leng", 
            "shape_area", 
            "wacname", 
            "wakname", 
            "wapname", 
            "wadname",
            "objtype",
            "objyear",
            "kdpkab",
            "kdppum",
            "ptnid",
            "pola_ruang",
            "kws_hutan",
            "pt",
            "nama_pul_1",
            "nama_pul_2",
            "nama_pul_3",
            "verified",
            "ket_rtrw",
            "verified_b",
            "tipadm",
            "gname_rek",
            "glabel",
            "fitcode",
            "idsn",
            "gcode50",
            "gname50",
            "gcode100",
            "gcode25",
            "kode",
            "gname25",
            "layer",
            "elevation",
            "gqname25",
            "id_masterdata",
            "hcode",
            "toponimi",
            "gm_type",
            "kkcode",
            "kfcode",
            "kscode",
            "kkname",
            "kfname",
            "ocode",
            "oname_rek",
            "kode_pt",
            "qcode100",
            "qcode25",
            "qname50",
            "qcode50",
            "qlabel",
            "qname_rek",
            "qname100",
            "qcode100",
            "ktrsddlmta",
            "wcode",
            "wname_rek",
            "wlabel",
            "id_masterd",
            "su",
           "tglterbit", 
           "tglakhir", 
           "guna_usaha", 
           "est_prior", 
           "sumber", 
           "coding", 
           "kom_utama", 
           "kom_lain", 
           "produksi", 
           "penguasaan", 
           "jaraktanam", 
           "pkomoditas", 
           "jenistanah", 
           "jnspetani", 
           "jpts", 
           "itm", 
           "konflik", 
           "hat_ket", 
           "rd", 
           "rd_s", 
           "rd_p", 
           "ap", 
           "ap_s", 
           "lp2b", 
           "lp2b_s", 
           "ppb", 
           "ppb_s", 
           "kh", 
           "kh_s", 
           "a", 
           "b", 
           "c", 
           "tp_c", 
           "k_c", 
           "ocr", 
           "oca",
           "v_reclass",
           "tipologi",
           "hat",
           "q_reclass",
           "w_reclass",
           "w_s",
           "oname_rek",
           "wname_rek",  
           "oname20", 
           "qname20", 
           "qname5", 
           "qname10", 
           "qname25", 
           "wname20",
           "wadmkd",
           "oname10",
           "tkt_kritis",
           "tk_kritis",
           "pmnid",
           "psnid",
           "pfnid",
           "x",
           "y",
           "gq_rek",
           "vnamew",
           "vnameo",
           "kdpkec",
           "qcode",
           "gcode10",
           "qcode10",
           "gcode20",
           "qcode20",
           "gname20",
           "gq",
           "qlable",
           "perubahan",
           "glable",
           "hgu",
           "koordinat",
           "alamat_prs",
           "luas_ilok",
           "luas_ptp",
           "kode_kbli",
           "prof_ktp",
           "prof_riil",
           "status",
           "mark",
           "ppkt",
           "rtrw",
           "b_code",
           "u_code",
           "u_label",
           "x_code",
           "x_label",
           "d_code",
           "d_label",
           "e_code",
           "e_label",
           "__gid",
           "orde01",
           "orde02",
           "orde03",
           "orde04",
           "jnsrpr",
           "kkop_1",
           "kp2b_2",
           "krb_03",
           "ket_obj",
           "esri_oid"
           ];

           $layer = trim(str_replace('%20', ' ', $layer));
           $layer = explode(':', $layer)[1];


           if($layer == 'ptp_pk_p3t' || $layer == 'ptp_kppr_berusaha' || $layer == 'ptp_kppr_non_berusaha' || $layer == 'ptp_tnh_timbul' || $layer == 'ptp_kppr_stranas' || $layer == 'wp3wt_tnh_timbul'){
            $arrFilter[] = "luas_m2";
            }

            // if($layer !== 'wp3wt_ppkt'){
            //     $arrFilter[] = "pt";
            // }
        
            $columndata = [];

            foreach ($arr as $key => &$value) {
                if($value==null) $value = '-';
                if(in_array($key, $arrFilter)){
                   unset($key);
                }else{
                    $columndata["data"][$key] = $value;
                }
            }
            $columndata = array_filter($columndata);

           
            array_walk($columndata, function (&$item) {
                if(isset($item['wadmpr'])) $item['Provinsi'] = $item['wadmpr']; unset($item['wadmpr']);
                if(isset($item['wadmkk'])) $item['Kabupaten'] = $item['wadmkk']; unset($item['wadmkk']);
                if(isset($item['wadmkc'])) $item['Kecamatan'] = $item['wadmkc']; unset($item['wadmkc']);
                if(isset($item['ptnobjname']))  $item['PTN'] = $item['ptnobjname']; unset($item['ptnobjname']);
                if(isset($item['psnobjname']))  $item['PSN'] = $item['psnobjname']; unset($item['psnobjname']);
                if(isset($item['pfnobjname']))  $item['PFN'] = $item['pfnobjname']; unset($item['pfnobjname']);
                if(isset($item['pmnobjname']))  $item['PMN'] = $item['pmnobjname']; unset($item['pmnobjname']);
                if(isset($item['tahun_data']))  $item['Tahun'] = $item['tahun_data']; unset($item['tahun_data']);
                if(isset($item['luaswh']))  $item['Luas'] = $item['luaswh']; unset($item['luaswh']);
                if(isset($item['luas_ha']))  $item['Luas(ha)'] = $item['luas_ha']; unset($item['luas_ha']);
                if(isset($item['luas_m2']))  $item['Luas(m2)'] = $item['luas_m2']; unset($item['luas_m2']);
                if(isset($item['ket']))  $item['Keterangan'] = $item['ket']; unset($item['ket']);
                if(isset($item['vname']))  $item['Ketersediaan'] = $item['vname']; unset($item['vname']);
                if(isset($item['gname100']))  $item['PenggunaanLama'] = $item['gname100']; unset($item['gname100']);
                if(isset($item['gq_name']))  $item['Perubahan'] = $item['gq_name']; unset($item['gq_name']);
                if(isset($item['wname']))  $item['RTRW'] = $item['wname']; unset($item['wname']);
                if(isset($item['nname']))  $item['Kesesuaian'] = $item['nname']; unset($item['nname']);
                if(isset($item['oname']))  $item['Gambaranumum'] = $item['oname']; unset($item['oname']);
                if(isset($item['kaw_hutan']))  $item['Kawasan'] = $item['kaw_hutan']; unset($item['kaw_hutan']);
                if(isset($item['gqname']))  $item['Perubahan'] = $item['gqname']; unset($item['gqname']);
                if(isset($item['vname_rek']))  $item['Ketersediaan'] = $item['vname_rek']; unset($item['vname_rek']);
                if(isset($item['qname']))  $item['PenggunaanAkhir'] = $item['qname']; unset($item['qname']);
                if(isset($item['gname10']))  $item['PenggunaanLama'] = $item['gname10']; unset($item['gname10']);
                if(isset($item['keterangan']))  $item['Keterangan'] = $item['keterangan']; unset($item['keterangan']);
                if(isset($item['q']))  $item['PenggunaanAkhir'] = $item['q']; unset($item['q']);
                if(isset($item['w']))  $item['RTRW'] = $item['w']; unset($item['w']);
                if(isset($item['hgu_reclas']))  $item['HGU'] = $item['hgu_reclas']; unset($item['hgu_reclas']);
                if(isset($item['tipe']))  $item['Tipe'] = $item['tipe']; unset($item['tipe']);
                if(isset($item['pemilik']))  $item['Pemilik'] = $item['pemilik']; unset($item['pemilik']);
                if(isset($item['sk']))  $item['SK'] = $item['sk']; unset($item['sk']);
                if(isset($item['komoditas']))  $item['komoditas'] = $item['komoditas']; unset($item['komoditas']);
                if(isset($item['hat_reclas']))  $item['HAT'] = $item['hat_reclas']; unset($item['hat_reclas']);
                if(isset($item['lp2b_recla']))  $item['LP2B'] = $item['lp2b_recla']; unset($item['lp2b_recla']);
                if(isset($item['ppb_reclas']))  $item['PPB'] = $item['ppb_reclas']; unset($item['ppb_reclas']);
                if(isset($item['kh_reclass']))  $item['Kawasan'] = $item['kh_reclass']; unset($item['kh_reclass']);
                if(isset($item['kssn']))  $item['KSSN'] = $item['kssn']; unset($item['kssn']);
                if(isset($item['ktsdn']))  $item['KTSDN'] = $item['ktsdn']; unset($item['ktsdn']);
                if(isset($item['luasha']))  $item['Luas(ha)'] = $item['luasha']; unset($item['luasha']);
                if(isset($item['verifikasi']))  $item['Verifikasi'] = $item['verifikasi']; unset($item['verifikasi']);
                if(isset($item['pemohon']))  $item['Pemohon'] = $item['pemohon']; unset($item['pemohon']);
                if(isset($item['perusahaan']))  $item['Perusahaan'] = $item['perusahaan']; unset($item['perusahaan']);
                if(isset($item['nib']))  $item['NIB'] = $item['nib']; unset($item['nib']);
                if(isset($item['peruntukan']))  $item['Peruntukan'] = $item['peruntukan']; unset($item['peruntukan']);
                if(isset($item['alamat_ptk']))  $item['Alamat'] = $item['alamat_ptk']; unset($item['alamat_ptk']);
                if(isset($item['no_berkas']))  $item['No.Berkas'] = $item['no_berkas']; unset($item['no_berkas']);
                if(isset($item['tg_risalah']))  $item['Tgl.Risalah'] = $item['tg_risalah']; unset($item['tg_risalah']);
                if(isset($item['no_ptp']))  $item['No.PTP'] = $item['no_ptp']; unset($item['no_ptp']);
                if(isset($item['tgl_ptp']))  $item['Tgl.PTP'] = $item['tgl_ptp']; unset($item['tgl_ptp']);
                if(isset($item['no_ilok']))  $item['No.Ilok'] = $item['no_ilok']; unset($item['no_ilok']);
                if(isset($item['tgl_ilok']))  $item['Tgl.Ilok'] = $item['tgl_ilok']; unset($item['tgl_ilok']);
                if(isset($item['posisi']))  $item['Posisi'] = $item['posisi']; unset($item['posisi']);
                if(isset($item['info']))  $item['Info'] = $item['info']; unset($item['info']);
                if(isset($item['klas']))  $item['Kelas'] = $item['klas']; unset($item['klas']);
                if(isset($item['luas']))  $item['Luas(ha)'] = $item['luas']; unset($item['luas']);
                if(isset($item['lokasi']))  $item['Lokasi'] = $item['lokasi']; unset($item['lokasi']);
                if(isset($item['nama_perusahaan']))  $item['Perusahaan'] = $item['nama_perusahaan']; unset($item['nama_perusahaan']);
                if(isset($item['alamat_perusahaan']))  $item['Alamat'] = $item['alamat_perusahaan']; unset($item['alamat_perusahaan']);
                if(isset($item['rencana_kegiatan']))  $item['Rencana'] = $item['rencana_kegiatan']; unset($item['rencana_kegiatan']);
                if(isset($item['luasha']))  $item['Luas(ha)'] = $item['luasha']; unset($item['luasha']);
                if(isset($item['kbli']))  $item['KBLI'] = $item['kbli']; unset($item['kbli']);
                if(isset($item['jns_ptp']))  $item['Jenis'] = $item['jns_ptp']; unset($item['jns_ptp']);
                if(isset($item['land_use']))  $item['Penggunaan'] = $item['land_use']; unset($item['land_use']);
                if(isset($item['tata_ruang']))  $item['RTRW'] = $item['tata_ruang']; unset($item['tata_ruang']);
                if(isset($item['kesesuain']))  $item['Kesesuaian'] = $item['kesesuain']; unset($item['kesesuain']);
                if(isset($item['ketersedia']))  $item['Ketersediaan'] = $item['ketersedia']; unset($item['ketersedia']);
                if(isset($item['renc_kegiatan']))  $item['Rencana'] = $item['renc_kegiatan']; unset($item['renc_kegiatan']);
                if(isset($item['kd_bidang']))  $item['KD.Bidang'] = $item['kd_bidang']; unset($item['kd_bidang']);
                if(isset($item['tanggal_sk']))  $item['Tgl.SK'] = $item['tanggal_sk']; unset($item['tanggal_sk']);
                if(isset($item['kriteria']))  $item['Kriteria'] = $item['kriteria']; unset($item['kriteria']);
                if(isset($item['kelayakan']))  $item['Kelayakan'] = $item['kelayakan']; unset($item['kelayakan']);
                if(isset($item['aprogram']))  $item['Program'] = $item['aprogram']; unset($item['aprogram']);
                if(isset($item['kluster']))  $item['Kluster'] = $item['kluster']; unset($item['kluster']);
                if(isset($item['penggarap']))  $item['Penggarap'] = $item['penggarap']; unset($item['penggarap']);
                if(isset($item['jml_kk']))  $item['Jml.KK'] = $item['jml_kk']; unset($item['jml_kk']);
                if(isset($item['pokgar']))  $item['Garapan'] = $item['pokgar']; unset($item['pokgar']);
                if(isset($item['bdn_hukum']))  $item['Bdn.Hukum'] = $item['bdn_hukum']; unset($item['bdn_hukum']);
                if(isset($item['rncana_keg']))  $item['Rencana'] = $item['rncana_keg']; unset($item['rncana_keg']);
                if(isset($item['nomor_ptp']))  $item['No.PTP'] = $item['nomor_ptp']; unset($item['nomor_ptp']);
                if(isset($item['jenis_ptp']))  $item['Jenis.PTP'] = $item['jenis_ptp']; unset($item['jenis_ptp']);
                if(isset($item['hasil_ptp']))  $item['Hasil'] = $item['hasil_ptp']; unset($item['hasil_ptp']);
                if(isset($item['desa']))  $item['Desa'] = $item['desa']; unset($item['desa']);
                if(isset($item['nama_pulau']))  $item['Pulau'] = $item['nama_pulau']; unset($item['nama_pulau']);
                if(isset($item['pemilikan']))  $item['Pemilikan'] = $item['pemilikan']; unset($item['pemilikan']);
                if(isset($item['kawasan_hu']))  $item['Kawasan'] = $item['kawasan_hu']; unset($item['kawasan_hu']);
                //if(isset($item['pt']))  $item['Perusahaan'] = $item['pt']; unset($item['pt']);
                if(isset($item['tutupan_la']))  $item['Lahan'] = $item['tutupan_la']; unset($item['tutupan_la']);
                if(isset($item['arahan']))  $item['Arahan'] = $item['arahan']; unset($item['arahan']);
                if(isset($item['riwayat']))  $item['Riwayat'] = $item['riwayat']; unset($item['riwayat']);
                if(isset($item['oname19']))  $item['Gambaranumum'] = $item['oname19']; unset($item['oname19']);
                if(isset($item['qname19']))  $item['PenggunaanAkhir'] = $item['qname19']; unset($item['qname19']);
                if(isset($item['pmanfaatan']))  $item['Pemanfaatan'] = $item['pmanfaatan']; unset($item['pmanfaatan']);
                if(isset($item['wname19']))  $item['RTRW'] = $item['wname19']; unset($item['wname19']);
                if(isset($item['kegiatan']))  $item['Kegiatan'] = $item['kegiatan']; unset($item['kegiatan']);
                if(isset($item['pengelolah']))  $item['Pengolahan'] = $item['pengelolah']; unset($item['pengelolah']);
                if(isset($item['program']))  $item['Program'] = $item['program']; unset($item['program']);
                if(isset($item['b_name']))  $item['Lereng'] = $item['b_name']; unset($item['b_name']);
                if(isset($item['u_name']))  $item['Kedalaman'] = $item['u_name']; unset($item['u_name']);
                if(isset($item['x_name']))  $item['Tekstur'] = $item['x_name']; unset($item['x_name']);
                if(isset($item['d_name']))  $item['Drainase'] = $item['d_name']; unset($item['d_name']);
                if(isset($item['e_name']))  $item['Erosi'] = $item['e_name']; unset($item['e_name']);
                if(isset($item['l_name']))  $item['Faktor'] = $item['l_name']; unset($item['l_name']);
                if(isset($item['no_perda']))  $item['No.Perda'] = $item['no_perda']; unset($item['no_perda']);
                if(isset($item['hname']))  $item['Sungai'] = $item['hname']; unset($item['hname']);
                if(isset($item['panjang_m']))  $item['Panjang(km)'] = $item['panjang_m']; unset($item['panjang_m']);
                if(isset($item['ksname']))  $item['Jalan'] = $item['ksname']; unset($item['ksname']);
                if(isset($item['kesesuaian']))  $item['Kesesuaian'] = $item['kesesuaian']; unset($item['kesesuaian']);
                if(isset($item['sedia_tnah']))  $item['Ketersediaan'] = $item['sedia_tnah']; unset($item['sedia_tnah']);
              
         

             });

    
            
            $columndata["geomtype"] = strtoupper($arrData->features[0]->geometry->type);

           
     
            $columndata["layer"] = $layer;

            echo json_encode($columndata);




         }     
    }

    public function info($layer, $bbox, $width, $height, $x, $y, $tolpixel=5) {

        //http://103.6.53.254:4980/atrpgt/index.php/wms/info/pgt:wp3wt_penataan_perbatasan/107.44628906250001~-2.9101249120129014~118.87207031250001~3.6778915094650726/395/587/107.44628906250001/-2.9101249120129014
      //  http://103.6.53.254:13480/kgis/index.php/wms/info/bpjt:belawan_medan_tanjung_morawa/98.6662287791637~3.5873506613204587~98.74405460790912~3.7029647537341526/395/587/98.68557840837101/3.64634365893995
        // echo "input: l $layer, b $bbox, w $width, h $height, x $x, y $y, t $tolpixel\r\n";

        

        $layer = trim(str_replace('%20', ' ', $layer));

        $layer = explode(':', $layer)[1];
     
        // echo ($bbox);

        $abox = $this->abox($bbox);
        // var_dump($abox);
        //echo $abox;
        if (!$abox) { echo '{"data": null}'; return; }
        if ($abox[0] > $abox[2]) { echo '{"data": null}'; return; }
        $w = intval($this->as_int($width));
        $deg_per_pixel = ($abox[2] - $abox[0])/$w;
        $tol = $tolpixel * $deg_per_pixel;     

        // echo "input2: l<br> $layer, b $bbox, w $width, h $height, x $x, y $y, t $tol\r\n";

        $d = $this->db->select('*')->where('table_schema', 'spatial')->where('table_name', $layer)->get('information_schema.tables')->result_array(); 
        // echo $this->db->last_query();
        if (sizeof($d) < 1) { echo '{"data": null}'; return; }

        $stable = explode(',', $d[0]['table_name']);
        // $found = false;
        foreach($stable as $t) {
            $sql = "select * from spatial.$t where st_intersects(st_transform(geom, 4326), st_buffer(st_geomfromtext('POINT($x $y)', 4326), 0.00085920188673104)) limit 1";
            // $sql = "select * from spatial.$t where st_contains(st_buffer(st_geometryfromtext('POINT($x $y)', 4326), $tol), geom) limit 1";
            //$sql = "select * from spatial.$t where st_intersects(geom, st_buffer(st_geomfromtext('POINT(123.94784945273686 -9.406501809654486)', 4326), $tol)) limit 1";
            //$sql = "SELECT * FROM spatial.$t WHERE ST_DWithin(geom, ST_GeomFromText('POINT($x $y)', 4326), $tol) limit 1";
            // echo $sql;
            $rows = $this->db->query($sql)->result_array();
           
            if (sizeof($rows) > 0) {
                unset($rows[0]['geom']);
                $jdata = json_encode($rows[0]);
                echo '{"datax": ' . $jdata .'}';
                return;
            }    
        }
        echo '{"data": null, "sql": "'.$sql.'"}';

    }


    public function centroid_wms_services() {
        if (!is_cli()) return;

        echo "Setting map service centroid\n\n";
        $arr = $this->db->query("select id, map_service from map_services order by id")->result_array();
        $i = 1;
        $j = 1;
        $ctr = 1;
        $failed_service = [];
        foreach($arr as $msurl) {
            $id = $msurl['id'];
            $url = $msurl['map_service'];
            echo "\n---------------------------------------------\n".$ctr++.". Service URL: $id - $url\n";

            $data = file_get_contents("$url?f=json");
            $jextent = json_decode($data);
            if (!isset($jextent->extent)) {
                $j++;
                $failed_service[] = $url;
                continue;
            }

            $x1 = $jextent->extent->xmin;
            $y1 = $jextent->extent->ymin;
            $x2 = $jextent->extent->xmax;
            $y2 = $jextent->extent->ymax;
            $srid = $jextent->extent->spatialReference->latestWkid;
            // echo "x1: $x1, y1: $y1, x2: $x2, y2: $y2, srid:$srid\n";

            $xcen = $x1 + (($x2-$x1)/2);
            $ycen = $y1 + (($y2-$y1)/2);
            // echo "xcen: $xcen, ycen: $ycen\n";

            $sql = "select st_x(st_transform(st_geomfromtext('POINT($xcen $ycen)', $srid), 4326)) as xcen, 
                    st_y(st_transform(st_geomfromtext('POINT($xcen $ycen)', $srid), 4326))  as ycen";
            $arrcen = $this->db->query($sql)->result_array();
            echo "\tCentroid: ".$arrcen[0]['xcen'] .','. $arrcen[0]['ycen']."\n";

            $data = array (
                'center_longitude' => $arrcen[0]['xcen'],
                'center_latitude' => $arrcen[0]['ycen']
            );
            $this->db->where('id', $id)->update('map_services', $data);
            $i++;
        }

        echo "\n\n" . max(0, $i-1) . " data updated.";
        echo "\n" . max(0, $j-1) . " data failed.\n\n";

        if (sizeof($failed_service) > 0) {
            echo "\nInaccessible service url:\n";
            $k = 1;
            foreach($failed_service as $f) {
                echo "\t" . $k++. ". $f\n"; 
            }
        }
    }

}