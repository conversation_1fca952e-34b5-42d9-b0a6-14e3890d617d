<?php
defined('BASEPATH') OR exit('No direct script access allowed');
?>
<style>
 
    #tabExport 
    {
        table-layout:fixed;
        width:100%;
    }
    #tabExport th {
        white-space: normal;
        word-wrap: break-word;
        border: 1px solid black;
        text-align: left;
        padding: 8px;
    }
    #tabExport td {
        white-space: normal;
        word-wrap: break-word;
        border: 1px solid black;
        text-align: left;
        padding: 8px;
    }
</style>
 <div class="page-body">
    <!-- Server Side Processing table start -->
    <div class="card">
        <div class="card-header">
            <h5><?=$title;?></h5>
        </div>
        <div class="card-block">
        <div class="row">    
                <div class="col-sm-12">
                    <!-- <input type="text" id="plugins4_q" placeholder="Cari grup user" class="form-control fa fa-search" style="width:50%;"> -->
                    <!-- <button type="button" class="btn btn-primary waves-effect waves-light add" onclick="dtTambahRow();">Tambah Neraca Kabupaten
                    </button> -->
                </div>
                <div class="col-md-4" style="margin-top:20px;display:inline">
                        <label style="font-size:15pt;">Provinsi</label>
                        <select id="ikd_prov"onchange="filterChange($(this).val())" name="ikd_prov" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                </div>
                <div class="col-md-4" style="margin-top:20px;display:inline">
                        <label style="font-size:15pt;">Kabupaten/Kota</label>
                        <select id="ikd_kabkot"onchange="filterChange('nochange')" name="ikd_kabkot" class="bootstrap-select form-control" data-live-search="true">
                        </select>
                </div>
                
                
                <div class="col-md-4" style="margin-top:20px;display:inline">
                        <label style="font-size:15pt;">Tahun</label>
                        <select id="itahun_data"onchange="filterChange('nochange')" name="itahun_data" class="bootstrap-select form-control" data-live-search="true">
                            <option value="">Semua Tahun</option>
                            <?php
                            $y=intVal(date('Y'));

                            for ($i=$y; $i >= 2000 ; $i--) { 
                                
                                echo '<option value="'.$i.'">'.$i.'</option>';
                            }
                            ?>
                        </select>
                </div>
                
            </div>
            <br/>
            <div class="dt-responsive table-responsive">
                <button class="btn btn-primary" onclick="exp()">Export PDF</button>
                <table id="dt-server-processing" class="table table-striped table-bordered nowrap">
                    <thead>
                        <tr>
                            <th rowspan="4">No</th>
                            <th>Subjek PTP PKKPR/RKKPR Stranas</th>
                            <th>PTP</th>
                            <th>Letak</th>
                            <th>Kegiatan</th>
                            <th>PKKPR/RKKPR Stranas</th>
                            <th rowspan="4">Keterangan</th>
                        </tr>
                        <tr>
                            <th>a. Pemohon</th>
                            <th>(1) No PTP</th>
                            <th>a) Kab/Kota</th>
                            <th>a. Rencana kegiatan</th>
                            <th>1. No KKPR</th>
                        </tr>
                        <tr>
                            <th rowspan="2">b. Bertindak Atas Nama</th>
                            <th>(2) Tanggal PTP</th>
                            <th>b) Kecamatan</th>
                            <th  rowspan="2">b. Dasar Hukum penetapan strategis nasional</th>
                            <th>2. Tanggal KKPR</th>
                        </tr>
                        <tr>
                            <th>(3) Luas (m2)</th>
                            <th>c) Kelurahan/Desa</th>
                            <th>3. Luas (m2)</th>
                        </tr>
                    </thead>
                </table>
            </div>
            <!-- <div id="exports">
                <h2><center>TABEL REKAPITULASI PTP PKKPR UNTUK KEGIATAN NONBERUSAHA</center></h2>
                <table class="table " id="tabExport" style="border:1px solid black;width:100%">
                                                
                </table>
            </div> -->
        </div>
    </div>
    <!-- Server Side Processing table end -->
</div>   


<?php echo $jv_script; ?>
<?php echo $modal_tambah; ?>
<!-- <?php echo $modal_download; ?> -->
<?php echo $modal_edit; ?>
